{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "lib": ["es2022"], "outDir": "./dist", "rootDir": "./", "declaration": true, "declarationDir": "./dist", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"]}, "include": ["bin/**/*", "lib/**/*", "test/**/*"], "exclude": ["node_modules", "cdk.out", "dist"]}