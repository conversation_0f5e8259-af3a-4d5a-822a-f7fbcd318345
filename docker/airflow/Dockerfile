FROM apache/airflow:3.0.2-python3.11

# Switch to root for system packages (following official pattern)
USER root

# Install system dependencies (minimal, focused)
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    unzip \
    netcat-traditional \
    && rm -rf /var/lib/apt/lists/*

# Switch back to airflow user (preserve official user setup)
USER airflow

# Install Python packages (optimize for caching)
RUN pip install --no-cache-dir \
    apache-airflow-providers-amazon \
    boto3>=1.34.0 \
    botocore>=1.34.0 \
    s3fs>=2024.1.0 \
    pandas>=2.0.0 \
    numpy>=1.24.0 \
    requests>=2.31.0 \
    sqlalchemy>=2.0.0 \
    pydantic>=2.0.0 \
    jinja2>=3.1.0

# Create directories (align with official structure)
RUN mkdir -p /opt/airflow/dags /opt/airflow/plugins

# Set environment variables for optimization
ENV AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=True
ENV AIRFLOW__CORE__LOAD_EXAMPLES=False
ENV AIRFLOW__API__EXPOSE_CONFIG=False
ENV AIRFLOW__LOGGING__LOGGING_LEVEL=INFO

# DO NOT override ENTRYPOINT - use official /entrypoint
# This preserves all official functionality and compatibility