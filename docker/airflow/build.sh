#!/bin/bash

# Airflow自定义镜像构建脚本

set -e

# 默认参数
ENVIRONMENT="dev"
IMAGE_TAG="latest"
PUSH_TO_ECR=false
ECR_REGISTRY=""
AWS_REGION="ap-east-2"

# 帮助信息
show_help() {
    cat << EOF
用法: $0 [选项]

选项:
    -e, --env ENV           环境 (dev|prod) [默认: dev]
    -t, --tag TAG          镜像标签 [默认: latest]
    -p, --push             推送到ECR
    -r, --registry REG     ECR注册表地址
    --region REGION        AWS区域 [默认: ap-east-2]
    -h, --help             显示帮助信息

示例:
    $0 -e dev -t dev-latest
    $0 -e prod -t prod-v1.0.0 -p -r 123456789012.dkr.ecr.ap-east-2.amazonaws.com
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            -p|--push)
                PUSH_TO_ECR=true
                shift
                ;;
            -r|--registry)
                ECR_REGISTRY="$2"
                shift 2
                ;;
            --region)
                AWS_REGION="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 验证参数
validate_args() {
    if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
        echo "错误: 环境必须是 dev 或 prod"
        exit 1
    fi
    
    if [[ "$PUSH_TO_ECR" == true && -z "$ECR_REGISTRY" ]]; then
        echo "错误: 推送到ECR需要指定注册表地址"
        exit 1
    fi
}

# 构建镜像
build_image() {
    local image_name="yherp-airflow"
    local full_tag="${image_name}:${IMAGE_TAG}"
    
    echo "构建Airflow自定义镜像..."
    echo "环境: $ENVIRONMENT"
    echo "标签: $full_tag"
    
    # 构建镜像
    docker build \
        --platform linux/amd64 \
        --build-arg ENVIRONMENT="$ENVIRONMENT" \
        -t "$full_tag" \
        -f Dockerfile \
        .
    
    echo "镜像构建完成: $full_tag"
    
    # 如果需要推送到ECR
    if [[ "$PUSH_TO_ECR" == true ]]; then
        push_to_ecr "$image_name" "$IMAGE_TAG"
    fi
}

# 推送到ECR
push_to_ecr() {
    local image_name="$1"
    local tag="$2"
    local local_tag="${image_name}:${tag}"
    local remote_tag="${ECR_REGISTRY}/${image_name}:${tag}"
    
    echo "推送镜像到ECR..."
    echo "本地标签: $local_tag"
    echo "远程标签: $remote_tag"
    
    # 登录ECR
    echo "登录ECR..."
    aws ecr get-login-password --region "$AWS_REGION" | \
        docker login --username AWS --password-stdin "$ECR_REGISTRY"
    
    # 标记镜像
    docker tag "$local_tag" "$remote_tag"
    
    # 推送镜像
    docker push "$remote_tag"
    
    echo "镜像推送完成: $remote_tag"
}

# 主函数
main() {
    parse_args "$@"
    validate_args
    
    # 切换到脚本目录
    cd "$(dirname "$0")"
    
    build_image
    
    echo "构建过程完成！"
}

# 运行主函数
main "$@"