#!/bin/bash

# S3 PostgreSQL 数据库还原脚本
# 从 AWS S3 下载 gzip 压缩的数据库备份并直接还原到 PostgreSQL

set -e

# 常量配置
readonly DB_HOST="yuanhuiauroradatabase-pro-aurorapostgresqlclusterw-sespunnofj29.c3c8ccka8h7t.ap-east-2.rds.amazonaws.com"
readonly DB_USER="odoo_admin"
readonly DB_PASSWORD="0c1FK5hV3jUm4LXwyfZ6Myit00AM44Fl"
readonly DB_PORT="5432"

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查参数
check_arguments() {
    if [[ $# -ne 2 ]]; then
        echo "用法: $0 <s3_path> <database_name>"
        echo "示例: $0 s3://bucket/backup.sql.gz my_database"
        exit 1
    fi
}

# 检查依赖工具
check_dependencies() {
    log_info "检查依赖工具..."
    local tools=("aws" "psql" "gunzip")
    
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "缺少必要工具: $tool"
            exit 1
        fi
    done
    
    log_info "所有依赖工具已安装"
}

# 检查AWS配置
check_aws_config() {
    log_info "检查AWS配置..."
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS配置无效，请运行 aws configure"
        exit 1
    fi
    log_info "AWS配置验证成功"
}

# 验证S3文件
verify_s3_file() {
    local s3_path="$1"
    local bucket=$(echo "$s3_path" | sed 's|s3://||' | cut -d'/' -f1)
    local key="${s3_path#s3://$bucket/}"
    
    log_info "验证S3文件存在性和权限: $s3_path"
    
    # 检查文件是否存在
    if ! aws s3 ls "$s3_path" &> /dev/null; then
        log_error "S3文件不存在: $s3_path"
        exit 1
    fi
    
    # 测试下载权限
    log_info "测试文件下载权限..."
    if ! aws s3 cp "$s3_path" - --range bytes=0-1023 > /dev/null 2>&1; then
        log_error "无法下载S3文件: $s3_path"
        log_info "请检查S3权限设置"
        exit 1
    fi
    
    log_info "S3文件验证和权限测试成功"
}

# 测试数据库连接
test_db_connection() {
    log_info "测试数据库连接..."
    if ! PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "SELECT version();" &> /dev/null; then
        log_error "无法连接到数据库"
        exit 1
    fi
    log_info "数据库连接测试成功"
}

# 创建odoo用户和相关角色
create_odoo_users_and_roles() {
    log_step "创建odoo用户和相关角色"
    
    # 检查odoo角色是否已存在
    local odoo_exists
    odoo_exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -tAc "SELECT 1 FROM pg_roles WHERE rolname='odoo';" 2>/dev/null || echo "")
    
    if [[ "$odoo_exists" == "1" ]]; then
        log_info "odoo角色已存在，跳过创建"
        return 0
    fi
    
    log_info "创建odoo用户和角色体系..."
    
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres << 'EOF'
-- 创建主要的odoo角色（用户）
CREATE ROLE odoo WITH 
    LOGIN 
    CREATEDB 
    NOCREATEROLE 
    NOSUPERUSER 
    INHERIT 
    NOREPLICATION 
    CONNECTION LIMIT -1
    PASSWORD 'odoo123';

COMMENT ON ROLE odoo IS 'Odoo application main user';

-- 创建odoo_readonly角色（只读权限组）
CREATE ROLE odoo_readonly WITH 
    NOLOGIN 
    NOCREATEDB 
    NOCREATEROLE 
    NOSUPERUSER 
    INHERIT;

COMMENT ON ROLE odoo_readonly IS 'Odoo read-only access role group';

-- 创建odoo_readwrite角色（读写权限组）
CREATE ROLE odoo_readwrite WITH 
    NOLOGIN 
    NOCREATEDB 
    NOCREATEROLE 
    NOSUPERUSER 
    INHERIT;

COMMENT ON ROLE odoo_readwrite IS 'Odoo read-write access role group';

-- 创建odoo_admin角色（管理权限）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'odoo_admin') THEN
        CREATE ROLE odoo_admin WITH 
            LOGIN 
            CREATEDB 
            CREATEROLE 
            NOSUPERUSER 
            INHERIT 
            PASSWORD 'odoo_admin123';
        COMMENT ON ROLE odoo_admin IS 'Odoo administrative user';
    END IF;
END $$;

-- 建立角色层次关系
GRANT odoo_readonly TO odoo_readwrite;
GRANT odoo_readwrite TO odoo;
GRANT odoo TO odoo_admin;

-- 显示创建的角色
SELECT 
    rolname as role_name,
    rolcanlogin as can_login,
    rolcreatedb as can_create_db,
    rolcreaterole as can_create_role
FROM pg_roles 
WHERE rolname LIKE '%odoo%'
ORDER BY rolname;
EOF
    
    if [[ $? -eq 0 ]]; then
        log_info "✓ odoo用户和角色创建成功"
        log_info "  - odoo: 主要应用用户 (密码: odoo123)"
        log_info "  - odoo_admin: 管理用户 (密码: odoo_admin123)"
        log_info "  - odoo_readonly: 只读权限组"
        log_info "  - odoo_readwrite: 读写权限组"
    else
        log_error "✗ odoo用户和角色创建失败"
        exit 1
    fi
}

# 检查数据库是否存在
check_database_exists() {
    local db_name="$1"
    local db_exists
    db_exists=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -tAc "SELECT 1 FROM pg_database WHERE datname='$db_name';" 2>/dev/null || echo "")
    
    if [[ "$db_exists" == "1" ]]; then
        log_warn "数据库 '$db_name' 已存在"
        read -p "是否要删除并重新创建? (y/N): " confirm
        if [[ "$confirm" =~ ^[Yy]$ ]]; then
            log_info "删除现有数据库..."
            PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "DROP DATABASE IF EXISTS \"$db_name\";"
        else
            log_error "数据库已存在，操作取消"
            exit 1
        fi
    fi
}

# 创建数据库
create_database() {
    local db_name="$1"
    
    log_info "创建数据库: $db_name"
    
    # 创建数据库并设置odoo为所有者
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres -c "
CREATE DATABASE \"$db_name\" 
    WITH OWNER = odoo 
    ENCODING = 'UTF8' 
    LC_COLLATE = 'en_US.UTF-8' 
    LC_CTYPE = 'en_US.UTF-8' 
    TEMPLATE = template0;"
    
    if [[ $? -eq 0 ]]; then
        log_info "数据库创建成功"
        
        # 设置数据库权限
        log_info "设置数据库权限..."
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d postgres << EOF
-- 授予连接权限
GRANT CONNECT ON DATABASE "$db_name" TO odoo;
GRANT CONNECT ON DATABASE "$db_name" TO odoo_readonly;
GRANT CONNECT ON DATABASE "$db_name" TO odoo_readwrite;
GRANT CONNECT ON DATABASE "$db_name" TO odoo_admin;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO odoo_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO odoo_readwrite;
EOF
    else
        log_error "数据库创建失败"
        exit 1
    fi
}

# 还原数据库
restore_database() {
    local s3_path="$1"
    local db_name="$2"
    
    log_info "开始从S3还原数据库..."
    log_info "源文件: $s3_path"
    log_info "目标数据库: $db_name"
    
    # 获取文件大小
    local file_size
    file_size=$(aws s3 ls "$s3_path" | awk '{print $3}')
    if [[ -n "$file_size" ]]; then
        log_info "文件大小: $(numfmt --to=iec $file_size)"
    fi
    
    # 创建临时目录用于存储下载的文件
    local temp_dir=$(mktemp -d)
    local backup_filename=$(basename "$s3_path")
    local local_backup_file="$temp_dir/$backup_filename"
    local error_log="$temp_dir/restore_error.log"
    
    # 清理函数
    cleanup() {
        log_info "清理临时文件..."
        rm -rf "$temp_dir"
    }
    
    # 设置退出时清理
    trap cleanup EXIT
    
    log_step "步骤1: 从S3下载备份文件到本地"
    log_info "下载位置: $local_backup_file"
    
    if aws s3 cp "$s3_path" "$local_backup_file" --progress; then
        log_info "✓ S3文件下载成功"
        
        # 验证下载的文件
        if [[ ! -f "$local_backup_file" ]]; then
            log_error "下载的文件不存在: $local_backup_file"
            exit 1
        fi
        
        local downloaded_size=$(stat -f%z "$local_backup_file" 2>/dev/null || stat -c%s "$local_backup_file" 2>/dev/null)
        log_info "本地文件大小: $(numfmt --to=iec $downloaded_size)"
    else
        log_error "✗ S3文件下载失败"
        exit 1
    fi
    
    log_step "步骤2: 解压并还原数据库"
    log_info "执行: gunzip | psql 还原操作"
    
    # 执行还原，使用odoo用户作为默认所有者
    if gunzip -c "$local_backup_file" | PGPASSWORD="$DB_PASSWORD" psql \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$db_name" \
        -v ON_ERROR_STOP=0 \
        --single-transaction \
        -q \
        2>"$error_log"; then
        
        log_info "✓ 数据库还原完成"
        
        # 检查是否有重要错误
        if [[ -s "$error_log" ]]; then
            local important_errors
            important_errors=$(grep -v "does not exist" "$error_log" | grep -v "already exists" | grep -i "error" | head -5)
            if [[ -n "$important_errors" ]]; then
                log_warn "还原过程中的重要错误："
                echo "$important_errors"
            fi
        fi
    else
        log_error "✗ 数据库还原失败"
        if [[ -s "$error_log" ]]; then
            log_error "错误详情："
            cat "$error_log"
        fi
        exit 1
    fi
    
    # 修复对象所有者
    log_info "修复数据库对象所有者..."
    PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" << 'EOF'
-- 将所有表的所有者改为odoo
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ALTER TABLE public.' || quote_ident(r.tablename) || ' OWNER TO odoo';
    END LOOP;
END $$;

-- 将所有序列的所有者改为odoo
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN SELECT sequencename FROM pg_sequences WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ALTER SEQUENCE public.' || quote_ident(r.sequencename) || ' OWNER TO odoo';
    END LOOP;
END $$;

-- 将所有视图的所有者改为odoo
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN SELECT viewname FROM pg_views WHERE schemaname = 'public'
    LOOP
        EXECUTE 'ALTER VIEW public.' || quote_ident(r.viewname) || ' OWNER TO odoo';
    END LOOP;
END $$;
EOF
    
    log_info "数据库还原流程完成"
}

# 验证还原结果
verify_restore() {
    local db_name="$1"
    
    log_info "验证还原结果..."
    
    # 检查表数量
    local table_count
    table_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -tAc "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null || echo "0")
    
    log_info "数据库表数量: $table_count"
    
    if [[ "$table_count" -eq 0 ]]; then
        log_warn "数据库中没有发现表，请检查备份文件内容"
    else
        log_info "数据库还原验证成功"
        
        # 显示一些基本信息
        log_info "数据库基本信息："
        PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$db_name" -c "
SELECT 
    schemaname,
    COUNT(*) as table_count
FROM pg_tables 
WHERE schemaname IN ('public')
GROUP BY schemaname;"
    fi
}

# 主函数
main() {
    local s3_path="$1"
    local db_name="$2"
    
    log_info "开始S3数据库还原脚本"
    log_info "S3路径: $s3_path"
    log_info "目标数据库: $db_name"
    log_info "数据库主机: $DB_HOST:$DB_PORT"
    
    check_arguments "$@"
    check_dependencies
    check_aws_config
    verify_s3_file "$s3_path"
    test_db_connection
    create_odoo_users_and_roles
    check_database_exists "$db_name"
    create_database "$db_name"
    restore_database "$s3_path" "$db_name"
    verify_restore "$db_name"
    
    log_info "数据库还原完成!"
    log_info "连接命令:"
    log_info "  管理员: psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $db_name"
    log_info "  odoo用户: psql -h $DB_HOST -p $DB_PORT -U odoo -d $db_name"
}

main "$@"

