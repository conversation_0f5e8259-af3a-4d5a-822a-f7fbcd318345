#!/bin/bash

# Apache Airflow 数据库初始化脚本
# 修复EFS挂载问题后的简化版本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 获取环境变量
get_environment_info() {
    log_info "获取环境信息..."
    
    ENVIRONMENT=${1:-dev}
    REGION=${AWS_DEFAULT_REGION:-ap-east-2}
    
    log_info "环境: $ENVIRONMENT"
    log_info "区域: $REGION"
    
    # 获取集群名称
    CLUSTER_NAME=$(aws cloudformation describe-stacks \
        --stack-name "YuanhuiEcs-${ENVIRONMENT}" \
        --query 'Stacks[0].Outputs[?OutputKey==`ClusterName`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$CLUSTER_NAME" ]; then
        log_error "无法获取ECS集群名称"
        exit 1
    fi
    
    log_info "ECS集群: $CLUSTER_NAME"
    
    # 获取子网ID
    SUBNET_ID=$(aws cloudformation describe-stacks \
        --stack-name "YuanhuiNetwork-${ENVIRONMENT}" \
        --query 'Stacks[0].Outputs[?OutputKey==`PrivateSubnetIds`].OutputValue' \
        --output text | cut -d',' -f1 2>/dev/null || echo "")
    
    if [ -z "$SUBNET_ID" ]; then
        log_error "无法获取子网ID"
        exit 1
    fi
    
    log_info "子网ID: $SUBNET_ID"
    
    # 获取安全组ID
    SECURITY_GROUP_ID=$(aws cloudformation describe-stacks \
        --stack-name "YuanhuiNetwork-${ENVIRONMENT}" \
        --query 'Stacks[0].Outputs[?OutputKey==`EcsSecurityGroupId`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    if [ -z "$SECURITY_GROUP_ID" ]; then
        log_error "无法获取安全组ID"
        exit 1
    fi
    
    log_info "安全组ID: $SECURITY_GROUP_ID"
}

# 等待Airflow栈部署完成
wait_for_airflow_stack() {
    log_step "等待Airflow栈部署完成..."
    
    while true; do
        STACK_STATUS=$(aws cloudformation describe-stacks \
            --stack-name "YuanhuiAirflow-${ENVIRONMENT}" \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "STACK_NOT_FOUND")
        
        case $STACK_STATUS in
            "CREATE_COMPLETE")
                log_info "Airflow栈部署完成"
                break
                ;;
            "CREATE_IN_PROGRESS")
                log_info "Airflow栈正在部署中，等待..."
                sleep 30
                ;;
            "CREATE_FAILED"|"ROLLBACK_*")
                log_error "Airflow栈部署失败: $STACK_STATUS"
                exit 1
                ;;
            "STACK_NOT_FOUND")
                log_error "Airflow栈不存在"
                exit 1
                ;;
            *)
                log_warn "未知的栈状态: $STACK_STATUS"
                sleep 30
                ;;
        esac
    done
}

# 运行数据库初始化任务
run_init_task() {
    log_step "运行Airflow数据库初始化任务..."
    
    # 检查初始化任务定义是否存在
    TASK_DEF_EXISTS=$(aws ecs describe-task-definition \
        --task-definition "airflow-init-${ENVIRONMENT}" \
        --query 'taskDefinition.taskDefinitionArn' \
        --output text 2>/dev/null || echo "NOT_FOUND")
    
    if [ "$TASK_DEF_EXISTS" = "NOT_FOUND" ]; then
        log_error "初始化任务定义不存在: airflow-init-${ENVIRONMENT}"
        exit 1
    fi
    
    log_info "找到初始化任务定义: $TASK_DEF_EXISTS"
    
    # 运行初始化任务
    log_info "启动初始化任务..."
    TASK_ARN=$(aws ecs run-task \
        --cluster "$CLUSTER_NAME" \
        --task-definition "airflow-init-${ENVIRONMENT}" \
        --launch-type EC2 \
        --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_ID],securityGroups=[$SECURITY_GROUP_ID],assignPublicIp=DISABLED}" \
        --count 1 \
        --query 'tasks[0].taskArn' \
        --output text)
    
    if [ -z "$TASK_ARN" ] || [ "$TASK_ARN" = "None" ]; then
        log_error "无法启动初始化任务"
        exit 1
    fi
    
    log_info "初始化任务已启动: $TASK_ARN"
    
    # 等待任务完成
    log_info "等待初始化任务完成..."
    
    while true; do
        TASK_STATUS=$(aws ecs describe-tasks \
            --cluster "$CLUSTER_NAME" \
            --tasks "$TASK_ARN" \
            --query 'tasks[0].lastStatus' \
            --output text)
        
        log_info "任务状态: $TASK_STATUS"
        
        if [ "$TASK_STATUS" = "STOPPED" ]; then
            break
        fi
        
        sleep 10
    done
    
    # 检查任务退出代码
    EXIT_CODE=$(aws ecs describe-tasks \
        --cluster "$CLUSTER_NAME" \
        --tasks "$TASK_ARN" \
        --query 'tasks[0].containers[0].exitCode' \
        --output text)
    
    if [ "$EXIT_CODE" = "0" ]; then
        log_info "数据库初始化任务成功完成"
    else
        log_error "数据库初始化任务失败，退出代码: $EXIT_CODE"
        
        # 显示日志
        log_info "获取任务日志..."
        TASK_ID=$(echo "$TASK_ARN" | cut -d'/' -f3)
        
        aws logs get-log-events \
            --log-group-name "/ecs/airflow-init" \
            --log-stream-name "ecs/AirflowInitContainer/$TASK_ID" \
            --query 'events[].message' \
            --output text 2>/dev/null || log_warn "无法获取日志"
        
        exit 1
    fi
}

# 验证Airflow服务状态
verify_airflow_services() {
    log_step "验证Airflow服务状态..."
    
    # 检查webserver服务
    WEBSERVER_STATUS=$(aws ecs describe-services \
        --cluster "$CLUSTER_NAME" \
        --services "airflow-webserver-${ENVIRONMENT}" \
        --query 'services[0].status' \
        --output text 2>/dev/null || echo "NOT_FOUND")
    
    log_info "Webserver服务状态: $WEBSERVER_STATUS"
    
    # 检查scheduler服务
    SCHEDULER_STATUS=$(aws ecs describe-services \
        --cluster "$CLUSTER_NAME" \
        --services "airflow-scheduler-${ENVIRONMENT}" \
        --query 'services[0].status' \
        --output text 2>/dev/null || echo "NOT_FOUND")
    
    log_info "Scheduler服务状态: $SCHEDULER_STATUS"
    
    # 获取Webserver URL
    WEBSERVER_URL=$(aws cloudformation describe-stacks \
        --stack-name "YuanhuiAirflow-${ENVIRONMENT}" \
        --query 'Stacks[0].Outputs[?OutputKey==`AirflowWebserverURL`].OutputValue' \
        --output text 2>/dev/null || echo "")
    
    if [ -n "$WEBSERVER_URL" ]; then
        log_info "Airflow Webserver URL: $WEBSERVER_URL"
        log_info "默认登录凭据: admin / admin"
    fi
}

# 主函数
main() {
    echo "========================================"
    echo "Apache Airflow 3.0.2 数据库初始化"
    echo "========================================"
    echo ""
    
    get_environment_info "$1"
    wait_for_airflow_stack
    run_init_task
    verify_airflow_services
    
    log_info "Airflow初始化完成！"
}

# 执行主函数
main "$@"
