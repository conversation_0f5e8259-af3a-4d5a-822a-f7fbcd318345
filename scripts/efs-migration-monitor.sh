#!/bin/bash

#=====================================================
# EFS跨账号迁移 - 进度监控脚本
# 用途：实时监控迁移进度和状态
# 可独立运行，不影响主迁移进程
#=====================================================

set -euo pipefail

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROGRESS_FILE="$SCRIPT_DIR/migration-progress.json"
PID_FILE="$SCRIPT_DIR/efs-migration.pid"
REFRESH_INTERVAL=5

# Unicode字符
PROGRESS_CHARS="▏▎▍▌▋▊▉█"
CHECK_MARK="✓"
CROSS_MARK="✗"
CLOCK="⏰"
GEAR="⚙️"

# 检测终端特性
detect_terminal_capabilities() {
    # 检查是否支持Unicode
    if command -v locale >/dev/null 2>&1 && locale -k LC_CTYPE | grep -q 'charmap="UTF-8"'; then
        UNICODE_SUPPORT=true
    else
        UNICODE_SUPPORT=false
        PROGRESS_CHARS="12345678#"
        CHECK_MARK="OK"
        CROSS_MARK="FAIL"
        CLOCK="TIME"
        GEAR="WORK"
    fi
    
    # 检查终端宽度
    TERM_WIDTH=$(tput cols 2>/dev/null || echo 80)
}

# 清屏和光标控制
clear_screen() {
    clear
    printf '\033[H'  # 移动光标到左上角
}

hide_cursor() {
    printf '\033[?25l'
}

show_cursor() {
    printf '\033[?25h'
}

# 信号处理 - 确保退出时显示光标
cleanup_on_exit() {
    show_cursor
    echo ""
    exit 0
}

trap cleanup_on_exit SIGINT SIGTERM EXIT

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%H:%M:%S') - $1"
}

# 帮助函数
show_help() {
    cat << EOF
EFS迁移进度监控脚本

用法:
    $0 [选项]

选项:
    -h, --help                显示此帮助信息
    -i, --interval N          刷新间隔秒数 (默认: 5)
    -o, --output FORMAT       输出格式 (dashboard|json|simple)
    -f, --follow              持续监控模式
    -1, --once                仅显示一次状态
    --no-color               禁用颜色输出
    --no-auto-exit           禁用自动退出，需手动停止监控

输出格式:
    dashboard                 交互式仪表板 (默认)
    json                     JSON格式输出
    simple                   简单文本格式

示例:
    $0                       # 启动交互式监控
    $0 --once               # 显示当前状态并退出
    $0 --output json        # JSON格式输出
    $0 --interval 2         # 2秒刷新间隔
EOF
}

# 参数解析
OUTPUT_FORMAT="dashboard"
FOLLOW_MODE=true
NO_COLOR=false
NO_AUTO_EXIT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -i|--interval)
            REFRESH_INTERVAL="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FORMAT="$2"
            shift 2
            ;;
        -f|--follow)
            FOLLOW_MODE=true
            shift
            ;;
        -1|--once)
            FOLLOW_MODE=false
            shift
            ;;
        --no-color)
            NO_COLOR=true
            shift
            ;;
        --no-auto-exit)
            NO_AUTO_EXIT=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 禁用颜色
if [[ "$NO_COLOR" == "true" ]]; then
    RED=''
    GREEN=''
    YELLOW=''
    BLUE=''
    PURPLE=''
    CYAN=''
    BOLD=''
    NC=''
fi

# 读取进度数据
read_progress_data() {
    if [[ ! -f "$PROGRESS_FILE" ]]; then
        echo '{"stage": "not_started", "progress_percent": 0, "details": "Migration not started", "timestamp": ""}'
        return
    fi
    
    # 验证JSON格式
    if ! jq empty "$PROGRESS_FILE" 2>/dev/null; then
        echo '{"stage": "error", "progress_percent": 0, "details": "Invalid progress file", "timestamp": ""}'
        return
    fi
    
    cat "$PROGRESS_FILE"
}

# 检查迁移进程状态
check_migration_process() {
    if [[ ! -f "$PID_FILE" ]]; then
        echo "not_running"
        return
    fi
    
    local pid
    pid=$(cat "$PID_FILE" 2>/dev/null || echo "")
    
    if [[ -z "$pid" ]]; then
        echo "not_running"
        return
    fi
    
    if kill -0 "$pid" 2>/dev/null; then
        echo "running"
    else
        echo "stopped"
    fi
}

# 格式化时间戳
format_timestamp() {
    local timestamp="$1"
    if [[ -z "$timestamp" || "$timestamp" == "null" ]]; then
        echo "N/A"
        return
    fi
    
    # 转换ISO 8601时间戳为本地时间
    if command -v gdate >/dev/null 2>&1; then
        gdate -d "$timestamp" '+%Y-%m-%d %H:%M:%S' 2>/dev/null || echo "$timestamp"
    elif date --version >/dev/null 2>&1; then
        date -d "$timestamp" '+%Y-%m-%d %H:%M:%S' 2>/dev/null || echo "$timestamp"
    else
        echo "$timestamp"
    fi
}

# 计算ETA
calculate_eta() {
    local progress_percent="$1"
    local start_time="$2"
    
    if [[ "$progress_percent" -le 0 || -z "$start_time" ]]; then
        echo "N/A"
        return
    fi
    
    local current_time
    current_time=$(date +%s)
    local start_timestamp
    
    # 尝试解析时间戳
    if command -v gdate >/dev/null 2>&1; then
        start_timestamp=$(gdate -d "$start_time" +%s 2>/dev/null || echo "$current_time")
    elif date --version >/dev/null 2>&1; then
        start_timestamp=$(date -d "$start_time" +%s 2>/dev/null || echo "$current_time")
    else
        start_timestamp="$current_time"
    fi
    
    local elapsed=$((current_time - start_timestamp))
    if [[ $elapsed -le 0 ]]; then
        echo "N/A"
        return
    fi
    
    local rate=$((progress_percent * 1000 / elapsed))  # 千分比进度率
    if [[ $rate -le 0 ]]; then
        echo "N/A"
        return
    fi
    
    local remaining_percent=$((100 - progress_percent))
    local eta_seconds=$((remaining_percent * 1000 / rate))
    
    # 格式化ETA
    if [[ $eta_seconds -lt 60 ]]; then
        echo "${eta_seconds}秒"
    elif [[ $eta_seconds -lt 3600 ]]; then
        echo "$((eta_seconds / 60))分钟"
    else
        echo "$((eta_seconds / 3600))小时$((eta_seconds % 3600 / 60))分钟"
    fi
}

# 绘制进度条
draw_progress_bar() {
    local progress="$1"
    local width="${2:-50}"
    
    local filled=$((progress * width / 100))
    local empty=$((width - filled))
    
    local bar=""
    
    # 已完成部分
    for ((i=0; i<filled; i++)); do
        if [[ "$UNICODE_SUPPORT" == "true" ]]; then
            bar+="█"
        else
            bar+="#"
        fi
    done
    
    # 未完成部分
    for ((i=0; i<empty; i++)); do
        bar+=" "
    done
    
    echo "[$bar] ${progress}%"
}

# 获取阶段描述
get_stage_description() {
    local stage="$1"
    
    case "$stage" in
        "not_started")
            echo "未开始"
            ;;
        "starting")
            echo "正在启动"
            ;;
        "mounted")
            echo "文件系统已挂载"
            ;;
        "pre_check_completed")
            echo "预检查已完成"
            ;;
        "syncing")
            echo "正在同步数据"
            ;;
        "sync_completed")
            echo "数据同步完成"
            ;;
        "verifying")
            echo "正在验证数据"
            ;;
        "verification_completed")
            echo "数据验证完成"
            ;;
        "completed")
            echo "迁移完成"
            ;;
        "failed")
            echo "迁移失败"
            ;;
        "interrupted")
            echo "迁移中断"
            ;;
        "dry_run_completed")
            echo "预演完成"
            ;;
        *)
            echo "$stage"
            ;;
    esac
}

# 获取阶段状态图标
get_stage_icon() {
    local stage="$1"
    
    case "$stage" in
        "not_started")
            echo "⏸️"
            ;;
        "starting"|"syncing"|"verifying")
            echo "$GEAR"
            ;;
        "mounted"|"pre_check_completed"|"sync_completed"|"verification_completed"|"completed"|"dry_run_completed")
            echo "$CHECK_MARK"
            ;;
        "failed"|"interrupted")
            echo "$CROSS_MARK"
            ;;
        *)
            echo "❓"
            ;;
    esac
}

# 仪表板显示
show_dashboard() {
    local progress_data="$1"
    local process_status="$2"
    
    # 解析数据
    local stage
    stage=$(echo "$progress_data" | jq -r '.stage // "unknown"')
    local progress_percent
    progress_percent=$(echo "$progress_data" | jq -r '.progress_percent // 0')
    local details
    details=$(echo "$progress_data" | jq -r '.details // ""')
    local timestamp
    timestamp=$(echo "$progress_data" | jq -r '.timestamp // ""')
    
    # 计算进度条宽度（根据终端宽度调整）
    local bar_width=$((TERM_WIDTH - 20))
    if [[ $bar_width -lt 30 ]]; then
        bar_width=30
    elif [[ $bar_width -gt 60 ]]; then
        bar_width=60
    fi
    
    # 输出仪表板
    echo -e "${BOLD}EFS迁移进度监控${NC}"
    echo "=============================================="
    echo ""
    
    # 进程状态
    echo -e "${BOLD}进程状态:${NC}"
    case "$process_status" in
        "running")
            echo -e "  ${GREEN}● 运行中${NC}"
            ;;
        "stopped")
            echo -e "  ${YELLOW}● 已停止${NC}"
            ;;
        "not_running")
            echo -e "  ${RED}● 未运行${NC}"
            ;;
    esac
    echo ""
    
    # 迁移阶段
    echo -e "${BOLD}当前阶段:${NC}"
    local stage_icon
    stage_icon=$(get_stage_icon "$stage")
    local stage_desc
    stage_desc=$(get_stage_description "$stage")
    echo -e "  $stage_icon $stage_desc"
    echo ""
    
    # 进度条
    echo -e "${BOLD}总体进度:${NC}"
    local progress_bar
    progress_bar=$(draw_progress_bar "$progress_percent" "$bar_width")
    
    if [[ "$progress_percent" -lt 30 ]]; then
        echo -e "  ${RED}$progress_bar${NC}"
    elif [[ "$progress_percent" -lt 70 ]]; then
        echo -e "  ${YELLOW}$progress_bar${NC}"
    else
        echo -e "  ${GREEN}$progress_bar${NC}"
    fi
    echo ""
    
    # 详细信息
    if [[ -n "$details" && "$details" != "null" ]]; then
        echo -e "${BOLD}详细信息:${NC}"
        echo "  $details"
        echo ""
    fi
    
    # 时间信息
    echo -e "${BOLD}时间信息:${NC}"
    local formatted_time
    formatted_time=$(format_timestamp "$timestamp")
    echo "  最后更新: $formatted_time"
    
    # ETA计算（仅在同步阶段显示）
    if [[ "$stage" == "syncing" && "$progress_percent" -gt 0 ]]; then
        local eta
        eta=$(calculate_eta "$progress_percent" "$timestamp")
        echo "  预计完成: $eta"
    fi
    echo ""
    
    # 控制提示
    echo -e "${CYAN}按 Ctrl+C 退出监控${NC}"
    
    # 添加一些装饰性空行
    echo ""
}

# JSON格式输出
show_json() {
    local progress_data="$1"
    local process_status="$2"
    
    echo "$progress_data" | jq --arg process_status "$process_status" '. + {process_status: $process_status}'
}

# 简单格式输出
show_simple() {
    local progress_data="$1"
    local process_status="$2"
    
    local stage
    stage=$(echo "$progress_data" | jq -r '.stage // "unknown"')
    local progress_percent
    progress_percent=$(echo "$progress_data" | jq -r '.progress_percent // 0')
    local details
    details=$(echo "$progress_data" | jq -r '.details // ""')
    
    echo "进程状态: $process_status"
    echo "迁移阶段: $(get_stage_description "$stage")"
    echo "进度: ${progress_percent}%"
    if [[ -n "$details" && "$details" != "null" ]]; then
        echo "详情: $details"
    fi
}

# 监控循环
monitor_loop() {
    # 检查jq是否可用
    if ! command -v jq >/dev/null 2>&1; then
        log_error "需要安装jq来解析JSON数据"
        exit 1
    fi
    
    # 检测终端特性
    detect_terminal_capabilities
    
    if [[ "$OUTPUT_FORMAT" == "dashboard" && "$FOLLOW_MODE" == "true" ]]; then
        hide_cursor
    fi
    
    local iteration=0
    
    while true; do
        local progress_data
        progress_data=$(read_progress_data)
        
        local process_status
        process_status=$(check_migration_process)
        
        # 根据输出格式显示信息
        case "$OUTPUT_FORMAT" in
            "dashboard")
                if [[ "$FOLLOW_MODE" == "true" && $iteration -gt 0 ]]; then
                    clear_screen
                fi
                show_dashboard "$progress_data" "$process_status"
                ;;
            "json")
                show_json "$progress_data" "$process_status"
                ;;
            "simple")
                show_simple "$progress_data" "$process_status"
                ;;
        esac
        
        # 检查是否只运行一次
        if [[ "$FOLLOW_MODE" == "false" ]]; then
            break
        fi
        
        # 检查是否已完成（只有真正完成的状态才退出）
        local stage
        stage=$(echo "$progress_data" | jq -r '.stage // "unknown"')
        
        # 检查进程是否还在运行
        local process_status
        process_status=$(check_migration_process)
        
        # 自动退出逻辑（可通过--no-auto-exit禁用）
        if [[ "$NO_AUTO_EXIT" == "false" && "$process_status" == "not_running" && ("$stage" == "completed" || "$stage" == "failed" || "$stage" == "dry_run_completed" || "$stage" == "interrupted") ]]; then
            if [[ "$OUTPUT_FORMAT" == "dashboard" ]]; then
                echo ""
                if [[ "$stage" == "completed" || "$stage" == "dry_run_completed" ]]; then
                    log_success "迁移已完成！"
                elif [[ "$stage" == "failed" ]]; then
                    log_error "迁移失败！"
                else
                    log_warning "迁移被中断！"
                fi
                echo "监控将在10秒后自动退出... (使用 --no-auto-exit 禁用自动退出)"
                sleep 10
            fi
            break
        elif [[ "$NO_AUTO_EXIT" == "true" && "$process_status" == "not_running" && ("$stage" == "completed" || "$stage" == "failed" || "$stage" == "dry_run_completed" || "$stage" == "interrupted") ]]; then
            # 禁用自动退出时，只显示状态不退出
            if [[ "$OUTPUT_FORMAT" == "dashboard" ]]; then
                if [[ "$stage" == "completed" || "$stage" == "dry_run_completed" ]]; then
                    log_success "迁移已完成！(监控继续运行，按 Ctrl+C 退出)"
                elif [[ "$stage" == "failed" ]]; then
                    log_error "迁移失败！(监控继续运行，按 Ctrl+C 退出)"
                else
                    log_warning "迁移被中断！(监控继续运行，按 Ctrl+C 退出)"
                fi
            fi
        fi
        
        # 如果进程仍在运行，继续监控（即使状态显示某种"完成"）
        if [[ "$process_status" == "running" && ("$stage" == "sync_completed" || "$stage" == "verification_completed") ]]; then
            # 这些是中间完成状态，不应该退出监控
            if [[ "$OUTPUT_FORMAT" == "dashboard" && "$VERBOSE" == "true" ]]; then
                log_info "检测到中间完成状态: $stage，继续监控..."
            fi
        fi
        
        sleep "$REFRESH_INTERVAL"
        ((iteration++))
    done
}

# 主函数
main() {
    log_info "启动EFS迁移进度监控..."
    
    # 检查进度文件是否存在
    if [[ ! -f "$PROGRESS_FILE" && "$FOLLOW_MODE" == "false" ]]; then
        log_warning "进度文件不存在: $PROGRESS_FILE"
        log_info "迁移可能尚未开始"
    fi
    
    # 启动监控循环
    monitor_loop
    
    log_info "进度监控结束"
}

# 执行主函数
main "$@"