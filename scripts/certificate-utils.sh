#!/bin/bash

# =============================================================================
# 证书工具辅助脚本
# Certificate Utilities for E-Invoice Turnkey System
# =============================================================================
# 
# 用途：提供证书处理的辅助功能
# 功能：证书转换、格式验证、信息提取等
# 
# 使用方法：
#   ./certificate-utils.sh <command> [options]
#
# 作者：Yuan Hui Infrastructure Team
# 版本：1.0.0
# 日期：2024-08-07
# =============================================================================

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# 工具函数
# =============================================================================

log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" >&2
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message" >&2
            ;;
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message"
            ;;
    esac
}

error_exit() {
    log "ERROR" "$1"
    exit 1
}

check_dependencies() {
    local deps=("openssl" "base64" "jq")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" >/dev/null 2>&1; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        error_exit "缺少必要依赖：${missing_deps[*]}"
    fi
}

# =============================================================================
# 证书转换功能
# =============================================================================

pfx_to_base64() {
    local pfx_file=$1
    local output_file=${2:-}
    
    if [[ ! -f "$pfx_file" ]]; then
        error_exit "PFX文件不存在：$pfx_file"
    fi
    
    log "INFO" "转换PFX文件到Base64格式"
    log "INFO" "输入文件：$pfx_file"
    
    local base64_content
    base64_content=$(base64 -i "$pfx_file")
    
    if [[ -n "$output_file" ]]; then
        echo "$base64_content" > "$output_file"
        log "INFO" "Base64内容已保存到：$output_file"
    else
        echo "Base64编码内容："
        echo "$base64_content"
    fi
}

base64_to_pfx() {
    local base64_input=$1
    local output_file=$2
    
    if [[ ! -f "$base64_input" ]]; then
        error_exit "Base64文件不存在：$base64_input"
    fi
    
    log "INFO" "转换Base64到PFX文件"
    log "INFO" "输入文件：$base64_input"
    log "INFO" "输出文件：$output_file"
    
    if base64 -d "$base64_input" > "$output_file"; then
        log "INFO" "转换成功"
    else
        error_exit "Base64解码失败"
    fi
}

# =============================================================================
# 证书信息提取
# =============================================================================

extract_pfx_info() {
    local pfx_file=$1
    local password=$2
    
    if [[ ! -f "$pfx_file" ]]; then
        error_exit "PFX文件不存在：$pfx_file"
    fi
    
    log "INFO" "提取PFX证书信息"
    log "INFO" "证书文件：$pfx_file"
    
    echo
    echo -e "${BLUE}=== 证书信息 ===${NC}"
    
    # 提取证书信息
    if openssl pkcs12 -in "$pfx_file" -noout -info -passin "pass:$password" 2>/dev/null; then
        echo "证书格式：PKCS#12 (PFX)"
        
        # 提取证书详情
        echo
        echo -e "${BLUE}=== 证书详情 ===${NC}"
        openssl pkcs12 -in "$pfx_file" -clcerts -nokeys -passin "pass:$password" 2>/dev/null | \
        openssl x509 -noout -subject -issuer -dates -fingerprint 2>/dev/null || \
        log "WARN" "无法提取详细信息，可能是密码错误或证书格式问题"
    else
        log "ERROR" "无法读取PFX文件，请检查密码是否正确"
        return 1
    fi
}

validate_certificate_password() {
    local pfx_file=$1
    local password=$2
    
    if [[ ! -f "$pfx_file" ]]; then
        error_exit "PFX文件不存在：$pfx_file"
    fi
    
    log "INFO" "验证证书密码"
    
    if openssl pkcs12 -in "$pfx_file" -noout -passin "pass:$password" 2>/dev/null; then
        log "INFO" "密码正确"
        return 0
    else
        log "ERROR" "密码错误或文件损坏"
        return 1
    fi
}

# =============================================================================
# JSON模板生成
# =============================================================================

generate_certificate_template() {
    local pfx_file=${1:-}
    local password=${2:-}
    local sign_id=${3:-}
    local output_file=${4:-"certificate-template.json"}
    
    log "INFO" "生成证书JSON模板"
    
    local pfx_base64=""
    local cert_info=""
    
    if [[ -n "$pfx_file" && -f "$pfx_file" ]]; then
        if [[ -n "$password" ]]; then
            # 验证密码
            if validate_certificate_password "$pfx_file" "$password"; then
                pfx_base64=$(base64 -i "$pfx_file")
                
                # 尝试提取证书信息
                cert_info=$(openssl pkcs12 -in "$pfx_file" -clcerts -nokeys -passin "pass:$password" 2>/dev/null | \
                           openssl x509 -noout -dates 2>/dev/null || echo "")
            else
                log "WARN" "密码验证失败，将生成空模板"
            fi
        else
            log "WARN" "未提供密码，将生成空模板"
        fi
    fi
    
    # 提取日期信息
    local issued_date=""
    local expiry_date=""
    
    if [[ -n "$cert_info" ]]; then
        issued_date=$(echo "$cert_info" | grep "notBefore" | sed 's/notBefore=//' | xargs date -f - +%Y-%m-%d 2>/dev/null || echo "")
        expiry_date=$(echo "$cert_info" | grep "notAfter" | sed 's/notAfter=//' | xargs date -f - +%Y-%m-%d 2>/dev/null || echo "")
    fi
    
    # 生成JSON模板
    local template_json
    template_json=$(cat << EOF
{
  "certificates": [
    {
      "pfx_base64": "${pfx_base64:-BASE64_ENCODED_PFX_HERE}",
      "pfx_password": "${password:-YOUR_CERTIFICATE_PASSWORD}",
      "sign_id": "${sign_id:-UNIQUE_SIGN_ID}",
      "sign_type": "corporate",
      "description": "电子发票证书",
      "issued_date": "${issued_date:-2024-01-01}",
      "expiry_date": "${expiry_date:-2026-01-01}",
      "issuer": "中华民国财政部电子发票整合服务平台"
    }
  ],
  "metadata": {
    "version": "1.0",
    "last_updated": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
    "total_certificates": 1,
    "environment": "dev"
  }
}
EOF
)
    
    echo "$template_json" | jq . > "$output_file"
    log "INFO" "JSON模板已生成：$output_file"
}

# =============================================================================
# 批量处理功能
# =============================================================================

batch_convert_pfx() {
    local pfx_dir=$1
    local output_dir=${2:-"./converted"}
    
    if [[ ! -d "$pfx_dir" ]]; then
        error_exit "PFX目录不存在：$pfx_dir"
    fi
    
    mkdir -p "$output_dir"
    
    log "INFO" "批量转换PFX文件"
    log "INFO" "输入目录：$pfx_dir"
    log "INFO" "输出目录：$output_dir"
    
    local count=0
    for pfx_file in "$pfx_dir"/*.pfx "$pfx_dir"/*.p12; do
        if [[ -f "$pfx_file" ]]; then
            local filename=$(basename "$pfx_file")
            local base_name="${filename%.*}"
            local output_file="$output_dir/${base_name}.base64"
            
            log "INFO" "转换：$filename"
            if pfx_to_base64 "$pfx_file" "$output_file"; then
                count=$((count + 1))
            fi
        fi
    done
    
    log "INFO" "批量转换完成，处理了 $count 个文件"
}

# =============================================================================
# 帮助信息
# =============================================================================

show_help() {
    cat << EOF
证书工具辅助脚本 v1.0.0

用途：提供证书处理的辅助功能

使用方法：
    $0 <command> [options] [arguments]

命令：
    pfx-to-base64 <pfx_file> [output_file]
        将PFX证书文件转换为Base64格式
        
    base64-to-pfx <base64_file> <output_pfx>
        将Base64内容转换为PFX文件
        
    extract-info <pfx_file> <password>
        提取PFX证书的详细信息
        
    validate-password <pfx_file> <password>
        验证PFX证书密码是否正确
        
    generate-template [pfx_file] [password] [sign_id] [output_file]
        生成证书JSON模板文件
        
    batch-convert <pfx_dir> [output_dir]
        批量转换目录中的所有PFX文件
        
    help
        显示此帮助信息

示例：
    # 转换PFX到Base64
    $0 pfx-to-base64 certificate.pfx cert.base64
    
    # 提取证书信息
    $0 extract-info certificate.pfx "password123"
    
    # 验证密码
    $0 validate-password certificate.pfx "password123"
    
    # 生成完整的JSON模板
    $0 generate-template certificate.pfx "password123" "company_cert_001" template.json
    
    # 批量转换
    $0 batch-convert ./certificates ./base64_output

依赖项：
    - openssl (SSL/TLS工具)
    - base64 (Base64编码工具)
    - jq (JSON处理工具)

EOF
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi
    
    check_dependencies
    
    local command=$1
    shift
    
    case $command in
        pfx-to-base64)
            if [[ $# -lt 1 ]]; then
                error_exit "pfx-to-base64命令需要指定PFX文件路径"
            fi
            pfx_to_base64 "$@"
            ;;
        base64-to-pfx)
            if [[ $# -lt 2 ]]; then
                error_exit "base64-to-pfx命令需要指定输入和输出文件路径"
            fi
            base64_to_pfx "$1" "$2"
            ;;
        extract-info)
            if [[ $# -lt 2 ]]; then
                error_exit "extract-info命令需要指定PFX文件和密码"
            fi
            extract_pfx_info "$1" "$2"
            ;;
        validate-password)
            if [[ $# -lt 2 ]]; then
                error_exit "validate-password命令需要指定PFX文件和密码"
            fi
            validate_certificate_password "$1" "$2"
            ;;
        generate-template)
            generate_certificate_template "$@"
            ;;
        batch-convert)
            if [[ $# -lt 1 ]]; then
                error_exit "batch-convert命令需要指定PFX目录"
            fi
            batch_convert_pfx "$@"
            ;;
        help)
            show_help
            ;;
        *)
            error_exit "未知命令：$command。使用 '$0 help' 查看帮助信息。"
            ;;
    esac
}

main "$@"