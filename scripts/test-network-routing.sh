#!/bin/bash

# 网络路由测试脚本
# 用于验证域名解析、SSL证书、负载均衡器路由等功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试DNS解析
test_dns_resolution() {
    local domain=$1
    local expected_type=${2:-A}
    
    log_info "测试DNS解析: $domain ($expected_type记录)"
    
    if nslookup $domain > /dev/null 2>&1; then
        local ip=$(nslookup $domain | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "未找到")
        log_success "DNS解析成功: $domain -> $ip"
        return 0
    else
        log_error "DNS解析失败: $domain"
        return 1
    fi
}

# 测试HTTP连接
test_http_connection() {
    local url=$1
    local expected_status=${2:-200}
    local timeout=${3:-10}
    
    log_info "测试HTTP连接: $url"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout $timeout "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        log_success "HTTP连接成功: $url (状态码: $response)"
        return 0
    elif [ "$response" = "000" ]; then
        log_error "HTTP连接失败: $url (连接超时或无法连接)"
        return 1
    else
        log_warning "HTTP连接异常: $url (状态码: $response, 期望: $expected_status)"
        return 1
    fi
}

# 测试HTTPS连接和SSL证书
test_https_connection() {
    local url=$1
    local timeout=${2:-10}
    
    log_info "测试HTTPS连接和SSL证书: $url"
    
    # 测试SSL证书
    local ssl_info=$(echo | openssl s_client -servername $(echo $url | sed 's|https://||' | sed 's|/.*||') -connect $(echo $url | sed 's|https://||' | sed 's|/.*||'):443 2>/dev/null | openssl x509 -noout -subject -dates 2>/dev/null || echo "SSL证书获取失败")
    
    if [[ "$ssl_info" != *"SSL证书获取失败"* ]]; then
        log_success "SSL证书验证成功"
        echo "  证书信息: $ssl_info"
    else
        log_error "SSL证书验证失败: $url"
        return 1
    fi
    
    # 测试HTTPS连接
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout $timeout "$url" 2>/dev/null || echo "000")
    
    if [ "$response" = "200" ] || [ "$response" = "302" ] || [ "$response" = "301" ]; then
        log_success "HTTPS连接成功: $url (状态码: $response)"
        return 0
    else
        log_error "HTTPS连接失败: $url (状态码: $response)"
        return 1
    fi
}

# 测试负载均衡器健康检查
test_load_balancer_health() {
    local alb_dns=$1
    local health_path=${2:-/web/health}

    log_info "测试负载均衡器健康检查: $alb_dns$health_path"

    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "http://$alb_dns$health_path" 2>/dev/null || echo "000")

    if [ "$response" = "200" ]; then
        log_success "负载均衡器健康检查通过: $alb_dns"
        return 0
    else
        log_error "负载均衡器健康检查失败: $alb_dns (状态码: $response)"
        return 1
    fi
}

# 测试longpolling端点
test_longpolling_endpoint() {
    local domain=$1
    local protocol=${2:-http}

    log_info "测试Longpolling端点: $protocol://$domain/longpolling/poll"

    # 测试longpolling端点是否可访问
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$protocol://$domain/longpolling/poll" 2>/dev/null || echo "000")

    # longpolling端点可能返回400（需要特定参数）或404，这都是正常的
    if [ "$response" = "400" ] || [ "$response" = "404" ] || [ "$response" = "200" ]; then
        log_success "Longpolling端点可访问: $domain (状态码: $response)"
        return 0
    else
        log_error "Longpolling端点不可访问: $domain (状态码: $response)"
        return 1
    fi
}

# 测试Host header路由
test_host_header_routing() {
    local alb_dns=$1
    local host_header=$2
    local expected_response=${3:-200}
    
    log_info "测试Host header路由: $host_header -> $alb_dns"
    
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 -H "Host: $host_header" "http://$alb_dns/" 2>/dev/null || echo "000")
    
    if [ "$response" = "$expected_response" ]; then
        log_success "Host header路由成功: $host_header (状态码: $response)"
        return 0
    else
        log_error "Host header路由失败: $host_header (状态码: $response, 期望: $expected_response)"
        return 1
    fi
}

# 测试WAF规则
test_waf_protection() {
    local url=$1
    
    log_info "测试WAF保护规则: $url"
    
    # 测试SQL注入攻击
    local malicious_url="${url}?id=1' OR '1'='1"
    local response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$malicious_url" 2>/dev/null || echo "000")
    
    if [ "$response" = "403" ] || [ "$response" = "406" ]; then
        log_success "WAF SQL注入保护正常 (状态码: $response)"
    else
        log_warning "WAF SQL注入保护可能未生效 (状态码: $response)"
    fi
    
    # 测试XSS攻击
    local xss_url="${url}?search=<script>alert('xss')</script>"
    response=$(curl -s -o /dev/null -w "%{http_code}" --connect-timeout 10 "$xss_url" 2>/dev/null || echo "000")
    
    if [ "$response" = "403" ] || [ "$response" = "406" ]; then
        log_success "WAF XSS保护正常 (状态码: $response)"
    else
        log_warning "WAF XSS保护可能未生效 (状态码: $response)"
    fi
}

# 测试CloudFront分发
test_cloudfront_distribution() {
    local domain=$1
    
    log_info "测试CloudFront分发: $domain"
    
    # 检查CloudFront头部
    local cf_headers=$(curl -s -I "https://$domain" | grep -i "cloudfront" || echo "未找到CloudFront头部")
    
    if [[ "$cf_headers" != *"未找到CloudFront头部"* ]]; then
        log_success "CloudFront分发正常"
        echo "  CloudFront头部: $cf_headers"
    else
        log_warning "未检测到CloudFront分发"
    fi
    
    # 测试静态资源缓存
    local static_url="https://$domain/web/static/src/css/bootstrap.css"
    local cache_response=$(curl -s -I "$static_url" | grep -i "cache-control" || echo "未找到缓存头部")
    
    if [[ "$cache_response" != *"未找到缓存头部"* ]]; then
        log_success "静态资源缓存配置正常"
        echo "  缓存头部: $cache_response"
    else
        log_warning "静态资源缓存配置可能有问题"
    fi
}

# 获取栈输出信息
get_stack_outputs() {
    local env=$1
    
    log_info "获取栈输出信息..."
    
    # 获取负载均衡器DNS
    PUBLIC_ALB_DNS=$(aws cloudformation describe-stacks --stack-name YuanhuiApplication-$env --query 'Stacks[0].Outputs[?OutputKey==`PublicLoadBalancerDnsName`].OutputValue' --output text 2>/dev/null || echo "")
    INTERNAL_ALB_DNS=$(aws cloudformation describe-stacks --stack-name YuanhuiApplication-$env --query 'Stacks[0].Outputs[?OutputKey==`InternalLoadBalancerDnsName`].OutputValue' --output text 2>/dev/null || echo "")
    
    # 获取CloudFront分发域名
    CLOUDFRONT_DOMAIN=$(aws cloudformation describe-stacks --stack-name YuanhuiCloudFront-$env --query 'Stacks[0].Outputs[?OutputKey==`DistributionDomainName`].OutputValue' --output text 2>/dev/null || echo "")
    
    log_info "公网ALB DNS: $PUBLIC_ALB_DNS"
    log_info "内部ALB DNS: $INTERNAL_ALB_DNS"
    log_info "CloudFront域名: $CLOUDFRONT_DOMAIN"
}

# 运行完整测试套件
run_full_test_suite() {
    local env=${1:-dev}
    
    log_info "开始运行完整的网络路由测试套件..."
    echo ""
    
    # 获取栈输出信息
    get_stack_outputs $env
    
    local test_results=()
    local total_tests=0
    local passed_tests=0
    
    # 定义测试域名
    if [ "$env" = "prod" ]; then
        YHERP_INTERNAL_DOMAIN="yh.kh2u.com"
        YHERP_PUBLIC_DOMAIN="dp.kh2u.com"
        KHMALL_DOMAIN="jmall.tw"
    else
        YHERP_INTERNAL_DOMAIN="yh-dev.kh2u.com"
        YHERP_PUBLIC_DOMAIN="dp-dev.kh2u.com"
        KHMALL_DOMAIN="jmall-dev.tw"
    fi
    
    echo "========================================"
    echo "DNS解析测试"
    echo "========================================"
    
    # DNS解析测试
    ((total_tests++))
    if test_dns_resolution $YHERP_PUBLIC_DOMAIN; then
        ((passed_tests++))
        test_results+=("✓ DNS解析 - $YHERP_PUBLIC_DOMAIN")
    else
        test_results+=("✗ DNS解析 - $YHERP_PUBLIC_DOMAIN")
    fi
    
    ((total_tests++))
    if test_dns_resolution $KHMALL_DOMAIN; then
        ((passed_tests++))
        test_results+=("✓ DNS解析 - $KHMALL_DOMAIN")
    else
        test_results+=("✗ DNS解析 - $KHMALL_DOMAIN")
    fi
    
    echo ""
    echo "========================================"
    echo "负载均衡器测试"
    echo "========================================"
    
    # 负载均衡器测试
    if [ -n "$PUBLIC_ALB_DNS" ]; then
        ((total_tests++))
        if test_load_balancer_health $PUBLIC_ALB_DNS; then
            ((passed_tests++))
            test_results+=("✓ 公网ALB健康检查")
        else
            test_results+=("✗ 公网ALB健康检查")
        fi
        
        # Host header路由测试
        ((total_tests++))
        if test_host_header_routing $PUBLIC_ALB_DNS $YHERP_PUBLIC_DOMAIN; then
            ((passed_tests++))
            test_results+=("✓ Host路由 - $YHERP_PUBLIC_DOMAIN")
        else
            test_results+=("✗ Host路由 - $YHERP_PUBLIC_DOMAIN")
        fi
        
        ((total_tests++))
        if test_host_header_routing $PUBLIC_ALB_DNS $KHMALL_DOMAIN; then
            ((passed_tests++))
            test_results+=("✓ Host路由 - $KHMALL_DOMAIN")
        else
            test_results+=("✗ Host路由 - $KHMALL_DOMAIN")
        fi
    fi
    
    echo ""
    echo "========================================"
    echo "Longpolling端点测试"
    echo "========================================"

    # Longpolling测试
    ((total_tests++))
    if test_longpolling_endpoint "$YHERP_PUBLIC_DOMAIN" "https"; then
        ((passed_tests++))
        test_results+=("✓ Longpolling端点 - $YHERP_PUBLIC_DOMAIN")
    else
        test_results+=("✗ Longpolling端点 - $YHERP_PUBLIC_DOMAIN")
    fi

    ((total_tests++))
    if test_longpolling_endpoint "$KHMALL_DOMAIN" "https"; then
        ((passed_tests++))
        test_results+=("✓ Longpolling端点 - $KHMALL_DOMAIN")
    else
        test_results+=("✗ Longpolling端点 - $KHMALL_DOMAIN")
    fi

    echo ""
    echo "========================================"
    echo "HTTPS和SSL证书测试"
    echo "========================================"

    # HTTPS测试
    ((total_tests++))
    if test_https_connection "https://$YHERP_PUBLIC_DOMAIN"; then
        ((passed_tests++))
        test_results+=("✓ HTTPS连接 - $YHERP_PUBLIC_DOMAIN")
    else
        test_results+=("✗ HTTPS连接 - $YHERP_PUBLIC_DOMAIN")
    fi

    ((total_tests++))
    if test_https_connection "https://$KHMALL_DOMAIN"; then
        ((passed_tests++))
        test_results+=("✓ HTTPS连接 - $KHMALL_DOMAIN")
    else
        test_results+=("✗ HTTPS连接 - $KHMALL_DOMAIN")
    fi
    
    echo ""
    echo "========================================"
    echo "WAF保护测试"
    echo "========================================"
    
    # WAF测试
    ((total_tests++))
    test_waf_protection "https://$YHERP_PUBLIC_DOMAIN"
    ((passed_tests++))  # WAF测试总是通过，因为它只是警告
    test_results+=("✓ WAF保护测试 - $YHERP_PUBLIC_DOMAIN")
    
    echo ""
    echo "========================================"
    echo "CloudFront CDN测试"
    echo "========================================"
    
    # CloudFront测试
    if [ "$env" = "prod" ] || [ -n "$CLOUDFRONT_DOMAIN" ]; then
        ((total_tests++))
        test_cloudfront_distribution $KHMALL_DOMAIN
        ((passed_tests++))  # CloudFront测试总是通过，因为它只是警告
        test_results+=("✓ CloudFront分发测试 - $KHMALL_DOMAIN")
    else
        log_info "CloudFront在开发环境中未启用，跳过测试"
    fi
    
    echo ""
    echo "========================================"
    echo "测试结果汇总"
    echo "========================================"
    
    for result in "${test_results[@]}"; do
        echo "$result"
    done
    
    echo ""
    log_info "测试完成: $passed_tests/$total_tests 通过"
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "所有测试通过！网络路由配置正常"
        return 0
    else
        log_warning "部分测试失败，请检查配置"
        return 1
    fi
}

# 主函数
main() {
    local env=${1:-dev}
    
    echo "========================================"
    echo "网络路由测试脚本"
    echo "========================================"
    echo ""
    
    # 检查必要工具
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装，请先安装 curl"
        exit 1
    fi
    
    if ! command -v nslookup &> /dev/null; then
        log_error "nslookup 未安装，请先安装 bind-utils 或 dnsutils"
        exit 1
    fi
    
    if ! command -v openssl &> /dev/null; then
        log_error "openssl 未安装，请先安装 openssl"
        exit 1
    fi
    
    run_full_test_suite $env
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
