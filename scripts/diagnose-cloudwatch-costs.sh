#!/bin/bash

# CloudWatch费用诊断脚本 - 专注于关键问题
# 快速识别高费用来源

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

REGION=${AWS_DEFAULT_REGION:-ap-east-2}

echo -e "${BLUE}CloudWatch费用诊断${NC}"
echo "=================================="
echo "区域: $REGION"
echo "时间: $(date)"
echo ""

# 函数：安全执行AWS命令
safe_aws_cmd() {
    local cmd="$1"
    local timeout_sec="${2:-10}"
    local result
    
    result=$(timeout $timeout_sec $cmd 2>/dev/null || echo "ERROR")
    echo "$result"
}

# 1. 检查ECS集群Container Insights状态
echo -e "${BLUE}1. ECS Container Insights状态${NC}"
echo "----------------------------------"

clusters=$(safe_aws_cmd "aws ecs list-clusters --region $REGION --query 'clusterArns[]' --output text" 5)

if [ "$clusters" = "ERROR" ] || [ -z "$clusters" ]; then
    echo -e "${GREEN}✅ 无ECS集群或无权限访问${NC}"
else
    echo "发现ECS集群:"
    for cluster_arn in $clusters; do
        if [ -n "$cluster_arn" ] && [ "$cluster_arn" != "None" ]; then
            cluster_name=$(basename "$cluster_arn")
            echo ""
            echo "集群: $cluster_name"
            
            # 检查Container Insights设置
            settings=$(safe_aws_cmd "aws ecs describe-clusters --region $REGION --clusters $cluster_arn --query 'clusters[0].settings' --output json" 5)
            
            if [ "$settings" = "ERROR" ]; then
                echo -e "  ${YELLOW}无法检查Container Insights状态${NC}"
            elif echo "$settings" | grep -q "containerInsights.*enabled"; then
                echo -e "  ${RED}❌ Container Insights: 启用 (产生费用!)${NC}"
            else
                echo -e "  ${GREEN}✅ Container Insights: 禁用${NC}"
            fi
        fi
    done
fi

echo ""

# 2. 检查高费用指标命名空间
echo -e "${BLUE}2. 高费用指标检查${NC}"
echo "----------------------------------"

# 检查Container Insights指标
echo "检查 ECS/ContainerInsights 指标..."
ci_count=$(safe_aws_cmd "aws cloudwatch list-metrics --region $REGION --namespace 'ECS/ContainerInsights' --query 'length(Metrics)' --output text" 8)

if [ "$ci_count" = "ERROR" ]; then
    echo -e "${YELLOW}⚠️  无法检查Container Insights指标${NC}"
elif [ "$ci_count" -gt 0 ] 2>/dev/null; then
    echo -e "${RED}❌ Container Insights指标: $ci_count 个 (每个约\$0.30/月)${NC}"
    estimated_cost=$((ci_count * 30 / 100))
    echo -e "${RED}   预估费用: ~\$${estimated_cost}/月${NC}"
else
    echo -e "${GREEN}✅ Container Insights指标: 0 个${NC}"
fi

# 检查自定义指标命名空间
echo ""
echo "检查自定义指标命名空间..."
custom_namespaces=$(safe_aws_cmd "aws cloudwatch list-metrics --region $REGION --query 'Metrics[?!starts_with(Namespace, \`AWS/\`)].Namespace' --output text" 10)

if [ "$custom_namespaces" = "ERROR" ]; then
    echo -e "${YELLOW}⚠️  无法检查自定义指标${NC}"
elif [ -n "$custom_namespaces" ] && [ "$custom_namespaces" != "None" ]; then
    echo -e "${YELLOW}发现自定义指标命名空间:${NC}"
    for ns in $(echo "$custom_namespaces" | tr '\t' '\n' | sort -u | head -10); do
        if [ -n "$ns" ]; then
            count=$(safe_aws_cmd "aws cloudwatch list-metrics --region $REGION --namespace '$ns' --query 'length(Metrics)' --output text" 5)
            if [ "$count" != "ERROR" ] && [ "$count" -gt 0 ] 2>/dev/null; then
                echo -e "  ${RED}$ns: $count 个指标${NC}"
            fi
        fi
    done
else
    echo -e "${GREEN}✅ 无自定义指标命名空间${NC}"
fi

echo ""

# 3. 检查日志组
echo -e "${BLUE}3. 日志组检查${NC}"
echo "----------------------------------"

log_groups=$(safe_aws_cmd "aws logs describe-log-groups --region $REGION --query 'logGroups[?contains(logGroupName, \`ecs\`) || contains(logGroupName, \`yuanhui\`)].{Name:logGroupName,Retention:retentionInDays}' --output table" 8)

if [ "$log_groups" = "ERROR" ]; then
    echo -e "${YELLOW}⚠️  无法检查日志组${NC}"
elif [ -n "$log_groups" ] && [ "$log_groups" != "None" ]; then
    echo "相关日志组:"
    echo "$log_groups"
else
    echo -e "${GREEN}✅ 无相关日志组${NC}"
fi

echo ""

# 4. 总结和建议
echo -e "${BLUE}4. 诊断总结${NC}"
echo "----------------------------------"

# 基于检查结果给出建议
if echo "$ci_count" | grep -q "^[0-9]\+$" && [ "$ci_count" -gt 0 ]; then
    echo -e "${RED}🚨 发现问题: Container Insights产生费用${NC}"
    echo ""
    echo "立即解决方案:"
    echo "1. 编辑配置文件，关闭开发环境详细监控"
    echo "2. 重新部署ECS栈"
    echo ""
    echo "执行命令:"
    echo "  cd /Users/<USER>/code/yuan/iac"
    echo "  # 确认配置已更新: enableDetailedMonitoring: false"
    echo "  npm run build"
    echo "  npx cdk deploy YuanhuiEcs-dev"
    echo ""
    echo -e "${YELLOW}预估节省: ~\$${estimated_cost}/月${NC}"
else
    echo -e "${GREEN}✅ 未发现明显的高费用来源${NC}"
    echo ""
    echo "可能的其他原因:"
    echo "1. 其他AWS服务的CloudWatch指标"
    echo "2. 大量日志存储"
    echo "3. 频繁的API调用"
    echo ""
    echo "建议检查AWS Cost Explorer获取详细费用分析"
fi

echo ""
echo -e "${BLUE}诊断完成${NC}"
echo ""
echo "如需详细分析，请运行:"
echo "  ./scripts/monitor-cloudwatch-costs.sh"
