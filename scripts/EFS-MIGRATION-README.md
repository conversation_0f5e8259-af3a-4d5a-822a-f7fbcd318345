# EFS跨账号迁移方案 - 使用说明

## 概述

本方案通过VPC对等连接实现256GB EFS数据从账号************的ap-northeast-1区域迁移到账号************的ap-east-2区域。

## 架构说明

```
源账号************(ap-northeast-1) ←→ 目标账号************(ap-east-2)
        ↓ VPC对等连接 ↓
源EFS(256GB) ←--内网传输--→ 目标EFS(Swarm Stack)
                    ↑
               执行EC2(目标区域)
```

## 脚本组件

### 1. 配置脚本 (`efs-cross-account-config.sh`)
**用途**: 环境配置和权限设置
- 验证AWS环境和权限
- 检查网络连通性
- 安装必需工具
- 生成配置文件

### 2. 主迁移脚本 (`efs-migration-main.sh`)
**用途**: 执行实际的数据迁移
- EFS文件系统挂载
- 数据同步（rsync）
- 进度监控
- 错误处理和恢复

### 3. 进度监控脚本 (`efs-migration-monitor.sh`)
**用途**: 实时监控迁移进度
- 交互式仪表板
- JSON/简单格式输出
- 进度条和ETA计算

### 4. 数据验证脚本 (`efs-migration-verify.sh`)
**用途**: 验证数据完整性
- 文件计数和大小验证
- 目录结构验证
- 校验和验证（可选）
- 采样验证（可选）

### 5. 清理脚本 (`efs-migration-cleanup.sh`)
**用途**: 清理迁移资源
- 卸载EFS文件系统
- 清理临时文件
- 清理日志（可选）

## 快速开始

### 前置条件

1. **网络配置**
   - VPC对等连接已建立并激活
   - 路由表配置正确
   - 安全组允许NFS流量（端口2049）

2. **权限配置**
   - 目标账号EC2具有EFS访问权限
   - 源账号EFS允许跨账号访问
   - AWS CLI已配置正确的凭证

3. **环境变量**
   ```bash
   export SOURCE_EFS_ID="fs-01234567890abcdef"      # 源EFS ID
   export TARGET_EFS_ID="fs-fedcba0987654321f"      # 目标EFS ID  
   export VPC_PEERING_CONNECTION_ID="pcx-1234567890abcdef0"  # VPC对等连接ID
   ```

### 执行步骤

#### 第一步: 环境配置
```bash
cd /path/to/scripts
./efs-cross-account-config.sh
```

**可选参数**:
- `--dry-run`: 预演模式
- `--verbose`: 详细输出

#### 第二步: 启动迁移
```bash
./efs-migration-main.sh
```

**可选参数**:
- `--dry-run`: 预演模式
- `--force`: 强制执行
- `--resume`: 断点续传
- `--skip-verification`: 跳过验证
- `--parallel-threads N`: 并行线程数

#### 第三步: 监控进度（另一个终端）
```bash
./efs-migration-monitor.sh
```

**监控选项**:
- `--output dashboard`: 交互式仪表板（默认）
- `--output json`: JSON格式输出
- `--output simple`: 简单文本格式
- `--once`: 仅显示一次状态

#### 第四步: 数据验证（可选）
```bash
./efs-migration-verify.sh --source /mnt/source-efs --target /mnt/target-efs
```

**验证选项**:
- `--quick`: 快速验证（仅计数和大小）
- `--enable-checksum`: 启用校验和验证
- `--enable-sampling`: 启用采样验证
- `--sample-size N`: 采样文件数量

#### 第五步: 资源清理
```bash
./efs-migration-cleanup.sh
```

**清理选项**:
- `--cleanup-old-logs`: 清理旧日志
- `--cleanup-config`: 清理配置文件
- `--force`: 强制清理

## 详细使用指南

### 配置阶段

#### 1. 环境变量设置
```bash
# 必需环境变量
export SOURCE_EFS_ID="fs-01234567890abcdef"
export TARGET_EFS_ID="fs-fedcba0987654321f"
export VPC_PEERING_CONNECTION_ID="pcx-1234567890abcdef0"

# 可选环境变量
export DRY_RUN=false
```

#### 2. 预演模式测试
```bash
# 预演配置过程
./efs-cross-account-config.sh --dry-run

# 预演迁移过程
./efs-migration-main.sh --dry-run
```

### 迁移执行

#### 1. 标准迁移
```bash
# 完整迁移流程
./efs-cross-account-config.sh
./efs-migration-main.sh

# 在另一个终端监控
./efs-migration-monitor.sh
```

#### 2. 断点续传
如果迁移中断，可以从断点继续：
```bash
./efs-migration-main.sh --resume
```

#### 3. 强制执行
跳过确认和重复检查：
```bash
./efs-migration-main.sh --force
```

### 进度监控

#### 1. 交互式仪表板
```bash
./efs-migration-monitor.sh
```
显示实时进度条、阶段信息和ETA。

#### 2. JSON输出（适合脚本集成）
```bash
./efs-migration-monitor.sh --output json --once
```

#### 3. 持续监控
```bash
./efs-migration-monitor.sh --interval 2 --follow
```

### 数据验证

#### 1. 快速验证
```bash
./efs-migration-verify.sh \
  --source /mnt/source-efs \
  --target /mnt/target-efs \
  --quick
```

#### 2. 完整验证
```bash
./efs-migration-verify.sh \
  --source /mnt/source-efs \
  --target /mnt/target-efs \
  --enable-checksum \
  --enable-sampling \
  --sample-size 500
```

#### 3. 校验和验证
```bash
./efs-migration-verify.sh \
  --source /mnt/source-efs \
  --target /mnt/target-efs \
  --full-checksum \
  --checksum-algorithm sha256
```

### 资源清理

#### 1. 标准清理
```bash
./efs-migration-cleanup.sh
```

#### 2. 完全清理
```bash
./efs-migration-cleanup.sh \
  --cleanup-old-logs \
  --cleanup-config \
  --force
```

#### 3. 预览清理内容
```bash
./efs-migration-cleanup.sh --dry-run
```

## 性能优化

### 1. 网络优化
- 确保VPC对等连接路由最优
- 使用Placement Groups（如果可能）
- 启用Enhanced Networking

### 2. 迁移优化
```bash
# 增加并行线程
./efs-migration-main.sh --parallel-threads 16

# 启用压缩（降低网络使用）
export RSYNC_COMPRESS=true
```

### 3. 监控优化
```bash
# 降低监控频率以减少开销
./efs-migration-monitor.sh --interval 10
```

## 故障排除

### 常见问题

#### 1. 网络连通性问题
```bash
# 测试EFS连通性
nc -z $SOURCE_EFS_ID.efs.ap-northeast-1.amazonaws.com 2049

# 检查路由表
aws ec2 describe-route-tables --filters "Name=vpc-peering-connection-id,Values=$VPC_PEERING_CONNECTION_ID"
```

#### 2. 权限问题
```bash
# 检查EFS挂载目标
aws efs describe-mount-targets --file-system-id $SOURCE_EFS_ID

# 验证IAM权限
aws sts get-caller-identity
aws efs describe-file-systems --file-system-id $TARGET_EFS_ID
```

#### 3. 挂载问题
```bash
# 手动测试挂载
sudo mount -t nfs4 \
  -o nfsvers=4.1,rsize=1048576,wsize=1048576,hard,intr \
  $SOURCE_EFS_ID.efs.ap-northeast-1.amazonaws.com:/ /mnt/test
```

### 日志分析

#### 1. 主日志文件
```bash
# 查看迁移日志
tail -f logs/migration-*.log

# 查看错误日志
tail -f logs/migration-errors.log
```

#### 2. rsync日志
```bash
# 详细的rsync操作日志
tail -f logs/rsync.log
```

#### 3. 验证报告
```bash
# 查看最新验证报告
ls -la logs/verification-report-*.txt
```

## 性能预期

### 传输速度
- **内网VPC对等连接**: 50-200 MB/s
- **预计时间**: 1-3小时（256GB）
- **成本**: 仅EC2运行成本，无数据传输费用

### 资源使用
- **CPU**: 中等使用率（rsync + 校验）
- **内存**: 512MB - 2GB
- **磁盘**: 最小临时空间需求
- **网络**: 持续但不会饱和带宽

## 安全考虑

### 1. 网络安全
- 数据通过VPC对等连接传输（内网）
- 支持EFS传输加密
- 安全组限制访问范围

### 2. 权限安全
- 最小权限原则
- 临时凭证（如果可能）
- 审计日志记录

### 3. 数据安全
- 完整性验证
- 错误检测和恢复
- 备份验证建议

## 最佳实践

### 1. 迁移前
- 在测试环境验证脚本
- 备份源EFS（如果可能）
- 文档化配置和依赖

### 2. 迁移中
- 监控进度和资源使用
- 保持网络连接稳定
- 准备回滚计划

### 3. 迁移后
- 完整数据验证
- 性能测试
- 清理临时资源
- 更新文档和配置

## 支持和维护

### 脚本更新
所有脚本支持参数化配置，可根据具体环境调整：
- 超时设置
- 重试逻辑
- 日志级别
- 性能参数

### 扩展功能
脚本架构支持以下扩展：
- 多EFS并行迁移
- 增量同步
- 自动化调度
- 监控集成

### 问题报告
遇到问题时，请提供：
- 完整的错误日志
- 环境配置信息
- 网络架构图
- 复现步骤

---

**注意**: 本方案适用于一次性迁移场景。对于生产环境，建议先在测试环境验证完整流程。