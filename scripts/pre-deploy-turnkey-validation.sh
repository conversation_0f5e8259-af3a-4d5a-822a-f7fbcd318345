#!/bin/bash

# ================================================================
# Turnkey系统部署前验证脚本
# 
# 目的：
# 1. 配置文件语法检查
# 2. SSM参数存在性验证
# 3. 权限配置检查
# 4. 证书数据完整性验证
# 5. 环境依赖检查
# 6. 安全配置验证
#
# 确保Turnkey系统部署前所有依赖和配置都已正确设置
# ================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# 验证统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 检查结果数组
declare -a CHECK_RESULTS=()
declare -a CRITICAL_ISSUES=()
declare -a WARNINGS=()
declare -a RECOMMENDATIONS=()

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo -e "${CYAN}[DEBUG]${NC} $1"
    fi
}

log_check() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

log_critical() {
    echo -e "${MAGENTA}[CRITICAL]${NC} $1"
}

# 检查记录函数
start_check() {
    local check_name="$1"
    echo
    echo "----------------------------------------"
    log_check "Validating: $check_name"
    echo "----------------------------------------"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
}

pass_check() {
    local check_name="$1"
    local message="${2:-Check passed}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
    log_info "✅ PASS: $check_name - $message"
    CHECK_RESULTS+=("PASS: $check_name - $message")
}

fail_check() {
    local check_name="$1"
    local message="${2:-Check failed}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
    log_error "❌ FAIL: $check_name - $message"
    CHECK_RESULTS+=("FAIL: $check_name - $message")
    CRITICAL_ISSUES+=("$check_name: $message")
}

warn_check() {
    local check_name="$1"
    local message="${2:-Check has warnings}"
    WARNING_CHECKS=$((WARNING_CHECKS + 1))
    log_warn "⚠️ WARN: $check_name - $message"
    CHECK_RESULTS+=("WARN: $check_name - $message")
    WARNINGS+=("$check_name: $message")
}

add_recommendation() {
    local recommendation="$1"
    RECOMMENDATIONS+=("$recommendation")
    log_info "💡 RECOMMENDATION: $recommendation"
}

# 获取项目根目录
get_project_root() {
    local current_dir=$(pwd)
    
    # 如果当前目录包含package.json和cdk.json，则认为是项目根目录
    if [[ -f "package.json" && -f "cdk.json" ]]; then
        echo "$current_dir"
        return 0
    fi
    
    # 如果在scripts目录中，向上查找
    if [[ "$current_dir" == */scripts ]]; then
        echo "$(dirname "$current_dir")"
        return 0
    fi
    
    # 查找包含iac目录的父目录
    local dir="$current_dir"
    while [[ "$dir" != "/" ]]; do
        if [[ -d "$dir/lib" && -f "$dir/package.json" && -f "$dir/cdk.json" ]]; then
            echo "$dir"
            return 0
        fi
        dir=$(dirname "$dir")
    done
    
    log_error "Cannot find project root directory"
    return 1
}

# 设置环境变量
setup_environment() {
    log_info "Setting up validation environment..."
    
    PROJECT_ROOT=$(get_project_root) || exit 1
    SCRIPTS_DIR="$PROJECT_ROOT/scripts"
    LIB_DIR="$PROJECT_ROOT/lib"
    CONFIG_DIR="$LIB_DIR/config"
    APPS_DIR="$PROJECT_ROOT/apps"
    CDK_OUT_DIR="$PROJECT_ROOT/cdk.out"
    
    # 设置环境
    export NODE_ENV="${NODE_ENV:-dev}"
    export AWS_REGION="${AWS_REGION:-ap-east-2}"
    export AWS_DEFAULT_REGION="${AWS_DEFAULT_REGION:-$AWS_REGION}"
    
    log_debug "Project root: $PROJECT_ROOT"
    log_debug "Environment: $NODE_ENV"
    log_debug "AWS Region: $AWS_REGION"
}

# ================================================================
# 1. 配置文件语法检查
# ================================================================

validate_configuration_files() {
    start_check "Configuration Files Syntax"
    
    local config_files=(
        "$CONFIG_DIR/environments/$NODE_ENV/turnkey.ts"
        "$CONFIG_DIR/stacks/turnkey.ts"
        "$CONFIG_DIR/environments/$NODE_ENV/index.ts"
    )
    
    local syntax_errors=0
    
    for config_file in "${config_files[@]}"; do
        if [[ -f "$config_file" ]]; then
            log_debug "Checking syntax: $(basename "$config_file")"
            
            # 检查TypeScript语法（使用tsc --noEmit）
            if command -v tsc >/dev/null 2>&1; then
                if tsc --noEmit --skipLibCheck "$config_file" 2>/dev/null; then
                    log_debug "✅ Syntax OK: $(basename "$config_file")"
                else
                    log_error "❌ Syntax error in: $(basename "$config_file")"
                    syntax_errors=$((syntax_errors + 1))
                fi
            else
                log_debug "TypeScript compiler not available, checking basic syntax"
                # 基本语法检查
                if node -c "$config_file" 2>/dev/null; then
                    log_debug "✅ Basic syntax OK: $(basename "$config_file")"
                else
                    log_error "❌ Basic syntax error in: $(basename "$config_file")"
                    syntax_errors=$((syntax_errors + 1))
                fi
            fi
        else
            log_error "Configuration file not found: $config_file"
            syntax_errors=$((syntax_errors + 1))
        fi
    done
    
    if [[ $syntax_errors -eq 0 ]]; then
        pass_check "Configuration Syntax" "All configuration files have valid syntax"
    else
        fail_check "Configuration Syntax" "$syntax_errors configuration files have syntax errors"
    fi
    
    # 检查特定配置项
    local turnkey_config="$CONFIG_DIR/environments/$NODE_ENV/turnkey.ts"
    
    if [[ -f "$turnkey_config" ]]; then
        # 检查证书配置
        if grep -q "certificates:" "$turnkey_config"; then
            if grep -q "ssmParameterName:" "$turnkey_config"; then
                pass_check "Certificate Config" "Certificate SSM parameter configured"
            else
                fail_check "Certificate Config" "Certificate SSM parameter not configured"
            fi
            
            if grep -q "maxCertificates:" "$turnkey_config"; then
                pass_check "Certificate Limit" "Maximum certificates limit configured"
            else
                warn_check "Certificate Limit" "Maximum certificates limit not explicitly set"
            fi
        else
            fail_check "Certificate Config" "Certificate configuration section missing"
        fi
        
        # 检查数据库配置
        if grep -q "database:" "$turnkey_config"; then
            if grep -q "passwordSecretName:" "$turnkey_config"; then
                pass_check "Database Config" "Database password secret configured"
            else
                fail_check "Database Config" "Database password secret not configured"
            fi
        else
            fail_check "Database Config" "Database configuration section missing"
        fi
        
        # 检查监控配置
        if grep -q "monitoring:" "$turnkey_config"; then
            pass_check "Monitoring Config" "Monitoring configuration found"
        else
            warn_check "Monitoring Config" "Monitoring configuration not found"
            add_recommendation "Consider adding monitoring configuration for better observability"
        fi
    fi
}

# ================================================================
# 2. AWS环境和权限检查
# ================================================================

validate_aws_environment() {
    start_check "AWS Environment and Permissions"
    
    # 检查AWS CLI
    if ! command -v aws >/dev/null 2>&1; then
        fail_check "AWS CLI" "AWS CLI not installed or not in PATH"
        return 1
    else
        pass_check "AWS CLI" "AWS CLI available"
    fi
    
    # 检查AWS凭证
    if aws sts get-caller-identity >/dev/null 2>&1; then
        local account_id
        account_id=$(aws sts get-caller-identity --query "Account" --output text 2>/dev/null)
        local user_arn
        user_arn=$(aws sts get-caller-identity --query "Arn" --output text 2>/dev/null)
        
        pass_check "AWS Credentials" "AWS credentials valid - Account: $account_id"
        log_debug "AWS User/Role: $user_arn"
    else
        fail_check "AWS Credentials" "AWS credentials not configured or invalid"
        return 1
    fi
    
    # 检查区域配置
    local current_region
    current_region=$(aws configure get region 2>/dev/null || echo "$AWS_DEFAULT_REGION")
    
    if [[ "$current_region" == "$AWS_REGION" ]]; then
        pass_check "AWS Region" "AWS region correctly configured: $current_region"
    else
        warn_check "AWS Region" "AWS region mismatch. Expected: $AWS_REGION, Current: ${current_region:-not set}"
        add_recommendation "Set AWS_REGION environment variable or update AWS CLI configuration"
    fi
    
    # 检查关键AWS服务权限
    local required_permissions=(
        "ecs:DescribeClusters"
        "secretsmanager:DescribeSecret"
        "ssm:GetParameter"
        "ec2:DescribeVpcs"
        "efs:DescribeFileSystems"
    )
    
    local permission_errors=0
    
    for permission in "${required_permissions[@]}"; do
        local service
        service=$(echo "$permission" | cut -d':' -f1)
        local action
        action=$(echo "$permission" | cut -d':' -f2)
        
        log_debug "Testing permission: $permission"
        
        case "$service" in
            "ecs")
                if aws ecs describe-clusters --clusters "nonexistent" >/dev/null 2>&1 || [[ $? -eq 254 ]]; then
                    log_debug "✅ ECS permission OK"
                else
                    log_error "❌ Missing ECS permission: $permission"
                    permission_errors=$((permission_errors + 1))
                fi
                ;;
            "secretsmanager")
                if aws secretsmanager list-secrets --max-items 1 >/dev/null 2>&1; then
                    log_debug "✅ Secrets Manager permission OK"
                else
                    log_error "❌ Missing Secrets Manager permission: $permission"
                    permission_errors=$((permission_errors + 1))
                fi
                ;;
            "ssm")
                if aws ssm describe-parameters --max-items 1 >/dev/null 2>&1; then
                    log_debug "✅ SSM permission OK"
                else
                    log_error "❌ Missing SSM permission: $permission"
                    permission_errors=$((permission_errors + 1))
                fi
                ;;
            "ec2")
                if aws ec2 describe-vpcs --max-items 1 >/dev/null 2>&1; then
                    log_debug "✅ EC2 permission OK"
                else
                    log_error "❌ Missing EC2 permission: $permission"
                    permission_errors=$((permission_errors + 1))
                fi
                ;;
            "efs")
                if aws efs describe-file-systems --max-items 1 >/dev/null 2>&1; then
                    log_debug "✅ EFS permission OK"
                else
                    log_error "❌ Missing EFS permission: $permission"
                    permission_errors=$((permission_errors + 1))
                fi
                ;;
        esac
    done
    
    if [[ $permission_errors -eq 0 ]]; then
        pass_check "AWS Permissions" "All required AWS permissions available"
    else
        fail_check "AWS Permissions" "$permission_errors required permissions missing"
        add_recommendation "Review and update IAM policies to include missing permissions"
    fi
}

# ================================================================
# 3. SSM参数验证
# ================================================================

validate_ssm_parameters() {
    start_check "SSM Parameter Store"
    
    # 从配置文件获取SSM参数名称
    local turnkey_config="$CONFIG_DIR/environments/$NODE_ENV/turnkey.ts"
    local ssm_param_name
    
    if [[ -f "$turnkey_config" ]]; then
        ssm_param_name=$(grep "ssmParameterName:" "$turnkey_config" | sed "s/.*ssmParameterName:[[:space:]]*['\"]//g" | sed "s/['\"].*//g" | tr -d ',' | tr -d ' ')
        
        if [[ -n "$ssm_param_name" ]]; then
            log_debug "Found SSM parameter name in config: $ssm_param_name"
            
            # 检查SSM参数是否存在
            if aws ssm get-parameter --name "$ssm_param_name" --with-decryption >/dev/null 2>&1; then
                pass_check "SSM Parameter Exists" "Certificate parameter exists: $ssm_param_name"
                
                # 验证参数值格式
                local param_value
                param_value=$(aws ssm get-parameter --name "$ssm_param_name" --with-decryption --query "Parameter.Value" --output text 2>/dev/null)
                
                if [[ -n "$param_value" ]]; then
                    # 检查JSON格式
                    if echo "$param_value" | jq . >/dev/null 2>&1; then
                        pass_check "SSM Parameter Format" "Certificate parameter has valid JSON format"
                        
                        # 验证JSON结构
                        local cert_count
                        cert_count=$(echo "$param_value" | jq length 2>/dev/null)
                        
                        if [[ "$cert_count" =~ ^[0-9]+$ ]] && [[ "$cert_count" -gt 0 ]]; then
                            pass_check "SSM Parameter Content" "Found $cert_count certificate(s) in parameter"
                            
                            # 验证证书数据结构
                            local structure_valid=true
                            local required_fields=("pfx_base64" "pfx_password" "sign_id" "sign_type")
                            
                            for field in "${required_fields[@]}"; do
                                if ! echo "$param_value" | jq -e ".[0] | has(\"$field\")" >/dev/null 2>&1; then
                                    log_error "Missing required field in certificate data: $field"
                                    structure_valid=false
                                fi
                            done
                            
                            if [[ "$structure_valid" == "true" ]]; then
                                pass_check "SSM Certificate Structure" "Certificate data structure is valid"
                            else
                                fail_check "SSM Certificate Structure" "Certificate data missing required fields"
                            fi
                            
                        elif [[ "$cert_count" == "0" ]]; then
                            warn_check "SSM Parameter Content" "Certificate parameter exists but contains no certificates"
                        else
                            fail_check "SSM Parameter Content" "Certificate parameter has invalid structure"
                        fi
                    else
                        fail_check "SSM Parameter Format" "Certificate parameter is not valid JSON"
                    fi
                else
                    fail_check "SSM Parameter Content" "Certificate parameter is empty"
                fi
                
            else
                fail_check "SSM Parameter Exists" "Certificate parameter not found: $ssm_param_name"
                add_recommendation "Create the certificate parameter using: aws ssm put-parameter --name '$ssm_param_name' --value '[{\"pfx_base64\":\"...\",\"pfx_password\":\"...\",\"sign_id\":\"...\",\"sign_type\":\"...\"}]' --type SecureString"
            fi
        else
            fail_check "SSM Parameter Config" "SSM parameter name not found in configuration"
        fi
    else
        fail_check "SSM Parameter Config" "Turnkey configuration file not found"
    fi
}

# ================================================================
# 4. 证书数据完整性验证
# ================================================================

validate_certificate_data() {
    start_check "Certificate Data Integrity"
    
    # 从配置文件获取SSM参数名称
    local turnkey_config="$CONFIG_DIR/environments/$NODE_ENV/turnkey.ts"
    local ssm_param_name
    
    if [[ -f "$turnkey_config" ]]; then
        ssm_param_name=$(grep "ssmParameterName:" "$turnkey_config" | sed "s/.*ssmParameterName:[[:space:]]*['\"]//g" | sed "s/['\"].*//g" | tr -d ',' | tr -d ' ')
    fi
    
    if [[ -z "$ssm_param_name" ]]; then
        fail_check "Certificate Validation" "SSM parameter name not configured"
        return 1
    fi
    
    # 获取证书数据
    local cert_data
    if cert_data=$(aws ssm get-parameter --name "$ssm_param_name" --with-decryption --query "Parameter.Value" --output text 2>/dev/null); then
        
        if [[ -n "$cert_data" ]] && echo "$cert_data" | jq . >/dev/null 2>&1; then
            local cert_count
            cert_count=$(echo "$cert_data" | jq length)
            
            log_debug "Validating $cert_count certificate(s)"
            
            local valid_certs=0
            local invalid_certs=0
            
            for ((i=0; i<cert_count; i++)); do
                local cert_json
                cert_json=$(echo "$cert_data" | jq -r ".[$i]")
                
                # 验证必需字段
                local pfx_base64
                local pfx_password
                local sign_id
                local sign_type
                
                pfx_base64=$(echo "$cert_json" | jq -r '.pfx_base64 // empty')
                pfx_password=$(echo "$cert_json" | jq -r '.pfx_password // empty')
                sign_id=$(echo "$cert_json" | jq -r '.sign_id // empty')
                sign_type=$(echo "$cert_json" | jq -r '.sign_type // empty')
                
                local cert_valid=true
                local cert_issues=()
                
                # 验证pfx_base64
                if [[ -n "$pfx_base64" ]]; then
                    # 检查base64格式
                    if echo "$pfx_base64" | base64 -d >/dev/null 2>&1; then
                        log_debug "✅ Certificate $((i+1)): base64 data valid"
                    else
                        log_error "❌ Certificate $((i+1)): invalid base64 data"
                        cert_issues+=("invalid base64 data")
                        cert_valid=false
                    fi
                else
                    log_error "❌ Certificate $((i+1)): missing pfx_base64"
                    cert_issues+=("missing pfx_base64")
                    cert_valid=false
                fi
                
                # 验证密码
                if [[ -z "$pfx_password" ]]; then
                    log_warn "⚠️ Certificate $((i+1)): missing pfx_password"
                    cert_issues+=("missing pfx_password")
                fi
                
                # 验证标识
                if [[ -z "$sign_id" ]]; then
                    log_warn "⚠️ Certificate $((i+1)): missing sign_id"
                    cert_issues+=("missing sign_id")
                fi
                
                if [[ -z "$sign_type" ]]; then
                    log_warn "⚠️ Certificate $((i+1)): missing sign_type"
                    cert_issues+=("missing sign_type")
                fi
                
                if [[ "$cert_valid" == "true" ]]; then
                    valid_certs=$((valid_certs + 1))
                    log_debug "✅ Certificate $((i+1)): valid"
                else
                    invalid_certs=$((invalid_certs + 1))
                    log_error "❌ Certificate $((i+1)): invalid - ${cert_issues[*]}"
                fi
            done
            
            if [[ $invalid_certs -eq 0 ]]; then
                pass_check "Certificate Data" "All $valid_certs certificate(s) are valid"
            else
                fail_check "Certificate Data" "$invalid_certs of $cert_count certificate(s) are invalid"
            fi
            
            # 检查证书数量限制
            local max_certs
            max_certs=$(grep "maxCertificates:" "$turnkey_config" | sed 's/.*maxCertificates:[[:space:]]*//g' | sed 's/[,[:space:]].*//' || echo "20")
            
            if [[ "$cert_count" -le "$max_certs" ]]; then
                pass_check "Certificate Count" "Certificate count ($cert_count) within limit ($max_certs)"
            else
                warn_check "Certificate Count" "Certificate count ($cert_count) exceeds configured limit ($max_certs)"
            fi
            
        else
            fail_check "Certificate Data" "Certificate data is empty or not valid JSON"
        fi
        
    else
        fail_check "Certificate Data" "Cannot retrieve certificate data from SSM parameter"
    fi
}

# ================================================================
# 5. 数据库密码Secret验证
# ================================================================

validate_database_secrets() {
    start_check "Database Secrets"
    
    # 从配置文件获取数据库密码secret名称
    local turnkey_config="$CONFIG_DIR/environments/$NODE_ENV/turnkey.ts"
    local db_secret_name
    
    if [[ -f "$turnkey_config" ]]; then
        db_secret_name=$(grep "passwordSecretName:" "$turnkey_config" | sed "s/.*passwordSecretName:[[:space:]]*['\"]//g" | sed "s/['\"].*//g" | tr -d ',' | tr -d ' ')
    fi
    
    if [[ -n "$db_secret_name" ]]; then
        log_debug "Checking database secret: $db_secret_name"
        
        # 检查Secret是否存在
        if aws secretsmanager describe-secret --secret-id "$db_secret_name" >/dev/null 2>&1; then
            pass_check "Database Secret Exists" "Database password secret exists: $db_secret_name"
            
            # 尝试获取密码值（不显示）
            if aws secretsmanager get-secret-value --secret-id "$db_secret_name" --query "SecretString" --output text >/dev/null 2>&1; then
                pass_check "Database Secret Access" "Database password secret is accessible"
                
                # 检查密码格式
                local secret_value
                secret_value=$(aws secretsmanager get-secret-value --secret-id "$db_secret_name" --query "SecretString" --output text 2>/dev/null)
                
                if echo "$secret_value" | jq . >/dev/null 2>&1; then
                    # JSON格式
                    if echo "$secret_value" | jq -e '.password' >/dev/null 2>&1; then
                        pass_check "Database Secret Format" "Database secret has correct JSON structure with 'password' field"
                    else
                        warn_check "Database Secret Format" "Database secret is JSON but missing 'password' field"
                    fi
                else
                    # 纯文本格式
                    if [[ -n "$secret_value" ]]; then
                        pass_check "Database Secret Format" "Database secret contains password value"
                    else
                        fail_check "Database Secret Format" "Database secret is empty"
                    fi
                fi
                
            else
                fail_check "Database Secret Access" "Cannot access database password secret"
            fi
            
        else
            fail_check "Database Secret Exists" "Database password secret not found: $db_secret_name"
            add_recommendation "Create database password secret using: aws secretsmanager create-secret --name '$db_secret_name' --secret-string '{\"password\":\"your-secure-password\"}'"
        fi
        
    else
        fail_check "Database Secret Config" "Database password secret name not configured"
    fi
}

# ================================================================
# 6. ECS集群和基础设施验证
# ================================================================

validate_ecs_infrastructure() {
    start_check "ECS Infrastructure"
    
    # 检查ECS集群
    local cluster_name="yuanhui-odoo-$NODE_ENV"
    
    if aws ecs describe-clusters --clusters "$cluster_name" --query "clusters[0].status" --output text 2>/dev/null | grep -q "ACTIVE"; then
        pass_check "ECS Cluster" "ECS cluster is active: $cluster_name"
        
        # 检查容器实例
        local instance_count
        instance_count=$(aws ecs describe-clusters --clusters "$cluster_name" --query "clusters[0].registeredContainerInstancesCount" --output text 2>/dev/null)
        
        if [[ "$instance_count" -gt 0 ]]; then
            pass_check "ECS Instances" "$instance_count container instance(s) registered"
        else
            warn_check "ECS Instances" "No container instances registered in cluster"
            add_recommendation "Ensure ECS cluster has at least one container instance for deployment"
        fi
        
    else
        fail_check "ECS Cluster" "ECS cluster not found or not active: $cluster_name"
        add_recommendation "Deploy the ECS stack first: npm run deploy:core"
    fi
    
    # 检查VPC
    local vpc_name="Yuanhui-VPC-$NODE_ENV"
    
    if aws ec2 describe-vpcs --filters "Name=tag:Name,Values=$vpc_name" --query "Vpcs[0].State" --output text 2>/dev/null | grep -q "available"; then
        pass_check "VPC Infrastructure" "VPC is available: $vpc_name"
    else
        fail_check "VPC Infrastructure" "VPC not found or not available: $vpc_name"
        add_recommendation "Deploy the network stack first: npm run deploy:network"
    fi
    
    # 检查EFS文件系统
    if aws efs describe-file-systems --query "FileSystems[?Name=='yuanhui-shared-efs-$NODE_ENV'] | [0].LifeCycleState" --output text 2>/dev/null | grep -q "available"; then
        pass_check "EFS Storage" "Shared EFS file system is available"
    else
        warn_check "EFS Storage" "Shared EFS file system not found or not available"
        add_recommendation "Ensure EFS file system is created as part of the ECS stack"
    fi
}

# ================================================================
# 7. 安全配置验证
# ================================================================

validate_security_configuration() {
    start_check "Security Configuration"
    
    # 检查IAM角色
    local task_role_name="YuanhuiTurnkey-${NODE_ENV}-TurnkeyTaskDefinitionTaskRole"
    
    if aws iam get-role --role-name "$task_role_name" >/dev/null 2>&1; then
        pass_check "IAM Task Role" "ECS task role exists: $task_role_name"
        
        # 检查必要的权限策略
        local required_policies=(
            "EFS"
            "SecretsManager"
            "SSM"
        )
        
        local policy_errors=0
        
        for policy_pattern in "${required_policies[@]}"; do
            if aws iam list-attached-role-policies --role-name "$task_role_name" --query "AttachedPolicies[?contains(PolicyName, '$policy_pattern')]" --output text | grep -q "$policy_pattern"; then
                log_debug "✅ $policy_pattern policy attached"
            else
                log_error "❌ $policy_pattern policy not attached"
                policy_errors=$((policy_errors + 1))
            fi
        done
        
        if [[ $policy_errors -eq 0 ]]; then
            pass_check "IAM Policies" "All required policies attached to task role"
        else
            fail_check "IAM Policies" "$policy_errors required policies missing from task role"
        fi
        
    else
        fail_check "IAM Task Role" "ECS task role not found: $task_role_name"
    fi
    
    # 检查执行角色
    local execution_role_name="YuanhuiTurnkey-${NODE_ENV}-TurnkeyExecutionRole"
    
    if aws iam get-role --role-name "$execution_role_name" >/dev/null 2>&1; then
        pass_check "IAM Execution Role" "ECS execution role exists: $execution_role_name"
    else
        fail_check "IAM Execution Role" "ECS execution role not found: $execution_role_name"
    fi
    
    # 检查安全组
    if aws ec2 describe-security-groups --filters "Name=group-name,Values=*turnkey*$NODE_ENV*" --query "SecurityGroups[0].GroupId" --output text 2>/dev/null | grep -q "sg-"; then
        pass_check "Security Groups" "Turnkey security groups configured"
    else
        warn_check "Security Groups" "Turnkey security groups not found"
    fi
}

# ================================================================
# 8. 依赖服务验证
# ================================================================

validate_dependency_services() {
    start_check "Dependency Services"
    
    # 检查Aurora数据库集群
    local db_cluster_name="yuanhui-aurora-cluster-$NODE_ENV"
    
    if aws rds describe-db-clusters --db-cluster-identifier "$db_cluster_name" --query "DBClusters[0].Status" --output text 2>/dev/null | grep -q "available"; then
        pass_check "Aurora Database" "Aurora database cluster is available: $db_cluster_name"
        
        # 检查数据库端点
        local writer_endpoint
        writer_endpoint=$(aws rds describe-db-clusters --db-cluster-identifier "$db_cluster_name" --query "DBClusters[0].Endpoint" --output text 2>/dev/null)
        
        if [[ -n "$writer_endpoint" ]]; then
            pass_check "Database Endpoint" "Database writer endpoint available: $writer_endpoint"
        else
            warn_check "Database Endpoint" "Database writer endpoint not found"
        fi
        
    else
        fail_check "Aurora Database" "Aurora database cluster not available: $db_cluster_name"
        add_recommendation "Deploy the database stack first: npm run deploy:database"
    fi
    
    # 检查Service Connect命名空间
    if aws servicediscovery list-namespaces --query "Namespaces[?Name=='yuanhui-$NODE_ENV'] | [0].Id" --output text 2>/dev/null | grep -q "."; then
        pass_check "Service Connect" "Service Connect namespace exists"
    else
        warn_check "Service Connect" "Service Connect namespace not found"
        add_recommendation "Ensure Service Connect stack is deployed"
    fi
    
    # 检查负载均衡器（如果Turnkey配置了路由）
    local turnkey_config="$CONFIG_DIR/environments/$NODE_ENV/turnkey.ts"
    
    if [[ -f "$turnkey_config" ]] && grep -q "enabled: true" "$turnkey_config" | grep -A5 "routing:"; then
        local alb_name="yuanhui-alb-$NODE_ENV"
        
        if aws elbv2 describe-load-balancers --names "$alb_name" --query "LoadBalancers[0].State.Code" --output text 2>/dev/null | grep -q "active"; then
            pass_check "Load Balancer" "Application Load Balancer is active: $alb_name"
        else
            warn_check "Load Balancer" "Application Load Balancer not found: $alb_name"
            add_recommendation "Deploy the load balancer stack if external access is required"
        fi
    else
        log_debug "Turnkey routing not enabled, skipping load balancer check"
    fi
}

# ================================================================
# 9. 部署工具和环境检查
# ================================================================

validate_deployment_tools() {
    start_check "Deployment Tools"
    
    # 检查Node.js
    if command -v node >/dev/null 2>&1; then
        local node_version
        node_version=$(node --version)
        pass_check "Node.js" "Node.js available: $node_version"
    else
        fail_check "Node.js" "Node.js not installed"
    fi
    
    # 检查npm
    if command -v npm >/dev/null 2>&1; then
        local npm_version
        npm_version=$(npm --version)
        pass_check "npm" "npm available: $npm_version"
    else
        fail_check "npm" "npm not installed"
    fi
    
    # 检查CDK
    if command -v cdk >/dev/null 2>&1; then
        local cdk_version
        cdk_version=$(cdk --version)
        pass_check "AWS CDK" "AWS CDK available: $cdk_version"
    else
        fail_check "AWS CDK" "AWS CDK not installed"
        add_recommendation "Install AWS CDK: npm install -g aws-cdk"
    fi
    
    # 检查项目依赖
    if [[ -f "$PROJECT_ROOT/package.json" ]]; then
        if [[ -d "$PROJECT_ROOT/node_modules" ]]; then
            pass_check "Project Dependencies" "Node.js dependencies installed"
        else
            warn_check "Project Dependencies" "Node.js dependencies not installed"
            add_recommendation "Install dependencies: npm install"
        fi
    fi
    
    # 检查TypeScript编译
    if [[ -f "$PROJECT_ROOT/tsconfig.json" ]]; then
        if command -v tsc >/dev/null 2>&1; then
            pass_check "TypeScript" "TypeScript compiler available"
            
            # 尝试编译检查
            if npm run build >/dev/null 2>&1; then
                pass_check "Project Build" "Project builds successfully"
            else
                warn_check "Project Build" "Project build has errors"
                add_recommendation "Fix build errors: npm run build"
            fi
        else
            warn_check "TypeScript" "TypeScript compiler not available globally"
        fi
    fi
}

# ================================================================
# 10. Docker环境检查
# ================================================================

validate_docker_environment() {
    start_check "Docker Environment"
    
    # 检查Docker
    if command -v docker >/dev/null 2>&1; then
        pass_check "Docker" "Docker available"
        
        # 检查Docker服务状态
        if docker info >/dev/null 2>&1; then
            pass_check "Docker Service" "Docker service running"
        else
            warn_check "Docker Service" "Docker service not running"
            add_recommendation "Start Docker service before deployment"
        fi
    else
        warn_check "Docker" "Docker not installed"
        add_recommendation "Install Docker for local image building (optional)"
    fi
    
    # 检查ECR访问（如果使用自定义镜像）
    local turnkey_config="$CONFIG_DIR/environments/$NODE_ENV/turnkey.ts"
    
    if [[ -f "$turnkey_config" ]] && grep -q "useCustomImage: true" "$turnkey_config"; then
        if aws ecr get-login-token --region "$AWS_REGION" >/dev/null 2>&1; then
            pass_check "ECR Access" "ECR access configured for custom images"
        else
            warn_check "ECR Access" "ECR access not configured"
            add_recommendation "Configure ECR access for custom image deployment"
        fi
    fi
}

# ================================================================
# 报告生成
# ================================================================

generate_validation_report() {
    echo
    echo "=================================================="
    echo "        TURNKEY DEPLOYMENT VALIDATION REPORT      "
    echo "=================================================="
    echo
    echo "Environment: $NODE_ENV"
    echo "AWS Region:  $AWS_REGION"
    echo "Timestamp:   $(date -u +%Y-%m-%dT%H:%M:%SZ)"
    echo
    echo "Validation Summary:"
    echo "--------------------------------------------------"
    echo "Total Checks: $TOTAL_CHECKS"
    echo "Passed:      $PASSED_CHECKS"
    echo "Failed:      $FAILED_CHECKS"
    echo "Warnings:    $WARNING_CHECKS"
    echo
    
    # 整体状态
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        if [[ $WARNING_CHECKS -eq 0 ]]; then
            log_info "🎉 VALIDATION PASSED - Ready for deployment!"
        else
            log_warn "⚠️ VALIDATION PASSED WITH WARNINGS - Review warnings before deployment"
        fi
    else
        log_critical "❌ VALIDATION FAILED - $FAILED_CHECKS critical issues must be resolved"
    fi
    
    echo
    echo "Detailed Results:"
    echo "--------------------------------------------------"
    
    for result in "${CHECK_RESULTS[@]}"; do
        echo "$result"
    done
    
    # 关键问题
    if [[ ${#CRITICAL_ISSUES[@]} -gt 0 ]]; then
        echo
        echo "CRITICAL ISSUES TO RESOLVE:"
        echo "--------------------------------------------------"
        for issue in "${CRITICAL_ISSUES[@]}"; do
            echo "❌ $issue"
        done
    fi
    
    # 警告
    if [[ ${#WARNINGS[@]} -gt 0 ]]; then
        echo
        echo "WARNINGS TO REVIEW:"
        echo "--------------------------------------------------"
        for warning in "${WARNINGS[@]}"; do
            echo "⚠️ $warning"
        done
    fi
    
    # 建议
    if [[ ${#RECOMMENDATIONS[@]} -gt 0 ]]; then
        echo
        echo "RECOMMENDATIONS:"
        echo "--------------------------------------------------"
        for recommendation in "${RECOMMENDATIONS[@]}"; do
            echo "💡 $recommendation"
        done
    fi
    
    echo
    echo "=================================================="
    
    # 生成JSON报告
    local json_report="/tmp/turnkey-validation-report-$(date +%Y%m%d-%H%M%S).json"
    
    cat > "$json_report" << EOF
{
    "validation_report": {
        "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
        "environment": "$NODE_ENV",
        "aws_region": "$AWS_REGION",
        "summary": {
            "total_checks": $TOTAL_CHECKS,
            "passed": $PASSED_CHECKS,
            "failed": $FAILED_CHECKS,
            "warnings": $WARNING_CHECKS,
            "overall_status": "$(if [[ $FAILED_CHECKS -eq 0 ]]; then echo "PASSED"; else echo "FAILED"; fi)"
        },
        "results": [
$(IFS=$'\n'; for result in "${CHECK_RESULTS[@]}"; do
    status=$(echo "$result" | cut -d':' -f1)
    message=$(echo "$result" | cut -d':' -f2-)
    echo "            {\"status\": \"$status\", \"message\": \"$message\"},"
done | sed '$s/,$//')
        ],
        "critical_issues": [
$(IFS=$'\n'; for issue in "${CRITICAL_ISSUES[@]}"; do
    echo "            \"$issue\","
done | sed '$s/,$//')
        ],
        "warnings": [
$(IFS=$'\n'; for warning in "${WARNINGS[@]}"; do
    echo "            \"$warning\","
done | sed '$s/,$//')
        ],
        "recommendations": [
$(IFS=$'\n'; for rec in "${RECOMMENDATIONS[@]}"; do
    echo "            \"$rec\","
done | sed '$s/,$//')
        ]
    }
}
EOF
    
    log_info "Detailed JSON report saved to: $json_report"
    
    # 返回适当的退出代码
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        return 0
    else
        return 1
    fi
}

# ================================================================
# 主执行函数
# ================================================================

main() {
    log_info "Starting Turnkey Deployment Pre-validation"
    log_info "==========================================="
    
    # 设置环境
    setup_environment
    
    # 执行验证
    log_info "Starting validation checks..."
    
    validate_configuration_files
    validate_aws_environment
    validate_ssm_parameters
    validate_certificate_data
    validate_database_secrets
    validate_ecs_infrastructure
    validate_security_configuration
    validate_dependency_services
    validate_deployment_tools
    validate_docker_environment
    
    # 生成报告
    generate_validation_report
}

# 处理命令行参数
case "${1:-}" in
    "--help"|"-h")
        echo "Usage: $0 [options]"
        echo
        echo "Pre-deployment validation for Turnkey system"
        echo
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --debug        Enable debug output"
        echo "  --env ENV      Set environment (dev/prod) [default: dev]"
        echo "  --region REGION Set AWS region [default: ap-east-2]"
        echo
        echo "Environment Variables:"
        echo "  NODE_ENV       CDK environment (dev/prod) [default: dev]"
        echo "  AWS_REGION     AWS region [default: ap-east-2]"
        echo "  DEBUG          Enable debug logging [default: false]"
        echo
        echo "Exit Codes:"
        echo "  0              All validations passed"
        echo "  1              One or more critical validations failed"
        exit 0
        ;;
    "--debug")
        export DEBUG=true
        main
        ;;
    "--env")
        export NODE_ENV="$2"
        shift 2
        main
        ;;
    "--region")
        export AWS_REGION="$2"
        export AWS_DEFAULT_REGION="$2"
        shift 2
        main
        ;;
    "")
        main
        ;;
    *)
        log_error "Unknown option: $1"
        log_info "Use --help for usage information"
        exit 1
        ;;
esac