# EFS迁移身份管理使用指南

## 概述

EFS迁移脚本现已增强，支持在迁移过程中自动设置文件所有权为特定用户/组（odoo:odoo 101:101）。

## 新增功能

### 1. 自动身份映射
- 使用rsync的`--usermap`和`--groupmap`选项将所有文件映射到目标身份
- 支持配置目标用户ID/组ID和用户名/组名
- 提供备用的权限修正机制

### 2. 身份配置验证
- 配置阶段检查目标用户/组是否存在
- 验证现有用户/组ID是否匹配期望值
- 提供详细的身份配置信息

### 3. 完整性验证增强
- 新增文件所有权验证功能
- 检查所有迁移文件是否具有正确的所有权
- 生成详细的所有权验证报告

## 使用方法

### 第一步：配置环境

```bash
# 设置环境变量
export SOURCE_EFS_ID="fs-01234567890abcdef"
export TARGET_EFS_ID="fs-fedcba0987654321f"
export VPC_PEERING_CONNECTION_ID="pcx-1234567890abcdef0"

# 运行配置脚本
./efs-cross-account-config.sh
```

配置脚本会自动：
- 检查目标用户odoo (101)和组odoo (101)是否存在
- 生成包含身份配置的配置文件
- 设置用户映射为启用状态

### 第二步：执行迁移

```bash
# 标准迁移（自动应用身份映射）
./efs-migration-main.sh

# 预览模式
./efs-migration-main.sh --dry-run

# 强制执行
./efs-migration-main.sh --force
```

迁移过程中会：
- 使用rsync用户映射将所有文件设置为odoo:odoo (101:101)
- 如果用户映射失败，会执行备用的权限修正
- 提供详细的进度监控和日志记录

### 第三步：验证结果

```bash
# 完整验证（包括所有权检查）
./efs-migration-verify.sh --source /mnt/source-efs --target /mnt/target-efs

# 仅验证所有权
./efs-migration-verify.sh --source /mnt/source-efs --target /mnt/target-efs \
  --skip-count --skip-size --skip-structure

# 自定义预期身份
./efs-migration-verify.sh --source /mnt/source-efs --target /mnt/target-efs \
  --expected-user-id 1001 --expected-group-id 1001 \
  --expected-user-name myuser --expected-group-name mygroup
```

## 配置选项

### 配置文件设置 (efs-migration-config.env)

```bash
# 目标文件系统身份配置
TARGET_USER_ID=101              # 目标用户ID
TARGET_GROUP_ID=101             # 目标组ID
TARGET_USER_NAME="odoo"         # 目标用户名
TARGET_GROUP_NAME="odoo"        # 目标组名
FORCE_OWNERSHIP=true            # 强制所有权修正
ENABLE_USER_MAPPING=true        # 启用用户映射
```

### 命令行选项

#### efs-migration-main.sh
- 所有原有选项保持不变
- 自动读取身份配置并应用用户映射

#### efs-migration-verify.sh
新增选项：
- `--skip-ownership`: 跳过文件所有权验证
- `--expected-user-id ID`: 设置预期用户ID
- `--expected-group-id ID`: 设置预期组ID
- `--expected-user-name NAME`: 设置预期用户名
- `--expected-group-name NAME`: 设置预期组名

## 技术实现

### 1. 用户映射机制
```bash
# rsync选项示例
rsync -avHAXS \
  --usermap=*:101 \
  --groupmap=*:101 \
  --numeric-ids \
  /source/ /target/
```

### 2. 权限修正备用方案
```bash
# 批量修正文件所有权
find /target -type f -exec chown 101:101 {} +
find /target -type d -exec chown 101:101 {} +
```

### 3. 所有权验证
```bash
# 检查所有文件的所有权
find /target -exec stat -c "%U:%G (%u:%g) %n" {} \; | \
  grep -v "^odoo:odoo (101:101)"
```

## 故障排除

### 常见问题

1. **用户映射不生效**
   - 检查rsync版本是否支持--usermap选项
   - 启用FORCE_OWNERSHIP进行备用权限修正

2. **权限修正失败**
   - 确保运行脚本的用户有sudo权限
   - 检查目标EFS挂载点的写权限

3. **验证失败**
   - 检查预期的用户/组ID是否正确
   - 使用--verbose选项查看详细错误信息

### 日志位置
- 主日志：`logs/migration-YYYYMMDD_HHMMSS.log`
- 错误日志：`logs/migration-errors.log`
- 验证报告：`logs/verification-report-YYYYMMDD_HHMMSS.txt`
- 进度文件：`migration-progress.json`

## 性能优化

### 大文件系统优化
- 用户映射比后期权限修正更高效
- 分批处理权限修正以避免内存问题
- 并行验证以提高检查速度

### 资源使用
- 用户映射：零额外开销
- 权限修正：需要额外的遍历时间
- 所有权验证：轻量级stat操作

## 安全考虑

- 配置文件权限设置为600（仅所有者可读写）
- 所有sudo操作都有适当的错误处理
- 敏感信息不会记录到日志中
- 支持预演模式进行安全测试