#!/bin/bash

# Airflow数据库验证脚本
# 验证Airflow专用数据库和用户是否正确创建

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."
    
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI 未安装或不在PATH中"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装或不在PATH中"
        exit 1
    fi
    
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL客户端 (psql) 未安装或不在PATH中"
        log_warning "请安装PostgreSQL客户端: brew install postgresql (macOS) 或 apt-get install postgresql-client (Ubuntu)"
        exit 1
    fi
    
    log_success "所有必要工具已安装"
}

# 获取环境变量
get_environment() {
    ENVIRONMENT=${NODE_ENV:-dev}
    log_info "使用环境: $ENVIRONMENT"
    
    # 验证AWS凭证
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS凭证未配置或无效"
        log_info "请运行: aws configure"
        exit 1
    fi
    
    log_success "AWS凭证验证成功"
}

# 获取数据库信息
get_database_info() {
    log_info "获取数据库连接信息..."
    
    # 获取主数据库端点
    DATABASE_ENDPOINT=$(aws cloudformation describe-stacks \
        --stack-name "YuanhuiAuroraDatabase-${ENVIRONMENT}" \
        --query 'Stacks[0].Outputs[?OutputKey==`DatabaseEndpoint`].OutputValue' \
        --output text 2>/dev/null)
    
    if [ -z "$DATABASE_ENDPOINT" ]; then
        log_error "无法获取数据库端点"
        log_info "请确保Aurora Database Stack已部署"
        exit 1
    fi
    
    # 获取主数据库凭证ARN
    MASTER_SECRET_ARN=$(aws cloudformation describe-stacks \
        --stack-name "YuanhuiAuroraDatabase-${ENVIRONMENT}" \
        --query 'Stacks[0].Outputs[?OutputKey==`DatabaseSecretArn`].OutputValue' \
        --output text 2>/dev/null)
    
    # 获取Airflow数据库凭证ARN
    AIRFLOW_SECRET_ARN=$(aws cloudformation describe-stacks \
        --stack-name "YuanhuiAuroraDatabase-${ENVIRONMENT}" \
        --query 'Stacks[0].Outputs[?OutputKey==`AirflowDatabaseSecretArn`].OutputValue' \
        --output text 2>/dev/null)
    
    # 获取Airflow数据库名称
    AIRFLOW_DB_NAME=$(aws cloudformation describe-stacks \
        --stack-name "YuanhuiAuroraDatabase-${ENVIRONMENT}" \
        --query 'Stacks[0].Outputs[?OutputKey==`AirflowDatabaseName`].OutputValue' \
        --output text 2>/dev/null)
    
    if [ -z "$AIRFLOW_SECRET_ARN" ]; then
        log_error "无法获取Airflow数据库凭证ARN"
        log_info "请确保数据库初始化已完成"
        exit 1
    fi
    
    # 解析数据库主机和端口
    DB_HOST=$(echo $DATABASE_ENDPOINT | cut -d: -f1)
    DB_PORT=5432
    
    log_success "数据库信息获取成功"
    log_info "数据库主机: $DB_HOST"
    log_info "数据库端口: $DB_PORT"
    log_info "Airflow数据库名称: $AIRFLOW_DB_NAME"
}

# 获取数据库凭证
get_credentials() {
    log_info "获取数据库凭证..."
    
    # 获取主数据库凭证
    MASTER_CREDS=$(aws secretsmanager get-secret-value \
        --secret-id "$MASTER_SECRET_ARN" \
        --query SecretString --output text)
    
    MASTER_USER=$(echo $MASTER_CREDS | jq -r .username)
    MASTER_PASS=$(echo $MASTER_CREDS | jq -r .password)
    
    # 获取Airflow数据库凭证
    AIRFLOW_CREDS=$(aws secretsmanager get-secret-value \
        --secret-id "$AIRFLOW_SECRET_ARN" \
        --query SecretString --output text)
    
    AIRFLOW_USER=$(echo $AIRFLOW_CREDS | jq -r .username)
    AIRFLOW_PASS=$(echo $AIRFLOW_CREDS | jq -r .password)
    
    log_success "数据库凭证获取成功"
    log_info "主数据库用户: $MASTER_USER"
    log_info "Airflow数据库用户: $AIRFLOW_USER"
}

# 测试网络连接
test_network_connection() {
    log_info "测试网络连接..."
    
    # 测试TCP连接
    if timeout 10 bash -c "echo >/dev/tcp/$DB_HOST/$DB_PORT" 2>/dev/null; then
        log_success "网络连接正常 (TCP $DB_HOST:$DB_PORT)"
    else
        log_error "网络连接失败 (TCP $DB_HOST:$DB_PORT)"
        log_info "可能的原因:"
        log_info "1. 安全组配置不正确"
        log_info "2. 数据库实例未运行"
        log_info "3. 网络路由问题"
        exit 1
    fi
    
    # 测试DNS解析
    if nslookup $DB_HOST &> /dev/null; then
        log_success "DNS解析正常"
    else
        log_warning "DNS解析可能有问题"
    fi
}

# 测试主数据库连接
test_master_connection() {
    log_info "测试主数据库连接..."
    
    export PGPASSWORD=$MASTER_PASS
    if psql -h $DB_HOST -p $DB_PORT -U $MASTER_USER -d postgres -c "SELECT version();" &> /dev/null; then
        log_success "主数据库连接成功"
    else
        log_error "主数据库连接失败"
        log_info "正在进行详细诊断..."
        
        # 详细诊断
        log_info "尝试连接详细输出:"
        export PGPASSWORD=$MASTER_PASS
        psql -h $DB_HOST -p $DB_PORT -U $MASTER_USER -d postgres -c "SELECT version();" 2>&1 || true
        
        exit 1
    fi
}

# 验证Airflow数据库存在
verify_airflow_database() {
    log_info "验证Airflow数据库是否存在..."
    
    export PGPASSWORD=$MASTER_PASS
    DB_EXISTS=$(psql -h $DB_HOST -p $DB_PORT -U $MASTER_USER -d postgres \
        -t -c "SELECT 1 FROM pg_database WHERE datname = '$AIRFLOW_DB_NAME';" | xargs)
    
    if [ "$DB_EXISTS" = "1" ]; then
        log_success "Airflow数据库 '$AIRFLOW_DB_NAME' 存在"
    else
        log_error "Airflow数据库 '$AIRFLOW_DB_NAME' 不存在"
        exit 1
    fi
}

# 验证Airflow用户存在
verify_airflow_user() {
    log_info "验证Airflow用户是否存在..."
    
    export PGPASSWORD=$MASTER_PASS
    USER_EXISTS=$(psql -h $DB_HOST -p $DB_PORT -U $MASTER_USER -d postgres \
        -t -c "SELECT 1 FROM pg_user WHERE usename = '$AIRFLOW_USER';" | xargs)
    
    if [ "$USER_EXISTS" = "1" ]; then
        log_success "Airflow用户 '$AIRFLOW_USER' 存在"
    else
        log_error "Airflow用户 '$AIRFLOW_USER' 不存在"
        exit 1
    fi
}

# 测试Airflow用户连接
test_airflow_connection() {
    log_info "测试Airflow用户数据库连接..."
    
    export PGPASSWORD=$AIRFLOW_PASS
    if psql -h $DB_HOST -p $DB_PORT -U $AIRFLOW_USER -d $AIRFLOW_DB_NAME -c "SELECT current_user, current_database();" &> /dev/null; then
        log_success "Airflow用户数据库连接成功"
    else
        log_error "Airflow用户数据库连接失败"
        exit 1
    fi
}

# 验证Airflow用户权限
verify_airflow_permissions() {
    log_info "验证Airflow用户权限..."
    
    export PGPASSWORD=$AIRFLOW_PASS
    
    # 测试创建表权限
    if psql -h $DB_HOST -p $DB_PORT -U $AIRFLOW_USER -d $AIRFLOW_DB_NAME \
        -c "CREATE TABLE IF NOT EXISTS test_table (id SERIAL PRIMARY KEY, name VARCHAR(50));" &> /dev/null; then
        log_success "Airflow用户具有创建表权限"
        
        # 清理测试表
        psql -h $DB_HOST -p $DB_PORT -U $AIRFLOW_USER -d $AIRFLOW_DB_NAME \
            -c "DROP TABLE IF EXISTS test_table;" &> /dev/null
    else
        log_error "Airflow用户缺少创建表权限"
        exit 1
    fi
}

# 验证数据库隔离
verify_database_isolation() {
    log_info "验证数据库隔离..."
    
    export PGPASSWORD=$AIRFLOW_PASS
    
    # 尝试访问主数据库（应该失败）
    if psql -h $DB_HOST -p $DB_PORT -U $AIRFLOW_USER -d postgres -c "SELECT 1;" &> /dev/null; then
        log_warning "Airflow用户可以访问主数据库 - 可能存在权限过度授予"
    else
        log_success "Airflow用户无法访问主数据库 - 隔离正确"
    fi
    
    # 尝试访问odoo数据库（如果存在，应该失败）
    export PGPASSWORD=$MASTER_PASS
    ODOO_DB_EXISTS=$(psql -h $DB_HOST -p $DB_PORT -U $MASTER_USER -d postgres \
        -t -c "SELECT 1 FROM pg_database WHERE datname = 'odoo';" | xargs)
    
    if [ "$ODOO_DB_EXISTS" = "1" ]; then
        export PGPASSWORD=$AIRFLOW_PASS
        if psql -h $DB_HOST -p $DB_PORT -U $AIRFLOW_USER -d odoo -c "SELECT 1;" &> /dev/null; then
            log_warning "Airflow用户可以访问Odoo数据库 - 可能存在权限过度授予"
        else
            log_success "Airflow用户无法访问Odoo数据库 - 隔离正确"
        fi
    else
        log_info "Odoo数据库不存在，跳过隔离测试"
    fi
}

# 生成连接字符串
generate_connection_string() {
    log_info "生成Airflow数据库连接字符串..."
    
    # URL编码密码
    ENCODED_PASS=$(python3 -c "import urllib.parse; print(urllib.parse.quote_plus('$AIRFLOW_PASS'))")
    
    CONNECTION_STRING="postgresql://${AIRFLOW_USER}:${ENCODED_PASS}@${DB_HOST}:${DB_PORT}/${AIRFLOW_DB_NAME}"
    
    log_success "连接字符串生成成功"
    echo ""
    echo "Airflow数据库连接字符串:"
    echo "$CONNECTION_STRING"
    echo ""
}

# 主函数
main() {
    echo "========================================"
    echo "    Airflow数据库验证脚本"
    echo "========================================"
    echo ""
    
    check_prerequisites
    get_environment
    get_database_info
    get_credentials
    test_network_connection
    test_master_connection
    verify_airflow_database
    verify_airflow_user
    test_airflow_connection
    verify_airflow_permissions
    verify_database_isolation
    generate_connection_string
    
    echo ""
    log_success "所有验证测试通过！"
    echo "========================================"
}

# 运行主函数
main "$@"
