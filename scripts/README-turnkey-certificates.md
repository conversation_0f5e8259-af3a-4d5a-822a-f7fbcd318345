# 电子发票证书管理工具

## 概述

这个工具套件用于管理存储在AWS SSM Parameter Store中的电子发票证书。它提供了安全、可靠的方式来上传、下载、验证和备份证书数据。

## 功能特性

- ✅ **安全存储**: 证书数据加密存储在AWS SSM Parameter Store中
- ✅ **多环境支持**: 支持开发(dev)和生产(prod)环境分离
- ✅ **格式验证**: 自动验证JSON格式和必要字段
- ✅ **证书检查**: 验证Base64格式和证书过期状态
- ✅ **安全预览**: 支持dry-run模式预览操作
- ✅ **自动备份**: 内置备份功能防止数据丢失
- ✅ **详细日志**: 支持详细日志记录和文件输出
- ✅ **交互确认**: 危险操作需要用户确认

## 目录结构

```
scripts/
├── manage-turnkey-certificates.sh     # 主要管理脚本
├── examples/
│   └── turnkey-certificates.json      # 示例证书JSON文件
├── backups/                           # 自动创建的备份目录
│   ├── certificates_20240807_143022_dev.json
│   └── certificates_20240807_143022_prod.json
└── README-turnkey-certificates.md     # 本文档
```

## 依赖项

确保系统已安装以下工具：

```bash
# AWS CLI (必须已配置凭证)
aws --version

# jq (JSON处理工具)
jq --version

# base64 (通常系统自带)
base64 --version
```

### 安装依赖项

**macOS:**
```bash
# 安装 AWS CLI
brew install awscli

# 安装 jq
brew install jq

# 配置 AWS 凭证
aws configure
```

**Linux (Ubuntu/Debian):**
```bash
# 安装 AWS CLI
sudo apt update
sudo apt install awscli

# 安装 jq
sudo apt install jq

# 配置 AWS 凭证
aws configure
```

## 快速开始

### 1. 权限配置

确保AWS凭证已正确配置并具有以下权限：

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ssm:PutParameter",
                "ssm:GetParameter",
                "ssm:GetParameters",
                "ssm:DeleteParameter"
            ],
            "Resource": "arn:aws:ssm:*:*:parameter/yuanhui/einvoice-turnkey/*"
        }
    ]
}
```

### 2. 验证工具是否正常工作

```bash
# 显示帮助信息
./manage-turnkey-certificates.sh help

# 检查依赖项
./manage-turnkey-certificates.sh validate examples/turnkey-certificates.json -e dev
```

### 3. 基本操作流程

```bash
# 1. 上传证书到开发环境
./manage-turnkey-certificates.sh upload examples/turnkey-certificates.json -e dev

# 2. 列出证书信息
./manage-turnkey-certificates.sh list -e dev

# 3. 备份证书
./manage-turnkey-certificates.sh backup -e dev

# 4. 下载证书到本地
./manage-turnkey-certificates.sh download /tmp/dev-certificates.json -e dev
```

## 详细使用说明

### 命令语法

```bash
./manage-turnkey-certificates.sh <command> -e <environment> [options] [arguments]
```

### 命令详解

#### 1. upload - 上传证书

将本地JSON文件中的证书上传到SSM Parameter Store。

```bash
# 基本用法
./manage-turnkey-certificates.sh upload examples/turnkey-certificates.json -e dev

# 预览上传操作
./manage-turnkey-certificates.sh upload examples/turnkey-certificates.json -e dev --dry-run

# 强制覆盖现有证书
./manage-turnkey-certificates.sh upload examples/turnkey-certificates.json -e dev --force

# 详细日志模式
./manage-turnkey-certificates.sh upload examples/turnkey-certificates.json -e dev -v
```

**注意事项:**
- 上传前会自动验证JSON格式
- 如果参数已存在，需要确认或使用 `--force` 参数
- 证书数据会自动加密存储

#### 2. download - 下载证书

从SSM Parameter Store下载证书到本地文件。

```bash
# 基本用法
./manage-turnkey-certificates.sh download /tmp/certificates.json -e dev

# 预览下载操作
./manage-turnkey-certificates.sh download /tmp/certificates.json -e dev --dry-run

# 强制覆盖现有文件
./manage-turnkey-certificates.sh download /tmp/certificates.json -e dev --force
```

#### 3. list - 列出证书

显示环境中存储的证书信息摘要（不显示敏感数据）。

```bash
# 列出开发环境证书
./manage-turnkey-certificates.sh list -e dev

# 列出生产环境证书
./manage-turnkey-certificates.sh list -e prod
```

输出示例：
```
=== 证书汇总信息 ===
环境: dev
参数名称: /yuanhui/einvoice-turnkey/dev/certificates
总证书数量: 2
最后更新: 2024-08-07T12:30:45Z

=== 证书详情 ===
ID  签名ID               类型         发行日期        过期日期      描述
------------------------------------------------------------------------------------
1   <USER>  <GROUP>    2024-01-01     2026-01-01   企业电子发票证书
2   branch_certificate   branch       2024-02-15     2026-02-15   分公司电子发票证书
```

#### 4. validate - 验证证书

验证本地文件或SSM中证书的格式和内容。

```bash
# 验证本地文件
./manage-turnkey-certificates.sh validate examples/turnkey-certificates.json -e dev

# 验证SSM中的证书
./manage-turnkey-certificates.sh validate -e dev
```

验证检查包括：
- JSON格式正确性
- 必要字段完整性
- Base64格式有效性
- 证书过期状态
- 证书数量限制

#### 5. backup - 备份证书

将SSM中的证书备份到本地文件。

```bash
# 自动命名备份
./manage-turnkey-certificates.sh backup -e dev

# 指定备份名称
./manage-turnkey-certificates.sh backup prod_monthly_backup -e prod

# 预览备份操作
./manage-turnkey-certificates.sh backup -e dev --dry-run
```

备份文件保存位置: `scripts/backups/`

### 选项参数

| 参数 | 描述 | 示例 |
|------|------|------|
| `-e, --environment` | 指定环境 (dev\|prod) | `-e dev` |
| `--dry-run` | 预览模式，不实际执行 | `--dry-run` |
| `--force` | 强制覆盖现有数据 | `--force` |
| `-v, --verbose` | 显示详细日志 | `-v` |
| `--log-file` | 指定日志文件路径 | `--log-file /tmp/cert.log` |
| `-h, --help` | 显示帮助信息 | `-h` |

## 证书JSON格式

### 基本结构

```json
{
  "certificates": [
    {
      "pfx_base64": "证书的Base64编码",
      "pfx_password": "证书密码",
      "sign_id": "唯一签名ID",
      "sign_type": "证书类型",
      "description": "证书描述（可选）",
      "issued_date": "发行日期（可选）",
      "expiry_date": "过期日期（可选）",
      "issuer": "发行机构（可选）"
    }
  ],
  "metadata": {
    "version": "1.0",
    "last_updated": "2024-08-07T12:30:45Z",
    "total_certificates": 1,
    "environment": "dev"
  }
}
```

### 必要字段说明

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `pfx_base64` | string | ✅ | PFX证书文件的Base64编码 |
| `pfx_password` | string | ✅ | 证书密码 |
| `sign_id` | string | ✅ | 唯一的签名标识符 |
| `sign_type` | string | ✅ | 证书类型：corporate/branch/individual |

### 可选字段说明

| 字段 | 类型 | 描述 |
|------|------|------|
| `description` | string | 证书的人类可读描述 |
| `issued_date` | string | 发行日期 (YYYY-MM-DD格式) |
| `expiry_date` | string | 过期日期 (YYYY-MM-DD格式) |
| `issuer` | string | 证书发行机构 |

### 证书类型说明

- `corporate`: 企业证书
- `branch`: 分公司证书
- `individual`: 个人证书

## 安全最佳实践

### 1. 证书文件安全

```bash
# 设置适当的文件权限
chmod 600 /path/to/certificate.json

# 不要将证书文件提交到版本控制
echo "*.json" >> .gitignore
```

### 2. 环境分离

```bash
# 始终明确指定环境
./manage-turnkey-certificates.sh upload cert.json -e dev  # 开发环境
./manage-turnkey-certificates.sh upload cert.json -e prod # 生产环境
```

### 3. 定期备份

```bash
# 设置定期备份脚本
#!/bin/bash
./manage-turnkey-certificates.sh backup "monthly_$(date +%Y%m)" -e prod
./manage-turnkey-certificates.sh backup "monthly_$(date +%Y%m)" -e dev
```

### 4. 证书过期监控

```bash
# 定期检查证书状态
./manage-turnkey-certificates.sh list -e prod
./manage-turnkey-certificates.sh validate -e prod
```

## 故障排除

### 常见错误及解决方案

#### 1. AWS凭证错误

**错误信息:** `AWS凭证配置错误`

**解决方案:**
```bash
# 检查AWS配置
aws configure list
aws sts get-caller-identity

# 重新配置凭证
aws configure
```

#### 2. 权限不足

**错误信息:** `AccessDenied: User is not authorized`

**解决方案:**
确保IAM用户/角色具有SSM参数访问权限。

#### 3. JSON格式错误

**错误信息:** `JSON格式错误`

**解决方案:**
```bash
# 验证JSON格式
jq . your-certificate.json

# 使用示例文件作为模板
cp examples/turnkey-certificates.json your-certificate.json
```

#### 4. Base64格式错误

**错误信息:** `pfx_base64不是有效的base64格式`

**解决方案:**
```bash
# 重新编码PFX文件
base64 -i certificate.pfx -o certificate_base64.txt

# 验证Base64格式
base64 -d certificate_base64.txt > /dev/null && echo "格式正确" || echo "格式错误"
```

### 日志分析

启用详细日志进行问题诊断：

```bash
# 启用详细日志
./manage-turnkey-certificates.sh upload cert.json -e dev -v

# 保存日志到文件
./manage-turnkey-certificates.sh upload cert.json -e dev -v --log-file /tmp/cert-debug.log
```

## 高级用法

### 批量操作脚本

创建批量处理脚本：

```bash
#!/bin/bash
# batch-certificate-update.sh

CERT_DIR="/path/to/certificates"
ENVIRONMENTS=("dev" "prod")

for env in "${ENVIRONMENTS[@]}"; do
    echo "Processing environment: $env"
    
    # 备份现有证书
    ./manage-turnkey-certificates.sh backup "pre_update_$(date +%Y%m%d)" -e "$env"
    
    # 上传新证书
    ./manage-turnkey-certificates.sh upload "$CERT_DIR/$env-certificates.json" -e "$env" --force
    
    # 验证上传结果
    ./manage-turnkey-certificates.sh validate -e "$env"
    
    echo "Environment $env processed successfully"
done
```

### 监控脚本

创建证书监控脚本：

```bash
#!/bin/bash
# monitor-certificates.sh

check_certificate_expiry() {
    local env=$1
    echo "Checking certificates in $env environment..."
    
    ./manage-turnkey-certificates.sh list -e "$env" | grep -E "(红色|黄色)" && {
        echo "WARNING: Found expiring certificates in $env environment"
        # 发送告警通知
        # send_alert "$env"
    }
}

check_certificate_expiry "dev"
check_certificate_expiry "prod"
```

## 集成指南

### CI/CD集成

在CI/CD流水线中使用证书管理工具：

```yaml
# .github/workflows/certificate-update.yml
name: Certificate Update

on:
  push:
    paths:
      - 'certificates/*.json'

jobs:
  update-certificates:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-east-2
      
      - name: Update Development Certificates
        run: |
          chmod +x scripts/manage-turnkey-certificates.sh
          ./scripts/manage-turnkey-certificates.sh upload certificates/dev.json -e dev --force
      
      - name: Update Production Certificates
        run: |
          ./scripts/manage-turnkey-certificates.sh upload certificates/prod.json -e prod --force
        if: github.ref == 'refs/heads/main'
```

## 支持与贡献

### 报告问题

如发现问题，请包含以下信息：

1. 操作系统和版本
2. 工具版本信息
3. 完整的错误信息
4. 重现步骤
5. 预期行为

### 开发指南

如需修改或扩展工具：

1. 遵循现有的代码风格
2. 添加适当的错误处理
3. 更新相关文档
4. 添加测试用例

## 版本历史

- **v1.0.0** (2024-08-07): 初始版本
  - 基本的上传、下载、列表功能
  - JSON格式验证
  - 多环境支持
  - 备份功能

## 许可证

本工具属于Yuan Hui基础设施团队内部使用工具。