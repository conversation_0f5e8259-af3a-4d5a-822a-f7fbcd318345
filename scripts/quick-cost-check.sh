#!/bin/bash

# 快速CloudWatch成本检查脚本
# 简化版本，专注于关键问题诊断

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
REGION=${AWS_DEFAULT_REGION:-ap-east-2}

echo -e "${BLUE}=== 快速CloudWatch成本检查 ===${NC}"
echo "区域: $REGION"
echo "时间: $(date)"
echo ""

# 基础检查
echo -e "${BLUE}1. 基础环境检查${NC}"
echo "----------------------------------------"

# 检查AWS CLI
if ! command -v aws >/dev/null 2>&1; then
    echo -e "${RED}❌ AWS CLI未安装${NC}"
    exit 1
else
    echo -e "${GREEN}✅ AWS CLI已安装${NC}"
fi

# 检查AWS配置
if aws sts get-caller-identity >/dev/null 2>&1; then
    account=$(aws sts get-caller-identity --query Account --output text 2>/dev/null)
    user=$(aws sts get-caller-identity --query Arn --output text 2>/dev/null)
    echo -e "${GREEN}✅ AWS认证成功${NC}"
    echo "   账户: $account"
    echo "   用户: $user"
else
    echo -e "${RED}❌ AWS认证失败${NC}"
    echo "请运行: aws configure"
    exit 1
fi

echo ""

# 检查ECS集群
echo -e "${BLUE}2. ECS Container Insights检查${NC}"
echo "----------------------------------------"

# 简单的集群检查
cluster_count=$(aws ecs list-clusters --region $REGION --query 'length(clusterArns)' --output text 2>/dev/null || echo "0")

if [ "$cluster_count" -eq 0 ]; then
    echo -e "${GREEN}✅ 未找到ECS集群 (无Container Insights费用)${NC}"
else
    echo "发现 $cluster_count 个ECS集群:"
    
    # 获取集群列表
    clusters=$(aws ecs list-clusters --region $REGION --query 'clusterArns[]' --output text 2>/dev/null)
    
    for cluster_arn in $clusters; do
        if [ -n "$cluster_arn" ] && [ "$cluster_arn" != "None" ]; then
            cluster_name=$(basename "$cluster_arn")
            echo ""
            echo "集群: $cluster_name"
            
            # 检查Container Insights
            insights_setting=$(aws ecs describe-clusters --region $REGION --clusters "$cluster_arn" \
                --query 'clusters[0].settings[?name==`containerInsights`]' --output json 2>/dev/null)
            
            if [ "$insights_setting" = "[]" ] || [ "$insights_setting" = "null" ]; then
                echo -e "  Container Insights: ${GREEN}未配置/禁用${NC} ✅"
            else
                insights_value=$(echo "$insights_setting" | grep -o '"value":"[^"]*"' | cut -d'"' -f4)
                if [ "$insights_value" = "enabled" ]; then
                    echo -e "  Container Insights: ${RED}启用${NC} ❌ (产生费用!)"
                else
                    echo -e "  Container Insights: ${GREEN}禁用${NC} ✅"
                fi
            fi
            
            # 检查服务数量
            service_count=$(aws ecs list-services --region $REGION --cluster "$cluster_arn" \
                --query 'length(serviceArns)' --output text 2>/dev/null || echo "0")
            echo "  运行服务: $service_count 个"
        fi
    done
fi

echo ""

# 检查关键指标命名空间
echo -e "${BLUE}3. 关键指标检查${NC}"
echo "----------------------------------------"

# 检查ECS相关指标
ecs_metrics=$(aws cloudwatch list-metrics --region $REGION --namespace "AWS/ECS" \
    --query 'length(Metrics)' --output text 2>/dev/null || echo "0")
echo "AWS/ECS 指标数量: $ecs_metrics"

# 检查Container Insights指标 (添加超时)
echo "正在检查Container Insights指标..."
ci_metrics=$(timeout 10 aws cloudwatch list-metrics --region $REGION --namespace "ECS/ContainerInsights" \
    --query 'length(Metrics)' --output text 2>/dev/null || echo "0")

if [ "$ci_metrics" = "0" ] || [ -z "$ci_metrics" ]; then
    echo -e "ECS/ContainerInsights 指标: ${GREEN}0 个${NC} ✅"
    ci_metrics=0
elif [ "$ci_metrics" -gt 0 ] 2>/dev/null; then
    echo -e "ECS/ContainerInsights 指标: ${RED}$ci_metrics 个${NC} ❌ (产生费用!)"
else
    echo -e "ECS/ContainerInsights 指标: ${YELLOW}检查超时或出错${NC}"
    ci_metrics=0
fi

echo ""

# 成本评估
echo -e "${BLUE}4. 成本评估${NC}"
echo "----------------------------------------"

total_cost_estimate=0

if [ "$ci_metrics" -gt 0 ]; then
    # Container Insights费用估算
    ci_cost=$((ci_metrics * 30 / 100)) # 每个指标约$0.30/月
    total_cost_estimate=$((total_cost_estimate + ci_cost))
    echo -e "${RED}Container Insights预估费用: ~\$${ci_cost}/月${NC}"
fi

if [ "$total_cost_estimate" -eq 0 ]; then
    echo -e "${GREEN}✅ 当前配置预估费用: \$0/月${NC}"
else
    echo -e "${RED}❌ 当前配置预估费用: ~\$${total_cost_estimate}/月${NC}"
fi

echo ""

# 建议
echo -e "${BLUE}5. 建议操作${NC}"
echo "----------------------------------------"

if [ "$ci_metrics" -gt 0 ]; then
    echo -e "${YELLOW}发现Container Insights指标，建议:${NC}"
    echo "1. 检查CDK配置中的 enableDetailedMonitoring 设置"
    echo "2. 开发环境建议设置为 false"
    echo "3. 重新部署ECS栈以应用更改"
    echo ""
    echo "立即操作:"
    echo "  cd /Users/<USER>/code/yuan/iac"
    echo "  npm run build"
    echo "  npx cdk deploy YuanhuiEcs-dev"
else
    echo -e "${GREEN}✅ 配置良好，无需额外操作${NC}"
fi

echo ""
echo -e "${GREEN}检查完成${NC}"
