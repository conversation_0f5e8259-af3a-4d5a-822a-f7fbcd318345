#!/bin/bash

#=====================================================
# EFS跨账号迁移 - 资源清理脚本
# 用途：清理迁移过程中创建的临时资源和文件
# 可独立运行或被主迁移脚本调用
#=====================================================

set -euo pipefail

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="$SCRIPT_DIR/efs-migration-config.env"
PID_FILE="$SCRIPT_DIR/efs-migration.pid"
PROGRESS_FILE="$SCRIPT_DIR/migration-progress.json"
LOG_DIR="$SCRIPT_DIR/logs"

# 清理选项
CLEANUP_MOUNTS=true
CLEANUP_TEMP_FILES=true
CLEANUP_OLD_LOGS=false
CLEANUP_CONFIG=false
FORCE_CLEANUP=false
PRESERVE_REPORTS=true
LOG_RETENTION_DAYS=7

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 帮助函数
show_help() {
    cat << EOF
EFS迁移资源清理脚本

用法:
    $0 [选项]

清理选项:
    --skip-mounts             跳过卸载EFS文件系统
    --skip-temp-files         跳过临时文件清理
    --cleanup-old-logs        清理旧日志文件
    --cleanup-config          清理配置文件
    --log-retention-days N    日志保留天数 (默认: 7)
    --preserve-reports        保留验证报告 (默认启用)
    --no-preserve-reports     不保留验证报告

执行选项:
    -f, --force               强制清理，不要求确认
    -v, --verbose             详细输出
    --dry-run                预演模式，显示将要清理的内容
    -h, --help               显示此帮助信息

清理范围:
    默认清理:
    - 卸载EFS挂载点
    - 清理临时文件
    - 清理进程状态文件
    
    可选清理:
    - 旧日志文件 (--cleanup-old-logs)
    - 配置文件 (--cleanup-config)
    - 验证报告 (--no-preserve-reports)

示例:
    $0                       # 标准清理
    $0 --dry-run            # 预览清理内容
    $0 --force              # 强制清理，不要求确认
    $0 --cleanup-old-logs   # 包含旧日志清理
    $0 --cleanup-config     # 完全清理（包括配置）
EOF
}

# 参数解析
VERBOSE=false
DRY_RUN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-mounts)
            CLEANUP_MOUNTS=false
            shift
            ;;
        --skip-temp-files)
            CLEANUP_TEMP_FILES=false
            shift
            ;;
        --cleanup-old-logs)
            CLEANUP_OLD_LOGS=true
            shift
            ;;
        --cleanup-config)
            CLEANUP_CONFIG=true
            shift
            ;;
        --log-retention-days)
            LOG_RETENTION_DAYS="$2"
            shift 2
            ;;
        --preserve-reports)
            PRESERVE_REPORTS=true
            shift
            ;;
        --no-preserve-reports)
            PRESERVE_REPORTS=false
            shift
            ;;
        -f|--force)
            FORCE_CLEANUP=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            set -x
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 加载配置（如果存在）
load_config() {
    if [[ -f "$CONFIG_FILE" ]]; then
        log_info "加载配置文件: $CONFIG_FILE"
        # shellcheck source=/dev/null
        source "$CONFIG_FILE"
    else
        log_warning "配置文件不存在: $CONFIG_FILE"
        # 设置默认挂载点
        SOURCE_MOUNT_POINT="/mnt/source-efs"
        TARGET_MOUNT_POINT="/mnt/target-efs"
    fi
}

# 检查运行中的迁移进程
check_running_migration() {
    if [[ -f "$PID_FILE" ]]; then
        local pid
        pid=$(cat "$PID_FILE" 2>/dev/null || echo "")
        
        if [[ -n "$pid" ]] && kill -0 "$pid" 2>/dev/null; then
            if [[ "$FORCE_CLEANUP" == "true" ]]; then
                log_warning "发现运行中的迁移进程 (PID: $pid)，强制停止..."
                if [[ "$DRY_RUN" == "false" ]]; then
                    kill "$pid" || true
                    sleep 3
                    if kill -0 "$pid" 2>/dev/null; then
                        log_warning "进程仍在运行，使用SIGKILL强制终止..."
                        kill -9 "$pid" || true
                    fi
                fi
            else
                log_error "迁移进程正在运行 (PID: $pid)"
                log_info "使用 --force 强制清理，或等待迁移完成"
                exit 1
            fi
        else
            log_info "清理无效的PID文件"
            if [[ "$DRY_RUN" == "false" ]]; then
                rm -f "$PID_FILE"
            fi
        fi
    fi
}

# 卸载EFS文件系统
cleanup_mounts() {
    if [[ "$CLEANUP_MOUNTS" != "true" ]]; then
        log_info "跳过EFS挂载点清理"
        return 0
    fi
    
    log_info "开始清理EFS挂载点..."
    
    # 从配置或默认值获取挂载点
    local mount_points=()
    if [[ -n "${SOURCE_MOUNT_POINT:-}" ]]; then
        mount_points+=("$SOURCE_MOUNT_POINT")
    fi
    if [[ -n "${TARGET_MOUNT_POINT:-}" ]]; then
        mount_points+=("$TARGET_MOUNT_POINT")
    fi
    
    # 添加常见的挂载点（以防配置丢失）
    mount_points+=("/mnt/source-efs" "/mnt/target-efs")
    
    local cleaned_count=0
    
    for mount_point in "${mount_points[@]}"; do
        if [[ ! -d "$mount_point" ]]; then
            continue
        fi
        
        # 检查是否已挂载
        if mountpoint -q "$mount_point" 2>/dev/null; then
            log_info "卸载EFS: $mount_point"
            if [[ "$DRY_RUN" == "false" ]]; then
                if sudo umount "$mount_point" 2>/dev/null; then
                    log_success "成功卸载: $mount_point"
                    ((cleaned_count++))
                else
                    log_warning "卸载失败: $mount_point (可能需要手动处理)"
                    
                    # 尝试强制卸载
                    if [[ "$FORCE_CLEANUP" == "true" ]]; then
                        log_info "尝试强制卸载..."
                        sudo umount -f "$mount_point" 2>/dev/null || \
                        sudo umount -l "$mount_point" 2>/dev/null || \
                        log_warning "强制卸载也失败了"
                    fi
                fi
            else
                log_info "[DRY RUN] 将卸载: $mount_point"
                ((cleaned_count++))
            fi
        else
            log_info "挂载点未挂载: $mount_point"
        fi
        
        # 清理空的挂载点目录
        if [[ -d "$mount_point" ]] && [[ -z "$(ls -A "$mount_point" 2>/dev/null)" ]]; then
            log_info "清理空挂载点目录: $mount_point"
            if [[ "$DRY_RUN" == "false" ]]; then
                sudo rmdir "$mount_point" 2>/dev/null || log_warning "无法删除挂载点目录: $mount_point"
            else
                log_info "[DRY RUN] 将删除空目录: $mount_point"
            fi
        fi
    done
    
    log_success "EFS挂载点清理完成 (处理了 $cleaned_count 个挂载点)"
}

# 清理临时文件
cleanup_temp_files() {
    if [[ "$CLEANUP_TEMP_FILES" != "true" ]]; then
        log_info "跳过临时文件清理"
        return 0
    fi
    
    log_info "开始清理临时文件..."
    
    local temp_patterns=(
        "/tmp/efs_*_$$"
        "/tmp/rsync_progress_*"
        "/tmp/efs_structure_*"
        "/tmp/efs_checksum_*"
        "/tmp/efs_sampling_*"
    )
    
    local cleaned_count=0
    
    # 清理匹配模式的临时文件
    for pattern in "${temp_patterns[@]}"; do
        # 移除进程ID部分进行通配符匹配
        local search_pattern
        search_pattern=$(echo "$pattern" | sed 's/_\$\$/_*/g')
        
        if compgen -G "$search_pattern" > /dev/null 2>&1; then
            log_info "清理临时文件: $search_pattern"
            if [[ "$DRY_RUN" == "false" ]]; then
                rm -rf $search_pattern 2>/dev/null || true
                ((cleaned_count++))
            else
                log_info "[DRY RUN] 将删除: $search_pattern"
                ((cleaned_count++))
            fi
        fi
    done
    
    # 清理进程状态文件
    local status_files=(
        "$PID_FILE"
        "$PROGRESS_FILE"
    )
    
    for file in "${status_files[@]}"; do
        if [[ -f "$file" ]]; then
            log_info "清理状态文件: $file"
            if [[ "$DRY_RUN" == "false" ]]; then
                rm -f "$file"
                ((cleaned_count++))
            else
                log_info "[DRY RUN] 将删除: $file"
                ((cleaned_count++))
            fi
        fi
    done
    
    log_success "临时文件清理完成 (清理了 $cleaned_count 个项目)"
}

# 清理旧日志文件
cleanup_old_logs() {
    if [[ "$CLEANUP_OLD_LOGS" != "true" ]]; then
        log_info "跳过旧日志文件清理"
        return 0
    fi
    
    log_info "开始清理旧日志文件 (保留 $LOG_RETENTION_DAYS 天)..."
    
    if [[ ! -d "$LOG_DIR" ]]; then
        log_info "日志目录不存在: $LOG_DIR"
        return 0
    fi
    
    local cleaned_count=0
    
    # 查找超过保留期的日志文件
    local old_files
    old_files=$(find "$LOG_DIR" -type f -name "*.log" -mtime +$LOG_RETENTION_DAYS 2>/dev/null || true)
    
    if [[ -n "$old_files" ]]; then
        while IFS= read -r file; do
            if [[ "$PRESERVE_REPORTS" == "true" && "$file" == *"report"* ]]; then
                log_info "保留报告文件: $file"
                continue
            fi
            
            log_info "删除旧日志: $file"
            if [[ "$DRY_RUN" == "false" ]]; then
                rm -f "$file"
                ((cleaned_count++))
            else
                log_info "[DRY RUN] 将删除: $file"
                ((cleaned_count++))
            fi
        done <<< "$old_files"
    fi
    
    # 清理空的日志目录
    if [[ "$CLEANUP_CONFIG" == "true" && -d "$LOG_DIR" ]]; then
        if [[ -z "$(ls -A "$LOG_DIR" 2>/dev/null)" ]]; then
            log_info "删除空日志目录: $LOG_DIR"
            if [[ "$DRY_RUN" == "false" ]]; then
                rmdir "$LOG_DIR" 2>/dev/null || true
            else
                log_info "[DRY RUN] 将删除空目录: $LOG_DIR"
            fi
        fi
    fi
    
    log_success "旧日志文件清理完成 (清理了 $cleaned_count 个文件)"
}

# 清理配置文件
cleanup_config_files() {
    if [[ "$CLEANUP_CONFIG" != "true" ]]; then
        log_info "跳过配置文件清理"
        return 0
    fi
    
    log_info "开始清理配置文件..."
    
    local config_files=(
        "$CONFIG_FILE"
        "$SCRIPT_DIR/efs-migration-config.env.backup"
    )
    
    local cleaned_count=0
    
    for file in "${config_files[@]}"; do
        if [[ -f "$file" ]]; then
            log_info "删除配置文件: $file"
            if [[ "$DRY_RUN" == "false" ]]; then
                rm -f "$file"
                ((cleaned_count++))
            else
                log_info "[DRY RUN] 将删除: $file"
                ((cleaned_count++))
            fi
        fi
    done
    
    log_success "配置文件清理完成 (清理了 $cleaned_count 个文件)"
}

# 检查磁盘空间回收
check_space_recovered() {
    log_info "检查磁盘空间回收情况..."
    
    # 检查/tmp目录
    local tmp_usage
    tmp_usage=$(df /tmp | awk 'NR==2{print $5}' | sed 's/%//')
    log_info "当前/tmp目录使用率: ${tmp_usage}%"
    
    # 检查日志目录
    if [[ -d "$LOG_DIR" ]]; then
        local log_size
        log_size=$(du -sh "$LOG_DIR" 2>/dev/null | cut -f1 || echo "未知")
        log_info "日志目录大小: $log_size"
    fi
    
    # 检查当前目录
    local current_dir_size
    current_dir_size=$(du -sh "$SCRIPT_DIR" 2>/dev/null | cut -f1 || echo "未知")
    log_info "脚本目录大小: $current_dir_size"
}

# 生成清理报告
generate_cleanup_report() {
    local report_file="$LOG_DIR/cleanup-report-$(date +%Y%m%d_%H%M%S).txt"
    
    # 确保日志目录存在
    mkdir -p "$LOG_DIR"
    
    cat > "$report_file" << EOF
EFS迁移资源清理报告
=========================================

清理时间: $(date)
脚本位置: $SCRIPT_DIR

清理配置:
- 清理挂载点: $CLEANUP_MOUNTS
- 清理临时文件: $CLEANUP_TEMP_FILES
- 清理旧日志: $CLEANUP_OLD_LOGS
- 清理配置文件: $CLEANUP_CONFIG
- 保留报告: $PRESERVE_REPORTS
- 日志保留天数: $LOG_RETENTION_DAYS
- 强制清理: $FORCE_CLEANUP
- 预演模式: $DRY_RUN

清理结果:
$(if [[ "$CLEANUP_MOUNTS" == "true" ]]; then echo "✓ EFS挂载点已清理"; else echo "- 跳过EFS挂载点清理"; fi)
$(if [[ "$CLEANUP_TEMP_FILES" == "true" ]]; then echo "✓ 临时文件已清理"; else echo "- 跳过临时文件清理"; fi)
$(if [[ "$CLEANUP_OLD_LOGS" == "true" ]]; then echo "✓ 旧日志文件已清理"; else echo "- 跳过旧日志文件清理"; fi)
$(if [[ "$CLEANUP_CONFIG" == "true" ]]; then echo "✓ 配置文件已清理"; else echo "- 跳过配置文件清理"; fi)

清理完成时间: $(date)
=========================================
EOF
    
    log_success "清理报告已生成: $report_file"
}

# 用户确认
user_confirmation() {
    if [[ "$FORCE_CLEANUP" == "true" || "$DRY_RUN" == "true" ]]; then
        return 0
    fi
    
    echo ""
    echo "=========================================="
    echo "EFS迁移资源清理确认"
    echo "=========================================="
    echo "将要执行的清理操作:"
    
    if [[ "$CLEANUP_MOUNTS" == "true" ]]; then
        echo "✓ 卸载EFS文件系统"
    fi
    
    if [[ "$CLEANUP_TEMP_FILES" == "true" ]]; then
        echo "✓ 清理临时文件和状态文件"
    fi
    
    if [[ "$CLEANUP_OLD_LOGS" == "true" ]]; then
        echo "✓ 清理旧日志文件 (超过 $LOG_RETENTION_DAYS 天)"
    fi
    
    if [[ "$CLEANUP_CONFIG" == "true" ]]; then
        echo "✓ 清理配置文件"
    fi
    
    if [[ "$PRESERVE_REPORTS" == "false" ]]; then
        echo "✓ 清理验证报告"
    else
        echo "- 保留验证报告"
    fi
    
    echo "=========================================="
    echo ""
    
    read -p "确认执行清理操作? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log_info "用户取消清理操作"
        exit 0
    fi
}

# 主函数
main() {
    log_info "开始EFS迁移资源清理..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_warning "运行在预演模式，不会执行实际清理"
    fi
    
    # 加载配置
    load_config
    
    # 检查运行中的迁移进程
    check_running_migration
    
    # 用户确认
    user_confirmation
    
    # 执行清理操作
    cleanup_mounts
    cleanup_temp_files
    cleanup_old_logs
    cleanup_config_files
    
    # 检查空间回收
    check_space_recovered
    
    # 生成清理报告（除非在配置清理模式下）
    if [[ "$CLEANUP_CONFIG" != "true" ]]; then
        generate_cleanup_report
    fi
    
    log_success "EFS迁移资源清理完成！"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        echo ""
        echo "=========================================="
        echo "清理完成摘要:"
        echo "- EFS挂载点: $(if [[ "$CLEANUP_MOUNTS" == "true" ]]; then echo "已清理"; else echo "跳过"; fi)"
        echo "- 临时文件: $(if [[ "$CLEANUP_TEMP_FILES" == "true" ]]; then echo "已清理"; else echo "跳过"; fi)"
        echo "- 旧日志文件: $(if [[ "$CLEANUP_OLD_LOGS" == "true" ]]; then echo "已清理"; else echo "跳过"; fi)"
        echo "- 配置文件: $(if [[ "$CLEANUP_CONFIG" == "true" ]]; then echo "已清理"; else echo "跳过"; fi)"
        echo "=========================================="
    fi
}

# 信号处理
cleanup_on_signal() {
    log_warning "接收到中断信号，清理操作可能不完整"
    exit 130
}

trap cleanup_on_signal SIGINT SIGTERM

# 执行主函数
main "$@"