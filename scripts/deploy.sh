#!/bin/bash

# 元晖Odoo应用服务部署脚本
# 使用方法: ./scripts/deploy.sh [dev|prod] [选项]
# 
# 支持的选项：
#   --dry-run                 预览模式，不执行实际部署
#   --stacks stack1,stack2    部署指定栈（逗号分隔）
#   --group [core|apps|all]   部署栈组（core=基础设施，apps=应用服务）
#   --timeout <seconds>       设置全局超时时间（默认1800秒）
#   --parallel               启用并行部署（适用于独立栈）
#   --auto-rollback          部署失败时自动回滚
#   --logs                   显示实时部署日志

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 状态图标
ICON_INFO="ℹ️"
ICON_SUCCESS="✅"
ICON_WARNING="⚠️"
ICON_ERROR="❌"
ICON_PROGRESS="🔄"
ICON_PENDING="⏳"
ICON_STACK="📦"
ICON_TIME="⏱️"

# 全局配置
GLOBAL_TIMEOUT=1800  # 30分钟默认超时
STACK_TIMEOUT=600    # 10分钟单栈超时
ENABLE_PARALLEL=false
ENABLE_LOGS=false
AUTO_ROLLBACK=false
SELECTED_STACKS=""
STACK_GROUP="all"
ENABLE_RETRY=true
MAX_RETRIES=2

# 配置文件路径
CONFIG_FILE="deploy.config.json"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 加载配置文件
load_config() {
    local env=$1
    local config_path="$PROJECT_ROOT/$CONFIG_FILE"
    
    if [[ -f "$config_path" && -r "$config_path" ]]; then
        log_info "加载配置文件: $config_path"
        
        # 使用node读取JSON配置
        if command -v node &> /dev/null; then
            local config_data
            config_data=$(node -e "
                try {
                    const config = require('$config_path');
                    const envConfig = config['$env'] || {};
                    console.log(JSON.stringify(envConfig));
                } catch (e) {
                    console.log('{}');
                }
            " 2>/dev/null)
            
            if [[ -n "$config_data" && "$config_data" != "{}" ]]; then
                # 解析配置参数
                local timeout=$(echo "$config_data" | node -e "
                    const config = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                    console.log(config.timeout || $GLOBAL_TIMEOUT);
                " 2>/dev/null)
                
                local stack_timeout=$(echo "$config_data" | node -e "
                    const config = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                    console.log(config.stackTimeout || $STACK_TIMEOUT);
                " 2>/dev/null)
                
                local parallel=$(echo "$config_data" | node -e "
                    const config = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                    console.log(config.parallel === true ? 'true' : 'false');
                " 2>/dev/null)
                
                local logs=$(echo "$config_data" | node -e "
                    const config = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                    console.log(config.logs === true ? 'true' : 'false');
                " 2>/dev/null)
                
                local auto_rollback=$(echo "$config_data" | node -e "
                    const config = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                    console.log(config.autoRollback === true ? 'true' : 'false');
                " 2>/dev/null)
                
                local default_group=$(echo "$config_data" | node -e "
                    const config = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                    console.log(config.defaultGroup || 'all');
                " 2>/dev/null)
                
                local retry_enabled=$(echo "$config_data" | node -e "
                    const config = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                    console.log(config.retryEnabled === false ? 'false' : 'true');
                " 2>/dev/null)
                
                local max_retries=$(echo "$config_data" | node -e "
                    const config = JSON.parse(require('fs').readFileSync('/dev/stdin', 'utf8'));
                    console.log(config.maxRetries || $MAX_RETRIES);
                " 2>/dev/null)
                
                # 应用配置
                GLOBAL_TIMEOUT=${timeout}
                STACK_TIMEOUT=${stack_timeout}
                ENABLE_PARALLEL=${parallel}
                ENABLE_LOGS=${logs}
                AUTO_ROLLBACK=${auto_rollback}
                STACK_GROUP=${default_group}
                ENABLE_RETRY=${retry_enabled}
                MAX_RETRIES=${max_retries}
                
                log_success "已加载 $env 环境配置"
            fi
        fi
    else
        log_info "配置文件不存在，使用默认配置"
    fi
}

# 加载环境变量配置
load_env_config() {
    # 从环境变量读取配置
    GLOBAL_TIMEOUT=${DEPLOY_TIMEOUT:-$GLOBAL_TIMEOUT}
    STACK_TIMEOUT=${DEPLOY_STACK_TIMEOUT:-$STACK_TIMEOUT}
    ENABLE_PARALLEL=${DEPLOY_PARALLEL:-$ENABLE_PARALLEL}
    ENABLE_LOGS=${DEPLOY_LOGS:-$ENABLE_LOGS}
    AUTO_ROLLBACK=${DEPLOY_AUTO_ROLLBACK:-$AUTO_ROLLBACK}
    STACK_GROUP=${DEPLOY_GROUP:-$STACK_GROUP}
    ENABLE_RETRY=${DEPLOY_RETRY:-$ENABLE_RETRY}
    MAX_RETRIES=${DEPLOY_MAX_RETRIES:-$MAX_RETRIES}
    
    # 从npm config读取配置
    if command -v npm &> /dev/null; then
        local npm_timeout=$(npm config get yuanhui:deploy:timeout 2>/dev/null)
        local npm_parallel=$(npm config get yuanhui:deploy:parallel 2>/dev/null)
        local npm_logs=$(npm config get yuanhui:deploy:logs 2>/dev/null)
        local npm_rollback=$(npm config get yuanhui:deploy:autoRollback 2>/dev/null)
        
        if [[ "$npm_timeout" != "undefined" && -n "$npm_timeout" ]]; then
            GLOBAL_TIMEOUT=$npm_timeout
        fi
        if [[ "$npm_parallel" == "true" ]]; then
            ENABLE_PARALLEL=true
        fi
        if [[ "$npm_logs" == "true" ]]; then
            ENABLE_LOGS=true
        fi
        if [[ "$npm_rollback" == "true" ]]; then
            AUTO_ROLLBACK=true
        fi
    fi
}

# 检查并设置npm环境
setup_npm_environment() {
    # 检查是否在npm项目中运行
    if [[ -f "$PROJECT_ROOT/package.json" ]]; then
        log_info "检测到npm项目环境"
        
        # 检查依赖是否安装
        if [[ ! -d "$PROJECT_ROOT/node_modules" ]]; then
            log_info "安装npm依赖..."
            cd "$PROJECT_ROOT" && npm install
        fi
        
        # 检查是否需要构建
        if [[ ! -d "$PROJECT_ROOT/dist" || "$PROJECT_ROOT/lib" -nt "$PROJECT_ROOT/dist" ]]; then
            log_info "检测到代码变更，重新构建..."
            cd "$PROJECT_ROOT" && npm run build
        fi
    fi
}

# 显示配置来源
show_config_sources() {
    local env=$1
    
    log_info "配置来源优先级:"
    log_info "  1. CLI参数 (最高优先级)"
    log_info "  2. 环境变量 (DEPLOY_*)"
    log_info "  3. npm config (yuanhui:deploy:*)"
    log_info "  4. 配置文件 ($CONFIG_FILE)"
    log_info "  5. 默认值 (最低优先级)"
    
    # 检查配置文件是否存在
    local config_path="$PROJECT_ROOT/$CONFIG_FILE"
    if [[ -f "$config_path" ]]; then
        log_success "找到配置文件: $CONFIG_FILE"
    else
        log_warning "未找到配置文件，将使用默认配置"
        log_info "可以创建 $CONFIG_FILE 来自定义配置"
    fi
}

# 日志函数
log_info() {
    echo -e "${BLUE}${ICON_INFO}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}${ICON_SUCCESS}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}${ICON_WARNING}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}${ICON_ERROR}[ERROR]${NC} $1"
}

log_progress() {
    echo -e "${CYAN}${ICON_PROGRESS}[PROGRESS]${NC} $1"
}

log_stack() {
    echo -e "${PURPLE}${ICON_STACK}[STACK]${NC} $1"
}

log_time() {
    echo -e "${YELLOW}${ICON_TIME}[TIME]${NC} $1"
}

# 显示使用方法
show_usage() {
    cat << EOF
使用方法: $0 [环境] [选项]

环境:
  dev      部署到开发环境
  prod     部署到生产环境

选项:
  --dry-run                   预览模式，不执行实际部署
  --stacks stack1,stack2      部署指定栈（逗号分隔，不含环境后缀）
  --group [core|apps|all]     部署栈组
                              core: 基础设施栈（网络、数据库、ECS等）
                              apps: 应用服务栈（应用、监控等）
                              all:  所有栈（默认）
  --timeout <seconds>         全局超时时间（默认1800秒）
  --parallel                  启用并行部署（适用于独立栈）
  --auto-rollback            部署失败时自动回滚
  --logs                     显示实时部署日志
  -h, --help                 显示此帮助信息

栈组说明:
  core栈组包括:
    - Network: VPC、安全组、WAF
    - Ecs: ECS集群
    - ServiceConnect: 服务发现
    - AuroraDatabase: Aurora数据库
    - Redis: Redis缓存
    - RabbitMQ: 消息队列
    - LoadBalancer: 应用负载均衡器
    - Airflow: 工作流引擎
    - Security: 安全配置

  apps栈组包括:
    - Application: Odoo应用服务
    - ClaudeRelay: Claude代理服务
    - CloudFront: CDN（生产环境）
    - OpenZiti: 零信任网络（生产环境）
    - Monitoring: 监控告警

示例:
  $0 dev                                    # 部署所有栈到开发环境
  $0 prod --dry-run                         # 预览生产环境部署
  $0 dev --stacks Network,Database          # 只部署网络和数据库栈
  $0 dev --group core                       # 部署基础设施栈组
  $0 dev --parallel --logs                  # 并行部署并显示实时日志
  $0 prod --timeout 3600 --auto-rollback    # 生产部署，1小时超时，自动回滚
EOF
}

# 定义栈组和依赖关系
define_stacks() {
    local env=$1
    
    # 核心基础设施栈（按依赖顺序）
    CORE_STACKS=(
        "YuanhuiNetwork-${env}"
        "YuanhuiEcs-${env}"
        "YuanhuiServiceConnect-${env}"
        "YuanhuiAuroraDatabase-${env}"
        "YuanhuiRedis-${env}"
        "YuanhuiRabbitMQ-${env}"
        "YuanhuiLoadBalancer-${env}"
        "YuanhuiAirflow-${env}"
        "YuanhuiSecurity-${env}"
    )
    
    # 应用服务栈
    APP_STACKS=(
        "YuanhuiApplication-${env}"
        "YuanhuiTurnkey-${env}"
        "YuanhuiClaudeRelay-${env}"
        "YuanhuiMonitoring-${env}"
    )
    
    # 可选栈（根据配置启用）
    OPTIONAL_STACKS=()
    if [[ "$env" == "prod" ]]; then
        OPTIONAL_STACKS+=("YuanhuiCloudFront-${env}")
        OPTIONAL_STACKS+=("YuanhuiOpenZiti-${env}")
    fi
    
    # 所有栈
    ALL_STACKS=("${CORE_STACKS[@]}" "${APP_STACKS[@]}" "${OPTIONAL_STACKS[@]}")
    
    # 栈依赖关系映射
    declare -gA STACK_DEPENDENCIES
    STACK_DEPENDENCIES["YuanhuiNetwork-${env}"]=""
    STACK_DEPENDENCIES["YuanhuiEcs-${env}"]="YuanhuiNetwork-${env}"
    STACK_DEPENDENCIES["YuanhuiServiceConnect-${env}"]="YuanhuiEcs-${env}"
    STACK_DEPENDENCIES["YuanhuiAuroraDatabase-${env}"]="YuanhuiNetwork-${env}"
    STACK_DEPENDENCIES["YuanhuiRedis-${env}"]="YuanhuiServiceConnect-${env}"
    STACK_DEPENDENCIES["YuanhuiRabbitMQ-${env}"]="YuanhuiServiceConnect-${env}"
    STACK_DEPENDENCIES["YuanhuiLoadBalancer-${env}"]="YuanhuiNetwork-${env}"
    STACK_DEPENDENCIES["YuanhuiAirflow-${env}"]="YuanhuiAuroraDatabase-${env} YuanhuiRabbitMQ-${env} YuanhuiLoadBalancer-${env}"
    STACK_DEPENDENCIES["YuanhuiSecurity-${env}"]=""
    STACK_DEPENDENCIES["YuanhuiApplication-${env}"]="YuanhuiAuroraDatabase-${env} YuanhuiRedis-${env} YuanhuiServiceConnect-${env} YuanhuiLoadBalancer-${env}"
    STACK_DEPENDENCIES["YuanhuiTurnkey-${env}"]="YuanhuiAuroraDatabase-${env} YuanhuiServiceConnect-${env} YuanhuiLoadBalancer-${env}"
    STACK_DEPENDENCIES["YuanhuiClaudeRelay-${env}"]="YuanhuiRedis-${env} YuanhuiServiceConnect-${env} YuanhuiLoadBalancer-${env}"
    STACK_DEPENDENCIES["YuanhuiMonitoring-${env}"]="YuanhuiApplication-${env} YuanhuiTurnkey-${env} YuanhuiLoadBalancer-${env} YuanhuiRabbitMQ-${env} YuanhuiAirflow-${env}"
    
    if [[ "$env" == "prod" ]]; then
        STACK_DEPENDENCIES["YuanhuiCloudFront-${env}"]="YuanhuiApplication-${env} YuanhuiLoadBalancer-${env}"
        STACK_DEPENDENCIES["YuanhuiOpenZiti-${env}"]="YuanhuiApplication-${env} YuanhuiLoadBalancer-${env}"
    fi
}

# 获取可部署的栈列表
get_available_stacks() {
    local env=$1
    npx cdk list 2>/dev/null | grep "Yuanhui.*-${env}$" || echo ""
}

# 验证栈是否存在
validate_stacks() {
    local env=$1
    local stacks_to_validate=$2
    local available_stacks
    available_stacks=$(get_available_stacks "$env")
    
    IFS=',' read -ra STACK_ARRAY <<< "$stacks_to_validate"
    for stack_name in "${STACK_ARRAY[@]}"; do
        local full_stack_name="Yuanhui${stack_name}-${env}"
        # 使用grep来检查栈是否存在，避免换行符问题
        if ! echo "$available_stacks" | grep -q "^${full_stack_name}$"; then
            log_error "栈 '$full_stack_name' 不存在"
            log_info "可用栈列表:"
            echo "$available_stacks" | sed 's/^/  /'
            return 1
        fi
    done
    return 0
}

# 解析指定的栈
parse_selected_stacks() {
    local env=$1
    local input_stacks=$2
    local result_stacks=()
    
    if [[ -n "$input_stacks" ]]; then
        IFS=',' read -ra STACK_ARRAY <<< "$input_stacks"
        for stack_name in "${STACK_ARRAY[@]}"; do
            # 移除空格
            stack_name=$(echo "$stack_name" | xargs)
            # 添加环境后缀
            result_stacks+=("Yuanhui${stack_name}-${env}")
        done
    fi
    
    printf '%s\n' "${result_stacks[@]}"
}

# 根据栈组获取栈列表
get_stacks_by_group() {
    local env=$1
    local group=$2
    
    define_stacks "$env"
    
    case "$group" in
        "core")
            printf '%s\n' "${CORE_STACKS[@]}"
            ;;
        "apps")
            printf '%s\n' "${APP_STACKS[@]}" "${OPTIONAL_STACKS[@]}"
            ;;
        "all")
            printf '%s\n' "${ALL_STACKS[@]}"
            ;;
        *)
            log_error "未知的栈组: $group"
            return 1
            ;;
    esac
}

# 实时监控CloudFormation事件
monitor_stack_events() {
    local stack_name=$1
    local timeout=${2:-$STACK_TIMEOUT}
    local start_time=$(date +%s)
    local last_event_time=""
    
    log_progress "监控栈 $stack_name 的部署事件..."
    
    while true; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        # 检查超时
        if [[ $elapsed -gt $timeout ]]; then
            log_error "栈 $stack_name 部署超时（${timeout}秒）"
            return 1
        fi
        
        # 获取栈状态
        local stack_status
        stack_status=$(aws cloudformation describe-stacks \
            --stack-name "$stack_name" \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "NOT_FOUND")
        
        case "$stack_status" in
            *_COMPLETE)
                if [[ "$stack_status" == "CREATE_COMPLETE" || "$stack_status" == "UPDATE_COMPLETE" ]]; then
                    log_success "栈 $stack_name 部署成功 (状态: $stack_status)"
                    return 0
                else
                    log_error "栈 $stack_name 部署失败 (状态: $stack_status)"
                    return 1
                fi
                ;;
            *_FAILED | *_ROLLBACK_*)
                log_error "栈 $stack_name 部署失败 (状态: $stack_status)"
                return 1
                ;;
            *_IN_PROGRESS)
                # 继续监控
                if [[ "$ENABLE_LOGS" == "true" ]]; then
                    # 显示最新事件
                    show_recent_stack_events "$stack_name" "$last_event_time"
                    last_event_time=$(date -u +"%Y-%m-%dT%H:%M:%S.000Z")
                fi
                ;;
            "NOT_FOUND")
                log_warning "栈 $stack_name 未找到，可能是新栈"
                ;;
        esac
        
        sleep 10
    done
}

# 显示最近的栈事件
show_recent_stack_events() {
    local stack_name=$1
    local since_time=${2:-$(date -u -d '1 minute ago' +"%Y-%m-%dT%H:%M:%S.000Z")}
    
    local events
    events=$(aws cloudformation describe-stack-events \
        --stack-name "$stack_name" \
        --query "StackEvents[?Timestamp>=\`$since_time\`].[Timestamp,LogicalResourceId,ResourceType,ResourceStatus,ResourceStatusReason]" \
        --output text 2>/dev/null || echo "")
    
    if [[ -n "$events" ]]; then
        echo "$events" | while IFS=$'\t' read -r timestamp resource_id resource_type status reason; do
            local color="$NC"
            local icon="$ICON_INFO"
            
            case "$status" in
                *_COMPLETE)
                    color="$GREEN"
                    icon="$ICON_SUCCESS"
                    ;;
                *_FAILED)
                    color="$RED"
                    icon="$ICON_ERROR"
                    ;;
                *_IN_PROGRESS)
                    color="$CYAN"
                    icon="$ICON_PROGRESS"
                    ;;
            esac
            
            echo -e "  ${color}${icon}${NC} ${timestamp} | ${resource_id} | ${status} | ${reason:-}"
        done
    fi
}

# 监控ECS服务状态
monitor_ecs_services() {
    local env=$1
    local cluster_name="yuanhui-odoo-${env}"
    
    log_info "检查ECS服务状态..."
    
    local services=("yherp-${env}" "khmall-${env}" "odoo-cron-${env}" "redis-${env}" "rabbitmq-${env}" "airflow-webserver-${env}")
    
    for service in "${services[@]}"; do
        local status
        status=$(aws ecs describe-services \
            --cluster "$cluster_name" \
            --services "$service" \
            --query 'services[0].status' \
            --output text 2>/dev/null || echo "NOT_FOUND")
        
        local running_count
        running_count=$(aws ecs describe-services \
            --cluster "$cluster_name" \
            --services "$service" \
            --query 'services[0].runningCount' \
            --output text 2>/dev/null || echo "0")
        
        local desired_count
        desired_count=$(aws ecs describe-services \
            --cluster "$cluster_name" \
            --services "$service" \
            --query 'services[0].desiredCount' \
            --output text 2>/dev/null || echo "0")
        
        if [[ "$status" == "ACTIVE" && "$running_count" == "$desired_count" && "$running_count" != "0" ]]; then
            log_success "服务 $service 运行正常 ($running_count/$desired_count)"
        elif [[ "$status" == "ACTIVE" ]]; then
            log_warning "服务 $service 部分运行 ($running_count/$desired_count)"
        else
            log_warning "服务 $service 状态: $status"
        fi
    done
}

# 检查栈依赖是否满足
check_stack_dependencies() {
    local stack_name=$1
    local env=$2
    
    define_stacks "$env"
    
    local dependencies="${STACK_DEPENDENCIES[$stack_name]}"
    if [[ -z "$dependencies" ]]; then
        return 0  # 无依赖
    fi
    
    for dep in $dependencies; do
        local dep_status
        dep_status=$(aws cloudformation describe-stacks \
            --stack-name "$dep" \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "NOT_FOUND")
        
        if [[ "$dep_status" != "CREATE_COMPLETE" && "$dep_status" != "UPDATE_COMPLETE" ]]; then
            log_error "依赖栈 $dep 状态不正确: $dep_status"
            return 1
        fi
    done
    
    return 0
}

# 带超时的栈部署
deploy_stack_with_timeout() {
    local stack_name=$1
    local env=$2
    local timeout=${3:-$STACK_TIMEOUT}
    
    log_stack "开始部署栈: $stack_name"
    log_time "超时设置: ${timeout}秒"
    
    # 检查依赖
    if ! check_stack_dependencies "$stack_name" "$env"; then
        log_error "栈 $stack_name 的依赖检查失败"
        return 1
    fi
    
    # 启动部署（后台运行）
    local cdk_args="--require-approval never"
    if [[ "$env" == "prod" ]]; then
        cdk_args="--require-approval broadening"
    fi
    
    local cdk_pid
    npx cdk deploy "$stack_name" $cdk_args &
    cdk_pid=$!
    
    # 监控部署进程和CloudFormation事件
    local start_time=$(date +%s)
    
    while kill -0 $cdk_pid 2>/dev/null; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [[ $elapsed -gt $timeout ]]; then
            log_error "栈 $stack_name 部署超时，终止进程"
            kill $cdk_pid 2>/dev/null || true
            wait $cdk_pid 2>/dev/null || true
            return 1
        fi
        
        sleep 5
    done
    
    # 等待进程结束并获取退出码
    wait $cdk_pid
    local exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "栈 $stack_name 部署成功"
        return 0
    else
        log_error "栈 $stack_name 部署失败 (退出码: $exit_code)"
        return 1
    fi
}

# 拓扑排序 - 根据依赖关系排序栈
topological_sort() {
    local env=$1
    local input_stacks=("$@")
    shift
    
    define_stacks "$env"
    
    # 建立节点入度映射
    declare -A indegree
    declare -A graph
    local sorted_result=()
    local queue=()
    
    # 初始化入度和图
    for stack in "${input_stacks[@]}"; do
        indegree["$stack"]=0
        graph["$stack"]=""
    done
    
    # 计算入度
    for stack in "${input_stacks[@]}"; do
        local deps="${STACK_DEPENDENCIES[$stack]}"
        if [[ -n "$deps" ]]; then
            for dep in $deps; do
                # 只考虑在输入栈列表中的依赖
                if [[ " ${input_stacks[*]} " =~ " $dep " ]]; then
                    indegree["$stack"]=$((indegree["$stack"] + 1))
                    graph["$dep"]="${graph[$dep]} $stack"
                fi
            done
        fi
    done
    
    # 找到入度为0的节点
    for stack in "${input_stacks[@]}"; do
        if [[ ${indegree[$stack]} -eq 0 ]]; then
            queue+=("$stack")
        fi
    done
    
    # 拓扑排序
    while [[ ${#queue[@]} -gt 0 ]]; do
        local current="${queue[0]}"
        queue=("${queue[@]:1}")  # 移除第一个元素
        sorted_result+=("$current")
        
        # 处理当前节点的邻接节点
        local neighbors="${graph[$current]}"
        for neighbor in $neighbors; do
            indegree["$neighbor"]=$((indegree["$neighbor"] - 1))
            if [[ ${indegree[$neighbor]} -eq 0 ]]; then
                queue+=("$neighbor")
            fi
        done
    done
    
    # 检查是否有环
    if [[ ${#sorted_result[@]} -ne ${#input_stacks[@]} ]]; then
        log_error "检测到循环依赖，无法进行拓扑排序"
        return 1
    fi
    
    printf '%s\n' "${sorted_result[@]}"
}

# 获取栈的依赖层级
get_stack_level() {
    local stack_name=$1
    local env=$2
    
    define_stacks "$env"
    
    local deps="${STACK_DEPENDENCIES[$stack_name]}"
    if [[ -z "$deps" ]]; then
        echo 0
        return
    fi
    
    local max_level=0
    for dep in $deps; do
        local dep_level
        dep_level=$(get_stack_level "$dep" "$env")
        if [[ $dep_level -gt $max_level ]]; then
            max_level=$dep_level
        fi
    done
    
    echo $((max_level + 1))
}

# 按层级分组栈
group_stacks_by_level() {
    local env=$1
    shift
    local stacks=("$@")
    
    declare -A levels
    local max_level=0
    
    # 计算每个栈的层级
    for stack in "${stacks[@]}"; do
        local level
        level=$(get_stack_level "$stack" "$env")
        levels["$stack"]=$level
        if [[ $level -gt $max_level ]]; then
            max_level=$level
        fi
    done
    
    # 按层级输出
    for ((i=0; i<=max_level; i++)); do
        local level_stacks=()
        for stack in "${stacks[@]}"; do
            if [[ ${levels[$stack]} -eq $i ]]; then
                level_stacks+=("$stack")
            fi
        done
        if [[ ${#level_stacks[@]} -gt 0 ]]; then
            echo "LEVEL_$i:${level_stacks[*]}"
        fi
    done
}

# 并行部署栈组
deploy_stacks_parallel() {
    local level_stacks=("$@")
    local env=$2
    local pids=()
    local results=()
    
    log_info "并行部署 ${#level_stacks[@]} 个栈"
    
    # 启动并行部署
    for stack in "${level_stacks[@]}"; do
        log_stack "启动并行部署: $stack"
        (
            deploy_stack_with_timeout "$stack" "$env" "$STACK_TIMEOUT"
            echo $? > "/tmp/deploy_${stack}_result"
        ) &
        pids+=($!)
    done
    
    # 等待所有部署完成
    local all_success=true
    for i in "${!pids[@]}"; do
        local pid=${pids[$i]}
        local stack=${level_stacks[$i]}
        
        wait $pid
        local result_file="/tmp/deploy_${stack}_result"
        if [[ -f "$result_file" ]]; then
            local result=$(cat "$result_file")
            rm -f "$result_file"
            if [[ $result -eq 0 ]]; then
                log_success "并行部署成功: $stack"
            else
                log_error "并行部署失败: $stack"
                all_success=false
            fi
        else
            log_error "无法获取部署结果: $stack"
            all_success=false
        fi
    done
    
    if [[ "$all_success" == "true" ]]; then
        return 0
    else
        return 1
    fi
}

# 诊断部署失败原因
diagnose_deployment_failure() {
    local stack_name=$1
    local env=$2
    
    log_error "诊断栈 $stack_name 的部署失败原因..."
    
    # 检查CloudFormation栈状态
    local stack_status
    stack_status=$(aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --query 'Stacks[0].StackStatus' \
        --output text 2>/dev/null || echo "NOT_FOUND")
    
    case "$stack_status" in
        *_ROLLBACK_COMPLETE | *_ROLLBACK_FAILED)
            log_error "栈状态: $stack_status - 部署失败并已回滚"
            
            # 获取失败事件
            local failed_events
            failed_events=$(aws cloudformation describe-stack-events \
                --stack-name "$stack_name" \
                --query 'StackEvents[?ResourceStatus==`CREATE_FAILED` || ResourceStatus==`UPDATE_FAILED`].[ResourceType,LogicalResourceId,ResourceStatusReason]' \
                --output text 2>/dev/null || echo "")
            
            if [[ -n "$failed_events" ]]; then
                log_error "失败的资源:"
                echo "$failed_events" | while IFS=$'\t' read -r resource_type logical_id reason; do
                    echo "  - $resource_type ($logical_id): $reason"
                done
            fi
            ;;
        *_FAILED)
            log_error "栈状态: $stack_status - 部署失败"
            ;;
        "NOT_FOUND")
            log_error "栈不存在，可能是初始化失败"
            ;;
        *)
            log_warning "栈状态: $stack_status - 部署可能仍在进行中"
            ;;
    esac
    
    # 检查常见问题
    check_common_issues "$stack_name" "$env"
}

# 检查常见部署问题
check_common_issues() {
    local stack_name=$1
    local env=$2
    
    log_info "检查常见问题..."
    
    # 检查AWS权限
    if ! aws sts get-caller-identity &>/dev/null; then
        log_error "AWS凭证问题 - 请检查AWS配置"
        return
    fi
    
    # 检查区域配置
    local current_region=${AWS_DEFAULT_REGION:-$(aws configure get region)}
    if [[ -z "$current_region" ]]; then
        log_warning "未设置AWS区域"
    else
        log_info "当前AWS区域: $current_region"
    fi
    
    # 检查账户限制
    local account_id
    account_id=$(aws sts get-caller-identity --query Account --output text)
    log_info "当前AWS账户: $account_id"
    
    # 检查ECS服务限制
    if [[ "$stack_name" == *"Application"* || "$stack_name" == *"Ecs"* ]]; then
        local cluster_name="yuanhui-odoo-${env}"
        local service_count
        service_count=$(aws ecs list-services --cluster "$cluster_name" --query 'length(serviceArns)' --output text 2>/dev/null || echo "0")
        log_info "ECS集群 $cluster_name 当前服务数: $service_count"
    fi
    
    # 检查数据库配置
    if [[ "$stack_name" == *"Database"* || "$stack_name" == *"Aurora"* ]]; then
        local db_cluster_id="yuanhui-aurora-${env}"
        local db_status
        db_status=$(aws rds describe-db-clusters \
            --db-cluster-identifier "$db_cluster_id" \
            --query 'DBClusters[0].Status' \
            --output text 2>/dev/null || echo "NOT_FOUND")
        log_info "Aurora集群状态: $db_status"
    fi
}

# 智能重试部署
retry_deployment() {
    local stack_name=$1
    local env=$2
    local max_retries=${3:-2}
    local retry_delay=${4:-30}
    
    for ((i=1; i<=max_retries; i++)); do
        log_warning "尝试第 $i 次重试部署栈 $stack_name"
        sleep $retry_delay
        
        if deploy_stack_with_timeout "$stack_name" "$env" "$STACK_TIMEOUT"; then
            log_success "重试成功: $stack_name (第 $i 次尝试)"
            return 0
        else
            log_error "重试失败: $stack_name (第 $i 次尝试)"
            if [[ $i -eq $max_retries ]]; then
                log_error "已达到最大重试次数，停止重试"
                return 1
            fi
        fi
    done
    
    return 1
}

# 渐进式回滚
progressive_rollback() {
    local env=$1
    shift
    local failed_stacks=("$@")
    
    if [[ ${#failed_stacks[@]} -eq 0 ]]; then
        return 0
    fi
    
    log_warning "开始渐进式回滚..."
    
    # 按依赖关系逆序回滚
    local rollback_order=()
    for stack in "${failed_stacks[@]}"; do
        rollback_order=("$stack" "${rollback_order[@]}")
    done
    
    for stack in "${rollback_order[@]}"; do
        log_warning "回滚栈: $stack"
        
        # 检查栈是否存在
        local stack_status
        stack_status=$(aws cloudformation describe-stacks \
            --stack-name "$stack" \
            --query 'Stacks[0].StackStatus' \
            --output text 2>/dev/null || echo "NOT_FOUND")
        
        if [[ "$stack_status" != "NOT_FOUND" ]]; then
            if npx cdk destroy "$stack" --force; then
                log_success "回滚成功: $stack"
            else
                log_error "回滚失败: $stack"
            fi
        else
            log_info "栈 $stack 不存在，跳过回滚"
        fi
    done
}

# 增强的部署栈函数
deploy_stack_enhanced() {
    local stack_name=$1
    local env=$2
    local enable_retry=${3:-false}
    
    log_stack "部署栈: $stack_name"
    
    # 首次尝试
    if deploy_stack_with_timeout "$stack_name" "$env" "$STACK_TIMEOUT"; then
        return 0
    fi
    
    # 诊断失败原因
    diagnose_deployment_failure "$stack_name" "$env"
    
    # 如果启用重试
    if [[ "$enable_retry" == "true" ]]; then
        log_info "启动智能重试..."
        if retry_deployment "$stack_name" "$env"; then
            return 0
        fi
    fi
    
    return 1
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    # 检查AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI 未安装"
        exit 1
    fi
    
    # 检查CDK
    if ! command -v cdk &> /dev/null; then
        log_error "AWS CDK 未安装"
        exit 1
    fi
    
    # 检查AWS凭证
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS 凭证未配置或无效"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 设置环境变量
setup_environment() {
    local env=$1
    
    log_info "设置环境变量 (${env})..."
    
    export NODE_ENV=$env
    export CDK_DEFAULT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
    export CDK_DEFAULT_REGION=${AWS_DEFAULT_REGION:-ap-northeast-1}
    
    log_info "环境配置:"
    log_info "  NODE_ENV: $NODE_ENV"
    log_info "  CDK_DEFAULT_ACCOUNT: $CDK_DEFAULT_ACCOUNT"
    log_info "  CDK_DEFAULT_REGION: $CDK_DEFAULT_REGION"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装依赖..."
        npm install
    fi
    
    # 编译TypeScript
    log_info "编译TypeScript..."
    npm run build
    
    log_success "项目构建完成"
}

# 验证部署
validate_deployment() {
    local dry_run=$1
    
    log_info "验证部署配置..."
    
    if [ "$dry_run" = true ]; then
        log_info "执行干运行模式..."
        npx cdk diff
    else
        log_info "验证CloudFormation模板..."
        npx cdk synth --quiet > /dev/null
    fi
    
    log_success "部署配置验证通过"
}

# 执行部署
deploy_stacks() {
    local env=$1
    local dry_run=$2
    local stacks_to_deploy=()
    
    if [ "$dry_run" = true ]; then
        log_info "干运行模式，显示部署计划..."
        npx cdk diff
        return 0
    fi
    
    # 确定要部署的栈
    if [[ -n "$SELECTED_STACKS" ]]; then
        log_info "部署指定栈: $SELECTED_STACKS"
        mapfile -t stacks_to_deploy < <(parse_selected_stacks "$env" "$SELECTED_STACKS")
    else
        log_info "部署栈组: $STACK_GROUP"
        mapfile -t stacks_to_deploy < <(get_stacks_by_group "$env" "$STACK_GROUP")
    fi
    
    if [[ ${#stacks_to_deploy[@]} -eq 0 ]]; then
        log_error "没有找到要部署的栈"
        return 1
    fi
    
    log_info "将要部署的栈:"
    for stack in "${stacks_to_deploy[@]}"; do
        echo "  - $stack"
    done
    
    log_info "开始部署到 ${env} 环境..."
    log_time "全局超时: ${GLOBAL_TIMEOUT}秒，单栈超时: ${STACK_TIMEOUT}秒"
    
    local failed_stacks=()
    local successful_stacks=()
    local total_start_time=$(date +%s)
    
    if [[ "$ENABLE_PARALLEL" == "true" ]]; then
        log_info "使用并行部署模式"
        
        # 按层级分组栈
        local level_groups
        level_groups=$(group_stacks_by_level "$env" "${stacks_to_deploy[@]}")
        
        # 逐层部署
        while IFS= read -r level_line; do
            if [[ -n "$level_line" ]]; then
                local level="${level_line%%:*}"
                local level_stacks_str="${level_line#*:}"
                local level_stacks=($level_stacks_str)
                
                log_info "部署层级 $level (${#level_stacks[@]} 个栈)"
                
                # 检查全局超时
                local current_time=$(date +%s)
                local total_elapsed=$((current_time - total_start_time))
                if [[ $total_elapsed -gt $GLOBAL_TIMEOUT ]]; then
                    log_error "全局部署超时（${GLOBAL_TIMEOUT}秒），停止部署"
                    break
                fi
                
                if [[ ${#level_stacks[@]} -eq 1 ]]; then
                    # 单个栈，直接部署
                    local stack="${level_stacks[0]}"
                    log_stack "部署栈: $stack"
                    if deploy_stack_with_timeout "$stack" "$env" "$STACK_TIMEOUT"; then
                        successful_stacks+=("$stack")
                    else
                        failed_stacks+=("$stack")
                        break
                    fi
                else
                    # 多个栈，并行部署
                    if deploy_stacks_parallel "${level_stacks[@]}" "$env"; then
                        successful_stacks+=("${level_stacks[@]}")
                    else
                        failed_stacks+=("${level_stacks[@]}")
                        break
                    fi
                fi
            fi
        done <<< "$level_groups"
    else
        log_info "使用顺序部署模式"
        
        # 拓扑排序栈
        local sorted_stacks
        if sorted_stacks=$(topological_sort "$env" "${stacks_to_deploy[@]}"); then
            log_info "栈部署顺序:"
            echo "$sorted_stacks" | sed 's/^/  - /'
            
            # 顺序部署栈
            while IFS= read -r stack; do
                if [[ -n "$stack" ]]; then
                    local current_time=$(date +%s)
                    local total_elapsed=$((current_time - total_start_time))
                    
                    if [[ $total_elapsed -gt $GLOBAL_TIMEOUT ]]; then
                        log_error "全局部署超时（${GLOBAL_TIMEOUT}秒），停止后续部署"
                        break
                    fi
                    
                    log_info "=" 
                    log_stack "部署栈: $stack"
                    
                    if deploy_stack_with_timeout "$stack" "$env" "$STACK_TIMEOUT"; then
                        successful_stacks+=("$stack")
                        
                        # 如果启用日志监控，显示相关服务状态
                        if [[ "$ENABLE_LOGS" == "true" && "$stack" == *"Application"* ]]; then
                            monitor_ecs_services "$env"
                        fi
                    else
                        failed_stacks+=("$stack")
                        
                        if [[ "$AUTO_ROLLBACK" == "true" ]]; then
                            log_warning "启用自动回滚，尝试回滚栈 $stack"
                            npx cdk destroy "$stack" --force || log_warning "回滚失败"
                        fi
                        
                        log_error "栈 $stack 部署失败，停止后续部署"
                        break
                    fi
                fi
            done <<< "$sorted_stacks"
        else
            log_error "栈排序失败，无法继续部署"
            return 1
        fi
    fi
    
    # 部署总结
    local total_end_time=$(date +%s)
    local total_duration=$((total_end_time - total_start_time))
    
    echo
    log_info "部署总结:"
    log_time "总耗时: ${total_duration}秒"
    
    if [[ ${#successful_stacks[@]} -gt 0 ]]; then
        log_success "成功部署的栈 (${#successful_stacks[@]}):"
        for stack in "${successful_stacks[@]}"; do
            echo "  ✅ $stack"
        done
    fi
    
    if [[ ${#failed_stacks[@]} -gt 0 ]]; then
        log_error "部署失败的栈 (${#failed_stacks[@]}):"
        for stack in "${failed_stacks[@]}"; do
            echo "  ❌ $stack"
        done
        return 1
    fi
    
    log_success "所有栈部署完成"
    return 0
}

# 部署后验证
post_deployment_check() {
    local env=$1
    
    log_info "执行部署后验证..."
    
    # 检查ECS集群状态
    log_info "检查ECS集群状态..."
    local cluster_name="yuanhui-odoo-${env}"
    local cluster_status
    cluster_status=$(aws ecs describe-clusters \
        --clusters "$cluster_name" \
        --query 'clusters[0].status' \
        --output text 2>/dev/null || echo "NOT_FOUND")

    if [[ "$cluster_status" == "ACTIVE" ]]; then
        log_success "ECS集群状态正常"
        
        # 监控关键服务状态
        monitor_ecs_services "$env"
    else
        log_warning "ECS集群状态: $cluster_status"
    fi
    
    # 检查Aurora数据库状态
    log_info "检查Aurora数据库状态..."
    local db_cluster_id="yuanhui-aurora-${env}"
    local db_status
    db_status=$(aws rds describe-db-clusters \
        --db-cluster-identifier "$db_cluster_id" \
        --query 'DBClusters[0].Status' \
        --output text 2>/dev/null || echo "NOT_FOUND")
    
    if [[ "$db_status" == "available" ]]; then
        log_success "Aurora数据库状态正常"
    else
        log_warning "Aurora数据库状态: $db_status"
    fi
    
    # 检查ALB状态
    log_info "检查应用负载均衡器状态..."
    local alb_name="yuanhui-public-alb-${env}"
    local alb_status
    alb_status=$(aws elbv2 describe-load-balancers \
        --names "$alb_name" \
        --query 'LoadBalancers[0].State.Code' \
        --output text 2>/dev/null || echo "NOT_FOUND")
    
    if [[ "$alb_status" == "active" ]]; then
        log_success "应用负载均衡器状态正常"
    else
        log_warning "应用负载均衡器状态: $alb_status"
    fi
    
    log_success "部署后验证完成"
}

# 主函数
main() {
    local env=""
    local dry_run=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            dev|prod)
                env="$1"
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --stacks)
                SELECTED_STACKS="$2"
                shift 2
                ;;
            --group)
                CLI_STACK_GROUP="$2"
                shift 2
                ;;
            --timeout)
                CLI_TIMEOUT="$2"
                shift 2
                ;;
            --parallel)
                CLI_PARALLEL=true
                shift
                ;;
            --logs)
                CLI_LOGS=true
                shift
                ;;
            --auto-rollback)
                CLI_AUTO_ROLLBACK=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # 验证参数
    if [[ -z "$env" ]]; then
        log_error "请指定环境 (dev 或 prod)"
        show_usage
        exit 1
    fi
    
    # 加载配置（按优先级顺序）
    load_config "$env"          # 配置文件
    load_env_config             # 环境变量和npm config
    
    # CLI参数覆盖配置文件和环境变量
    if [[ -n "${CLI_TIMEOUT:-}" ]]; then
        GLOBAL_TIMEOUT="$CLI_TIMEOUT"
        STACK_TIMEOUT=$((GLOBAL_TIMEOUT / 3))
    fi
    if [[ -n "${CLI_PARALLEL:-}" ]]; then
        ENABLE_PARALLEL="$CLI_PARALLEL"
    fi
    if [[ -n "${CLI_LOGS:-}" ]]; then
        ENABLE_LOGS="$CLI_LOGS"
    fi
    if [[ -n "${CLI_AUTO_ROLLBACK:-}" ]]; then
        AUTO_ROLLBACK="$CLI_AUTO_ROLLBACK"
    fi
    if [[ -n "${CLI_STACK_GROUP:-}" ]]; then
        STACK_GROUP="$CLI_STACK_GROUP"
    fi
    
    # 验证配置
    if [[ "$STACK_GROUP" != "core" && "$STACK_GROUP" != "apps" && "$STACK_GROUP" != "all" ]]; then
        log_error "无效的栈组: $STACK_GROUP (支持: core, apps, all)"
        exit 1
    fi
    
    # 验证指定的栈（如果有）
    if [[ -n "$SELECTED_STACKS" ]]; then
        if ! validate_stacks "$env" "$SELECTED_STACKS"; then
            exit 1
        fi
        STACK_GROUP=""  # 清空栈组，使用指定栈
    fi
    
    # 显示配置信息
    log_info "部署配置:"
    log_info "  环境: $env"
    log_info "  模式: $([ "$dry_run" = true ] && echo "预览" || echo "部署")"
    if [[ -n "$SELECTED_STACKS" ]]; then
        log_info "  指定栈: $SELECTED_STACKS"
    else
        log_info "  栈组: $STACK_GROUP"
    fi
    log_info "  全局超时: ${GLOBAL_TIMEOUT}秒"
    log_info "  单栈超时: ${STACK_TIMEOUT}秒"
    log_info "  并行部署: $([ "$ENABLE_PARALLEL" = true ] && echo "启用" || echo "禁用")"
    log_info "  实时日志: $([ "$ENABLE_LOGS" = true ] && echo "启用" || echo "禁用")"
    log_info "  自动回滚: $([ "$AUTO_ROLLBACK" = true ] && echo "启用" || echo "禁用")"
    
    # 生产环境额外确认
    if [[ "$env" == "prod" && "$dry_run" == false ]]; then
        log_warning "即将部署到生产环境！"
        echo -n "确认继续? (yes/no): "
        read -r confirm
        if [[ "$confirm" != "yes" ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
    
    log_info "开始部署元晖Odoo应用服务到 ${env} 环境"
    
    # 设置npm环境
    setup_npm_environment
    
    # 执行部署流程
    check_prerequisites
    setup_environment "$env"
    build_project
    validate_deployment "$dry_run"
    deploy_stacks "$env" "$dry_run"
    
    if [[ "$dry_run" == false ]]; then
        post_deployment_check "$env"
        log_success "部署完成！"
        log_info "请查看CloudWatch仪表板监控系统状态"
        
        # 显示相关链接
        if [[ "$env" == "prod" ]]; then
            log_info "相关链接:"
            log_info "  - Yherp生产环境: https://yh.kh2u.com"
            log_info "  - Yherp公共访问: https://dp.kh2u.com" 
            log_info "  - J2Mall商城: https://j2mall.com"
        else
            log_info "相关链接:"
            log_info "  - Yherp开发环境: https://yh-dev.kh2u.com"
            log_info "  - Yherp公共访问: https://dp-dev.kh2u.com"
            log_info "  - J2Mall商城: https://j2mall.tw"
        fi
    else
        log_info "预览完成"
    fi
}

# 执行主函数
main "$@"
