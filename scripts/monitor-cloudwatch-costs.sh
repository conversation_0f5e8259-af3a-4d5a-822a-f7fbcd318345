#!/bin/bash

# CloudWatch成本监控脚本
# 用于检查和分析CloudWatch相关费用

# 移除 set -e 以避免脚本在某些AWS命令失败时退出
# set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
REGION=${AWS_DEFAULT_REGION:-ap-east-2}
DAYS_BACK=${1:-7}

echo -e "${BLUE}=== CloudWatch成本监控报告 ===${NC}"
echo "区域: $REGION"
echo "时间范围: 最近 $DAYS_BACK 天"
echo "生成时间: $(date)"
echo ""

# 检查AWS CLI配置
echo "检查AWS CLI配置..."
if ! command -v aws >/dev/null 2>&1; then
    echo -e "${RED}错误: AWS CLI未安装${NC}"
    exit 1
fi

if ! aws sts get-caller-identity >/dev/null 2>&1; then
    echo -e "${RED}错误: AWS CLI未正确配置或无权限${NC}"
    echo "请检查:"
    echo "1. AWS凭证是否配置 (aws configure)"
    echo "2. 区域是否正确设置"
    echo "3. 用户是否有必要权限"
    exit 1
fi

echo -e "${GREEN}AWS CLI配置正常${NC}"

# 1. 检查ECS集群的Container Insights状态
echo -e "${BLUE}1. ECS Container Insights状态${NC}"
echo "----------------------------------------"

echo "正在检查ECS集群..."
clusters=$(aws ecs list-clusters --region $REGION --query 'clusterArns[]' --output text 2>/dev/null)

if [ $? -ne 0 ] || [ -z "$clusters" ]; then
    echo -e "${YELLOW}未找到ECS集群或无权限访问${NC}"
else
    for cluster_arn in $clusters; do
        if [ -z "$cluster_arn" ] || [ "$cluster_arn" = "None" ]; then
            continue
        fi

        cluster_name=$(basename "$cluster_arn")
        echo "集群: $cluster_name"

        # 检查Container Insights设置
        insights=$(aws ecs describe-clusters --region $REGION --clusters "$cluster_arn" \
            --query 'clusters[0].settings[?name==`containerInsights`].value' --output text 2>/dev/null)

        if [ $? -ne 0 ]; then
            echo -e "  ${RED}无法获取Container Insights状态${NC}"
            continue
        fi

        if [ "$insights" = "enabled" ]; then
            echo -e "  Container Insights: ${YELLOW}启用${NC} (产生费用)"
        elif [ "$insights" = "disabled" ]; then
            echo -e "  Container Insights: ${GREEN}禁用${NC} (无费用)"
        else
            echo -e "  Container Insights: ${GREEN}未配置/禁用${NC} (无费用)"
        fi

        # 检查服务数量
        services=$(aws ecs list-services --region $REGION --cluster "$cluster_arn" \
            --query 'length(serviceArns)' --output text 2>/dev/null || echo "0")
        echo "  服务数量: $services"
        echo ""
    done
fi

# 2. 检查CloudWatch自定义指标
echo -e "${BLUE}2. CloudWatch自定义指标统计${NC}"
echo "----------------------------------------"

# 获取最近的指标统计
# 修复macOS date命令兼容性问题
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    start_time=$(date -u -v-${DAYS_BACK}d +%Y-%m-%dT%H:%M:%S)
    end_time=$(date -u +%Y-%m-%dT%H:%M:%S)
else
    # Linux
    start_time=$(date -u -d "$DAYS_BACK days ago" +%Y-%m-%dT%H:%M:%S)
    end_time=$(date -u +%Y-%m-%dT%H:%M:%S)
fi

# 检查指标使用量
echo "检查时间范围: $start_time 到 $end_time"

# 获取指标命名空间
echo "正在检查CloudWatch指标..."
namespaces=$(aws cloudwatch list-metrics --region $REGION \
    --query 'Metrics[].Namespace' --output text 2>/dev/null)

if [ $? -ne 0 ]; then
    echo -e "${RED}无法获取CloudWatch指标，请检查权限${NC}"
elif [ -n "$namespaces" ]; then
    # 处理命名空间，去重并限制数量
    unique_namespaces=$(echo "$namespaces" | tr '\t' '\n' | sort -u | head -20)
    echo "发现的指标命名空间:"

    for ns in $unique_namespaces; do
        if [ -z "$ns" ] || [ "$ns" = "None" ]; then
            continue
        fi

        count=$(aws cloudwatch list-metrics --region $REGION --namespace "$ns" \
            --query 'length(Metrics)' --output text 2>/dev/null)

        if [ $? -eq 0 ] && [ "$count" -gt 0 ] 2>/dev/null; then
            if [[ "$ns" == *"ECS"* ]] || [[ "$ns" == *"ContainerInsights"* ]]; then
                echo -e "  ${YELLOW}$ns${NC}: $count 个指标 (ECS相关)"
            elif [[ "$ns" == AWS/* ]]; then
                echo -e "  ${GREEN}$ns${NC}: $count 个指标 (AWS标准)"
            else
                echo -e "  ${RED}$ns${NC}: $count 个指标 (自定义 - 收费)"
            fi
        fi
    done
else
    echo "未找到指标"
fi

echo ""

# 3. 检查CloudWatch日志组
echo -e "${BLUE}3. CloudWatch日志组分析${NC}"
echo "----------------------------------------"

echo "正在检查CloudWatch日志组..."
log_groups=$(aws logs describe-log-groups --region $REGION \
    --query 'logGroups[?contains(logGroupName, `ecs`) || contains(logGroupName, `yuanhui`)].{Name:logGroupName,Retention:retentionInDays,Size:storedBytes}' \
    --output table 2>/dev/null)

if [ $? -ne 0 ]; then
    echo -e "${RED}无法获取日志组信息，请检查权限${NC}"
elif [ -n "$log_groups" ] && [ "$log_groups" != "None" ]; then
    echo "$log_groups"
else
    echo "未找到相关日志组"
fi

echo ""

# 4. 成本估算
echo -e "${BLUE}4. 成本估算和建议${NC}"
echo "----------------------------------------"

echo "基于当前配置的月度成本估算:"
echo ""

# Container Insights成本估算
if [ -n "$clusters" ]; then
    for cluster_arn in $clusters; do
        cluster_name=$(basename $cluster_arn)
        insights=$(aws ecs describe-clusters --region $REGION --clusters $cluster_arn \
            --query 'clusters[0].settings[?name==`containerInsights`].value' --output text 2>/dev/null || echo "disabled")
        
        if [ "$insights" = "enabled" ]; then
            services=$(aws ecs list-services --region $REGION --cluster $cluster_arn \
                --query 'length(serviceArns)' --output text 2>/dev/null || echo "0")
            
            # 估算Container Insights费用 (每个服务约$10-15/月)
            cost_estimate=$((services * 12))
            echo -e "  ${cluster_name}: ${RED}\$${cost_estimate}/月${NC} (Container Insights)"
        else
            echo -e "  ${cluster_name}: ${GREEN}\$0/月${NC} (Container Insights已禁用)"
        fi
    done
fi

echo ""
echo -e "${YELLOW}成本优化建议:${NC}"
echo "1. 开发环境关闭Container Insights (节省 ~\$50/月)"
echo "2. 减少日志保留天数 (开发环境3天足够)"
echo "3. 限制自定义指标数量"
echo "4. 定期清理未使用的告警"
echo "5. 使用基础告警而非复合告警"

echo ""
echo -e "${BLUE}5. 立即行动项${NC}"
echo "----------------------------------------"
echo "要立即降低成本，请执行:"
echo "1. 更新CDK配置: enableDetailedMonitoring: false"
echo "2. 重新部署ECS栈"
echo "3. 验证Container Insights已禁用"

echo ""
echo -e "${GREEN}监控报告完成${NC}"
