#!/bin/bash

# =============================================================================
# 电子发票证书管理工具
# Certificate Management Tool for E-Invoice Turnkey System
# =============================================================================
# 
# 用途：管理存储在AWS SSM Parameter Store中的电子发票证书
# 功能：上传、下载、列表、验证、备份证书JSON文件
# 
# 使用方法：
#   ./manage-turnkey-certificates.sh <command> [options]
#
# 作者：Yuan Hui Infrastructure Team
# 版本：1.0.0
# 日期：2024-08-07
# =============================================================================

set -euo pipefail

# =============================================================================
# 全局变量和配置
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
EXAMPLES_DIR="${SCRIPT_DIR}/examples"
BACKUP_DIR="${SCRIPT_DIR}/backups"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 默认值
ENVIRONMENT=""
DRY_RUN=false
FORCE=false
VERBOSE=false
LOG_FILE=""

# SSM参数路径模板
SSM_PARAMETER_PREFIX="/yuanhui/einvoice-turnkey"

# =============================================================================
# 工具函数
# =============================================================================

log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" >&2
            [[ -n "$LOG_FILE" ]] && echo "[ERROR] ${timestamp} - $message" >> "$LOG_FILE"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message" >&2
            [[ -n "$LOG_FILE" ]] && echo "[WARN] ${timestamp} - $message" >> "$LOG_FILE"
            ;;
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message"
            [[ -n "$LOG_FILE" ]] && echo "[INFO] ${timestamp} - $message" >> "$LOG_FILE"
            ;;
        "DEBUG")
            if [[ "$VERBOSE" == "true" ]]; then
                echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message"
                [[ -n "$LOG_FILE" ]] && echo "[DEBUG] ${timestamp} - $message" >> "$LOG_FILE"
            fi
            ;;
    esac
}

error_exit() {
    log "ERROR" "$1"
    exit 1
}

check_dependencies() {
    local deps=("aws" "jq" "base64")
    local missing_deps=()
    
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" >/dev/null 2>&1; then
            missing_deps+=("$dep")
        fi
    done
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        error_exit "缺少必要依赖：${missing_deps[*]}。请安装后重试。"
    fi
}

check_aws_credentials() {
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        error_exit "AWS凭证配置错误。请运行 'aws configure' 或设置环境变量。"
    fi
}

validate_environment() {
    if [[ -z "$ENVIRONMENT" ]]; then
        error_exit "必须指定环境参数 -e dev|prod"
    fi
    
    if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
        error_exit "环境参数只能是 'dev' 或 'prod'"
    fi
}

get_ssm_parameter_name() {
    echo "${SSM_PARAMETER_PREFIX}/${ENVIRONMENT}/certificates"
}

create_backup_dir() {
    mkdir -p "$BACKUP_DIR"
}

# =============================================================================
# JSON验证函数
# =============================================================================

validate_json_format() {
    local json_file=$1
    
    if [[ ! -f "$json_file" ]]; then
        error_exit "文件不存在：$json_file"
    fi
    
    log "DEBUG" "验证JSON格式：$json_file"
    
    # 基本JSON格式验证
    if ! jq . "$json_file" >/dev/null 2>&1; then
        error_exit "JSON格式错误：$json_file"
    fi
    
    # 验证必要的顶层结构
    local certificates_count=$(jq -r '.certificates | length // 0' "$json_file")
    if [[ "$certificates_count" == "0" ]]; then
        error_exit "证书数组为空或不存在"
    fi
    
    log "INFO" "发现 $certificates_count 个证书"
    
    # 验证每个证书的必要字段
    local cert_index=0
    while [[ $cert_index -lt $certificates_count ]]; do
        log "DEBUG" "验证证书 #$((cert_index + 1))"
        
        # 检查必要字段
        local required_fields=("pfx_base64" "pfx_password" "sign_id" "sign_type")
        for field in "${required_fields[@]}"; do
            local field_value=$(jq -r ".certificates[$cert_index].$field // empty" "$json_file")
            if [[ -z "$field_value" || "$field_value" == "null" ]]; then
                error_exit "证书 #$((cert_index + 1)) 缺少必要字段：$field"
            fi
        done
        
        # 验证base64格式
        local pfx_base64=$(jq -r ".certificates[$cert_index].pfx_base64" "$json_file")
        if ! echo "$pfx_base64" | base64 -d >/dev/null 2>&1; then
            error_exit "证书 #$((cert_index + 1)) 的pfx_base64不是有效的base64格式"
        fi
        
        # 验证sign_type
        local sign_type=$(jq -r ".certificates[$cert_index].sign_type" "$json_file")
        if [[ "$sign_type" != "corporate" && "$sign_type" != "branch" && "$sign_type" != "individual" ]]; then
            log "WARN" "证书 #$((cert_index + 1)) 的sign_type值不标准：$sign_type"
        fi
        
        cert_index=$((cert_index + 1))
    done
    
    # 检查证书数量限制（最多10个）
    if [[ $certificates_count -gt 10 ]]; then
        error_exit "证书数量超过限制（最多10个），当前：$certificates_count"
    fi
    
    log "INFO" "JSON格式验证通过"
    return 0
}

validate_certificate_expiry() {
    local json_file=$1
    local current_date=$(date +%Y-%m-%d)
    local warn_days=30
    local warn_timestamp=$(date -d "+${warn_days} days" +%Y-%m-%d)
    
    local certificates_count=$(jq -r '.certificates | length' "$json_file")
    local cert_index=0
    
    while [[ $cert_index -lt $certificates_count ]]; do
        local expiry_date=$(jq -r ".certificates[$cert_index].expiry_date // empty" "$json_file")
        local sign_id=$(jq -r ".certificates[$cert_index].sign_id" "$json_file")
        
        if [[ -n "$expiry_date" && "$expiry_date" != "null" ]]; then
            if [[ "$expiry_date" < "$current_date" ]]; then
                log "WARN" "证书已过期：$sign_id (过期日期: $expiry_date)"
            elif [[ "$expiry_date" < "$warn_timestamp" ]]; then
                log "WARN" "证书即将过期：$sign_id (过期日期: $expiry_date)"
            fi
        fi
        
        cert_index=$((cert_index + 1))
    done
}

# =============================================================================
# 核心功能函数
# =============================================================================

upload_certificates() {
    local json_file=$1
    local parameter_name=$(get_ssm_parameter_name)
    
    log "INFO" "开始上传证书到SSM Parameter Store"
    log "INFO" "环境：$ENVIRONMENT"
    log "INFO" "参数名称：$parameter_name"
    log "INFO" "源文件：$json_file"
    
    # 验证JSON文件
    validate_json_format "$json_file"
    validate_certificate_expiry "$json_file"
    
    # 检查现有参数
    local existing_param=""
    if aws ssm get-parameter --name "$parameter_name" >/dev/null 2>&1; then
        existing_param="存在"
        if [[ "$FORCE" == "false" ]]; then
            log "WARN" "参数已存在，使用 --force 参数强制覆盖"
            read -p "确认覆盖现有证书? (y/N): " -r
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log "INFO" "操作已取消"
                return 0
            fi
        fi
    else
        existing_param="不存在"
    fi
    
    # 更新元数据
    local updated_json=$(jq ".metadata.last_updated = \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\" | .metadata.environment = \"$ENVIRONMENT\"" "$json_file")
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将要执行的操作："
        log "INFO" "[DRY RUN] - 参数名称：$parameter_name"
        log "INFO" "[DRY RUN] - 现有参数：$existing_param"
        log "INFO" "[DRY RUN] - 证书数量：$(echo "$updated_json" | jq -r '.certificates | length')"
        return 0
    fi
    
    # 上传到SSM
    if echo "$updated_json" | aws ssm put-parameter \
        --name "$parameter_name" \
        --value file:///dev/stdin \
        --type "SecureString" \
        --overwrite >/dev/null; then
        log "INFO" "证书上传成功"
        log "INFO" "参数名称：$parameter_name"
        log "INFO" "证书数量：$(echo "$updated_json" | jq -r '.certificates | length')"
    else
        error_exit "证书上传失败"
    fi
}

download_certificates() {
    local output_file=$1
    local parameter_name=$(get_ssm_parameter_name)
    
    log "INFO" "开始从SSM Parameter Store下载证书"
    log "INFO" "环境：$ENVIRONMENT"
    log "INFO" "参数名称：$parameter_name"
    log "INFO" "输出文件：$output_file"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将要执行的操作："
        log "INFO" "[DRY RUN] - 从参数下载：$parameter_name"
        log "INFO" "[DRY RUN] - 保存到文件：$output_file"
        return 0
    fi
    
    # 检查参数是否存在
    if ! aws ssm get-parameter --name "$parameter_name" >/dev/null 2>&1; then
        error_exit "参数不存在：$parameter_name"
    fi
    
    # 检查输出文件是否存在
    if [[ -f "$output_file" && "$FORCE" == "false" ]]; then
        log "WARN" "输出文件已存在，使用 --force 参数强制覆盖"
        read -p "确认覆盖现有文件? (y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "INFO" "操作已取消"
            return 0
        fi
    fi
    
    # 创建输出目录
    local output_dir=$(dirname "$output_file")
    mkdir -p "$output_dir"
    
    # 下载证书
    if aws ssm get-parameter --name "$parameter_name" --with-decryption --query 'Parameter.Value' --output text | jq . > "$output_file"; then
        log "INFO" "证书下载成功"
        local cert_count=$(jq -r '.certificates | length' "$output_file")
        log "INFO" "证书数量：$cert_count"
        log "INFO" "保存位置：$output_file"
        
        # 验证下载的文件
        validate_json_format "$output_file"
    else
        error_exit "证书下载失败"
    fi
}

list_certificates() {
    local parameter_name=$(get_ssm_parameter_name)
    
    log "INFO" "列出证书信息"
    log "INFO" "环境：$ENVIRONMENT"
    log "INFO" "参数名称：$parameter_name"
    
    # 检查参数是否存在
    if ! aws ssm get-parameter --name "$parameter_name" >/dev/null 2>&1; then
        log "WARN" "未找到证书参数：$parameter_name"
        return 0
    fi
    
    # 获取证书数据
    local cert_data=$(aws ssm get-parameter --name "$parameter_name" --with-decryption --query 'Parameter.Value' --output text)
    
    if [[ -z "$cert_data" ]]; then
        log "WARN" "证书参数为空"
        return 0
    fi
    
    # 显示汇总信息
    echo
    echo -e "${CYAN}=== 证书汇总信息 ===${NC}"
    echo "环境: $ENVIRONMENT"
    echo "参数名称: $parameter_name"
    echo "总证书数量: $(echo "$cert_data" | jq -r '.certificates | length')"
    echo "最后更新: $(echo "$cert_data" | jq -r '.metadata.last_updated // "未知"')"
    echo
    
    # 显示证书列表
    echo -e "${CYAN}=== 证书详情 ===${NC}"
    printf "%-3s %-20s %-12s %-15s %-12s %-30s\n" "ID" "签名ID" "类型" "发行日期" "过期日期" "描述"
    echo "$(printf '%*s' 92 '' | tr ' ' '-')"
    
    local cert_count=$(echo "$cert_data" | jq -r '.certificates | length')
    local cert_index=0
    local current_date=$(date +%Y-%m-%d)
    
    while [[ $cert_index -lt $cert_count ]]; do
        local sign_id=$(echo "$cert_data" | jq -r ".certificates[$cert_index].sign_id")
        local sign_type=$(echo "$cert_data" | jq -r ".certificates[$cert_index].sign_type // \"未知\"")
        local issued_date=$(echo "$cert_data" | jq -r ".certificates[$cert_index].issued_date // \"未知\"")
        local expiry_date=$(echo "$cert_data" | jq -r ".certificates[$cert_index].expiry_date // \"未知\"")
        local description=$(echo "$cert_data" | jq -r ".certificates[$cert_index].description // \"无描述\"")
        
        # 检查证书状态
        local status_color="${NC}"
        if [[ "$expiry_date" != "未知" && "$expiry_date" < "$current_date" ]]; then
            status_color="${RED}"
        elif [[ "$expiry_date" != "未知" && "$expiry_date" < "$(date -d '+30 days' +%Y-%m-%d)" ]]; then
            status_color="${YELLOW}"
        fi
        
        printf "${status_color}%-3d %-20s %-12s %-15s %-12s %-30s${NC}\n" \
            $((cert_index + 1)) \
            "$(echo "$sign_id" | cut -c1-20)" \
            "$sign_type" \
            "$issued_date" \
            "$expiry_date" \
            "$(echo "$description" | cut -c1-30)"
        
        cert_index=$((cert_index + 1))
    done
    
    echo
    echo -e "${YELLOW}注意：${NC}"
    echo -e "  ${RED}红色${NC} = 已过期"
    echo -e "  ${YELLOW}黄色${NC} = 30天内过期"
}

validate_certificates() {
    local json_file=${1:-}
    local parameter_name=$(get_ssm_parameter_name)
    
    if [[ -n "$json_file" ]]; then
        log "INFO" "验证本地证书文件：$json_file"
        validate_json_format "$json_file"
        validate_certificate_expiry "$json_file"
        log "INFO" "本地证书文件验证通过"
    else
        log "INFO" "验证SSM中的证书"
        log "INFO" "参数名称：$parameter_name"
        
        if ! aws ssm get-parameter --name "$parameter_name" >/dev/null 2>&1; then
            log "WARN" "未找到证书参数：$parameter_name"
            return 0
        fi
        
        # 下载到临时文件进行验证
        local temp_file=$(mktemp)
        trap "rm -f $temp_file" EXIT
        
        aws ssm get-parameter --name "$parameter_name" --with-decryption --query 'Parameter.Value' --output text | jq . > "$temp_file"
        
        validate_json_format "$temp_file"
        validate_certificate_expiry "$temp_file"
        log "INFO" "SSM中的证书验证通过"
    fi
}

backup_certificates() {
    local backup_name=${1:-"certificates_$(date +%Y%m%d_%H%M%S)"}
    local parameter_name=$(get_ssm_parameter_name)
    
    create_backup_dir
    local backup_file="${BACKUP_DIR}/${backup_name}_${ENVIRONMENT}.json"
    
    log "INFO" "备份证书到本地"
    log "INFO" "环境：$ENVIRONMENT"
    log "INFO" "参数名称：$parameter_name"
    log "INFO" "备份文件：$backup_file"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log "INFO" "[DRY RUN] 将要执行的备份操作"
        return 0
    fi
    
    # 检查参数是否存在
    if ! aws ssm get-parameter --name "$parameter_name" >/dev/null 2>&1; then
        error_exit "参数不存在：$parameter_name"
    fi
    
    # 下载证书到备份文件
    if aws ssm get-parameter --name "$parameter_name" --with-decryption --query 'Parameter.Value' --output text | jq . > "$backup_file"; then
        log "INFO" "证书备份成功"
        log "INFO" "备份位置：$backup_file"
        local cert_count=$(jq -r '.certificates | length' "$backup_file")
        log "INFO" "证书数量：$cert_count"
    else
        error_exit "证书备份失败"
    fi
}

# =============================================================================
# 帮助和使用说明
# =============================================================================

show_help() {
    cat << EOF
电子发票证书管理工具 v1.0.0

用途：管理存储在AWS SSM Parameter Store中的电子发票证书

使用方法：
    $0 <command> -e <environment> [options] [arguments]

命令：
    upload <file>       上传本地JSON证书文件到SSM Parameter Store
    download <file>     从SSM下载证书JSON到本地文件
    list               列出当前存储的证书信息（不显示敏感数据）
    validate [file]    验证证书JSON格式（文件或SSM中的）
    backup [name]      备份当前证书到本地文件
    help               显示此帮助信息

必需参数：
    -e, --environment <env>    指定环境 (dev|prod)

可选参数：
    --dry-run                 预览操作，不实际执行
    --force                   强制覆盖现有证书或文件
    -v, --verbose             显示详细日志
    --log-file <file>         指定日志文件路径
    -h, --help               显示帮助信息

示例：
    # 上传证书到开发环境
    $0 upload examples/turnkey-certificates.json -e dev

    # 下载生产环境证书
    $0 download /tmp/prod-certificates.json -e prod

    # 列出开发环境证书
    $0 list -e dev

    # 验证本地证书文件
    $0 validate examples/turnkey-certificates.json -e dev

    # 备份生产环境证书
    $0 backup prod_backup_$(date +%Y%m%d) -e prod

    # 预览操作（不实际执行）
    $0 upload examples/turnkey-certificates.json -e dev --dry-run

文件格式：
    证书JSON文件必须包含以下结构：
    {
      "certificates": [
        {
          "pfx_base64": "base64编码的PFX证书",
          "pfx_password": "证书密码",
          "sign_id": "签名ID",
          "sign_type": "证书类型 (corporate|branch|individual)",
          "description": "证书描述（可选）",
          "issued_date": "发行日期（可选）",
          "expiry_date": "过期日期（可选）"
        }
      ],
      "metadata": {
        "version": "1.0",
        "total_certificates": 1
      }
    }

注意事项：
    - 所有证书数据都会加密存储在AWS SSM Parameter Store中
    - 证书密码等敏感信息不会在日志中显示
    - 建议定期备份证书文件
    - 生产环境操作需要特别谨慎

依赖项：
    - AWS CLI (已配置凭证)
    - jq (JSON处理工具)
    - base64 (Base64编码工具)

EOF
}

# =============================================================================
# 参数解析
# =============================================================================

parse_arguments() {
    local command=""
    
    # 检查是否有参数
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi
    
    # 解析命令
    command=$1
    shift
    
    # 解析选项
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --log-file)
                LOG_FILE="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                break
                ;;
        esac
    done
    
    # 根据命令执行相应操作
    case $command in
        upload)
            if [[ $# -lt 1 ]]; then
                error_exit "upload命令需要指定证书文件路径"
            fi
            validate_environment
            check_dependencies
            check_aws_credentials
            upload_certificates "$1"
            ;;
        download)
            if [[ $# -lt 1 ]]; then
                error_exit "download命令需要指定输出文件路径"
            fi
            validate_environment
            check_dependencies
            check_aws_credentials
            download_certificates "$1"
            ;;
        list)
            validate_environment
            check_dependencies
            check_aws_credentials
            list_certificates
            ;;
        validate)
            validate_environment
            check_dependencies
            if [[ $# -gt 0 ]]; then
                validate_certificates "$1"
            else
                check_aws_credentials
                validate_certificates
            fi
            ;;
        backup)
            validate_environment
            check_dependencies
            check_aws_credentials
            if [[ $# -gt 0 ]]; then
                backup_certificates "$1"
            else
                backup_certificates
            fi
            ;;
        help)
            show_help
            exit 0
            ;;
        *)
            error_exit "未知命令：$command。使用 '$0 help' 查看帮助信息。"
            ;;
    esac
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 检查是否是help命令，直接处理
    if [[ $# -gt 0 && "$1" == "help" ]]; then
        show_help
        exit 0
    fi
    
    # 创建日志文件目录
    if [[ -n "$LOG_FILE" ]]; then
        mkdir -p "$(dirname "$LOG_FILE")"
    fi
    
    log "INFO" "电子发票证书管理工具启动"
    log "DEBUG" "脚本目录：$SCRIPT_DIR"
    log "DEBUG" "项目根目录：$PROJECT_ROOT"
    
    parse_arguments "$@"
    
    log "INFO" "操作完成"
}

# 执行主函数
main "$@"