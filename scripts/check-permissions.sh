#!/bin/bash

# CDK权限检查脚本
# 用于验证当前用户是否具有CDK bootstrap所需的权限

echo "🔍 检查CDK Bootstrap所需权限..."
echo "=================================="

# 检查基本AWS连接
echo "1. 检查AWS连接..."
if aws sts get-caller-identity > /dev/null 2>&1; then
    echo "✅ AWS连接正常"
    aws sts get-caller-identity
else
    echo "❌ AWS连接失败"
    exit 1
fi

echo ""

# 检查IAM权限
echo "2. 检查IAM权限..."

# 测试创建角色权限
echo "   测试 iam:CreateRole 权限..."
if aws iam create-role --role-name test-cdk-role --assume-role-policy-document '{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "cloudformation.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}' --dry-run > /dev/null 2>&1; then
    echo "   ✅ iam:CreateRole 权限正常"
else
    echo "   ❌ 缺少 iam:CreateRole 权限"
fi

# 测试获取角色权限
echo "   测试 iam:GetRole 权限..."
if aws iam get-role --role-name test-cdk-role > /dev/null 2>&1; then
    echo "   ✅ iam:GetRole 权限正常"
    # 清理测试角色
    aws iam delete-role --role-name test-cdk-role > /dev/null 2>&1
else
    echo "   ❌ 缺少 iam:GetRole 权限"
fi

echo ""

# 检查CloudFormation权限
echo "3. 检查CloudFormation权限..."
if aws cloudformation list-stacks > /dev/null 2>&1; then
    echo "✅ CloudFormation权限正常"
else
    echo "❌ 缺少CloudFormation权限"
fi

echo ""

# 检查S3权限
echo "4. 检查S3权限..."
if aws s3 ls > /dev/null 2>&1; then
    echo "✅ S3权限正常"
else
    echo "❌ 缺少S3权限"
fi

echo ""

# 检查ECR权限
echo "5. 检查ECR权限..."
if aws ecr describe-repositories > /dev/null 2>&1; then
    echo "✅ ECR权限正常"
else
    echo "❌ 缺少ECR权限"
fi

echo ""

# 检查现有CDK栈状态
echo "6. 检查CDK Bootstrap状态..."
if aws cloudformation describe-stacks --stack-name CDKToolkit --region ap-east-2 > /dev/null 2>&1; then
    STACK_STATUS=$(aws cloudformation describe-stacks --stack-name CDKToolkit --region ap-east-2 --query 'Stacks[0].StackStatus' --output text)
    echo "CDKToolkit栈状态: $STACK_STATUS"
    
    if [ "$STACK_STATUS" = "ROLLBACK_COMPLETE" ] || [ "$STACK_STATUS" = "ROLLBACK_FAILED" ]; then
        echo "⚠️  需要删除失败的CDKToolkit栈"
        echo "   运行: aws cloudformation delete-stack --stack-name CDKToolkit --region ap-east-2"
    elif [ "$STACK_STATUS" = "CREATE_COMPLETE" ]; then
        echo "✅ CDK Bootstrap已完成"
    fi
else
    echo "ℹ️  CDKToolkit栈不存在，需要执行bootstrap"
fi

echo ""
echo "=================================="
echo "权限检查完成"

# 提供下一步建议
echo ""
echo "📋 下一步建议："
echo "1. 如果发现权限问题，请联系AWS管理员添加必要权限"
echo "2. 权限策略文件位于: docs/iam-policy-for-cdk.json"
echo "3. 如果CDKToolkit栈失败，先删除后重新bootstrap"
echo "4. 运行 'npx cdk bootstrap aws://138264596682/ap-east-2' 进行bootstrap"
