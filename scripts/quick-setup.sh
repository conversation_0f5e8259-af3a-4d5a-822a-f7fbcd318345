#!/bin/bash

# =============================================================================
# 证书管理工具快速设置脚本
# Quick Setup Script for Certificate Management Tools
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# =============================================================================
# 输出函数
# =============================================================================

print_banner() {
    echo
    echo -e "${CYAN}=================================================================${NC}"
    echo -e "${CYAN}           电子发票证书管理工具 - 快速设置向导${NC}"
    echo -e "${CYAN}=================================================================${NC}"
    echo
}

print_step() {
    echo -e "${BLUE}🔧 步骤 $1: $2${NC}"
    echo
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${PURPLE}ℹ️  $1${NC}"
}

# =============================================================================
# 检查函数
# =============================================================================

check_dependencies() {
    print_step "1" "检查系统依赖"
    
    local deps=("aws" "jq" "base64" "openssl")
    local missing=()
    local all_good=true
    
    for dep in "${deps[@]}"; do
        if command -v "$dep" >/dev/null 2>&1; then
            print_success "$dep 已安装: $(command -v "$dep")"
        else
            print_error "$dep 未安装"
            missing+=("$dep")
            all_good=false
        fi
    done
    
    if [[ $all_good == false ]]; then
        echo
        print_error "发现缺失依赖项，请先安装："
        echo
        echo "macOS (使用 Homebrew):"
        echo "  brew install awscli jq"
        echo
        echo "Linux (Ubuntu/Debian):"
        echo "  sudo apt update && sudo apt install awscli jq"
        echo
        exit 1
    fi
    
    echo
    print_success "所有依赖项已安装"
    echo
}

check_aws_config() {
    print_step "2" "验证AWS配置"
    
    # 检查AWS凭证
    if ! aws sts get-caller-identity >/dev/null 2>&1; then
        print_error "AWS凭证未配置或已过期"
        echo
        echo "请运行以下命令配置AWS凭证："
        echo "  aws configure"
        echo
        echo "或设置环境变量："
        echo "  export AWS_ACCESS_KEY_ID=your_access_key"
        echo "  export AWS_SECRET_ACCESS_KEY=your_secret_key"
        echo "  export AWS_DEFAULT_REGION=ap-east-2"
        echo
        exit 1
    fi
    
    local aws_identity=$(aws sts get-caller-identity --output text --query 'Arn' 2>/dev/null)
    print_success "AWS凭证配置正确"
    print_info "当前身份: $aws_identity"
    
    # 检查权限
    echo
    echo "检查SSM权限..."
    if aws ssm describe-parameters --parameter-filters "Key=Name,Values=/yuanhui/einvoice-turnkey" >/dev/null 2>&1; then
        print_success "SSM Parameter Store权限正常"
    else
        print_warning "可能缺少SSM Parameter Store权限"
        print_info "请确保IAM用户/角色具有以下权限："
        echo "  - ssm:PutParameter"
        echo "  - ssm:GetParameter"
        echo "  - ssm:DeleteParameter"
        echo "  资源: arn:aws:ssm:*:*:parameter/yuanhui/einvoice-turnkey/*"
    fi
    
    echo
}

verify_tools() {
    print_step "3" "验证证书管理工具"
    
    # 运行完整测试
    echo "运行工具测试套件..."
    echo
    
    if "$SCRIPT_DIR/test-certificate-tools.sh"; then
        echo
        print_success "所有工具测试通过"
    else
        echo
        print_error "工具测试失败，请检查上述错误信息"
        exit 1
    fi
    
    echo
}

interactive_demo() {
    print_step "4" "交互式演示"
    
    echo "是否要运行交互式演示来学习如何使用工具？"
    read -p "输入 y/Y 继续，或其他键跳过: " -r
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo
        echo -e "${CYAN}=== 交互式演示开始 ===${NC}"
        echo
        
        # 演示验证功能
        echo -e "${BLUE}演示1: 验证示例证书文件${NC}"
        echo "命令: ./scripts/manage-turnkey-certificates.sh validate examples/turnkey-certificates.json -e dev"
        echo
        read -p "按回车键执行命令..."
        "$SCRIPT_DIR/manage-turnkey-certificates.sh" validate examples/turnkey-certificates.json -e dev
        echo
        
        # 演示列表功能（如果有证书的话）
        echo -e "${BLUE}演示2: 列出开发环境证书（如果存在）${NC}"
        echo "命令: ./scripts/manage-turnkey-certificates.sh list -e dev"
        echo
        read -p "按回车键执行命令（可能显示为空）..."
        "$SCRIPT_DIR/manage-turnkey-certificates.sh" list -e dev 2>/dev/null || print_info "开发环境暂无证书数据"
        echo
        
        # 演示工具帮助
        echo -e "${BLUE}演示3: 查看证书工具帮助${NC}"
        echo "命令: ./scripts/certificate-utils.sh help"
        echo
        read -p "按回车键执行命令..."
        "$SCRIPT_DIR/certificate-utils.sh" help | head -20
        echo "... (更多帮助信息已截断)"
        echo
        
        print_success "交互式演示完成"
    else
        print_info "跳过交互式演示"
    fi
    
    echo
}

create_user_config() {
    print_step "5" "创建用户配置"
    
    local config_dir="$HOME/.yuanhui-cert-tools"
    local config_file="$config_dir/config.sh"
    
    if [[ ! -d "$config_dir" ]]; then
        mkdir -p "$config_dir"
        print_success "创建配置目录: $config_dir"
    fi
    
    if [[ ! -f "$config_file" ]]; then
        cat > "$config_file" << 'EOF'
#!/bin/bash
# Yuan Hui 证书管理工具用户配置

# 默认环境（可选: dev, prod）
export YUANHUI_DEFAULT_ENV="dev"

# 默认日志级别（可选: DEBUG, INFO, WARN, ERROR）
export YUANHUI_LOG_LEVEL="INFO"

# 默认备份目录
export YUANHUI_BACKUP_DIR="$HOME/yuanhui-cert-backups"

# 工具目录（根据实际安装路径调整）
export YUANHUI_TOOLS_DIR="/Users/<USER>/code/yuan/iac/scripts"

# 便捷别名
alias cert-manage="$YUANHUI_TOOLS_DIR/manage-turnkey-certificates.sh"
alias cert-utils="$YUANHUI_TOOLS_DIR/certificate-utils.sh"
alias cert-test="$YUANHUI_TOOLS_DIR/test-certificate-tools.sh"

# 常用函数
cert-list() {
    local env=${1:-$YUANHUI_DEFAULT_ENV}
    cert-manage list -e "$env"
}

cert-backup() {
    local env=${1:-$YUANHUI_DEFAULT_ENV}
    local name=${2:-"backup_$(date +%Y%m%d_%H%M)"}
    cert-manage backup "$name" -e "$env"
}

cert-upload() {
    local file=$1
    local env=${2:-$YUANHUI_DEFAULT_ENV}
    cert-manage upload "$file" -e "$env"
}
EOF
        
        chmod +x "$config_file"
        print_success "创建用户配置文件: $config_file"
        
        echo
        print_info "将以下内容添加到你的 ~/.bashrc 或 ~/.zshrc 以启用配置："
        echo "  source $config_file"
        echo
        print_info "重新加载shell配置后，你可以使用以下快捷命令："
        echo "  cert-manage    # 主管理工具"
        echo "  cert-utils     # 证书处理工具"
        echo "  cert-test      # 测试工具"
        echo "  cert-list      # 列出证书"
        echo "  cert-backup    # 备份证书"
        echo "  cert-upload    # 上传证书"
    else
        print_info "用户配置文件已存在: $config_file"
    fi
    
    echo
}

show_next_steps() {
    print_step "6" "后续步骤建议"
    
    echo -e "${CYAN}🎯 恭喜！证书管理工具设置完成！${NC}"
    echo
    echo -e "${YELLOW}接下来你可以：${NC}"
    echo
    echo "1. 📖 阅读完整文档："
    echo "   less $SCRIPT_DIR/README-turnkey-certificates.md"
    echo
    echo "2. 📋 查看工具概览："
    echo "   less $SCRIPT_DIR/CERTIFICATE-MANAGEMENT-OVERVIEW.md"
    echo
    echo "3. 🔧 开始使用主要工具："
    echo "   ./scripts/manage-turnkey-certificates.sh help"
    echo "   ./scripts/certificate-utils.sh help"
    echo
    echo "4. 📝 创建你的第一个证书文件："
    echo "   cp scripts/examples/turnkey-certificates.json my-cert.json"
    echo "   # 编辑 my-cert.json 文件，替换为真实证书数据"
    echo "   ./scripts/manage-turnkey-certificates.sh validate my-cert.json -e dev"
    echo
    echo "5. 💾 设置定期备份（推荐）："
    echo "   # 添加到crontab（每周备份）"
    echo "   0 0 * * 0 $SCRIPT_DIR/manage-turnkey-certificates.sh backup weekly_\$(date +\%Y\%W) -e prod"
    echo
    echo -e "${GREEN}🚀 工具已准备就绪，开始管理你的电子发票证书吧！${NC}"
    echo
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    print_banner
    
    echo "这个向导将帮助你设置和验证证书管理工具。"
    echo
    read -p "按回车键开始设置..."
    
    # 执行设置步骤
    check_dependencies
    check_aws_config
    verify_tools
    interactive_demo
    create_user_config
    show_next_steps
    
    echo -e "${CYAN}=================================================================${NC}"
    echo -e "${CYAN}                        设置完成！${NC}"
    echo -e "${CYAN}=================================================================${NC}"
}

# 检查是否直接运行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi