#!/bin/bash

# =============================================================================
# 证书管理工具测试脚本
# Test Script for Certificate Management Tools
# =============================================================================

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[TEST]${NC} $*"
}

error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

# 测试依赖项
test_dependencies() {
    log "测试系统依赖项..."
    
    local deps=("aws" "jq" "base64" "openssl")
    local missing=()
    
    for dep in "${deps[@]}"; do
        if command -v "$dep" >/dev/null 2>&1; then
            echo "  ✓ $dep: $(command -v "$dep")"
        else
            echo "  ✗ $dep: 未找到"
            missing+=("$dep")
        fi
    done
    
    if [[ ${#missing[@]} -gt 0 ]]; then
        error "缺少依赖项：${missing[*]}"
        return 1
    fi
    
    log "所有依赖项检查通过"
    return 0
}

# 测试AWS凭证
test_aws_credentials() {
    log "测试AWS凭证配置..."
    
    if aws sts get-caller-identity >/dev/null 2>&1; then
        local identity=$(aws sts get-caller-identity --output text --query 'Arn')
        echo "  ✓ AWS凭证配置正确"
        echo "  身份: $identity"
    else
        error "AWS凭证配置错误"
        return 1
    fi
    
    return 0
}

# 测试证书管理脚本
test_certificate_manager() {
    log "测试证书管理脚本..."
    
    local script="${SCRIPT_DIR}/manage-turnkey-certificates.sh"
    
    if [[ ! -f "$script" ]]; then
        error "证书管理脚本不存在：$script"
        return 1
    fi
    
    if [[ ! -x "$script" ]]; then
        error "证书管理脚本不可执行：$script"
        return 1
    fi
    
    echo "  ✓ 脚本存在且可执行"
    
    # 测试帮助功能
    if "$script" help >/dev/null 2>&1; then
        echo "  ✓ 帮助功能正常"
    else
        error "帮助功能异常"
        return 1
    fi
    
    # 测试示例文件验证
    local example_file="${SCRIPT_DIR}/examples/turnkey-certificates.json"
    if [[ -f "$example_file" ]]; then
        echo "  ✓ 示例文件存在"
        
        if "$script" validate "$example_file" -e dev >/dev/null 2>&1; then
            echo "  ✓ 示例文件格式正确"
        else
            warn "示例文件格式验证失败（可能是AWS凭证问题）"
        fi
    else
        error "示例文件不存在：$example_file"
        return 1
    fi
    
    return 0
}

# 测试证书工具
test_certificate_utils() {
    log "测试证书工具脚本..."
    
    local script="${SCRIPT_DIR}/certificate-utils.sh"
    
    if [[ ! -f "$script" ]]; then
        error "证书工具脚本不存在：$script"
        return 1
    fi
    
    if [[ ! -x "$script" ]]; then
        error "证书工具脚本不可执行：$script"
        return 1
    fi
    
    echo "  ✓ 脚本存在且可执行"
    
    # 测试帮助功能
    if "$script" help >/dev/null 2>&1; then
        echo "  ✓ 帮助功能正常"
    else
        error "帮助功能异常"
        return 1
    fi
    
    return 0
}

# 测试JSON格式
test_json_format() {
    log "测试JSON格式验证..."
    
    local example_file="${SCRIPT_DIR}/examples/turnkey-certificates.json"
    
    if [[ ! -f "$example_file" ]]; then
        error "示例文件不存在"
        return 1
    fi
    
    # 验证JSON格式
    if jq . "$example_file" >/dev/null 2>&1; then
        echo "  ✓ JSON格式正确"
    else
        error "JSON格式错误"
        return 1
    fi
    
    # 验证必要字段
    local cert_count=$(jq '.certificates | length' "$example_file")
    echo "  ✓ 发现 $cert_count 个证书"
    
    if [[ "$cert_count" -gt 0 ]]; then
        local required_fields=("pfx_base64" "pfx_password" "sign_id" "sign_type")
        for field in "${required_fields[@]}"; do
            local field_exists=$(jq ".certificates[0] | has(\"$field\")" "$example_file")
            if [[ "$field_exists" == "true" ]]; then
                echo "  ✓ 字段存在：$field"
            else
                error "缺少必要字段：$field"
                return 1
            fi
        done
    fi
    
    return 0
}

# 测试目录结构
test_directory_structure() {
    log "测试目录结构..."
    
    local expected_dirs=(
        "${SCRIPT_DIR}/examples"
    )
    
    local expected_files=(
        "${SCRIPT_DIR}/manage-turnkey-certificates.sh"
        "${SCRIPT_DIR}/certificate-utils.sh"
        "${SCRIPT_DIR}/examples/turnkey-certificates.json"
        "${SCRIPT_DIR}/README-turnkey-certificates.md"
    )
    
    # 检查目录
    for dir in "${expected_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            echo "  ✓ 目录存在：$(basename "$dir")"
        else
            error "目录不存在：$dir"
            return 1
        fi
    done
    
    # 检查文件
    for file in "${expected_files[@]}"; do
        if [[ -f "$file" ]]; then
            echo "  ✓ 文件存在：$(basename "$file")"
        else
            error "文件不存在：$file"
            return 1
        fi
    done
    
    return 0
}

# 生成测试报告
generate_test_report() {
    local total_tests=$1
    local passed_tests=$2
    local failed_tests=$((total_tests - passed_tests))
    
    echo
    echo "==============================================="
    echo -e "${GREEN}证书管理工具测试报告${NC}"
    echo "==============================================="
    echo "总测试数: $total_tests"
    echo -e "通过测试: ${GREEN}$passed_tests${NC}"
    echo -e "失败测试: ${RED}$failed_tests${NC}"
    echo "成功率: $(( passed_tests * 100 / total_tests ))%"
    echo "==============================================="
    
    if [[ $failed_tests -eq 0 ]]; then
        echo -e "${GREEN}🎉 所有测试通过！证书管理工具已就绪。${NC}"
        return 0
    else
        echo -e "${RED}❌ 部分测试失败，请检查上述错误信息。${NC}"
        return 1
    fi
}

# 主函数
main() {
    echo "开始测试证书管理工具..."
    echo
    
    local tests=(
        "test_dependencies"
        "test_directory_structure"
        "test_json_format"
        "test_certificate_manager"
        "test_certificate_utils"
        "test_aws_credentials"
    )
    
    local total_tests=${#tests[@]}
    local passed_tests=0
    
    for test_func in "${tests[@]}"; do
        echo
        if $test_func; then
            passed_tests=$((passed_tests + 1))
            log "测试通过：$test_func"
        else
            error "测试失败：$test_func"
        fi
    done
    
    echo
    generate_test_report $total_tests $passed_tests
}

main "$@"