#!/bin/bash

#=====================================================
# EFS跨账号迁移 - 数据完整性验证脚本
# 用途：验证迁移后数据的完整性和一致性
# 可独立运行或被主迁移脚本调用
#=====================================================

set -euo pipefail

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VERIFICATION_REPORT=""
LOG_DIR=""
SOURCE_PATH=""
TARGET_PATH=""

# 验证选项
VERIFY_COUNT=true
VERIFY_SIZE=true
VERIFY_STRUCTURE=true
VERIFY_OWNERSHIP=true
VERIFY_CHECKSUM=false
VERIFY_SAMPLING=false
SAMPLE_SIZE=100
CHECKSUM_ALGORITHM="md5"
PARALLEL_JOBS=4

# 预期的目标身份配置
EXPECTED_USER_ID=101
EXPECTED_GROUP_ID=101
EXPECTED_USER_NAME="odoo"
EXPECTED_GROUP_NAME="odoo"

# 日志函数
log_info() {
    local msg="[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${BLUE}$msg${NC}"
    if [[ -n "$VERIFICATION_REPORT" ]]; then
        echo "$msg" >> "$VERIFICATION_REPORT"
    fi
}

log_success() {
    local msg="[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${GREEN}$msg${NC}"
    if [[ -n "$VERIFICATION_REPORT" ]]; then
        echo "$msg" >> "$VERIFICATION_REPORT"
    fi
}

log_warning() {
    local msg="[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${YELLOW}$msg${NC}"
    if [[ -n "$VERIFICATION_REPORT" ]]; then
        echo "$msg" >> "$VERIFICATION_REPORT"
    fi
}

log_error() {
    local msg="[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${RED}$msg${NC}"
    if [[ -n "$VERIFICATION_REPORT" ]]; then
        echo "$msg" >> "$VERIFICATION_REPORT"
    fi
}

# 帮助函数
show_help() {
    cat << EOF
EFS迁移数据完整性验证脚本

用法:
    $0 --source <源路径> --target <目标路径> [选项]

必需参数:
    --source PATH             源EFS挂载路径
    --target PATH             目标EFS挂载路径

可选参数:
    --log-dir DIR             日志目录 (默认: ./logs)
    --report FILE             验证报告文件路径
    --sample-size N           采样验证文件数量 (默认: 100)
    --parallel-jobs N         并行作业数 (默认: 4)
    --checksum-algorithm ALG  校验和算法 (md5|sha256, 默认: md5)

验证类型:
    --skip-count              跳过文件计数验证
    --skip-size               跳过大小验证
    --skip-structure          跳过目录结构验证
    --skip-ownership          跳过文件所有权验证
    --enable-checksum         启用校验和验证 (耗时)
    --enable-sampling         启用采样验证
    --full-checksum           对所有文件进行校验和验证 (非常耗时)
    
目标身份配置:
    --expected-user-id ID     预期的用户ID (默认: 101)
    --expected-group-id ID    预期的组ID (默认: 101)
    --expected-user-name NAME 预期的用户名 (默认: odoo)
    --expected-group-name NAME 预期的组名 (默认: odoo)

其他选项:
    -h, --help                显示此帮助信息
    -v, --verbose             详细输出
    --dry-run                预演模式
    --quick                   快速验证 (仅计数和大小)

示例:
    $0 --source /mnt/source-efs --target /mnt/target-efs
    $0 --source /mnt/source-efs --target /mnt/target-efs --enable-checksum
    $0 --source /mnt/source-efs --target /mnt/target-efs --quick
    $0 --source /mnt/source-efs --target /mnt/target-efs --enable-sampling --sample-size 200
EOF
}

# 参数解析
VERBOSE=false
DRY_RUN=false
QUICK_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --source)
            SOURCE_PATH="$2"
            shift 2
            ;;
        --target)
            TARGET_PATH="$2"
            shift 2
            ;;
        --log-dir)
            LOG_DIR="$2"
            shift 2
            ;;
        --report)
            VERIFICATION_REPORT="$2"
            shift 2
            ;;
        --sample-size)
            SAMPLE_SIZE="$2"
            shift 2
            ;;
        --parallel-jobs)
            PARALLEL_JOBS="$2"
            shift 2
            ;;
        --checksum-algorithm)
            CHECKSUM_ALGORITHM="$2"
            shift 2
            ;;
        --skip-count)
            VERIFY_COUNT=false
            shift
            ;;
        --skip-size)
            VERIFY_SIZE=false
            shift
            ;;
        --skip-structure)
            VERIFY_STRUCTURE=false
            shift
            ;;
        --skip-ownership)
            VERIFY_OWNERSHIP=false
            shift
            ;;
        --expected-user-id)
            EXPECTED_USER_ID="$2"
            shift 2
            ;;
        --expected-group-id)
            EXPECTED_GROUP_ID="$2"
            shift 2
            ;;
        --expected-user-name)
            EXPECTED_USER_NAME="$2"
            shift 2
            ;;
        --expected-group-name)
            EXPECTED_GROUP_NAME="$2"
            shift 2
            ;;
        --enable-checksum)
            VERIFY_CHECKSUM=true
            shift
            ;;
        --enable-sampling)
            VERIFY_SAMPLING=true
            shift
            ;;
        --full-checksum)
            VERIFY_CHECKSUM=true
            SAMPLE_SIZE=0
            shift
            ;;
        --quick)
            QUICK_MODE=true
            VERIFY_STRUCTURE=false
            VERIFY_OWNERSHIP=false
            VERIFY_CHECKSUM=false
            VERIFY_SAMPLING=false
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            set -x
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$SOURCE_PATH" || -z "$TARGET_PATH" ]]; then
    log_error "必须指定源路径和目标路径"
    show_help
    exit 1
fi

# 设置默认值
if [[ -z "$LOG_DIR" ]]; then
    LOG_DIR="$SCRIPT_DIR/logs"
fi

if [[ -z "$VERIFICATION_REPORT" ]]; then
    VERIFICATION_REPORT="$LOG_DIR/verification-report-$(date +%Y%m%d_%H%M%S).txt"
fi

# 创建日志目录
mkdir -p "$LOG_DIR"

# 验证路径存在性
check_paths() {
    log_info "验证路径有效性..."
    
    if [[ ! -d "$SOURCE_PATH" ]]; then
        log_error "源路径不存在: $SOURCE_PATH"
        exit 1
    fi
    
    if [[ ! -d "$TARGET_PATH" ]]; then
        log_error "目标路径不存在: $TARGET_PATH"
        exit 1
    fi
    
    if [[ ! -r "$SOURCE_PATH" ]]; then
        log_error "无法读取源路径: $SOURCE_PATH"
        exit 1
    fi
    
    if [[ ! -r "$TARGET_PATH" ]]; then
        log_error "无法读取目标路径: $TARGET_PATH"
        exit 1
    fi
    
    log_success "路径验证通过"
}

# 初始化验证报告
init_report() {
    cat > "$VERIFICATION_REPORT" << EOF
EFS迁移数据完整性验证报告
=========================================

验证时间: $(date)
源路径: $SOURCE_PATH
目标路径: $TARGET_PATH

验证配置:
- 文件计数验证: $VERIFY_COUNT
- 大小验证: $VERIFY_SIZE
- 目录结构验证: $VERIFY_STRUCTURE
- 文件所有权验证: $VERIFY_OWNERSHIP
- 校验和验证: $VERIFY_CHECKSUM
- 采样验证: $VERIFY_SAMPLING
- 采样大小: $SAMPLE_SIZE
- 并行作业: $PARALLEL_JOBS
- 校验和算法: $CHECKSUM_ALGORITHM

预期目标身份:
- 用户ID: $EXPECTED_USER_ID ($EXPECTED_USER_NAME)
- 组ID: $EXPECTED_GROUP_ID ($EXPECTED_GROUP_NAME)

=========================================

EOF
    
    log_info "验证报告初始化: $VERIFICATION_REPORT"
}

# 文件计数验证
verify_file_count() {
    if [[ "$VERIFY_COUNT" != "true" ]]; then
        return 0
    fi
    
    log_info "开始文件计数验证..."
    
    local source_count target_count
    
    log_info "统计源文件数量..."
    source_count=$(find "$SOURCE_PATH" -type f | wc -l)
    
    log_info "统计目标文件数量..."
    target_count=$(find "$TARGET_PATH" -type f | wc -l)
    
    log_info "文件计数结果:"
    log_info "  源文件数: $source_count"
    log_info "  目标文件数: $target_count"
    
    if [[ "$source_count" == "$target_count" ]]; then
        log_success "文件计数验证通过"
        return 0
    else
        log_error "文件计数验证失败: 源($source_count) != 目标($target_count)"
        return 1
    fi
}

# 大小验证
verify_total_size() {
    if [[ "$VERIFY_SIZE" != "true" ]]; then
        return 0
    fi
    
    log_info "开始总大小验证..."
    
    local source_size target_size
    
    log_info "计算源目录大小..."
    source_size=$(du -sb "$SOURCE_PATH" | cut -f1)
    
    log_info "计算目标目录大小..."
    target_size=$(du -sb "$TARGET_PATH" | cut -f1)
    
    # 转换为人类可读格式
    local source_size_human target_size_human
    source_size_human=$(numfmt --to=iec-i --suffix=B "$source_size" 2>/dev/null || echo "${source_size} bytes")
    target_size_human=$(numfmt --to=iec-i --suffix=B "$target_size" 2>/dev/null || echo "${target_size} bytes")
    
    log_info "大小比较结果:"
    log_info "  源大小: $source_size_human ($source_size bytes)"
    log_info "  目标大小: $target_size_human ($target_size bytes)"
    
    # 允许1%的误差（考虑到文件系统差异）
    local size_diff=$((source_size > target_size ? source_size - target_size : target_size - source_size))
    local tolerance=$((source_size / 100))
    
    if [[ $size_diff -le $tolerance ]]; then
        log_success "总大小验证通过 (差异: $size_diff bytes, 容忍度: $tolerance bytes)"
        return 0
    else
        log_error "总大小验证失败: 差异过大 ($size_diff bytes > $tolerance bytes)"
        return 1
    fi
}

# 目录结构验证
verify_directory_structure() {
    if [[ "$VERIFY_STRUCTURE" != "true" ]]; then
        return 0
    fi
    
    log_info "开始目录结构验证..."
    
    local temp_dir="/tmp/efs_structure_$$"
    mkdir -p "$temp_dir"
    
    local source_structure="$temp_dir/source_structure.txt"
    local target_structure="$temp_dir/target_structure.txt"
    
    # 生成目录结构文件（相对路径）
    log_info "生成源目录结构..."
    (cd "$SOURCE_PATH" && find . -type d | sort > "$source_structure")
    
    log_info "生成目标目录结构..."
    (cd "$TARGET_PATH" && find . -type d | sort > "$target_structure")
    
    # 比较结构
    local diff_output
    diff_output=$(diff "$source_structure" "$target_structure" || true)
    
    if [[ -z "$diff_output" ]]; then
        log_success "目录结构验证通过"
        rm -rf "$temp_dir"
        return 0
    else
        log_error "目录结构验证失败"
        log_error "结构差异:"
        echo "$diff_output" | head -20 | while IFS= read -r line; do
            log_error "  $line"
        done
        
        # 保存完整差异到报告
        echo "目录结构差异详情:" >> "$VERIFICATION_REPORT"
        echo "$diff_output" >> "$VERIFICATION_REPORT"
        
        rm -rf "$temp_dir"
        return 1
    fi
}

# 文件所有权验证
verify_file_ownership() {
    if [[ "$VERIFY_OWNERSHIP" != "true" ]]; then
        return 0
    fi
    
    log_info "开始文件所有权验证..."
    log_info "预期身份: $EXPECTED_USER_NAME:$EXPECTED_GROUP_NAME ($EXPECTED_USER_ID:$EXPECTED_GROUP_ID)"
    
    local temp_dir="/tmp/efs_ownership_$$"
    mkdir -p "$temp_dir"
    
    local ownership_report="$temp_dir/ownership_report.txt"
    local wrong_ownership="$temp_dir/wrong_ownership.txt"
    
    # 获取所有文件和目录的所有权信息
    log_info "收集目标路径所有权信息..."
    find "$TARGET_PATH" -exec stat -c "%U:%G (%u:%g) %n" {} \; > "$ownership_report" 2>/dev/null
    
    # 检查不匹配的文件
    local total_items mismatched_items
    total_items=$(wc -l < "$ownership_report")
    
    log_info "检查所有权匹配情况..."
    grep -v "^$EXPECTED_USER_NAME:$EXPECTED_GROUP_NAME ($EXPECTED_USER_ID:$EXPECTED_GROUP_ID)" "$ownership_report" > "$wrong_ownership" || true
    mismatched_items=$(wc -l < "$wrong_ownership")
    
    log_info "所有权检查结果:"
    log_info "  总项目数: $total_items"
    log_info "  不匹配项目数: $mismatched_items"
    
    if [[ $mismatched_items -eq 0 ]]; then
        log_success "文件所有权验证通过 - 所有文件都属于 $EXPECTED_USER_NAME:$EXPECTED_GROUP_NAME"
        rm -rf "$temp_dir"
        return 0
    else
        log_error "文件所有权验证失败 - 发现 $mismatched_items 个不匹配的项目"
        
        # 显示部分不匹配的项目
        log_error "不匹配的项目示例:"
        head -10 "$wrong_ownership" | while IFS= read -r line; do
            log_error "  $line"
        done
        
        # 保存详细报告
        echo "文件所有权验证详情:" >> "$VERIFICATION_REPORT"
        echo "预期身份: $EXPECTED_USER_NAME:$EXPECTED_GROUP_NAME ($EXPECTED_USER_ID:$EXPECTED_GROUP_ID)" >> "$VERIFICATION_REPORT"
        echo "不匹配的项目数: $mismatched_items" >> "$VERIFICATION_REPORT"
        echo "不匹配项目列表:" >> "$VERIFICATION_REPORT"
        cat "$wrong_ownership" >> "$VERIFICATION_REPORT"
        echo "" >> "$VERIFICATION_REPORT"
        
        rm -rf "$temp_dir"
        return 1
    fi
}

# 生成文件列表用于校验和验证
generate_file_list() {
    local base_path="$1"
    local output_file="$2"
    local sample_size="$3"
    
    if [[ "$sample_size" -eq 0 ]]; then
        # 所有文件
        find "$base_path" -type f | sort > "$output_file"
    else
        # 随机采样
        find "$base_path" -type f | shuf -n "$sample_size" | sort > "$output_file"
    fi
}

# 计算单个文件校验和
calculate_checksum() {
    local file_path="$1"
    local algorithm="$2"
    
    case "$algorithm" in
        "md5")
            if command -v md5sum >/dev/null 2>&1; then
                md5sum "$file_path" | cut -d' ' -f1
            elif command -v md5 >/dev/null 2>&1; then
                md5 -q "$file_path"
            else
                echo "ERROR: md5工具不可用"
                return 1
            fi
            ;;
        "sha256")
            if command -v sha256sum >/dev/null 2>&1; then
                sha256sum "$file_path" | cut -d' ' -f1
            elif command -v shasum >/dev/null 2>&1; then
                shasum -a 256 "$file_path" | cut -d' ' -f1
            else
                echo "ERROR: sha256工具不可用"
                return 1
            fi
            ;;
        *)
            echo "ERROR: 不支持的算法: $algorithm"
            return 1
            ;;
    esac
}

# 校验和验证
verify_checksums() {
    if [[ "$VERIFY_CHECKSUM" != "true" ]]; then
        return 0
    fi
    
    log_info "开始校验和验证 (算法: $CHECKSUM_ALGORITHM)..."
    
    local temp_dir="/tmp/efs_checksum_$$"
    mkdir -p "$temp_dir"
    
    local file_list="$temp_dir/file_list.txt"
    local source_checksums="$temp_dir/source_checksums.txt"
    local target_checksums="$temp_dir/target_checksums.txt"
    
    # 生成文件列表
    if [[ "$SAMPLE_SIZE" -eq 0 ]]; then
        log_info "生成所有文件列表进行校验和验证..."
        generate_file_list "$SOURCE_PATH" "$file_list" 0
    else
        log_info "生成随机采样文件列表 (大小: $SAMPLE_SIZE)..."
        generate_file_list "$SOURCE_PATH" "$file_list" "$SAMPLE_SIZE"
    fi
    
    local total_files
    total_files=$(wc -l < "$file_list")
    log_info "待验证文件数: $total_files"
    
    if [[ $total_files -eq 0 ]]; then
        log_warning "没有文件需要验证"
        rm -rf "$temp_dir"
        return 0
    fi
    
    # 并行计算源文件校验和
    log_info "计算源文件校验和..."
    export -f calculate_checksum
    export CHECKSUM_ALGORITHM
    
    cat "$file_list" | while IFS= read -r file; do
        echo "$SOURCE_PATH/$file"
    done | xargs -I {} -P "$PARALLEL_JOBS" bash -c '
        relative_path="${1#'"$SOURCE_PATH"'/}"
        checksum=$(calculate_checksum "$1" "$CHECKSUM_ALGORITHM")
        if [[ "$checksum" != ERROR:* ]]; then
            echo "$checksum  $relative_path"
        fi
    ' _ {} > "$source_checksums"
    
    # 并行计算目标文件校验和
    log_info "计算目标文件校验和..."
    cat "$file_list" | while IFS= read -r file; do
        target_file="$TARGET_PATH/$file"
        if [[ -f "$target_file" ]]; then
            echo "$target_file"
        fi
    done | xargs -I {} -P "$PARALLEL_JOBS" bash -c '
        relative_path="${1#'"$TARGET_PATH"'/}"
        checksum=$(calculate_checksum "$1" "$CHECKSUM_ALGORITHM")
        if [[ "$checksum" != ERROR:* ]]; then
            echo "$checksum  $relative_path"
        fi
    ' _ {} > "$target_checksums"
    
    # 比较校验和
    log_info "比较校验和结果..."
    local checksum_diff
    checksum_diff=$(diff "$source_checksums" "$target_checksums" || true)
    
    if [[ -z "$checksum_diff" ]]; then
        log_success "校验和验证通过 ($total_files 个文件)"
        rm -rf "$temp_dir"
        return 0
    else
        log_error "校验和验证失败"
        local diff_count
        diff_count=$(echo "$checksum_diff" | wc -l)
        log_error "发现 $diff_count 处差异"
        
        # 显示部分差异
        echo "$checksum_diff" | head -10 | while IFS= read -r line; do
            log_error "  $line"
        done
        
        # 保存完整差异到报告
        echo "校验和差异详情:" >> "$VERIFICATION_REPORT"
        echo "$checksum_diff" >> "$VERIFICATION_REPORT"
        
        rm -rf "$temp_dir"
        return 1
    fi
}

# 采样验证
verify_sampling() {
    if [[ "$VERIFY_SAMPLING" != "true" ]]; then
        return 0
    fi
    
    log_info "开始采样验证 (样本大小: $SAMPLE_SIZE)..."
    
    local temp_dir="/tmp/efs_sampling_$$"
    mkdir -p "$temp_dir"
    
    local sample_files="$temp_dir/sample_files.txt"
    
    # 生成随机采样文件列表
    find "$SOURCE_PATH" -type f | shuf -n "$SAMPLE_SIZE" > "$sample_files"
    
    local total_samples
    total_samples=$(wc -l < "$sample_files")
    log_info "采样文件数: $total_samples"
    
    local failed_samples=0
    local processed=0
    
    while IFS= read -r source_file; do
        ((processed++))
        
        local relative_path="${source_file#$SOURCE_PATH/}"
        local target_file="$TARGET_PATH/$relative_path"
        
        if [[ $((processed % 10)) -eq 0 ]]; then
            log_info "采样验证进度: $processed/$total_samples"
        fi
        
        # 检查目标文件是否存在
        if [[ ! -f "$target_file" ]]; then
            log_error "目标文件不存在: $relative_path"
            ((failed_samples++))
            continue
        fi
        
        # 比较文件大小
        local source_size target_size
        source_size=$(stat -c%s "$source_file" 2>/dev/null || stat -f%z "$source_file" 2>/dev/null)
        target_size=$(stat -c%s "$target_file" 2>/dev/null || stat -f%z "$target_file" 2>/dev/null)
        
        if [[ "$source_size" != "$target_size" ]]; then
            log_error "文件大小不匹配: $relative_path (源: $source_size, 目标: $target_size)"
            ((failed_samples++))
            continue
        fi
        
        # 比较校验和（如果启用）
        if [[ "$CHECKSUM_ALGORITHM" != "none" ]]; then
            local source_checksum target_checksum
            source_checksum=$(calculate_checksum "$source_file" "$CHECKSUM_ALGORITHM")
            target_checksum=$(calculate_checksum "$target_file" "$CHECKSUM_ALGORITHM")
            
            if [[ "$source_checksum" != "$target_checksum" ]]; then
                log_error "校验和不匹配: $relative_path"
                ((failed_samples++))
                continue
            fi
        fi
        
    done < "$sample_files"
    
    rm -rf "$temp_dir"
    
    if [[ $failed_samples -eq 0 ]]; then
        log_success "采样验证通过 ($total_samples 个样本)"
        return 0
    else
        log_error "采样验证失败: $failed_samples/$total_samples 个样本失败"
        return 1
    fi
}

# 生成最终报告
generate_final_report() {
    local overall_result="$1"
    
    cat >> "$VERIFICATION_REPORT" << EOF

=========================================
验证完成时间: $(date)

总体结果: $overall_result

详细结果:
EOF
    
    if [[ "$VERIFY_COUNT" == "true" ]]; then
        echo "- 文件计数验证: $(if verify_file_count &>/dev/null; then echo "通过"; else echo "失败"; fi)" >> "$VERIFICATION_REPORT"
    fi
    
    if [[ "$VERIFY_SIZE" == "true" ]]; then
        echo "- 大小验证: $(if verify_total_size &>/dev/null; then echo "通过"; else echo "失败"; fi)" >> "$VERIFICATION_REPORT"
    fi
    
    if [[ "$VERIFY_STRUCTURE" == "true" ]]; then
        echo "- 目录结构验证: $(if verify_directory_structure &>/dev/null; then echo "通过"; else echo "失败"; fi)" >> "$VERIFICATION_REPORT"
    fi
    
    if [[ "$VERIFY_OWNERSHIP" == "true" ]]; then
        echo "- 文件所有权验证: $(if verify_file_ownership &>/dev/null; then echo "通过"; else echo "失败"; fi)" >> "$VERIFICATION_REPORT"
    fi
    
    if [[ "$VERIFY_CHECKSUM" == "true" ]]; then
        echo "- 校验和验证: $(if verify_checksums &>/dev/null; then echo "通过"; else echo "失败"; fi)" >> "$VERIFICATION_REPORT"
    fi
    
    if [[ "$VERIFY_SAMPLING" == "true" ]]; then
        echo "- 采样验证: $(if verify_sampling &>/dev/null; then echo "通过"; else echo "失败"; fi)" >> "$VERIFICATION_REPORT"
    fi
    
    echo "=========================================" >> "$VERIFICATION_REPORT"
    
    log_info "最终验证报告已生成: $VERIFICATION_REPORT"
}

# 主验证函数
main() {
    log_info "开始EFS数据完整性验证..."
    log_info "源路径: $SOURCE_PATH"
    log_info "目标路径: $TARGET_PATH"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_warning "运行在预演模式"
        return 0
    fi
    
    # 初始化
    check_paths
    init_report
    
    local verification_results=()
    local overall_success=true
    
    # 执行各项验证
    if ! verify_file_count; then
        verification_results+=("文件计数验证失败")
        overall_success=false
    fi
    
    if ! verify_total_size; then
        verification_results+=("大小验证失败")
        overall_success=false
    fi
    
    if ! verify_directory_structure; then
        verification_results+=("目录结构验证失败")
        overall_success=false
    fi
    
    if ! verify_file_ownership; then
        verification_results+=("文件所有权验证失败")
        overall_success=false
    fi
    
    if ! verify_checksums; then
        verification_results+=("校验和验证失败")
        overall_success=false
    fi
    
    if ! verify_sampling; then
        verification_results+=("采样验证失败")
        overall_success=false
    fi
    
    # 生成最终报告
    local overall_result
    if [[ "$overall_success" == "true" ]]; then
        overall_result="验证通过"
        log_success "所有验证项目通过！"
    else
        overall_result="验证失败"
        log_error "验证失败，详情请查看报告"
        for result in "${verification_results[@]}"; do
            log_error "  - $result"
        done
    fi
    
    generate_final_report "$overall_result"
    
    if [[ "$overall_success" == "true" ]]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"