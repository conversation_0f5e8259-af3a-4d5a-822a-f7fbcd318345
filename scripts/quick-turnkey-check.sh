#!/bin/bash

# ================================================================
# Turnkey系统快速检查脚本
# 
# 目的：提供快速的Turnkey系统状态检查，适用于日常运维
# 用法：./quick-turnkey-check.sh [dev|prod]
# ================================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 环境设置
NODE_ENV="${1:-dev}"
AWS_REGION="${AWS_REGION:-ap-east-2}"

log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_check() { echo -e "${BLUE}[CHECK]${NC} $1"; }

echo "======================================"
echo "   Turnkey Quick Status Check ($NODE_ENV)"
echo "======================================"

# 1. 基础环境检查
log_check "AWS Environment"
if aws sts get-caller-identity >/dev/null 2>&1; then
    ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
    log_info "✅ AWS credentials valid - Account: $ACCOUNT"
else
    log_error "❌ AWS credentials invalid"
    exit 1
fi

# 2. SSM证书参数检查
log_check "Certificate Parameter"
PARAM_NAME="/turnkey/$NODE_ENV/certificates"
if aws ssm get-parameter --name "$PARAM_NAME" >/dev/null 2>&1; then
    CERT_COUNT=$(aws ssm get-parameter --name "$PARAM_NAME" --with-decryption --query "Parameter.Value" --output text | jq length 2>/dev/null || echo "0")
    log_info "✅ Certificate parameter exists with $CERT_COUNT certificate(s)"
else
    log_error "❌ Certificate parameter not found: $PARAM_NAME"
fi

# 3. 数据库Secret检查
log_check "Database Secret"
DB_SECRET_NAME="turnkey/$NODE_ENV/db-password"
if aws secretsmanager describe-secret --secret-id "$DB_SECRET_NAME" >/dev/null 2>&1; then
    log_info "✅ Database password secret exists"
else
    log_error "❌ Database password secret not found: $DB_SECRET_NAME"
fi

# 4. ECS服务状态检查
log_check "ECS Service Status"
CLUSTER_NAME="yuanhui-odoo-$NODE_ENV"
SERVICE_NAME="turnkey-$NODE_ENV"

if aws ecs describe-services --cluster "$CLUSTER_NAME" --services "$SERVICE_NAME" --query "services[0].status" --output text 2>/dev/null | grep -q "ACTIVE"; then
    DESIRED=$(aws ecs describe-services --cluster "$CLUSTER_NAME" --services "$SERVICE_NAME" --query "services[0].desiredCount" --output text)
    RUNNING=$(aws ecs describe-services --cluster "$CLUSTER_NAME" --services "$SERVICE_NAME" --query "services[0].runningCount" --output text)
    
    if [[ "$DESIRED" == "$RUNNING" ]]; then
        log_info "✅ ECS service healthy - $RUNNING/$DESIRED tasks running"
    else
        log_warn "⚠️ ECS service scaling - $RUNNING/$DESIRED tasks running"
    fi
else
    log_warn "⚠️ ECS service not found or not active"
fi

# 5. Aurora数据库状态检查
log_check "Aurora Database"
DB_CLUSTER_NAME="yuanhui-aurora-cluster-$NODE_ENV"
if aws rds describe-db-clusters --db-cluster-identifier "$DB_CLUSTER_NAME" --query "DBClusters[0].Status" --output text 2>/dev/null | grep -q "available"; then
    log_info "✅ Aurora database cluster available"
else
    log_warn "⚠️ Aurora database cluster not available"
fi

echo "======================================"
echo "Quick check completed for $NODE_ENV environment"
echo "======================================"

# 提供后续操作建议
echo
echo "Next Steps:"
echo "- For full validation: ./scripts/pre-deploy-turnkey-validation.sh --env $NODE_ENV"
echo "- For comprehensive testing: ./scripts/test-ecs-secrets-integration.sh"
echo "- For deployment: npm run deploy:turnkey:$NODE_ENV"