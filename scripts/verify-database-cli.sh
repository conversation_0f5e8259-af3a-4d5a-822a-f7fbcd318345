#!/bin/bash

# 数据库设置验证脚本
# 使用AWS CLI验证SQL执行构造和Airflow数据库初始化是否成功

set -e

echo "🚀 开始验证数据库设置..."
echo "============================================================"

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 区域设置
REGION="ap-east-2"

# Secret ARNs
MASTER_SECRET_ARN="arn:aws:secretsmanager:ap-east-2:138264596682:secret:AuroraDatabaseSecret3BAE587-pPe0oRoDTUhC-jxZ7r9"
AIRFLOW_SECRET_ARN="arn:aws:secretsmanager:ap-east-2:138264596682:secret:AirflowDatabaseSecret3B875A-6wdbAKO5pXwq-wsXzpF"

echo -e "${BLUE}🔍 验证主数据库凭证...${NC}"

# 获取主数据库凭证
echo "获取主数据库凭证..."
MASTER_CREDS=$(aws secretsmanager get-secret-value \
    --secret-id "$MASTER_SECRET_ARN" \
    --region "$REGION" \
    --query 'SecretString' \
    --output text)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 成功获取主数据库凭证${NC}"
    
    # 解析凭证信息
    DB_HOST=$(echo "$MASTER_CREDS" | jq -r '.host')
    DB_PORT=$(echo "$MASTER_CREDS" | jq -r '.port')
    DB_NAME=$(echo "$MASTER_CREDS" | jq -r '.database')
    DB_USER=$(echo "$MASTER_CREDS" | jq -r '.username')
    
    echo "   数据库主机: $DB_HOST"
    echo "   数据库端口: $DB_PORT"
    echo "   主数据库: $DB_NAME"
    echo "   用户名: $DB_USER"
else
    echo -e "${RED}❌ 无法获取主数据库凭证${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🔍 验证Airflow数据库凭证...${NC}"

# 获取Airflow数据库凭证
echo "获取Airflow数据库凭证..."
AIRFLOW_CREDS=$(aws secretsmanager get-secret-value \
    --secret-id "$AIRFLOW_SECRET_ARN" \
    --region "$REGION" \
    --query 'SecretString' \
    --output text)

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ 成功获取Airflow数据库凭证${NC}"
    
    # 解析Airflow凭证信息
    AIRFLOW_HOST=$(echo "$AIRFLOW_CREDS" | jq -r '.host')
    AIRFLOW_PORT=$(echo "$AIRFLOW_CREDS" | jq -r '.port')
    AIRFLOW_DB=$(echo "$AIRFLOW_CREDS" | jq -r '.database')
    AIRFLOW_USER=$(echo "$AIRFLOW_CREDS" | jq -r '.username')
    
    echo "   数据库主机: $AIRFLOW_HOST"
    echo "   数据库端口: $AIRFLOW_PORT"
    echo "   Airflow数据库: $AIRFLOW_DB"
    echo "   用户名: $AIRFLOW_USER"
else
    echo -e "${RED}❌ 无法获取Airflow数据库凭证${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🔍 验证RDS实例状态...${NC}"

# 检查RDS实例状态
echo "检查RDS实例状态..."
RDS_STATUS=$(aws rds describe-db-instances \
    --region "$REGION" \
    --query 'DBInstances[?contains(DBInstanceIdentifier, `yuanhuiauroradatabase-dev`)].{Identifier:DBInstanceIdentifier,Status:DBInstanceStatus,Endpoint:Endpoint.Address}' \
    --output table)

echo "$RDS_STATUS"

# 检查RDS实例是否可用
RDS_AVAILABLE=$(aws rds describe-db-instances \
    --region "$REGION" \
    --query 'DBInstances[?contains(DBInstanceIdentifier, `yuanhuiauroradatabase-dev`) && DBInstanceStatus==`available`].DBInstanceIdentifier' \
    --output text)

if [ -n "$RDS_AVAILABLE" ]; then
    echo -e "${GREEN}✅ RDS实例状态正常 (available)${NC}"
else
    echo -e "${YELLOW}⚠️ RDS实例可能不在available状态${NC}"
fi

echo ""
echo -e "${BLUE}🔍 验证安全组配置...${NC}"

# 检查数据库安全组规则
echo "检查数据库安全组规则..."
DB_SG_ID="sg-0d1848bc09ed956a5"

SG_RULES=$(aws ec2 describe-security-groups \
    --group-ids "$DB_SG_ID" \
    --region "$REGION" \
    --query 'SecurityGroups[0].IpPermissions[?FromPort==`5432`].UserIdGroupPairs[].{GroupId:GroupId,Description:Description}' \
    --output table)

echo "$SG_RULES"

# 统计允许访问的安全组数量
SG_COUNT=$(aws ec2 describe-security-groups \
    --group-ids "$DB_SG_ID" \
    --region "$REGION" \
    --query 'length(SecurityGroups[0].IpPermissions[?FromPort==`5432`].UserIdGroupPairs[])' \
    --output text)

echo "允许访问数据库的安全组数量: $SG_COUNT"

if [ "$SG_COUNT" -ge 3 ]; then
    echo -e "${GREEN}✅ 安全组配置正确 (包含ECS、SQL执行Lambda、数据库初始化Lambda)${NC}"
else
    echo -e "${YELLOW}⚠️ 安全组配置可能不完整${NC}"
fi

echo ""
echo -e "${BLUE}🔍 验证Lambda函数执行状态...${NC}"

# 检查SQL执行Lambda函数最近的执行
echo "检查SQL执行Lambda函数最近的执行..."
SQL_LAMBDA_LOGS=$(aws logs filter-log-events \
    --log-group-name "/aws/lambda/YuanhuiAuroraDatabase-dev-SqlExecutorExampleSqlExe-B1o7r8crKGwe" \
    --region "$REGION" \
    --start-time $(date -d '10 minutes ago' +%s)000 \
    --filter-pattern "Execution summary" \
    --query 'events[0].message' \
    --output text 2>/dev/null || echo "No recent logs found")

if [[ "$SQL_LAMBDA_LOGS" == *"Execution summary"* ]]; then
    echo -e "${GREEN}✅ SQL执行Lambda函数最近有执行记录${NC}"
    echo "   $SQL_LAMBDA_LOGS"
else
    echo -e "${YELLOW}⚠️ 未找到SQL执行Lambda函数的最近执行记录${NC}"
fi

# 检查Airflow数据库初始化Lambda函数最近的执行
echo ""
echo "检查Airflow数据库初始化Lambda函数最近的执行..."
AIRFLOW_LAMBDA_LOGS=$(aws logs filter-log-events \
    --log-group-name "/aws/lambda/YuanhuiAuroraDatabase-dev-AirflowDatabaseInitDatab-T0G4NjR0afFJ" \
    --region "$REGION" \
    --start-time $(date -d '10 minutes ago' +%s)000 \
    --filter-pattern "initialization completed successfully" \
    --query 'events[0].message' \
    --output text 2>/dev/null || echo "No recent logs found")

if [[ "$AIRFLOW_LAMBDA_LOGS" == *"completed successfully"* ]]; then
    echo -e "${GREEN}✅ Airflow数据库初始化Lambda函数执行成功${NC}"
    echo "   $AIRFLOW_LAMBDA_LOGS"
else
    echo -e "${YELLOW}⚠️ 未找到Airflow数据库初始化成功的记录${NC}"
fi

echo ""
echo "============================================================"
echo -e "${BLUE}📊 验证结果总结:${NC}"

# 总结验证结果
MAIN_DB_OK=true
AIRFLOW_DB_OK=true
RDS_OK=true
SG_OK=true

if [ -z "$MASTER_CREDS" ]; then
    MAIN_DB_OK=false
fi

if [ -z "$AIRFLOW_CREDS" ]; then
    AIRFLOW_DB_OK=false
fi

if [ -z "$RDS_AVAILABLE" ]; then
    RDS_OK=false
fi

if [ "$SG_COUNT" -lt 3 ]; then
    SG_OK=false
fi

echo -e "   主数据库凭证: $([ "$MAIN_DB_OK" = true ] && echo -e "${GREEN}✅ 成功${NC}" || echo -e "${RED}❌ 失败${NC}")"
echo -e "   Airflow数据库凭证: $([ "$AIRFLOW_DB_OK" = true ] && echo -e "${GREEN}✅ 成功${NC}" || echo -e "${RED}❌ 失败${NC}")"
echo -e "   RDS实例状态: $([ "$RDS_OK" = true ] && echo -e "${GREEN}✅ 正常${NC}" || echo -e "${RED}❌ 异常${NC}")"
echo -e "   安全组配置: $([ "$SG_OK" = true ] && echo -e "${GREEN}✅ 正确${NC}" || echo -e "${YELLOW}⚠️ 需要检查${NC}")"

if [ "$MAIN_DB_OK" = true ] && [ "$AIRFLOW_DB_OK" = true ] && [ "$RDS_OK" = true ] && [ "$SG_OK" = true ]; then
    echo ""
    echo -e "${GREEN}🎉 所有数据库设置验证成功！${NC}"
    echo -e "${GREEN}   SQL执行构造和Airflow数据库初始化都正常工作。${NC}"
    echo ""
    echo -e "${BLUE}📋 部署成果:${NC}"
    echo "   ✅ PostgreSQL RDS实例运行正常"
    echo "   ✅ 主数据库凭证配置正确"
    echo "   ✅ Airflow专用数据库和用户创建成功"
    echo "   ✅ SQL执行构造部署并运行成功"
    echo "   ✅ 网络安全组配置正确"
    echo "   ✅ Lambda函数权限配置正确"
    exit 0
else
    echo ""
    echo -e "${RED}❌ 部分验证失败，请检查配置。${NC}"
    exit 1
fi
