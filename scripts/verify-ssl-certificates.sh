#!/bin/bash

# SSL证书验证脚本
# 用于验证AWS Certificate Manager中的SSL证书状态和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取证书ARN
get_certificate_arn() {
    local env=$1
    local cert_type=${2:-primary}
    
    if [ "$cert_type" = "primary" ]; then
        aws cloudformation describe-stacks \
            --stack-name YuanhuiDns-$env \
            --query 'Stacks[0].Outputs[?OutputKey==`CertificateArn`].OutputValue' \
            --output text 2>/dev/null || echo ""
    else
        aws cloudformation describe-stacks \
            --stack-name <PERSON>huiDns-$env \
            --query 'Stacks[0].Outputs[?OutputKey==`KhmallCertificateArn`].OutputValue' \
            --output text 2>/dev/null || echo ""
    fi
}

# 检查证书状态
check_certificate_status() {
    local cert_arn=$1
    local cert_name=$2
    
    if [ -z "$cert_arn" ]; then
        log_warning "$cert_name 证书ARN未找到"
        return 1
    fi
    
    log_info "检查 $cert_name 证书状态..."
    
    local cert_info=$(aws acm describe-certificate --certificate-arn "$cert_arn" 2>/dev/null || echo "")
    
    if [ -z "$cert_info" ]; then
        log_error "$cert_name 证书信息获取失败"
        return 1
    fi
    
    local status=$(echo "$cert_info" | jq -r '.Certificate.Status' 2>/dev/null || echo "UNKNOWN")
    local domain_name=$(echo "$cert_info" | jq -r '.Certificate.DomainName' 2>/dev/null || echo "UNKNOWN")
    local subject_alternative_names=$(echo "$cert_info" | jq -r '.Certificate.SubjectAlternativeNames[]?' 2>/dev/null | tr '\n' ',' | sed 's/,$//' || echo "无")
    local validation_method=$(echo "$cert_info" | jq -r '.Certificate.DomainValidationOptions[0].ValidationMethod' 2>/dev/null || echo "UNKNOWN")
    local not_after=$(echo "$cert_info" | jq -r '.Certificate.NotAfter' 2>/dev/null || echo "UNKNOWN")
    
    echo "  证书ARN: $cert_arn"
    echo "  主域名: $domain_name"
    echo "  备用域名: $subject_alternative_names"
    echo "  状态: $status"
    echo "  验证方法: $validation_method"
    echo "  过期时间: $not_after"
    
    case $status in
        "ISSUED")
            log_success "$cert_name 证书已颁发并可用"
            return 0
            ;;
        "PENDING_VALIDATION")
            log_warning "$cert_name 证书等待验证中"
            check_certificate_validation "$cert_arn" "$cert_name"
            return 1
            ;;
        "VALIDATION_TIMED_OUT")
            log_error "$cert_name 证书验证超时"
            return 1
            ;;
        "FAILED")
            log_error "$cert_name 证书颁发失败"
            return 1
            ;;
        *)
            log_warning "$cert_name 证书状态未知: $status"
            return 1
            ;;
    esac
}

# 检查证书验证状态
check_certificate_validation() {
    local cert_arn=$1
    local cert_name=$2
    
    log_info "检查 $cert_name 证书验证记录..."
    
    local validation_options=$(aws acm describe-certificate --certificate-arn "$cert_arn" --query 'Certificate.DomainValidationOptions' --output json 2>/dev/null || echo "[]")
    
    if [ "$validation_options" = "[]" ]; then
        log_error "无法获取证书验证选项"
        return 1
    fi
    
    echo "$validation_options" | jq -r '.[] | "域名: \(.DomainName)\n验证状态: \(.ValidationStatus)\nDNS记录名称: \(.ResourceRecord.Name // "N/A")\nDNS记录值: \(.ResourceRecord.Value // "N/A")\n"'
    
    # 检查DNS验证记录是否存在
    local dns_records=$(echo "$validation_options" | jq -r '.[] | select(.ValidationMethod == "DNS") | .ResourceRecord.Name' 2>/dev/null || echo "")
    
    if [ -n "$dns_records" ]; then
        while IFS= read -r dns_record; do
            if [ -n "$dns_record" ] && [ "$dns_record" != "null" ]; then
                log_info "验证DNS记录: $dns_record"
                if nslookup "$dns_record" > /dev/null 2>&1; then
                    log_success "DNS验证记录存在: $dns_record"
                else
                    log_error "DNS验证记录不存在: $dns_record"
                    log_info "请确保在DNS提供商处添加了正确的CNAME记录"
                fi
            fi
        done <<< "$dns_records"
    fi
}

# 测试证书在负载均衡器上的使用
test_certificate_on_alb() {
    local domain=$1
    local cert_name=$2
    
    log_info "测试 $cert_name 证书在负载均衡器上的使用..."
    
    # 使用openssl检查证书
    local cert_info=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -text 2>/dev/null || echo "")
    
    if [ -n "$cert_info" ]; then
        local issuer=$(echo "$cert_info" | grep "Issuer:" | head -1 || echo "未知颁发者")
        local subject=$(echo "$cert_info" | grep "Subject:" | head -1 || echo "未知主题")
        local not_after=$(echo "$cert_info" | grep "Not After" | head -1 || echo "未知过期时间")
        
        log_success "$cert_name 证书在负载均衡器上正常工作"
        echo "  颁发者: $issuer"
        echo "  主题: $subject"
        echo "  过期时间: $not_after"
        
        # 检查证书链
        local cert_chain=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" -showcerts 2>/dev/null | grep -c "BEGIN CERTIFICATE" || echo "0")
        log_info "证书链长度: $cert_chain"
        
        return 0
    else
        log_error "$cert_name 证书在负载均衡器上无法访问"
        return 1
    fi
}

# 检查证书即将过期
check_certificate_expiry() {
    local cert_arn=$1
    local cert_name=$2
    local warning_days=${3:-30}
    
    log_info "检查 $cert_name 证书过期时间..."
    
    local not_after=$(aws acm describe-certificate --certificate-arn "$cert_arn" --query 'Certificate.NotAfter' --output text 2>/dev/null || echo "")
    
    if [ -z "$not_after" ]; then
        log_error "无法获取证书过期时间"
        return 1
    fi
    
    # 转换时间格式并计算剩余天数
    local expiry_timestamp=$(date -d "$not_after" +%s 2>/dev/null || echo "0")
    local current_timestamp=$(date +%s)
    local days_remaining=$(( (expiry_timestamp - current_timestamp) / 86400 ))
    
    echo "  过期时间: $not_after"
    echo "  剩余天数: $days_remaining 天"
    
    if [ $days_remaining -lt 0 ]; then
        log_error "$cert_name 证书已过期！"
        return 1
    elif [ $days_remaining -lt $warning_days ]; then
        log_warning "$cert_name 证书将在 $days_remaining 天后过期"
        return 1
    else
        log_success "$cert_name 证书有效期充足 ($days_remaining 天)"
        return 0
    fi
}

# 生成证书报告
generate_certificate_report() {
    local env=$1
    local output_file=${2:-"certificate-report-$env.json"}
    
    log_info "生成证书报告..."
    
    local primary_cert_arn=$(get_certificate_arn $env primary)
    local khmall_cert_arn=$(get_certificate_arn $env khmall)
    
    local report="{\"environment\": \"$env\", \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\", \"certificates\": []}"
    
    # 主证书信息
    if [ -n "$primary_cert_arn" ]; then
        local primary_info=$(aws acm describe-certificate --certificate-arn "$primary_cert_arn" 2>/dev/null || echo "{}")
        report=$(echo "$report" | jq --argjson cert "$primary_info" '.certificates += [{"type": "primary", "certificate": $cert}]')
    fi
    
    # Khmall证书信息
    if [ -n "$khmall_cert_arn" ] && [ "$khmall_cert_arn" != "$primary_cert_arn" ]; then
        local khmall_info=$(aws acm describe-certificate --certificate-arn "$khmall_cert_arn" 2>/dev/null || echo "{}")
        report=$(echo "$report" | jq --argjson cert "$khmall_info" '.certificates += [{"type": "khmall", "certificate": $cert}]')
    fi
    
    echo "$report" | jq '.' > "$output_file"
    log_success "证书报告已生成: $output_file"
}

# 主验证函数
verify_all_certificates() {
    local env=${1:-dev}
    
    log_info "开始验证所有SSL证书..."
    echo ""
    
    local total_tests=0
    local passed_tests=0
    local test_results=()
    
    # 定义域名
    if [ "$env" = "prod" ]; then
        YHERP_PUBLIC_DOMAIN="dp.kh2u.com"
        KHMALL_DOMAIN="jmall.tw"
    else
        YHERP_PUBLIC_DOMAIN="dp-dev.kh2u.com"
        KHMALL_DOMAIN="jmall-dev.tw"
    fi
    
    echo "========================================"
    echo "AWS Certificate Manager 证书状态检查"
    echo "========================================"
    
    # 检查主证书
    local primary_cert_arn=$(get_certificate_arn $env primary)
    ((total_tests++))
    if check_certificate_status "$primary_cert_arn" "主证书"; then
        ((passed_tests++))
        test_results+=("✓ 主证书状态检查")
        
        # 检查过期时间
        ((total_tests++))
        if check_certificate_expiry "$primary_cert_arn" "主证书"; then
            ((passed_tests++))
            test_results+=("✓ 主证书过期时间检查")
        else
            test_results+=("✗ 主证书过期时间检查")
        fi
    else
        test_results+=("✗ 主证书状态检查")
    fi
    
    echo ""
    
    # 检查Khmall证书（如果存在且不同于主证书）
    local khmall_cert_arn=$(get_certificate_arn $env khmall)
    if [ -n "$khmall_cert_arn" ] && [ "$khmall_cert_arn" != "$primary_cert_arn" ]; then
        ((total_tests++))
        if check_certificate_status "$khmall_cert_arn" "Khmall证书"; then
            ((passed_tests++))
            test_results+=("✓ Khmall证书状态检查")
            
            # 检查过期时间
            ((total_tests++))
            if check_certificate_expiry "$khmall_cert_arn" "Khmall证书"; then
                ((passed_tests++))
                test_results+=("✓ Khmall证书过期时间检查")
            else
                test_results+=("✗ Khmall证书过期时间检查")
            fi
        else
            test_results+=("✗ Khmall证书状态检查")
        fi
        echo ""
    fi
    
    echo "========================================"
    echo "负载均衡器证书测试"
    echo "========================================"
    
    # 测试证书在负载均衡器上的使用
    ((total_tests++))
    if test_certificate_on_alb "$YHERP_PUBLIC_DOMAIN" "Yherp公网域名"; then
        ((passed_tests++))
        test_results+=("✓ Yherp公网域名证书测试")
    else
        test_results+=("✗ Yherp公网域名证书测试")
    fi
    
    echo ""
    
    ((total_tests++))
    if test_certificate_on_alb "$KHMALL_DOMAIN" "Khmall域名"; then
        ((passed_tests++))
        test_results+=("✓ Khmall域名证书测试")
    else
        test_results+=("✗ Khmall域名证书测试")
    fi
    
    echo ""
    echo "========================================"
    echo "验证结果汇总"
    echo "========================================"
    
    for result in "${test_results[@]}"; do
        echo "$result"
    done
    
    echo ""
    log_info "证书验证完成: $passed_tests/$total_tests 通过"
    
    # 生成报告
    generate_certificate_report $env
    
    if [ $passed_tests -eq $total_tests ]; then
        log_success "所有证书验证通过！"
        return 0
    else
        log_warning "部分证书验证失败，请检查配置"
        return 1
    fi
}

# 主函数
main() {
    local env=${1:-dev}
    
    echo "========================================"
    echo "SSL证书验证脚本"
    echo "========================================"
    echo ""
    
    # 检查必要工具
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装，请先安装 jq"
        exit 1
    fi
    
    if ! command -v openssl &> /dev/null; then
        log_error "openssl 未安装"
        exit 1
    fi
    
    verify_all_certificates $env
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
