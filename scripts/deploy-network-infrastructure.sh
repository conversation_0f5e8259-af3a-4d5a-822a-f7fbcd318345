#!/bin/bash

# 元晖网络基础设施部署脚本
# 用于部署完整的网络入口和路由配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查部署前置条件..."
    
    # 检查AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI 未安装，请先安装 AWS CLI"
        exit 1
    fi
    
    # 检查CDK
    if ! command -v cdk &> /dev/null; then
        log_error "AWS CDK 未安装，请先安装 AWS CDK"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装，请先安装 npm"
        exit 1
    fi
    
    log_success "前置条件检查通过"
}

# 检查AWS凭证
check_aws_credentials() {
    log_info "检查AWS凭证..."
    
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS凭证未配置或已过期，请运行 'aws configure' 配置凭证"
        exit 1
    fi
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local region=$(aws configure get region)
    
    log_success "AWS凭证验证通过"
    log_info "账户ID: $account_id"
    log_info "区域: $region"
}

# 设置环境变量
setup_environment() {
    local env=${1:-dev}
    
    log_info "设置环境变量..."
    
    export NODE_ENV=$env
    export CDK_DEFAULT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
    export CDK_DEFAULT_REGION=$(aws configure get region)
    
    log_success "环境变量设置完成"
    log_info "部署环境: $env"
    log_info "AWS账户: $CDK_DEFAULT_ACCOUNT"
    log_info "AWS区域: $CDK_DEFAULT_REGION"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    if [ ! -d "node_modules" ]; then
        npm install
    else
        npm ci
    fi
    
    log_success "依赖安装完成"
}

# 编译TypeScript
build_project() {
    log_info "编译TypeScript代码..."
    
    npm run build
    
    log_success "代码编译完成"
}

# CDK Bootstrap
bootstrap_cdk() {
    log_info "初始化CDK环境..."
    
    cdk bootstrap
    
    log_success "CDK环境初始化完成"
}

# 部署栈
deploy_stacks() {
    local env=${1:-dev}
    local deploy_all=${2:-false}
    
    log_info "开始部署基础设施栈..."
    
    if [ "$deploy_all" = "true" ]; then
        log_info "部署所有栈..."
        cdk deploy --all --require-approval never
    else
        # 按顺序部署栈
        log_info "按顺序部署栈..."
        
        # 1. 网络栈
        log_info "部署网络栈..."
        cdk deploy YuanhuiNetwork-$env --require-approval never
        
        # 2. ECS栈
        log_info "部署ECS栈..."
        cdk deploy YuanhuiEcs-$env --require-approval never
        
        # 3. 数据库栈
        log_info "部署数据库栈..."
        cdk deploy YuanhuiDatabase-$env --require-approval never
        
        # 4. DNS栈
        log_info "部署DNS栈..."
        cdk deploy YuanhuiDns-$env --require-approval never
        
        # 5. 应用栈
        log_info "部署应用栈..."
        cdk deploy YuanhuiApplication-$env --require-approval never
        
        # 6. CloudFront栈（如果启用）
        if cdk list | grep -q "YuanhuiCloudFront-$env"; then
            log_info "部署CloudFront栈..."
            cdk deploy YuanhuiCloudFront-$env --require-approval never
        fi
        
        # 7. OpenZiti栈（如果启用）
        if cdk list | grep -q "YuanhuiOpenZiti-$env"; then
            log_info "部署OpenZiti栈..."
            cdk deploy YuanhuiOpenZiti-$env --require-approval never
        fi
        
        # 8. 监控栈
        log_info "部署监控栈..."
        cdk deploy YuanhuiMonitoring-$env --require-approval never
    fi
    
    log_success "所有栈部署完成"
}

# 验证部署
verify_deployment() {
    local env=${1:-dev}
    
    log_info "验证部署结果..."
    
    # 检查栈状态
    log_info "检查CloudFormation栈状态..."
    local stacks=(
        "YuanhuiNetwork-$env"
        "YuanhuiEcs-$env"
        "YuanhuiDatabase-$env"
        "YuanhuiDns-$env"
        "YuanhuiApplication-$env"
        "YuanhuiMonitoring-$env"
    )
    
    for stack in "${stacks[@]}"; do
        local status=$(aws cloudformation describe-stacks --stack-name $stack --query 'Stacks[0].StackStatus' --output text 2>/dev/null || echo "NOT_FOUND")
        if [ "$status" = "CREATE_COMPLETE" ] || [ "$status" = "UPDATE_COMPLETE" ]; then
            log_success "栈 $stack 状态正常: $status"
        else
            log_warning "栈 $stack 状态: $status"
        fi
    done
    
    # 获取输出信息
    log_info "获取部署输出信息..."
    
    # 获取负载均衡器DNS名称
    local public_alb_dns=$(aws cloudformation describe-stacks --stack-name YuanhuiApplication-$env --query 'Stacks[0].Outputs[?OutputKey==`PublicLoadBalancerDnsName`].OutputValue' --output text 2>/dev/null || echo "未找到")
    
    log_info "公网负载均衡器DNS: $public_alb_dns"
    
    # 获取域名信息
    local hosted_zone_id=$(aws cloudformation describe-stacks --stack-name YuanhuiDns-$env --query 'Stacks[0].Outputs[?OutputKey==`HostedZoneId`].OutputValue' --output text 2>/dev/null || echo "未找到")
    local name_servers=$(aws cloudformation describe-stacks --stack-name YuanhuiDns-$env --query 'Stacks[0].Outputs[?OutputKey==`NameServers`].OutputValue' --output text 2>/dev/null || echo "未找到")
    
    log_info "托管区域ID: $hosted_zone_id"
    log_info "域名服务器: $name_servers"
    
    log_success "部署验证完成"
}

# 运行域名和路由测试
run_tests() {
    local env=${1:-dev}
    
    log_info "运行域名和路由测试..."
    
    # 调用测试脚本
    if [ -f "scripts/test-network-routing.sh" ]; then
        bash scripts/test-network-routing.sh $env
    else
        log_warning "测试脚本不存在，跳过测试"
    fi
}

# 显示部署后说明
show_post_deployment_instructions() {
    local env=${1:-dev}
    
    log_info "部署后配置说明:"
    echo ""
    echo "1. DNS配置:"
    echo "   - 请将域名的NS记录指向上述域名服务器"
    echo "   - 等待DNS传播完成（通常需要24-48小时）"
    echo ""
    echo "2. SSL证书:"
    echo "   - SSL证书将自动验证和颁发"
    echo "   - 请确保DNS记录正确配置"
    echo ""
    echo "3. 应用访问:"
    echo "   - yh.kh2u.com (内部访问) - 需要OpenZiti客户端"
    echo "   - dp.kh2u.com (公网访问) - 直接访问"
    echo "   - jmall.tw (电商平台) - 通过CloudFront加速"
    echo ""
    echo "4. 监控和日志:"
    echo "   - CloudWatch仪表板: https://console.aws.amazon.com/cloudwatch/"
    echo "   - ECS服务日志: /aws/ecs/yherp-$env, /aws/ecs/khmall-$env"
    echo ""
    echo "5. 安全配置:"
    echo "   - WAF已启用，保护应用免受常见攻击"
    echo "   - 安全组已配置，限制网络访问"
    echo ""
}

# 主函数
main() {
    local env=${1:-dev}
    local deploy_all=${2:-false}
    
    echo "========================================"
    echo "元晖网络基础设施部署脚本"
    echo "========================================"
    echo ""
    
    check_prerequisites
    check_aws_credentials
    setup_environment $env
    install_dependencies
    build_project
    bootstrap_cdk
    deploy_stacks $env $deploy_all
    verify_deployment $env
    run_tests $env
    show_post_deployment_instructions $env
    
    log_success "部署完成！"
}

# 脚本入口
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
