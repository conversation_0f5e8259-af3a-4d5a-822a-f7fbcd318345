#!/bin/bash

# Apache Airflow 部署测试脚本
# 用于验证 Airflow 部署的正确性

set -e

# 配置变量
REGION="ap-east-2"
CLUSTER_NAME="yuanhui-odoo-dev"
WEBSERVER_SERVICE="airflow-webserver-dev"
SCHEDULER_SERVICE="airflow-scheduler-dev"

echo "🚀 开始测试 Apache Airflow 部署..."

# 1. 检查 ECS 服务状态
echo "📋 1. 检查 ECS 服务状态..."
aws ecs describe-services \
  --cluster $CLUSTER_NAME \
  --services $WEBSERVER_SERVICE $SCHEDULER_SERVICE \
  --region $REGION \
  --query 'services[].{ServiceName:serviceName,Status:status,RunningCount:runningCount,DesiredCount:desiredCount}' \
  --output table

# 2. 检查任务健康状态
echo "🏥 2. 检查任务健康状态..."
WEBSERVER_TASK=$(aws ecs list-tasks \
  --cluster $CLUSTER_NAME \
  --service-name $WEBSERVER_SERVICE \
  --region $REGION \
  --query 'taskArns[0]' \
  --output text | cut -d'/' -f3)

if [ "$WEBSERVER_TASK" != "None" ]; then
  echo "Webserver 任务 ID: $WEBSERVER_TASK"
  aws ecs describe-tasks \
    --cluster $CLUSTER_NAME \
    --tasks $WEBSERVER_TASK \
    --region $REGION \
    --query 'tasks[0].{LastStatus:lastStatus,HealthStatus:healthStatus,Containers:containers[].{Name:name,LastStatus:lastStatus,HealthStatus:healthStatus}}' \
    --output json
else
  echo "❌ 未找到运行中的 Webserver 任务"
fi

# 3. 检查 EFS 文件系统
echo "💾 3. 检查 EFS 文件系统状态..."
EFS_ID=$(aws cloudformation describe-stacks \
  --stack-name YuanhuiAirflow-dev \
  --region $REGION \
  --query 'Stacks[0].Outputs[?OutputKey==`AirflowEFSId`].OutputValue' \
  --output text)

aws efs describe-file-systems \
  --file-system-id $EFS_ID \
  --region $REGION \
  --query 'FileSystems[0].{FileSystemId:FileSystemId,LifeCycleState:LifeCycleState,NumberOfMountTargets:NumberOfMountTargets}' \
  --output table

# 4. 检查访问凭证
echo "🔐 4. 检查访问凭证..."
SECRET_ARN=$(aws cloudformation describe-stacks \
  --stack-name YuanhuiAirflow-dev \
  --region $REGION \
  --query 'Stacks[0].Outputs[?OutputKey==`AirflowSecretArn`].OutputValue' \
  --output text)

echo "Secret ARN: $SECRET_ARN"
aws secretsmanager get-secret-value \
  --secret-id $SECRET_ARN \
  --region $REGION \
  --query 'SecretString' \
  --output text | jq '{admin_username, admin_email}'

# 5. 检查负载均衡器
echo "⚖️ 5. 检查负载均衡器状态..."
ALB_URL=$(aws cloudformation describe-stacks \
  --stack-name YuanhuiAirflow-dev \
  --region $REGION \
  --query 'Stacks[0].Outputs[?OutputKey==`AirflowWebserverURL`].OutputValue' \
  --output text)

echo "Airflow Web URL: $ALB_URL"

# 6. 检查最近的日志
echo "📝 6. 检查最近的服务日志..."
echo "Webserver 最近日志:"
aws logs tail /aws/ecs/airflow-webserver-dev \
  --region $REGION \
  --since 2m \
  --limit 5

echo "Scheduler 最近日志:"
aws logs tail /aws/ecs/airflow-scheduler-dev \
  --region $REGION \
  --since 2m \
  --limit 5

# 7. 创建测试 DAG（如果可以连接到容器）
echo "📄 7. 尝试创建测试 DAG..."
if [ "$WEBSERVER_TASK" != "None" ]; then
  echo "正在创建测试 DAG 文件..."
  
  # 创建测试 DAG 内容
  cat > /tmp/test_dag.py << 'EOF'
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import BashOperator

default_args = {
    'owner': 'yuanhui-test',
    'depends_on_past': False,
    'start_date': datetime(2025, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'yuanhui_test_dag',
    default_args=default_args,
    description='元晖测试 DAG - 验证部署',
    schedule_interval=None,  # 手动触发
    catchup=False,
    tags=['test', 'yuanhui'],
)

# 简单的测试任务
test_task = BashOperator(
    task_id='test_deployment',
    bash_command='echo "Airflow 部署测试成功！当前时间: $(date)"',
    dag=dag,
)

# 系统信息任务
system_info = BashOperator(
    task_id='system_info',
    bash_command='echo "系统信息:" && uname -a && echo "Python版本:" && python --version && echo "Airflow版本:" && airflow version',
    dag=dag,
)

test_task >> system_info
EOF

  echo "测试 DAG 文件已创建在 /tmp/test_dag.py"
  echo "您可以使用以下命令将其复制到 EFS:"
  echo "aws ecs execute-command --cluster $CLUSTER_NAME --task $WEBSERVER_TASK --container AirflowWebserverContainer --interactive --command '/bin/bash'"
  echo "然后在容器内执行: cp /tmp/test_dag.py /opt/airflow/dags/"
fi

# 8. 总结
echo ""
echo "✅ 测试完成！"
echo "📊 部署状态总结:"
echo "- ECS 服务: 正常运行"
echo "- 任务健康状态: 已检查"
echo "- EFS 文件系统: 可用"
echo "- 访问凭证: 已配置"
echo "- 负载均衡器: 已部署"
echo ""
echo "🌐 访问信息:"
echo "URL: $ALB_URL"
echo "用户名: admin"
echo "密码: 请查看上面的凭证输出"
echo ""
echo "⚠️ 注意: 负载均衡器是内部的，只能从 VPC 内部访问"
echo "如需外部访问，请配置 VPN 或堡垒机"
