#!/bin/bash

# 安全栈部署脚本
# 用于部署GitHub Actions CI/CD权限管理栈

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
安全栈部署脚本

用法: $0 [选项]

选项:
    -e, --environment ENV    环境名称 (dev/prod，默认: dev)
    -r, --region REGION      AWS区域 (默认: ap-east-2)
    -a, --account ACCOUNT    AWS账户ID (默认: 从环境变量获取)
    -d, --dry-run           仅生成模板，不实际部署
    -h, --help              显示此帮助信息

示例:
    $0                                    # 部署开发环境
    $0 -e prod                           # 部署生产环境
    $0 -e dev -d                         # 预览开发环境模板
    $0 -e prod -r ap-east-2 -a ************  # 指定所有参数

环境变量:
    CDK_DEFAULT_ACCOUNT     AWS账户ID
    CDK_DEFAULT_REGION      AWS区域
    NODE_ENV               环境名称

EOF
}

# 默认值
ENVIRONMENT="dev"
REGION="ap-east-2"
ACCOUNT=""
DRY_RUN=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -a|--account)
            ACCOUNT="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
    log_error "环境必须是 'dev' 或 'prod'"
    exit 1
fi

# 获取账户ID
if [[ -z "$ACCOUNT" ]]; then
    if [[ -n "$CDK_DEFAULT_ACCOUNT" ]]; then
        ACCOUNT="$CDK_DEFAULT_ACCOUNT"
    else
        log_info "尝试从AWS CLI获取账户ID..."
        ACCOUNT=$(aws sts get-caller-identity --query Account --output text 2>/dev/null || echo "")
        if [[ -z "$ACCOUNT" ]]; then
            log_error "无法获取AWS账户ID，请设置 CDK_DEFAULT_ACCOUNT 环境变量或使用 -a 参数"
            exit 1
        fi
    fi
fi

# 设置环境变量
export NODE_ENV="$ENVIRONMENT"
export CDK_DEFAULT_ACCOUNT="$ACCOUNT"
export CDK_DEFAULT_REGION="$REGION"

# 显示配置信息
log_info "安全栈部署配置:"
log_info "  环境: $ENVIRONMENT"
log_info "  区域: $REGION"
log_info "  账户: $ACCOUNT"
log_info "  栈名称: YuanhuiSecurity-$ENVIRONMENT"
log_info "  模式: $([ "$DRY_RUN" = true ] && echo "预览模式" || echo "部署模式")"

echo ""

# 检查依赖
log_info "检查依赖..."

# 检查Node.js和npm
if ! command -v node &> /dev/null; then
    log_error "Node.js 未安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    log_error "npm 未安装"
    exit 1
fi

# 检查AWS CLI
if ! command -v aws &> /dev/null; then
    log_error "AWS CLI 未安装"
    exit 1
fi

# 检查CDK
if ! command -v cdk &> /dev/null; then
    log_error "AWS CDK 未安装，请运行: npm install -g aws-cdk"
    exit 1
fi

log_success "依赖检查通过"

# 验证AWS凭证
log_info "验证AWS凭证..."
if ! aws sts get-caller-identity &> /dev/null; then
    log_error "AWS凭证验证失败，请配置AWS CLI"
    exit 1
fi

CURRENT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
if [[ "$CURRENT_ACCOUNT" != "$ACCOUNT" ]]; then
    log_error "当前AWS账户 ($CURRENT_ACCOUNT) 与指定账户 ($ACCOUNT) 不匹配"
    exit 1
fi

log_success "AWS凭证验证通过"

# 编译项目
log_info "编译TypeScript项目..."
if ! npm run build; then
    log_error "项目编译失败"
    exit 1
fi

log_success "项目编译完成"

# 生成或部署
STACK_NAME="YuanhuiSecurity-$ENVIRONMENT"

if [[ "$DRY_RUN" = true ]]; then
    log_info "生成CloudFormation模板..."
    if cdk synth "$STACK_NAME"; then
        log_success "模板生成成功"
        echo ""
        log_info "要实际部署，请运行:"
        log_info "  $0 -e $ENVIRONMENT"
    else
        log_error "模板生成失败"
        exit 1
    fi
else
    log_info "部署安全栈..."
    echo ""
    log_warning "即将部署以下资源:"
    log_warning "  - IAM用户: yuanhui-github-actions-$ENVIRONMENT"
    log_warning "  - IAM访问密钥"
    log_warning "  - ECR权限策略"
    log_warning "  - ECS权限策略"
    echo ""
    
    read -p "确认部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
    
    if cdk deploy "$STACK_NAME" --require-approval never; then
        log_success "安全栈部署成功!"
        echo ""
        log_info "下一步操作:"
        log_info "1. 复制输出的 AWS_ACCESS_KEY_ID 和 AWS_SECRET_ACCESS_KEY"
        log_info "2. 在GitHub仓库设置中添加这两个密钥作为Secrets"
        log_info "3. 参考文档配置GitHub Actions工作流"
        echo ""
        log_info "详细文档: docs/security/github-actions-setup.md"
    else
        log_error "安全栈部署失败"
        exit 1
    fi
fi
