#!/bin/bash

#=====================================================
# EFS跨账号迁移 - 主迁移执行脚本（性能优化版）
# 用途：执行实际的EFS数据迁移操作
# 执行位置：目标账号(138264596682)的ap-east-2区域EC2
#
# 针对256GB大文件迁移的优化：
# 1. 网络优化：使用IP地址挂载，EFS兼容的性能选项(noatime,tcp)
# 2. rsync优化：启用压缩节省网络流量，智能选择whole-file/增量模式
# 3. 日志优化：可选择减少日志输出，降低IO开销
# 4. 并行验证：并行执行源和目标文件统计，减少验证时间
# 5. 批处理优化：权限修正使用批处理和并行处理
#=====================================================

set -euo pipefail

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_FILE="$SCRIPT_DIR/efs-migration.pid"
LOG_DIR="$SCRIPT_DIR/logs"
CONFIG_FILE="$SCRIPT_DIR/efs-migration-config.env"
PROGRESS_FILE="$SCRIPT_DIR/migration-progress.json"

# 性能优化参数
NETWORK_OPTIMIZATION=true
REDUCED_LOGGING=false
BATCH_SIZE=10000

# 创建日志目录
mkdir -p "$LOG_DIR"

# 日志文件配置
MAIN_LOG="$LOG_DIR/migration-$(date +%Y%m%d_%H%M%S).log"
ERROR_LOG="$LOG_DIR/migration-errors.log"

# 日志函数 - 同时输出到控制台和文件
log_info() {
    local msg="[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${BLUE}$msg${NC}"
    echo "$msg" >> "$MAIN_LOG"
}

log_success() {
    local msg="[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${GREEN}$msg${NC}"
    echo "$msg" >> "$MAIN_LOG"
}

log_warning() {
    local msg="[WARNING] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${YELLOW}$msg${NC}"
    echo "$msg" >> "$MAIN_LOG"
}

log_error() {
    local msg="[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${RED}$msg${NC}"
    echo "$msg" >> "$MAIN_LOG"
    echo "$msg" >> "$ERROR_LOG"
}

log_progress() {
    local msg="[PROGRESS] $(date '+%Y-%m-%d %H:%M:%S') - $1"
    echo -e "${PURPLE}$msg${NC}"
    echo "$msg" >> "$MAIN_LOG"
}

# 进度监控函数
update_progress() {
    local stage="$1"
    local progress="$2"
    local details="$3"
    
    cat > "$PROGRESS_FILE" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "stage": "$stage",
  "progress_percent": $progress,
  "details": "$details",
  "log_file": "$MAIN_LOG"
}
EOF
}

# 错误处理函数
handle_error() {
    local line_no=$1
    local error_code=$2
    log_error "迁移失败 at line $line_no with exit code $error_code"
    
    # 更新进度状态
    update_progress "failed" 0 "Migration failed at line $line_no"
    
    # 清理挂载点
    cleanup_mounts
    
    # 移除PID文件
    rm -f "$PID_FILE"
    
    exit $error_code
}

trap 'handle_error ${LINENO} $?' ERR

# 帮助函数
show_help() {
    cat << EOF
EFS跨账号迁移主执行脚本

用法:
    $0 [选项]

前置条件:
    1. 已运行 efs-cross-account-config.sh 完成环境配置
    2. 配置文件 efs-migration-config.env 存在
    3. 已配置跨账号EFS访问权限

选项:
    -h, --help                显示此帮助信息
    -d, --dry-run            预演模式，仅模拟操作
    -f, --force              强制执行，跳过确认
    -r, --resume             断点续传模式（自动使用增量传输）
    -v, --verbose            详细输出
    --skip-verification      跳过数据完整性验证
    --parallel-threads N     并行线程数 (默认: 8)
    --disable-network-opt    禁用网络优化
    --reduced-logging        减少日志输出以提高性能
    --batch-size N           批处理大小 (默认: 10000)
    --quick-verify           快速验证模式（只检查文件数量，跳过大小计算）
    --compress-level N       压缩级别 1-9 (默认: 6, 1=最快, 9=最高压缩)
    --no-compress           禁用压缩（适用于已压缩文件或高CPU使用场景）

示例:
    $0                                    # 标准迁移
    $0 --dry-run                         # 预演模式
    $0 --resume                          # 断点续传
    $0 --force --verbose                 # 强制执行且详细输出
    
针对256GB大文件迁移的推荐配置:
    $0 --reduced-logging --quick-verify              # 高性能模式（推荐）
    $0 --reduced-logging --quick-verify --compress-level=9   # 最大压缩节省流量
    $0 --no-compress --quick-verify                  # 禁用压缩提升速度
    $0 --disable-network-opt --verbose               # 调试模式
    $0 --skip-verification --reduced-logging         # 最快模式（跳过验证）
EOF
}

# 参数解析
DRY_RUN=false
FORCE=false
RESUME=false
VERBOSE=false
SKIP_VERIFICATION=false
PARALLEL_THREADS=8
QUICK_VERIFY=false
COMPRESS_LEVEL=6
NO_COMPRESS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        -r|--resume)
            RESUME=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            set -x
            shift
            ;;
        --skip-verification)
            SKIP_VERIFICATION=true
            shift
            ;;
        --parallel-threads)
            PARALLEL_THREADS="$2"
            shift 2
            ;;
        --disable-network-opt)
            NETWORK_OPTIMIZATION=false
            shift
            ;;
        --reduced-logging)
            REDUCED_LOGGING=true
            shift
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --quick-verify)
            QUICK_VERIFY=true
            shift
            ;;
        --compress-level)
            COMPRESS_LEVEL="$2"
            if [[ ! "$COMPRESS_LEVEL" =~ ^[1-9]$ ]]; then
                log_error "压缩级别必须是1-9之间的数字"
                exit 1
            fi
            shift 2
            ;;
        --no-compress)
            NO_COMPRESS=true
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查运行状态
check_running_status() {
    if [[ -f "$PID_FILE" ]]; then
        local old_pid
        old_pid=$(cat "$PID_FILE")
        if kill -0 "$old_pid" 2>/dev/null; then
            if [[ "$FORCE" == "true" ]]; then
                log_warning "强制停止正在运行的迁移进程 (PID: $old_pid)"
                kill "$old_pid" || true
                sleep 2
                rm -f "$PID_FILE"
            else
                log_error "迁移进程已在运行 (PID: $old_pid)"
                log_info "使用 --force 强制执行，或等待当前进程完成"
                exit 1
            fi
        else
            log_warning "清理无效的PID文件"
            rm -f "$PID_FILE"
        fi
    fi
    
    # 写入当前PID
    echo $$ > "$PID_FILE"
}

# 加载配置文件
load_config() {
    log_info "加载配置文件..."
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "配置文件不存在: $CONFIG_FILE"
        log_info "请先运行 efs-cross-account-config.sh"
        exit 1
    fi
    
    # shellcheck source=/dev/null
    source "$CONFIG_FILE"
    
    # 验证必需的配置变量
    local required_vars=(
        "SOURCE_EFS_ID" "TARGET_EFS_ID"
        "SOURCE_MOUNT_POINT" "TARGET_MOUNT_POINT"
        "SOURCE_REGION" "TARGET_REGION"
    )
    
    # 身份配置变量（用于报告，但不用于映射）
    TARGET_USER_ID="${TARGET_USER_ID:-101}"
    TARGET_GROUP_ID="${TARGET_GROUP_ID:-101}"
    TARGET_USER_NAME="${TARGET_USER_NAME:-odoo}"
    TARGET_GROUP_NAME="${TARGET_GROUP_NAME:-odoo}"
    FORCE_OWNERSHIP="${FORCE_OWNERSHIP:-false}"
    ENABLE_USER_MAPPING="false"  # 已禁用用户映射功能
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "配置变量未设置: $var"
            exit 1
        fi
    done
    
    log_success "配置加载完成"
    log_info "源EFS: $SOURCE_EFS_ID ($SOURCE_REGION)"
    log_info "目标EFS: $TARGET_EFS_ID ($TARGET_REGION)"
    log_info "目标身份配置: $TARGET_USER_NAME:$TARGET_GROUP_NAME ($TARGET_USER_ID:$TARGET_GROUP_ID)"
    log_info "用户映射已禁用，将保留源文件的所有属性"
}

# 用户确认
user_confirmation() {
    if [[ "$FORCE" == "true" || "$DRY_RUN" == "true" ]]; then
        return 0
    fi
    
    echo ""
    echo "=========================================="
    echo "EFS迁移确认"
    echo "=========================================="
    echo "源EFS: $SOURCE_EFS_ID ($SOURCE_REGION)"
    echo "目标EFS: $TARGET_EFS_ID ($TARGET_REGION)"
    echo "预计传输: 256GB"
    echo "预计时间: 1-3小时"
    echo "=========================================="
    echo ""
    
    read -p "确认开始迁移? (yes/no): " -r
    if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
        log_info "用户取消操作"
        exit 0
    fi
}

# 挂载EFS文件系统
mount_efs() {
    log_info "挂载EFS文件系统..."
    
    # EFS基础挂载选项
    local mount_options="nfsvers=4.1,rsize=1048576,wsize=1048576,hard,intr,timeo=600,retrans=2"
    
    if [[ "$NETWORK_OPTIMIZATION" == "true" ]]; then
        # EFS兼容的性能优化选项
        mount_options+=",noatime"          # 不更新访问时间，减少IO
        mount_options+=",tcp"              # 强制使用TCP协议
        mount_options+=",_netdev"          # 标记为网络设备
        log_info "启用EFS大文件传输优化选项"
    fi
    
    # 检查是否已挂载
    if mountpoint -q "$SOURCE_MOUNT_POINT" 2>/dev/null; then
        log_info "源EFS已挂载: $SOURCE_MOUNT_POINT"
    else
        # 使用直接IP地址避免DNS查询（需要在配置中指定）
        local source_endpoint="${SOURCE_EFS_IP:-*************}"
        log_info "挂载源EFS: $source_endpoint (使用IP地址避免DNS查询)"
        if [[ "$DRY_RUN" == "false" ]]; then
            sudo mount -t nfs4 -o "$mount_options" "$source_endpoint:/" "$SOURCE_MOUNT_POINT"
            log_success "源EFS挂载成功"
        else
            log_info "[DRY RUN] 将挂载源EFS"
        fi
    fi
    
    if mountpoint -q "$TARGET_MOUNT_POINT" 2>/dev/null; then
        log_info "目标EFS已挂载: $TARGET_MOUNT_POINT"
    else
        # 对目标EFS使用DNS名称（如果没有指定IP）
        local target_endpoint="${TARGET_EFS_IP:-$TARGET_EFS_ID.efs.$TARGET_REGION.amazonaws.com}"
        log_info "挂载目标EFS: $target_endpoint"
        if [[ "$DRY_RUN" == "false" ]]; then
            sudo mount -t nfs4 -o "$mount_options" "$target_endpoint:/" "$TARGET_MOUNT_POINT"
            log_success "目标EFS挂载成功"
        else
            log_info "[DRY RUN] 将挂载目标EFS"
        fi
    fi
}

# 预检查
pre_migration_check() {
    log_info "执行迁移前检查..."
    
    if [[ "$DRY_RUN" == "false" ]]; then
        # 检查挂载点访问
        if [[ ! -r "$SOURCE_MOUNT_POINT" ]]; then
            log_error "无法读取源EFS挂载点: $SOURCE_MOUNT_POINT"
            exit 1
        fi
        
        if [[ ! -w "$TARGET_MOUNT_POINT" ]]; then
            log_error "无法写入目标EFS挂载点: $TARGET_MOUNT_POINT"
            exit 1
        fi
        
        # 快速检查源EFS大小（避免du命令耗时过长）
        log_info "快速检查源EFS大小..."
        local source_size_gb="未知"
        
        # 使用df命令快速获取已使用空间（更快但不够精确）
        if command -v df >/dev/null 2>&1; then
            local df_used
            df_used=$(df "$SOURCE_MOUNT_POINT" 2>/dev/null | awk 'NR==2{print $3}' || echo "0")
            if [[ "$df_used" != "0" ]]; then
                source_size_gb=$((df_used / 1024 / 1024))
                log_info "源EFS大约使用: ${source_size_gb}GB (基于df估算)"
            else
                log_warning "无法快速获取源EFS大小，将在后续过程中监控"
            fi
        else
            log_warning "跳过源EFS大小检查以节省时间"
        fi
        
        # 检查目标EFS可用空间
        local target_available
        target_available=$(df "$TARGET_MOUNT_POINT" | awk 'NR==2{print $4}')
        local target_available_gb=$((target_available / 1024 / 1024))
        
        log_info "目标EFS可用空间: ${target_available_gb}GB"
        
        # 只在能获取到源大小时进行空间比较
        if [[ "$source_size_gb" != "未知" && $source_size_gb -gt $target_available_gb ]]; then
            log_warning "目标EFS可用空间可能不足"
        fi
    else
        log_info "[DRY RUN] 将执行挂载点和空间检查"
    fi
    
    log_success "迁移前检查完成"
}

# 执行数据同步
execute_migration() {
    log_info "开始数据迁移..."
    update_progress "syncing" 0 "Starting data synchronization"
    
    # rsync选项配置 - 针对大文件优化
    local rsync_options=(
        "-avHAXS"           # 归档模式，保留硬链接、ACL、扩展属性、稀疏文件
        "--partial"        # 支持断点续传
        "--inplace"        # 就地更新（减少磁盘使用）
        "--delete"         # 删除目标中多余的文件
    )
    
    # 压缩配置
    if [[ "$NO_COMPRESS" == "true" ]]; then
        log_info "压缩已禁用，适用于已压缩文件或高CPU使用场景"
    else
        rsync_options+=("--compress")
        rsync_options+=("--compress-level=$COMPRESS_LEVEL")
        log_info "启用压缩 (级别: $COMPRESS_LEVEL) 以节省网络传输成本"
    fi
    
    # 根据日志设置决定是否显示进度
    if [[ "$REDUCED_LOGGING" == "false" ]]; then
        rsync_options+=("--progress")
        rsync_options+=("--stats")
    else
        log_info "减少日志输出模式已启用"
    fi
    
    # 保留源文件的所有属性（不进行用户映射）
    rsync_options+=("--numeric-ids")  # 保留原有用户/组ID
    log_info "保留源文件的所有用户/组ID和属性"
    
    # 断点续传模式
    if [[ "$RESUME" == "true" ]]; then
        rsync_options+=("--append-verify")
        log_info "启用断点续传模式（使用增量传输）"
    else
        # 非断点续传模式，使用whole-file以提高大文件传输效率
        rsync_options+=("--whole-file")
        log_info "使用whole-file模式（传输完整文件）"
    fi
    
    # 网络和传输优化
    if [[ "$NETWORK_OPTIMIZATION" == "true" ]]; then
        # 针对大文件传输的网络优化
        rsync_options+=("--bwlimit=0")        # 无带宽限制
        rsync_options+=("--timeout=3600")      # 增加IO超时时间
        # 注意：--contimeout仅用于rsync daemon，本地到本地传输不适用
        log_info "启用网络传输优化"
    fi
    
    # 并行传输优化（已禁用压缩）
    if [[ $PARALLEL_THREADS -gt 1 ]]; then
        log_info "并行线程数: $PARALLEL_THREADS（注意：rsync本身不支持真正的多线程）"
    fi
    
    # 排除特殊文件
    local exclude_patterns=(
        "lost+found"
        ".Trash*"
        ".DS_Store"
        "*.tmp"
        "*.swp"
    )
    
    for pattern in "${exclude_patterns[@]}"; do
        rsync_options+=("--exclude=$pattern")
    done
    
    log_info "rsync选项: ${rsync_options[*]}"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        # 执行rsync
        local start_time
        start_time=$(date +%s)
        
        log_info "开始rsync传输..."
        
        # 立即更新进度状态
        update_progress "syncing" 2 "Starting rsync data transfer"
        
        # 使用改进的进度监控
        local progress_pipe="/tmp/rsync_progress_$$"
        
        if [[ "$REDUCED_LOGGING" == "true" ]]; then
            # 减少日志模式：使用定时监控
            log_info "使用减少日志模式执行rsync"
            
            # 启动定时进度更新
            (
                local sync_start=$(date +%s)
                while kill -0 $$ 2>/dev/null; do
                    sleep 60
                    local elapsed=$(($(date +%s) - sync_start))
                    local progress=$((5 + elapsed / 120))  # 每2分钟增加1%
                    if [[ $progress -gt 70 ]]; then progress=70; fi
                    update_progress "syncing" "$progress" "Transfer in progress, elapsed: ${elapsed}s"
                    log_info "Transfer progress: ${progress}%, elapsed: ${elapsed}s"
                done
            ) &
            local monitor_pid=$!
            
            if sudo rsync "${rsync_options[@]}" \
                --log-file="$LOG_DIR/rsync.log" \
                "$SOURCE_MOUNT_POINT/" "$TARGET_MOUNT_POINT/"; then
                
                kill $monitor_pid 2>/dev/null || true
                local end_time=$(date +%s)
                local duration=$((end_time - start_time))
                
                log_success "数据同步完成! 耗时: ${duration}秒"
                update_progress "sync_completed" 80 "Data synchronization completed in ${duration}s"
            else
                kill $monitor_pid 2>/dev/null || true
                log_error "数据同步失败"
                exit 1
            fi
        else
            # 标准模式：带详细进度监控
            mkfifo "$progress_pipe"
            
            # 启动进度监控（后台进程）
            monitor_rsync_progress "$progress_pipe" &
            local monitor_pid=$!
            
            if sudo rsync "${rsync_options[@]}" \
                --log-file="$LOG_DIR/rsync.log" \
                --out-format="%t %f %b" \
                "$SOURCE_MOUNT_POINT/" "$TARGET_MOUNT_POINT/" \
                2>&1 | tee "$progress_pipe"; then
                
                local end_time=$(date +%s)
                local duration=$((end_time - start_time))
                
                log_success "数据同步完成! 耗时: ${duration}秒"
                update_progress "sync_completed" 80 "Data synchronization completed in ${duration}s"
            else
                log_error "数据同步失败"
                kill $monitor_pid 2>/dev/null || true
                rm -f "$progress_pipe"
                exit 1
            fi
            
            # 清理进度监控
            kill $monitor_pid 2>/dev/null || true
            rm -f "$progress_pipe"
        fi
        
        # 用户映射已禁用，跳过权限修正
        log_info "跳过权限修正，保留源文件原始属性"
        
    else
        log_info "[DRY RUN] 将执行数据同步"
        log_info "[DRY RUN] rsync命令: sudo rsync ${rsync_options[*]} $SOURCE_MOUNT_POINT/ $TARGET_MOUNT_POINT/"
        update_progress "dry_run_completed" 100 "Dry run completed"
    fi
}

# 权限修正函数（备用方案）- 优化版本
fix_ownership_post_sync() {
    log_info "执行权限修正，确保所有文件属于 $TARGET_USER_NAME:$TARGET_GROUP_NAME..."
    update_progress "fixing_ownership" 85 "Fixing file ownership"
    
    local start_time
    start_time=$(date +%s)
    
    # 批量修正所有权（更高效的方法）
    log_info "开始批量修正所有权..."
    
    # 使用单个find命令同时处理文件和目录，减少系统调用
    if find "$TARGET_MOUNT_POINT" \( -type f -o -type d \) \
        -exec sudo chown "$TARGET_USER_ID:$TARGET_GROUP_ID" {} + 2>/dev/null; then
        log_info "所有权修正完成"
    else
        log_warning "批量修正遇到问题，尝试分批处理..."
        
        # 分批处理，减少内存使用
        find "$TARGET_MOUNT_POINT" \( -type f -o -type d \) -print0 | \
        xargs -0 -n "$BATCH_SIZE" -P 4 sudo chown "$TARGET_USER_ID:$TARGET_GROUP_ID" 2>/dev/null || \
        log_warning "部分文件可能无法修正所有权"
    fi
    
    local end_time
    end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_success "权限修正完成! 耗时: ${duration}秒"
    update_progress "ownership_fixed" 90 "File ownership correction completed"
}

# 监控rsync进度 - 改进版本
monitor_rsync_progress() {
    local pipe="$1"
    local completed_files=0
    local transferred_size=0
    local last_update=0
    local start_time=$(date +%s)
    local update_interval=30  # 每30秒更新一次
    
    log_info "开始监控rsync进度..."
    
    while IFS= read -r line < "$pipe"; do
        local current_time=$(date +%s)
        
        # 解析rsync输出格式: 时间 文件名 字节数
        if [[ "$line" =~ ^[0-9]{4}/[0-9]{2}/[0-9]{2}[[:space:]][0-9]{2}:[0-9]{2}:[0-9]{2}[[:space:]] ]]; then
            ((completed_files++))
            
            # 提取文件大小（如果有）
            local bytes=$(echo "$line" | awk '{print $NF}' | grep -o '[0-9]\+' || echo "0")
            if [[ "$bytes" =~ ^[0-9]+$ ]]; then
                transferred_size=$((transferred_size + bytes))
            fi
            
            # 每30秒或每1000个文件更新一次
            if ((completed_files % 1000 == 0)) || ((current_time - last_update >= update_interval)); then
                local elapsed=$((current_time - start_time))
                local progress_percent=5  # 基础进度，表示在进行中
                
                # 估算进度（基于时间和文件数）
                if [[ $elapsed -gt 300 ]]; then  # 5分钟后开始估算
                    progress_percent=$((5 + (elapsed - 300) / 100))  # 每100秒增加1%
                    if [[ $progress_percent -gt 75 ]]; then
                        progress_percent=75  # 最多75%，留给验证阶段
                    fi
                fi
                
                local size_mb=$((transferred_size / 1024 / 1024))
                update_progress "syncing" "$progress_percent" "Processed $completed_files files, ${size_mb}MB transferred"
                
                if [[ "$REDUCED_LOGGING" == "false" ]]; then
                    log_progress "已处理文件: $completed_files, 传输: ${size_mb}MB, 耗时: ${elapsed}s"
                fi
                
                last_update=$current_time
            fi
        elif [[ "$line" == *"speedup is"* ]]; then
            # rsync完成标志
            log_info "rsync传输完成标志检测到"
            update_progress "sync_completed" 75 "Data transfer completed, $completed_files files processed"
            break
        fi
    done
    
    log_info "rsync进度监控结束"
}

# 数据完整性验证
verify_migration() {
    if [[ "$SKIP_VERIFICATION" == "true" ]]; then
        log_warning "跳过数据完整性验证"
        return 0
    fi
    
    log_info "开始数据完整性验证..."
    update_progress "verifying" 0 "Starting data verification"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        # 使用专门的验证脚本
        if [[ -f "$SCRIPT_DIR/efs-migration-verify.sh" ]]; then
            log_info "运行数据验证脚本..."
            if bash "$SCRIPT_DIR/efs-migration-verify.sh" \
                --source "$SOURCE_MOUNT_POINT" \
                --target "$TARGET_MOUNT_POINT" \
                --log-dir "$LOG_DIR"; then
                log_success "数据完整性验证通过"
                update_progress "verification_completed" 100 "Data verification completed successfully"
            else
                log_error "数据完整性验证失败"
                exit 1
            fi
        else
            # 基础验证 - 智能选择验证模式
            if [[ "$QUICK_VERIFY" == "true" ]]; then
                log_info "执行快速验证模式（仅文件计数）..."
            else
                log_info "执行标准验证模式（文件计数 + 大小检查）..."
                log_warning "注意：大小检查可能需要较长时间，建议使用 --quick-verify 选项"
            fi
            
            if [[ "$QUICK_VERIFY" == "true" ]]; then
                # 快速模式：只统计文件数量
                {
                    log_info "统计源文件数量..."
                    source_count=$(sudo find "$SOURCE_MOUNT_POINT" -type f | wc -l)
                    echo "source_count=$source_count" > /tmp/source_stats.$$
                } &
                local source_pid=$!
                
                {
                    log_info "统计目标文件数量..."
                    target_count=$(sudo find "$TARGET_MOUNT_POINT" -type f | wc -l)
                    echo "target_count=$target_count" > /tmp/target_stats.$$
                } &
                local target_pid=$!
            else
                # 标准模式：统计文件数量和大小
                {
                    log_info "统计源文件数量和大小..."
                    source_count=$(sudo find "$SOURCE_MOUNT_POINT" -type f | wc -l)
                    source_size=$(sudo du -sb "$SOURCE_MOUNT_POINT" | cut -f1)
                    echo "source_count=$source_count" > /tmp/source_stats.$$
                    echo "source_size=$source_size" >> /tmp/source_stats.$$
                } &
                local source_pid=$!
                
                {
                    log_info "统计目标文件数量和大小..."
                    target_count=$(sudo find "$TARGET_MOUNT_POINT" -type f | wc -l)
                    target_size=$(sudo du -sb "$TARGET_MOUNT_POINT" | cut -f1)
                    echo "target_count=$target_count" > /tmp/target_stats.$$
                    echo "target_size=$target_size" >> /tmp/target_stats.$$
                } &
                local target_pid=$!
            fi
            
            # 等待两个进程完成
            wait $source_pid $target_pid
            
            # 读取统计结果
            source /tmp/source_stats.$$
            source /tmp/target_stats.$$
            rm -f /tmp/source_stats.$$ /tmp/target_stats.$$
            
            # 验证结果
            if [[ "$QUICK_VERIFY" == "true" ]]; then
                log_info "文件计数 - 源: $source_count, 目标: $target_count"
                
                if [[ "$source_count" == "$target_count" ]]; then
                    log_success "快速验证通过：文件数量匹配 ($source_count 个文件)"
                    update_progress "verification_completed" 100 "Quick verification completed"
                    log_info "提示：如需完整大小验证，请在迁移后单独运行详细验证脚本"
                else
                    log_error "快速验证失败：文件数量不匹配"
                    log_error "文件计数差异: $((source_count - target_count))"
                    
                    if [[ "$FORCE" == "true" ]]; then
                        log_warning "强制模式：忽略文件计数差异，继续执行"
                    else
                        exit 1
                    fi
                fi
            else
                log_info "文件计数 - 源: $source_count, 目标: $target_count"
                log_info "总大小 - 源: $source_size bytes, 目标: $target_size bytes"
                
                if [[ "$source_count" == "$target_count" && "$source_size" == "$target_size" ]]; then
                    log_success "完整验证通过：文件数量和大小都匹配"
                    update_progress "verification_completed" 100 "Full verification completed"
                else
                    log_error "完整验证失败"
                    if [[ "$source_count" != "$target_count" ]]; then
                        log_error "文件计数差异: $((source_count - target_count))"
                    fi
                    if [[ "$source_size" != "$target_size" ]]; then
                        log_error "大小差异: $((source_size - target_size)) bytes"
                    fi
                    
                    if [[ "$FORCE" == "true" ]]; then
                        log_warning "强制模式：忽略验证差异，继续执行"
                    else
                        exit 1
                    fi
                fi
            fi
        fi
    else
        log_info "[DRY RUN] 将执行数据完整性验证"
        update_progress "dry_run_verification" 100 "Dry run verification completed"
    fi
}

# 清理挂载点
cleanup_mounts() {
    log_info "清理挂载点..."
    
    local mount_points=("$SOURCE_MOUNT_POINT" "$TARGET_MOUNT_POINT")
    
    for mount_point in "${mount_points[@]}"; do
        if mountpoint -q "$mount_point" 2>/dev/null; then
            log_info "卸载: $mount_point"
            if sudo umount "$mount_point" 2>/dev/null; then
                log_success "卸载成功: $mount_point"
            else
                log_warning "卸载失败: $mount_point (可能需要手动处理)"
            fi
        fi
    done
}

# 生成迁移报告
generate_report() {
    log_info "生成迁移报告..."
    
    local report_file="$LOG_DIR/migration-report-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
EFS跨账号迁移报告
=====================================

迁移信息:
- 源EFS: $SOURCE_EFS_ID ($SOURCE_REGION)
- 目标EFS: $TARGET_EFS_ID ($TARGET_REGION)
- 执行时间: $(date)
- 日志文件: $MAIN_LOG

迁移状态: $(if [[ "$DRY_RUN" == "true" ]]; then echo "预演模式"; else echo "实际执行"; fi)

详细日志请查看: $MAIN_LOG
错误日志请查看: $ERROR_LOG

EOF
    
    # 添加rsync统计信息（如果存在）
    if [[ -f "$LOG_DIR/rsync.log" ]]; then
        echo "Rsync统计信息:" >> "$report_file"
        tail -20 "$LOG_DIR/rsync.log" >> "$report_file"
    fi
    
    log_success "迁移报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始EFS跨账号迁移..."
    log_info "执行时间: $(date)"
    log_info "脚本参数: $*"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_warning "运行在预演模式"
    fi
    
    # 检查运行状态
    check_running_status
    
    # 执行迁移步骤
    load_config
    user_confirmation
    update_progress "starting" 5 "Migration started"
    
    mount_efs
    update_progress "mounted" 10 "File systems mounted"
    
    pre_migration_check
    update_progress "pre_check_completed" 15 "Pre-migration checks completed"
    
    execute_migration
    update_progress "migration_completed" 80 "Data migration completed"
    
    verify_migration
    update_progress "verification_completed" 95 "Verification completed"
    
    cleanup_mounts
    generate_report
    update_progress "completed" 100 "Migration completed successfully"
    
    # 移除PID文件
    rm -f "$PID_FILE"
    
    log_success "EFS迁移完成!"
    
    echo ""
    echo "=========================================="
    echo "迁移完成摘要:"
    echo "- 源EFS: $SOURCE_EFS_ID"
    echo "- 目标EFS: $TARGET_EFS_ID"
    echo "- 日志文件: $MAIN_LOG"
    echo "- 进度文件: $PROGRESS_FILE"
    echo "=========================================="
}

# 信号处理
cleanup_on_signal() {
    log_warning "接收到中断信号，正在清理..."
    cleanup_mounts
    rm -f "$PID_FILE"
    update_progress "interrupted" 0 "Migration interrupted by signal"
    exit 130
}

trap cleanup_on_signal SIGINT SIGTERM

# 执行主函数
main "$@"