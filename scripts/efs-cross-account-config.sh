#!/bin/bash

#=====================================================
# EFS跨账号迁移 - 环境配置和权限设置脚本
# 用途：配置跨账号EFS访问权限和网络环境
# 执行位置：目标账号(************)的ap-east-2区域EC2
#=====================================================

set -euo pipefail

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 配置变量
SOURCE_ACCOUNT="************"
SOURCE_REGION="ap-northeast-1"
TARGET_ACCOUNT="************"
TARGET_REGION="ap-east-2"

# EFS配置 (需要用户提供实际的EFS ID)
SOURCE_EFS_ID="${SOURCE_EFS_ID:-}"
TARGET_EFS_ID="${TARGET_EFS_ID:-}"

# 挂载点配置
SOURCE_MOUNT_POINT="/mnt/source-efs"
TARGET_MOUNT_POINT="/mnt/target-efs"

# 目标身份配置
TARGET_USER_ID=101
TARGET_GROUP_ID=101
TARGET_USER_NAME="odoo"
TARGET_GROUP_NAME="odoo"

# VPC对等连接配置
VPC_PEERING_CONNECTION_ID="${VPC_PEERING_CONNECTION_ID:-}"

# 错误处理函数
handle_error() {
    local line_no=$1
    local error_code=$2
    log_error "脚本执行失败 at line $line_no with exit code $error_code"
    exit $error_code
}

trap 'handle_error ${LINENO} $?' ERR

# 帮助函数
show_help() {
    cat << EOF
EFS跨账号迁移配置脚本

用法:
    $0 [选项]

必需环境变量:
    SOURCE_EFS_ID              源EFS ID (fs-xxxxxxxxx)
    TARGET_EFS_ID              目标EFS ID (fs-xxxxxxxxx)
    VPC_PEERING_CONNECTION_ID  VPC对等连接ID (pcx-xxxxxxxxx)

可选环境变量:
    DRY_RUN                    预演模式 (true/false, 默认: false)

示例:
    export SOURCE_EFS_ID="fs-01234567890abcdef"
    export TARGET_EFS_ID="fs-fedcba0987654321f"
    export VPC_PEERING_CONNECTION_ID="pcx-1234567890abcdef0"
    $0

选项:
    -h, --help                 显示此帮助信息
    -d, --dry-run             预演模式，仅检查配置
    -v, --verbose             详细输出
EOF
}

# 参数解析
DRY_RUN=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            set -x
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
check_required_params() {
    log_info "检查必需参数..."
    
    local missing_params=()
    
    if [[ -z "$SOURCE_EFS_ID" ]]; then
        missing_params+=("SOURCE_EFS_ID")
    fi
    
    if [[ -z "$TARGET_EFS_ID" ]]; then
        missing_params+=("TARGET_EFS_ID")
    fi
    
    if [[ -z "$VPC_PEERING_CONNECTION_ID" ]]; then
        missing_params+=("VPC_PEERING_CONNECTION_ID")
    fi
    
    if [[ ${#missing_params[@]} -gt 0 ]]; then
        log_error "缺少必需的环境变量: ${missing_params[*]}"
        show_help
        exit 1
    fi
    
    log_success "所有必需参数已设置"
}

# 检查AWS CLI和权限
check_aws_environment() {
    log_info "检查AWS环境..."
    
    # 检查AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI未安装"
        exit 1
    fi
    
    # 检查AWS凭证
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS凭证未配置或无效"
        exit 1
    fi
    
    # 验证当前账号和区域
    local current_account=$(aws sts get-caller-identity --query Account --output text)
    local current_region=$(ec2-metadata --availability-zone | sed 's/placement: \(.*\).$/\1/')
    
    if [[ "$current_account" != "$TARGET_ACCOUNT" ]]; then
        log_error "当前AWS账号 ($current_account) 与目标账号 ($TARGET_ACCOUNT) 不匹配"
        exit 1
    fi
    
    if [[ "$current_region" != "$TARGET_REGION" ]]; then
        log_warning "当前AWS区域 ($current_region) 与目标区域 ($TARGET_REGION) 不匹配"
        log_info "设置区域为 $TARGET_REGION"
        export AWS_DEFAULT_REGION=$TARGET_REGION
    fi
    
    log_success "AWS环境检查通过"
}

# 检查NFS工具
check_nfs_tools() {
    log_info "检查NFS工具..."
    
    if ! command -v mount.nfs4 &> /dev/null; then
        log_info "安装NFS工具..."
        if [[ "$DRY_RUN" == "false" ]]; then
            if command -v yum &> /dev/null; then
                sudo yum install -y nfs-utils
            elif command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y nfs-common
            else
                log_error "无法识别的包管理器，请手动安装nfs-utils"
                exit 1
            fi
        else
            log_info "[DRY RUN] 将安装NFS工具"
        fi
    fi
    
    if ! command -v rsync &> /dev/null; then
        log_info "安装rsync..."
        if [[ "$DRY_RUN" == "false" ]]; then
            if command -v yum &> /dev/null; then
                sudo yum install -y rsync
            elif command -v apt-get &> /dev/null; then
                sudo apt-get install -y rsync
            fi
        else
            log_info "[DRY RUN] 将安装rsync"
        fi
    fi
    
    log_success "NFS工具检查完成"
}

# 验证网络连通性
check_network_connectivity() {
    log_info "检查VPC对等连接状态..."
    
    # 获取VPC对等连接信息
    local peering_info
    peering_info=$(aws ec2 describe-vpc-peering-connections \
        --vpc-peering-connection-ids "$VPC_PEERING_CONNECTION_ID" \
        --query 'VpcPeeringConnections[0]' \
        --output json 2>/dev/null || true)
    
    if [[ -z "$peering_info" || "$peering_info" == "null" ]]; then
        log_error "无法找到VPC对等连接 $VPC_PEERING_CONNECTION_ID"
        exit 1
    fi
    
    local peering_state
    peering_state=$(echo "$peering_info" | jq -r '.Status.Code')
    
    if [[ "$peering_state" != "active" ]]; then
        log_error "VPC对等连接状态异常: $peering_state (期望: active)"
        exit 1
    fi
    
    log_success "VPC对等连接状态正常"
    
    # 检查源EFS的网络连通性
    log_info "检查源EFS网络连通性..."
    local source_efs_dns="$SOURCE_EFS_ID.efs.$SOURCE_REGION.amazonaws.com"
    
    if timeout 10 nc -z "172.31.26.141" 2049 2>/dev/null; then
        log_success "源EFS网络连通性正常"
    else
        log_warning "无法连接到源EFS，可能需要配置安全组或路由表"
        log_info "源EFS DNS: $source_efs_dns"
        log_info "请确保以下配置正确："
        log_info "1. 源EFS安全组允许来自目标VPC的NFS访问 (端口2049)"
        log_info "2. 路由表包含通过VPC对等连接的路由"
        log_info "3. 目标EC2安全组允许出站NFS流量"
    fi
}

# 验证EFS访问权限
check_efs_access() {
    log_info "检查EFS访问权限..."
    
    # 检查目标EFS
    local target_efs_info
    target_efs_info=$(aws efs describe-file-systems \
        --file-system-id "$TARGET_EFS_ID" \
        --query 'FileSystems[0]' \
        --output json 2>/dev/null || true)
    
    if [[ -z "$target_efs_info" || "$target_efs_info" == "null" ]]; then
        log_error "无法访问目标EFS $TARGET_EFS_ID"
        exit 1
    fi
    
    local target_efs_state
    target_efs_state=$(echo "$target_efs_info" | jq -r '.LifeCycleState')
    
    if [[ "$target_efs_state" != "available" ]]; then
        log_error "目标EFS状态异常: $target_efs_state (期望: available)"
        exit 1
    fi
    
    log_success "目标EFS访问权限正常"
    
    # 检查源EFS访问（通过跨账号角色）
    log_info "尝试获取源EFS信息..."
    
    # 这里需要跨账号访问，可能需要特殊配置
    log_warning "源EFS跨账号访问需要额外配置"
    log_info "请确保源账号的EFS资源策略允许目标账号访问"
    log_info "或者使用跨账号IAM角色"
}

# 创建挂载点
create_mount_points() {
    log_info "创建挂载点..."
    
    local mount_points=("$SOURCE_MOUNT_POINT" "$TARGET_MOUNT_POINT")
    
    for mount_point in "${mount_points[@]}"; do
        if [[ "$DRY_RUN" == "false" ]]; then
            if [[ ! -d "$mount_point" ]]; then
                sudo mkdir -p "$mount_point"
                log_success "创建挂载点: $mount_point"
            else
                log_info "挂载点已存在: $mount_point"
            fi
        else
            log_info "[DRY RUN] 将创建挂载点: $mount_point"
        fi
    done
}

# 检查目标用户/组ID
check_target_identity() {
    log_info "检查目标身份配置..."
    
    # 检查目标用户是否存在
    if id "$TARGET_USER_NAME" &>/dev/null; then
        local existing_uid
        existing_uid=$(id -u "$TARGET_USER_NAME")
        if [[ "$existing_uid" != "$TARGET_USER_ID" ]]; then
            log_warning "用户 $TARGET_USER_NAME 存在但UID不匹配 (现有: $existing_uid, 期望: $TARGET_USER_ID)"
        else
            log_success "目标用户 $TARGET_USER_NAME ($TARGET_USER_ID) 存在且匹配"
        fi
    else
        log_warning "目标用户 $TARGET_USER_NAME 不存在"
        log_info "将在迁移过程中使用数字ID $TARGET_USER_ID"
    fi
    
    # 检查目标组是否存在
    if getent group "$TARGET_GROUP_NAME" &>/dev/null; then
        local existing_gid
        existing_gid=$(getent group "$TARGET_GROUP_NAME" | cut -d: -f3)
        if [[ "$existing_gid" != "$TARGET_GROUP_ID" ]]; then
            log_warning "组 $TARGET_GROUP_NAME 存在但GID不匹配 (现有: $existing_gid, 期望: $TARGET_GROUP_ID)"
        else
            log_success "目标组 $TARGET_GROUP_NAME ($TARGET_GROUP_ID) 存在且匹配"
        fi
    else
        log_warning "目标组 $TARGET_GROUP_NAME 不存在"
        log_info "将在迁移过程中使用数字ID $TARGET_GROUP_ID"
    fi
}

# 生成配置文件
generate_config_file() {
    log_info "生成迁移配置文件..."
    
    local config_file="./efs-migration-config.env"
    
    if [[ "$DRY_RUN" == "false" ]]; then
        cat > "$config_file" << EOF
# EFS迁移配置文件
# 由 efs-cross-account-config.sh 生成于 $(date)

# 账号信息
SOURCE_ACCOUNT="$SOURCE_ACCOUNT"
SOURCE_REGION="$SOURCE_REGION"
TARGET_ACCOUNT="$TARGET_ACCOUNT"
TARGET_REGION="$TARGET_REGION"

# EFS信息
SOURCE_EFS_ID="$SOURCE_EFS_ID"
TARGET_EFS_ID="$TARGET_EFS_ID"

# 挂载点
SOURCE_MOUNT_POINT="$SOURCE_MOUNT_POINT"
TARGET_MOUNT_POINT="$TARGET_MOUNT_POINT"

# 网络信息
VPC_PEERING_CONNECTION_ID="$VPC_PEERING_CONNECTION_ID"

# 迁移选项
RSYNC_OPTIONS="-avHAXS --numeric-ids --progress --stats"
VERIFICATION_ENABLED=true
CLEANUP_ON_SUCCESS=true

# 目标文件系统身份配置
TARGET_USER_ID=101
TARGET_GROUP_ID=101
TARGET_USER_NAME="odoo"
TARGET_GROUP_NAME="odoo"
FORCE_OWNERSHIP=true
ENABLE_USER_MAPPING=true

# 日志配置
LOG_DIR="./logs"
LOG_LEVEL="INFO"
EOF
        
        chmod 600 "$config_file"
        log_success "配置文件已生成: $config_file"
    else
        log_info "[DRY RUN] 将生成配置文件: $config_file"
    fi
}

# 主函数
main() {
    log_info "开始EFS迁移环境配置..."
    log_info "源: $SOURCE_ACCOUNT/$SOURCE_REGION"
    log_info "目标: $TARGET_ACCOUNT/$TARGET_REGION"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_warning "运行在预演模式，不会进行实际操作"
    fi
    
    # 执行检查步骤
    check_required_params
    check_aws_environment
    check_nfs_tools
    check_network_connectivity
    check_efs_access
    check_target_identity
    create_mount_points
    generate_config_file
    
    log_success "环境配置完成!"
    
    echo ""
    echo "=========================================="
    echo "下一步操作:"
    echo "1. 确认源EFS的跨账号访问权限已配置"
    echo "2. 检查网络安全组和路由表配置"
    echo "3. 运行主迁移脚本: ./efs-migration-main.sh"
    echo "=========================================="
}

# 执行主函数
main "$@"