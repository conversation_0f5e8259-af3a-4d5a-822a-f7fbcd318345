# Turnkey系统测试验证指南

本指南介绍如何使用Turnkey系统的测试验证工具，确保ECS Secrets集成正常工作。

## 概览

我们提供了三个主要的测试工具：

1. **快速检查** (`quick-turnkey-check.sh`) - 日常状态检查
2. **部署前验证** (`pre-deploy-turnkey-validation.sh`) - 全面的部署前检查
3. **集成测试** (`test-ecs-secrets-integration.sh`) - 深度功能测试

## 工具使用指南

### 1. 快速检查 (推荐日常使用)

**用途**: 快速检查Turnkey系统关键组件状态

```bash
# 检查开发环境
./scripts/quick-turnkey-check.sh dev

# 检查生产环境
./scripts/quick-turnkey-check.sh prod
```

**检查内容**:
- AWS凭证有效性
- 证书SSM参数存在性
- 数据库Secret状态
- ECS服务运行状态
- Aurora数据库状态

**输出示例**:
```
======================================
   Turnkey Quick Status Check (dev)
======================================
[CHECK] AWS Environment
[INFO] ✅ AWS credentials valid - Account: ************
[CHECK] Certificate Parameter
[INFO] ✅ Certificate parameter exists with 3 certificate(s)
[CHECK] Database Secret
[INFO] ✅ Database password secret exists
[CHECK] ECS Service Status
[INFO] ✅ ECS service healthy - 1/1 tasks running
[CHECK] Aurora Database
[INFO] ✅ Aurora database cluster available
```

### 2. 部署前验证 (部署前必须运行)

**用途**: 全面验证部署环境和配置

```bash
# 基本验证
./scripts/pre-deploy-turnkey-validation.sh

# 指定环境和区域
./scripts/pre-deploy-turnkey-validation.sh --env prod --region ap-east-2

# 启用详细调试信息
./scripts/pre-deploy-turnkey-validation.sh --debug
```

**验证内容**:
- 配置文件语法检查
- AWS环境和权限验证
- SSM参数完整性检查
- 证书数据有效性验证
- 数据库Secret验证
- ECS基础设施状态
- 安全配置验证
- 依赖服务状态
- 部署工具可用性
- Docker环境检查

**输出格式**:
```
==================================================
        TURNKEY DEPLOYMENT VALIDATION REPORT      
==================================================

Environment: dev
AWS Region:  ap-east-2

Validation Summary:
--------------------------------------------------
Total Checks: 25
Passed:      23
Failed:      0
Warnings:    2

🎉 VALIDATION PASSED - Ready for deployment!
```

### 3. 集成测试 (开发和调试使用)

**用途**: 深度测试证书处理和ECS Secrets集成功能

```bash
# 运行完整测试套件
./scripts/test-ecs-secrets-integration.sh

# 启用调试模式
./scripts/test-ecs-secrets-integration.sh --debug

# 清理测试文件
./scripts/test-ecs-secrets-integration.sh --clean
```

**测试内容**:
- CDK配置和CloudFormation模板验证
- 容器环境模拟测试
- entrypoint.sh脚本功能测试
- 环境变量数字后缀验证
- 边界情况和异常处理测试
- 向后兼容性测试
- 安全权限验证
- 性能和可靠性测试
- 端到端集成测试

## 推荐的测试流程

### 日常运维流程

```bash
# 1. 快速状态检查
./scripts/quick-turnkey-check.sh dev

# 如果有问题，运行详细验证
./scripts/pre-deploy-turnkey-validation.sh --env dev
```

### 部署前流程

```bash
# 1. 设置AWS环境
export AWS_PROFILE=your-profile
export AWS_REGION=ap-east-2

# 2. 运行部署前验证
./scripts/pre-deploy-turnkey-validation.sh --env dev

# 3. 如果验证通过，执行部署
npm run deploy:turnkey:dev

# 4. 部署后验证
./scripts/quick-turnkey-check.sh dev
```

### 开发和调试流程

```bash
# 1. 运行完整集成测试
./scripts/test-ecs-secrets-integration.sh --debug

# 2. 运行部署前验证
./scripts/pre-deploy-turnkey-validation.sh --debug

# 3. 部署和验证
npm run deploy:turnkey:dev
./scripts/quick-turnkey-check.sh dev
```

## 故障排除

### 常见问题解决

1. **AWS凭证问题**
   ```bash
   # 检查凭证
   aws sts get-caller-identity
   
   # 重新配置
   aws configure
   ```

2. **证书参数缺失**
   ```bash
   # 创建证书参数
   aws ssm put-parameter \
     --name "/turnkey/dev/certificates" \
     --value '[{"pfx_base64":"...","pfx_password":"...","sign_id":"cert1","sign_type":"invoice"}]' \
     --type "SecureString"
   ```

3. **数据库Secret缺失**
   ```bash
   # 创建数据库密码
   aws secretsmanager create-secret \
     --name "turnkey/dev/db-password" \
     --secret-string '{"password":"your-secure-password"}'
   ```

4. **ECS服务未运行**
   ```bash
   # 检查服务状态
   aws ecs describe-services --cluster yuanhui-odoo-dev --services turnkey-dev
   
   # 重新部署
   npm run deploy:turnkey:dev
   ```

### 测试失败处理

1. **查看详细错误信息**
   ```bash
   # 启用调试模式
   ./scripts/pre-deploy-turnkey-validation.sh --debug
   ```

2. **检查测试报告**
   ```bash
   # 查看JSON报告（保存在/tmp/目录）
   cat /tmp/turnkey-validation-report-*.json | jq .
   ```

3. **分步骤验证**
   ```bash
   # 先运行快速检查
   ./scripts/quick-turnkey-check.sh dev
   
   # 再运行特定测试
   ./scripts/test-ecs-secrets-integration.sh
   ```

## 测试报告说明

### 退出代码

- `0`: 所有测试通过
- `1`: 存在关键失败

### 日志级别

- `INFO`: 正常信息
- `WARN`: 警告，不影响部署但需要注意
- `ERROR`: 错误，必须修复
- `CHECK`: 正在执行的检查
- `CRITICAL`: 关键问题，阻止部署

### 报告文件

测试报告保存位置：
- 控制台输出：实时显示
- JSON报告：`/tmp/turnkey-validation-report-*.json`
- 测试结果：`/tmp/turnkey-secrets-test-*/test_report.json`

## 环境变量配置

### 必需环境变量

```bash
export AWS_PROFILE=your-aws-profile
export AWS_REGION=ap-east-2
export NODE_ENV=dev  # 或 prod
```

### 可选环境变量

```bash
export DEBUG=true              # 启用调试输出
export AWS_DEFAULT_REGION=ap-east-2  # AWS默认区域
```

## 自动化集成

### CI/CD Pipeline集成

```yaml
# GitHub Actions示例
- name: Validate Turnkey Configuration
  run: |
    ./scripts/pre-deploy-turnkey-validation.sh --env dev
    
- name: Run Integration Tests
  run: |
    ./scripts/test-ecs-secrets-integration.sh
    
- name: Deploy Turnkey
  if: success()
  run: |
    npm run deploy:turnkey:dev
    
- name: Post-deployment Check
  run: |
    ./scripts/quick-turnkey-check.sh dev
```

### 定时健康检查

```bash
# 添加到crontab进行定时检查
# 每日早上9点检查开发环境
0 9 * * * /path/to/scripts/quick-turnkey-check.sh dev >> /var/log/turnkey-check.log 2>&1
```

## 最佳实践

1. **部署前必须验证**
   - 总是在部署前运行`pre-deploy-turnkey-validation.sh`
   - 确保所有关键检查通过

2. **定期状态检查**
   - 使用`quick-turnkey-check.sh`进行日常监控
   - 在出现问题时快速定位原因

3. **开发时深度测试**
   - 修改证书处理逻辑后运行完整集成测试
   - 使用调试模式了解详细执行过程

4. **文档化问题和解决方案**
   - 记录常见问题的解决方案
   - 更新故障排除指南

5. **版本控制测试脚本**
   - 将测试脚本纳入版本控制
   - 随代码变更更新测试用例

## 支持和维护

如需帮助或报告问题：
1. 查看详细测试报告
2. 检查AWS CloudWatch日志
3. 参考故障排除指南
4. 联系DevOps团队

---

**文档版本**: v1.0  
**最后更新**: 2024-08-07