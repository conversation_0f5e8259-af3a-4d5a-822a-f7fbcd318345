# 电子发票证书管理工具套件

## 概述

本工具套件为Yuan Hui基础设施团队提供完整的电子发票证书管理解决方案，确保证书的安全存储、高效管理和可靠部署。

## 🚀 快速开始

### 1. 环境验证

```bash
# 运行完整的环境和工具测试
./scripts/test-certificate-tools.sh

# 验证示例证书格式
./scripts/manage-turnkey-certificates.sh validate examples/turnkey-certificates.json -e dev
```

### 2. 基本证书管理流程

```bash
# 第一步：准备证书文件（基于示例文件）
cp scripts/examples/turnkey-certificates.json my-certificates.json

# 第二步：编辑证书文件，替换为真实的证书数据

# 第三步：验证证书格式
./scripts/manage-turnkey-certificates.sh validate my-certificates.json -e dev

# 第四步：上传到开发环境
./scripts/manage-turnkey-certificates.sh upload my-certificates.json -e dev

# 第五步：验证上传结果
./scripts/manage-turnkey-certificates.sh list -e dev
```

## 📁 工具套件组成

### 核心工具

| 文件 | 功能 | 用途 |
|------|------|------|
| `manage-turnkey-certificates.sh` | 主管理工具 | 上传、下载、列表、验证、备份证书 |
| `certificate-utils.sh` | 证书处理工具 | PFX转换、信息提取、模板生成 |
| `test-certificate-tools.sh` | 测试验证工具 | 环境检查、工具验证 |

### 配置和示例

| 文件/目录 | 功能 |
|-----------|------|
| `examples/turnkey-certificates.json` | 证书JSON格式示例 |
| `backups/` | 自动创建的备份目录 |
| `README-turnkey-certificates.md` | 详细使用文档 |
| `CERTIFICATE-MANAGEMENT-OVERVIEW.md` | 本概览文档 |

## 🔧 工具功能矩阵

### 主管理工具功能

| 命令 | 功能描述 | 环境支持 | 安全特性 |
|------|----------|----------|----------|
| `upload` | 上传证书到SSM | dev/prod | 加密存储、覆盖确认 |
| `download` | 下载证书到本地 | dev/prod | 文件覆盖保护 |
| `list` | 列出证书信息 | dev/prod | 敏感数据隐藏 |
| `validate` | 验证证书格式 | dev/prod | 完整性检查 |
| `backup` | 备份证书数据 | dev/prod | 自动命名、版本控制 |

### 证书处理工具功能

| 命令 | 功能描述 | 输入 | 输出 |
|------|----------|------|------|
| `pfx-to-base64` | PFX转Base64 | PFX文件 | Base64文本 |
| `base64-to-pfx` | Base64转PFX | Base64文件 | PFX文件 |
| `extract-info` | 提取证书信息 | PFX+密码 | 证书详情 |
| `validate-password` | 验证密码 | PFX+密码 | 验证结果 |
| `generate-template` | 生成JSON模板 | PFX+密码+ID | JSON模板 |
| `batch-convert` | 批量转换 | PFX目录 | Base64文件 |

## 🏗️ 系统架构

```
证书管理工具套件架构
├── 用户界面层
│   ├── 命令行接口 (CLI)
│   ├── 交互式确认
│   └── 彩色日志输出
├── 业务逻辑层
│   ├── 证书验证引擎
│   ├── 格式转换器
│   ├── 安全检查器
│   └── 备份管理器
├── 数据存储层
│   ├── AWS SSM Parameter Store (加密)
│   ├── 本地备份文件
│   └── 临时工作文件
└── 基础设施层
    ├── AWS CLI 集成
    ├── OpenSSL 证书处理
    ├── jq JSON 处理
    └── 系统依赖检查
```

## 🛡️ 安全功能

### 数据保护
- ✅ **传输加密**: 所有AWS通信使用TLS加密
- ✅ **存储加密**: SSM Parameter Store使用AWS KMS加密
- ✅ **访问控制**: 基于IAM权限的细粒度访问控制
- ✅ **审计日志**: 详细的操作日志记录

### 操作安全
- ✅ **双重确认**: 危险操作需要用户确认
- ✅ **预览模式**: --dry-run支持操作预览
- ✅ **权限验证**: 自动检查AWS权限
- ✅ **格式验证**: 多层次的数据格式验证

### 数据完整性
- ✅ **格式校验**: JSON结构和必要字段验证
- ✅ **编码验证**: Base64格式完整性检查
- ✅ **证书验证**: OpenSSL证书有效性检查
- ✅ **过期检查**: 自动证书过期状态监控

## 📊 使用场景

### 日常运维场景

#### 1. 新证书部署
```bash
# 场景：部署新的企业证书到生产环境
# 工具流程：validation → backup → upload → verification

# 步骤1：验证新证书
./scripts/manage-turnkey-certificates.sh validate new-cert.json -e prod --dry-run

# 步骤2：备份现有证书
./scripts/manage-turnkey-certificates.sh backup pre_update_$(date +%Y%m%d) -e prod

# 步骤3：上传新证书
./scripts/manage-turnkey-certificates.sh upload new-cert.json -e prod

# 步骤4：验证部署结果
./scripts/manage-turnkey-certificates.sh list -e prod
```

#### 2. 证书过期监控
```bash
# 场景：定期检查证书过期状态
# 工具流程：list → validate → alert

./scripts/manage-turnkey-certificates.sh list -e prod | grep -E "(红色|黄色)"
```

#### 3. 跨环境证书同步
```bash
# 场景：将生产环境证书同步到开发环境
# 工具流程：download → upload → verify

# 从生产环境下载
./scripts/manage-turnkey-certificates.sh download /tmp/prod-certs.json -e prod

# 上传到开发环境
./scripts/manage-turnkey-certificates.sh upload /tmp/prod-certs.json -e dev

# 清理临时文件
rm /tmp/prod-certs.json
```

### 证书处理场景

#### 1. PFX证书准备
```bash
# 场景：将新的PFX证书转换为JSON格式
# 工具流程：validate → convert → template → upload

# 验证证书密码
./scripts/certificate-utils.sh validate-password new-cert.pfx "password123"

# 生成JSON模板
./scripts/certificate-utils.sh generate-template new-cert.pfx "password123" "company_cert_002" cert.json

# 上传证书
./scripts/manage-turnkey-certificates.sh upload cert.json -e dev
```

#### 2. 批量证书处理
```bash
# 场景：处理多个PFX证书文件
# 工具流程：batch-convert → template-generation → validation

# 批量转换PFX到Base64
./scripts/certificate-utils.sh batch-convert ./pfx-certificates ./base64-output

# 手动创建JSON文件或使用生成的模板
```

## 🔄 运维最佳实践

### 1. 证书生命周期管理

```bash
# 定期备份策略（建议每月执行）
#!/bin/bash
MONTH=$(date +%Y%m)
./scripts/manage-turnkey-certificates.sh backup "monthly_backup_$MONTH" -e prod
./scripts/manage-turnkey-certificates.sh backup "monthly_backup_$MONTH" -e dev
```

### 2. 证书监控脚本

```bash
# 证书过期监控（建议每周执行）
#!/bin/bash
for env in dev prod; do
    echo "检查 $env 环境证书状态..."
    ./scripts/manage-turnkey-certificates.sh validate -e "$env"
    ./scripts/manage-turnkey-certificates.sh list -e "$env"
done
```

### 3. 环境隔离策略

```bash
# 确保环境隔离的部署脚本
deploy_certificate() {
    local cert_file=$1
    local environment=$2
    
    echo "部署证书到 $environment 环境..."
    
    # 预检查
    ./scripts/manage-turnkey-certificates.sh validate "$cert_file" -e "$environment" --dry-run
    
    # 备份
    ./scripts/manage-turnkey-certificates.sh backup "pre_deploy_$(date +%Y%m%d_%H%M)" -e "$environment"
    
    # 部署
    ./scripts/manage-turnkey-certificates.sh upload "$cert_file" -e "$environment" --force
    
    # 验证
    ./scripts/manage-turnkey-certificates.sh validate -e "$environment"
    
    echo "$environment 环境证书部署完成"
}
```

## 🚨 故障排除指南

### 常见问题诊断

#### 1. AWS相关问题

**问题**: `AWS凭证配置错误`
```bash
# 诊断步骤
aws sts get-caller-identity
aws configure list

# 解决方案
aws configure
# 或设置环境变量
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret
export AWS_DEFAULT_REGION=ap-east-2
```

**问题**: `AccessDenied: User is not authorized`
```bash
# 检查当前用户权限
aws iam get-user
aws iam list-attached-user-policies --user-name your-username

# 需要的最小权限
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "ssm:PutParameter",
                "ssm:GetParameter",
                "ssm:DeleteParameter"
            ],
            "Resource": "arn:aws:ssm:*:*:parameter/yuanhui/einvoice-turnkey/*"
        }
    ]
}
```

#### 2. 证书格式问题

**问题**: `JSON格式错误`
```bash
# 验证JSON格式
jq . your-certificate.json

# 使用模板修复
cp scripts/examples/turnkey-certificates.json your-certificate.json
```

**问题**: `pfx_base64不是有效的base64格式`
```bash
# 重新生成Base64
./scripts/certificate-utils.sh pfx-to-base64 certificate.pfx

# 验证Base64格式
echo "your_base64_string" | base64 -d > /dev/null && echo "格式正确"
```

### 日志分析

**启用详细日志**
```bash
# 详细输出模式
./scripts/manage-turnkey-certificates.sh upload cert.json -e dev -v

# 保存日志到文件
./scripts/manage-turnkey-certificates.sh upload cert.json -e dev -v --log-file /tmp/cert-debug.log
```

## 📈 性能优化

### 大型证书处理

```bash
# 对于大量证书的批量操作
# 1. 使用批处理工具
./scripts/certificate-utils.sh batch-convert large-pfx-directory output-directory

# 2. 分批上传避免超时
for cert in cert1.json cert2.json cert3.json; do
    ./scripts/manage-turnkey-certificates.sh upload "$cert" -e dev
    sleep 2  # 避免API限制
done
```

### 缓存策略

```bash
# 利用本地备份避免重复下载
if [[ ! -f "backups/latest-prod.json" ]]; then
    ./scripts/manage-turnkey-certificates.sh download backups/latest-prod.json -e prod
fi
```

## 🔮 未来扩展

### 计划功能
- [ ] **GUI界面**: 基于Web的证书管理界面
- [ ] **API集成**: REST API支持自动化集成
- [ ] **通知系统**: 证书过期邮件/Slack通知
- [ ] **批量导入**: Excel/CSV批量证书导入
- [ ] **版本历史**: 证书变更历史追踪
- [ ] **合规检查**: 证书合规性自动检查
- [ ] **性能监控**: 证书操作性能监控
- [ ] **多云支持**: 支持其他云提供商

### 集成计划
- [ ] **CI/CD集成**: GitHub Actions自动化
- [ ] **监控集成**: Prometheus/CloudWatch指标
- [ ] **告警集成**: PagerDuty/OpsGenie集成
- [ ] **审计集成**: CloudTrail/审计日志集成

## 📞 支持联系

- **团队**: Yuan Hui Infrastructure Team
- **文档**: 本目录下的README-turnkey-certificates.md
- **问题反馈**: 通过内部工单系统

---

**版本**: v1.0.0  
**更新日期**: 2024-08-07  
**维护人员**: Yuan Hui Infrastructure Team