{"name": "yuanhui-odoo-iac", "version": "0.1.0", "description": "元晖Odoo应用服务AWS基础设施即代码项目", "author": "<EMAIL>", "bin": {"iac": "dist/bin/iac.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "clean": "rm -rf dist", "prebuild": "npm run clean", "deploy": "scripts/deploy.sh", "deploy:help": "scripts/deploy.sh --help", "deploy:dev": "scripts/deploy.sh dev", "deploy:prod": "scripts/deploy.sh prod", "deploy:dev:dry": "scripts/deploy.sh dev --dry-run", "deploy:prod:dry": "scripts/deploy.sh prod --dry-run", "deploy:core": "scripts/deploy.sh dev --group core", "deploy:core:prod": "scripts/deploy.sh prod --group core", "deploy:apps": "scripts/deploy.sh dev --group apps", "deploy:apps:prod": "scripts/deploy.sh prod --group apps", "deploy:logs": "scripts/deploy.sh dev --logs", "deploy:parallel": "scripts/deploy.sh dev --parallel", "deploy:fast": "scripts/deploy.sh dev --parallel --logs", "deploy:safe": "scripts/deploy.sh dev --auto-rollback", "deploy:network": "scripts/deploy.sh dev --stacks Network", "deploy:database": "scripts/deploy.sh dev --stacks AuroraDatabase", "deploy:application": "scripts/deploy.sh dev --stacks Application", "deploy:turnkey": "scripts/deploy.sh dev --stacks Turnkey", "deploy:dns": "scripts/deploy.sh dev --stacks Dns", "deploy:monitoring": "scripts/deploy.sh dev --stacks Monitoring"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "jest": "^29.7.0", "ts-jest": "^29.2.5", "aws-cdk": "2.1019.1", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"aws-cdk-lib": "2.202.0", "constructs": "^10.0.0"}}