-- Airflow生产环境数据库手动创建脚本
-- 连接到PostgreSQL主数据库执行

-- 1. 创建Airflow用户
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'airflow_user') THEN
    CREATE USER airflow_user WITH PASSWORD 'YOUR_AIRFLOW_PASSWORD_HERE';
  ELSE
    ALTER USER airflow_user WITH PASSWORD 'YOUR_AIRFLOW_PASSWORD_HERE';
  END IF;
END
$$;

-- 2. 创建Airflow数据库
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_database WHERE datname = 'airflow') THEN
    CREATE DATABASE "airflow";
  END IF;
END
$$;

-- 3. 授予权限
GRANT ALL PRIVILEGES ON DATABASE "airflow" TO airflow_user;
ALTER DATABASE "airflow" OWNER TO airflow_user;

-- 4. 连接到airflow数据库并授予schema权限
\c airflow

-- 授予public schema的所有权限
GRANT ALL ON SCHEMA public TO airflow_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO airflow_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO airflow_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO airflow_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO airflow_user;

-- 5. 验证创建结果
SELECT 'Database created successfully' as status;
SELECT datname, datdba, datacl FROM pg_database WHERE datname = 'airflow';
SELECT rolname, rolsuper, rolcreatedb, rolcreaterole FROM pg_roles WHERE rolname = 'airflow_user';