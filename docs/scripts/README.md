# 脚本文档

## 概述

本目录包含Yuan Hui基础设施项目的所有自动化脚本，涵盖部署、验证、监控和维护等各个方面。这些脚本简化了日常运维工作，提高了部署效率和可靠性。

## 脚本分类

### 🚀 部署脚本
| 脚本名称 | 功能描述 | 使用频率 |
|----------|----------|----------|
| `deploy.sh` | 主部署脚本，支持环境、栈组、并行部署 | 日常 |
| `deploy-network-infrastructure.sh` | 部署网络基础设施 | 初始化 |
| `deploy-security-stack.sh` | 部署GitHub Actions安全栈 | 初始化 |

### ✅ 验证脚本
| 脚本名称 | 功能描述 | 使用频率 |
|----------|----------|----------|
| `check-permissions.sh` | 检查AWS权限和配置 | 部署前必需 |
| `test-network-routing.sh` | 验证网络路由和连接性 | 部署后 |
| `verify-ssl-certificates.sh` | 验证SSL证书配置 | 定期 |
| `test-airflow-deployment.sh` | 验证Airflow部署状态 | 部署后 |
| `verify-airflow-database.sh` | 验证Airflow数据库配置 | 故障排除 |

### 📊 监控脚本
| 脚本名称 | 功能描述 | 使用频率 |
|----------|----------|----------|
| `quick-cost-check.sh` | 快速成本检查和分析 | 定期 |
| `monitor-cloudwatch-costs.sh` | 详细CloudWatch费用监控 | 定期 |
| `diagnose-cloudwatch-costs.sh` | 诊断CloudWatch费用异常 | 故障排除 |

### 🔧 维护脚本
| 脚本名称 | 功能描述 | 使用频率 |
|----------|----------|----------|
| `run-airflow-init.sh` | 初始化Airflow数据库 | 初始化 |
| `cleanup-resources.sh` | 清理过期资源 | 定期 |
| `backup-configuration.sh` | 备份配置和数据 | 定期 |

## 主要脚本详解

### 1. 部署脚本 (deploy.sh)

主要的部署自动化脚本，支持智能部署、依赖管理和错误处理。

#### 基本用法
```bash
# 基础部署
./scripts/deploy.sh dev                    # 部署开发环境所有栈
./scripts/deploy.sh prod --dry-run         # 预览生产环境部署

# 分组部署
./scripts/deploy.sh dev --group core       # 基础设施栈组
./scripts/deploy.sh dev --group apps       # 应用服务栈组

# 指定栈部署
./scripts/deploy.sh dev --stacks Network,Database,Application
```

#### 高级选项
```bash
# 并行部署和实时日志
./scripts/deploy.sh dev --parallel --logs

# 超时和自动回滚
./scripts/deploy.sh prod --timeout 3600 --auto-rollback

# 跳过确认（自动化场景）
./scripts/deploy.sh dev --no-confirm

# 继续部署（忽略失败）
./scripts/deploy.sh dev --continue-on-error
```

#### 栈组织
- **Core栈组**: Network, Ecs, ServiceConnect, AuroraDatabase, Redis, RabbitMQ, LoadBalancer, Airflow, Security
- **Apps栈组**: Application, ClaudeRelay, CloudFront, OpenZiti, Monitoring

#### 部署特性
- **智能依赖解析**: 自动确定栈部署顺序
- **并行部署**: 支持独立栈的并行部署
- **错误处理**: 智能重试和失败回滚
- **实时监控**: 显示部署进度和CloudFormation事件

### 2. 权限检查脚本 (check-permissions.sh)

部署前必须运行的权限验证脚本。

```bash
# 检查所有必需权限
./scripts/check-permissions.sh

# 输出示例：
# ✅ AWS CLI配置正确
# ✅ CloudFormation权限充足
# ✅ ECS权限充足
# ✅ RDS权限充足
# ⚠️  IAM权限可能不足
# ❌ Secrets Manager权限缺失
```

#### 检查项目
- AWS CLI配置和凭证
- CloudFormation栈管理权限
- EC2和VPC网络权限
- ECS容器服务权限
- RDS数据库权限
- IAM角色管理权限
- Secrets Manager密钥权限
- Certificate Manager证书权限

### 3. 网络验证脚本 (test-network-routing.sh)

验证网络配置和连接性的综合测试脚本。

```bash
# 测试网络配置
./scripts/test-network-routing.sh

# 测试特定环境
./scripts/test-network-routing.sh dev
./scripts/test-network-routing.sh prod
```

#### 测试项目
- VPC和子网配置
- 安全组规则
- NAT网关连接性
- 负载均衡器健康检查
- 域名解析和SSL证书
- 服务间通信

### 4. 成本监控脚本

#### 快速成本检查 (quick-cost-check.sh)
```bash
# 快速查看主要资源成本
./scripts/quick-cost-check.sh

# 输出示例：
# === AWS资源成本概览 ===
# ECS集群: $45.32/月
# Aurora数据库: $28.17/月
# 负载均衡器: $16.20/月
# NAT网关: $32.40/月
# 总计: $122.09/月
```

#### CloudWatch费用监控 (monitor-cloudwatch-costs.sh)
```bash
# 详细CloudWatch费用分析
./scripts/monitor-cloudwatch-costs.sh

# 检查特定时间段
./scripts/monitor-cloudwatch-costs.sh --days 7
./scripts/monitor-cloudwatch-costs.sh --start 2024-01-01 --end 2024-01-31
```

#### CloudWatch费用诊断 (diagnose-cloudwatch-costs.sh)
```bash
# 诊断费用异常
./scripts/diagnose-cloudwatch-costs.sh

# 输出高费用日志组和指标
# 提供优化建议
```

### 5. Airflow相关脚本

#### Airflow初始化 (run-airflow-init.sh)
```bash
# 初始化Airflow数据库和用户
./scripts/run-airflow-init.sh

# 指定环境
./scripts/run-airflow-init.sh dev
./scripts/run-airflow-init.sh prod
```

#### Airflow部署验证 (test-airflow-deployment.sh)
```bash
# 验证Airflow服务状态
./scripts/test-airflow-deployment.sh dev

# 输出示例：
# ✅ Airflow Webserver运行正常
# ✅ Airflow Scheduler运行正常
# ✅ 数据库连接正常
# ✅ Web界面可访问
```

## 脚本使用最佳实践

### 1. 执行前检查

```bash
# 设置执行权限
chmod +x scripts/*.sh

# 检查脚本语法
bash -n scripts/deploy.sh

# 查看脚本帮助
./scripts/deploy.sh --help
```

### 2. 环境变量设置

```bash
# 必需环境变量
export NODE_ENV=dev
export CDK_DEFAULT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
export CDK_DEFAULT_REGION=ap-east-2

# 可选配置变量
export DEPLOY_TIMEOUT=3600
export DEPLOY_PARALLEL=true
export DEPLOY_LOGS=true
```

### 3. 日志和调试

```bash
# 启用详细日志
./scripts/deploy.sh dev --verbose

# 保存日志到文件
./scripts/deploy.sh dev --logs 2>&1 | tee deploy.log

# 调试模式
bash -x ./scripts/deploy.sh dev
```

### 4. 错误处理

```bash
# 检查脚本退出状态
./scripts/deploy.sh dev
if [ $? -ne 0 ]; then
    echo "部署失败，请查看日志"
    exit 1
fi

# 使用set -e自动退出
set -e
./scripts/check-permissions.sh
./scripts/deploy.sh dev
```

## 自定义脚本开发

### 脚本模板

```bash
#!/bin/bash
set -euo pipefail  # 严格模式

# 脚本信息
SCRIPT_NAME="example-script"
SCRIPT_VERSION="1.0.0"
SCRIPT_DESC="Example script template"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 帮助信息
show_help() {
    cat << EOF
Usage: $0 [OPTIONS] ENVIRONMENT

$SCRIPT_DESC

ENVIRONMENT:
    dev     Development environment
    prod    Production environment

OPTIONS:
    -h, --help      Show this help message
    -v, --verbose   Enable verbose output
    --dry-run       Show what would be done without executing

Examples:
    $0 dev
    $0 prod --dry-run
    $0 dev --verbose

EOF
}

# 参数解析
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            dev|prod)
                ENVIRONMENT=$1
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    # 变量初始化
    VERBOSE=false
    DRY_RUN=false
    ENVIRONMENT=""

    # 解析参数
    parse_arguments "$@"

    # 验证必需参数
    if [[ -z "$ENVIRONMENT" ]]; then
        log_error "Environment is required"
        show_help
        exit 1
    fi

    # 脚本逻辑
    log_info "Starting $SCRIPT_NAME for $ENVIRONMENT environment"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "DRY RUN MODE - No changes will be made"
    fi
    
    # 实际工作...
    
    log_info "Completed successfully"
}

# 执行主函数
main "$@"
```

### 通用函数库

创建 `scripts/lib/common.sh` 共享函数库：

```bash
#!/bin/bash

# AWS工具函数
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI not found"
        return 1
    fi
}

get_account_id() {
    aws sts get-caller-identity --query Account --output text
}

get_stack_status() {
    local stack_name=$1
    aws cloudformation describe-stacks \
        --stack-name "$stack_name" \
        --query 'Stacks[0].StackStatus' \
        --output text 2>/dev/null || echo "NOT_FOUND"
}

# ECS工具函数
get_service_status() {
    local cluster=$1
    local service=$2
    aws ecs describe-services \
        --cluster "$cluster" \
        --services "$service" \
        --query 'services[0].status' \
        --output text
}

# 等待函数
wait_for_stack() {
    local stack_name=$1
    local timeout=${2:-1800}  # 30分钟默认超时
    
    log_info "Waiting for stack $stack_name..."
    aws cloudformation wait stack-create-complete \
        --stack-name "$stack_name" \
        --cli-read-timeout "$timeout"
}
```

## 故障排除

### 常见脚本错误

1. **权限不足**
   ```bash
   # 错误: Permission denied
   chmod +x scripts/*.sh
   ```

2. **环境变量未设置**
   ```bash
   # 检查必需变量
   echo $NODE_ENV
   echo $CDK_DEFAULT_ACCOUNT
   echo $CDK_DEFAULT_REGION
   ```

3. **AWS CLI配置问题**
   ```bash
   # 重新配置AWS CLI
   aws configure
   aws sts get-caller-identity
   ```

4. **脚本语法错误**
   ```bash
   # 检查脚本语法
   bash -n scripts/script-name.sh
   ```

### 调试技巧

```bash
# 启用调试模式
set -x

# 查看变量值
echo "Environment: $NODE_ENV"
echo "Account: $CDK_DEFAULT_ACCOUNT"

# 分步执行
bash -x scripts/deploy.sh dev --dry-run
```

### 日志分析

```bash
# 实时查看CloudFormation事件
aws cloudformation describe-stack-events \
    --stack-name YuanhuiNetwork-dev \
    --query 'StackEvents[0:10].[Timestamp,ResourceStatus,ResourceType,LogicalResourceId]' \
    --output table

# 查看ECS服务日志
aws logs tail /aws/ecs/yherp-dev --follow

# 搜索错误日志
aws logs filter-log-events \
    --log-group-name /aws/ecs/yherp-dev \
    --filter-pattern "ERROR"
```

## 维护和更新

### 定期维护

1. **每周**
   - 运行成本检查脚本
   - 检查服务健康状态
   - 清理临时资源

2. **每月**
   - 更新脚本文档
   - 审查权限配置
   - 备份重要配置

3. **每季度**
   - 脚本性能优化
   - 安全审查
   - 灾难恢复测试

### 版本管理

```bash
# 脚本版本标记
git tag -a scripts-v1.2.0 -m "Scripts version 1.2.0"
git push origin scripts-v1.2.0

# 查看脚本变更历史
git log --oneline -- scripts/
```

这个脚本文档为团队提供了完整的脚本使用指南，包括常用脚本详解、最佳实践和故障排除方法。