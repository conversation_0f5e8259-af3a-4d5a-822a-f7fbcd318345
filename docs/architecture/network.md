# 网络架构设计

## 概述

本文档描述了元晖Odoo应用服务的完整网络架构，采用多层安全防护和高可用设计，支持多域名路由、零信任网络访问和全球CDN加速。

## 网络架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        Internet                                 │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────────┐
│                  Route 53 DNS                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐              │
│  │ yh.kh2u.com │ │ dp.kh2u.com │ │ j2mall.com  │              │
│  └─────────────┘ └─────────────┘ └─────────────┘              │
└─────────────┬───────────┬───────────┬───────────────────────────┘
              │           │           │
┌─────────────┴───┐ ┌─────┴─────┐ ┌───┴──────────────┐
│   OpenZiti      │ │    WAF    │ │    CloudFront    │
│  Zero-Trust     │ │ Firewall  │ │      CDN         │
└─────────────┬───┘ └─────┬─────┘ └───┬──────────────┘
              │           │           │
              └─────────┬─┴─────────┬─┘
                        │           │
              ┌─────────┴───────────┴─────────┐
              │   Application Load Balancer   │
              │      (Multi-AZ Deployment)    │
              └─────────────┬─────────────────┘
                            │
    ┌───────────────────────┼───────────────────────┐
    │                     VPC                       │
    │  ┌─────────────┐  ┌──┴──┐  ┌─────────────┐    │
    │  │ Public      │  │ NAT │  │ Public      │    │
    │  │ Subnet      │  │ GW  │  │ Subnet      │    │
    │  │ (ALB)       │  │     │  │ (ALB)       │    │
    │  └─────────────┘  └──┬──┘  └─────────────┘    │
    │                     │                        │
    │  ┌─────────────┐  ┌──┴──┐  ┌─────────────┐    │
    │  │ Private     │  │     │  │ Private     │    │
    │  │ Subnet      │◄─┤ ECS │─►│ Subnet      │    │
    │  │ (App)       │  │     │  │ (App)       │    │
    │  └─────────────┘  └─────┘  └─────────────┘    │
    │                                               │
    │  ┌─────────────┐           ┌─────────────┐    │
    │  │ DB Subnet   │           │ DB Subnet   │    │
    │  │ (Aurora)    │◄─────────►│ (Aurora)    │    │
    │  └─────────────┘           └─────────────┘    │
    └───────────────────────────────────────────────┘
```

## 核心组件

### 1. VPC网络设计

- **网络分段**: 10.0.0.0/16 CIDR
- **多可用区**: 支持2-3个AZ的高可用部署
- **子网分层**:
  - 公有子网: 10.0.1.0/24, 10.0.2.0/24 (负载均衡器)
  - 私有子网: 10.0.11.0/24, 10.0.12.0/24 (应用服务)
  - 数据库子网: 10.0.21.0/24, 10.0.22.0/24 (Aurora)

### 2. 多域名路由配置

| 域名 | 应用 | 访问方式 | 负载均衡器 | 特殊功能 |
|------|------|----------|------------|----------|
| **yh.kh2u.com** | Yherp | 内部访问 | 内部ALB | OpenZiti零信任网络 + Longpolling |
| **dp.kh2u.com** | Yherp | 公网访问 | 公网ALB | WAF防护 + Longpolling |
| **j2mall.com** | Khmall | 电商平台 | 公网ALB | CloudFront CDN加速 + Longpolling |

### 3. 安全防护体系

#### 多层WAF防护
- **AWS托管规则**: Core Rule Set, Known Bad Inputs
- **自定义规则**: IP白名单、地理封锁、频率限制
- **实时监控**: WAF日志和CloudWatch告警

#### 零信任网络 (OpenZiti)
- **内部访问控制**: yh.kh2u.com 仅通过OpenZiti访问
- **身份验证**: 基于证书的双向认证
- **网络隔离**: 应用层面的微分段

#### SSL/TLS加密
- **证书管理**: AWS Certificate Manager自动管理
- **加密标准**: TLS 1.2+ 强制执行
- **HSTS**: HTTP严格传输安全

### 4. 负载均衡策略

#### Application Load Balancer配置
- **监听器配置**:
  - 端口80: HTTP重定向到HTTPS
  - 端口443: HTTPS应用流量
  - 端口8072: Odoo Longpolling支持

#### 目标组配置
- **健康检查**: HTTP GET /web/health
- **粘性会话**: 支持Odoo多数据库
- **连接排空**: 优雅的服务更新

### 5. CDN和缓存策略

#### CloudFront配置 (j2mall.com)
- **缓存行为**:
  - 静态资源: 1年缓存 (CSS, JS, 图片)
  - 动态内容: 1小时缓存
  - API端点: 不缓存
- **压缩**: Gzip/Brotli自动启用
- **边缘位置**: 全球200+节点

## 安全配置

### 网络安全组

#### ALB安全组
```typescript
const albSecurityGroup = new ec2.SecurityGroup(this, 'ALBSecurityGroup', {
  vpc,
  description: 'Security group for Application Load Balancer',
  allowAllOutbound: true,
});

albSecurityGroup.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(80));
albSecurityGroup.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(443));
albSecurityGroup.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(8072));
```

#### ECS服务安全组
```typescript
const ecsSecurityGroup = new ec2.SecurityGroup(this, 'ECSSecurityGroup', {
  vpc,
  description: 'Security group for ECS services',
  allowAllOutbound: true,
});

ecsSecurityGroup.addIngressRule(albSecurityGroup, ec2.Port.tcp(8069));
ecsSecurityGroup.addIngressRule(albSecurityGroup, ec2.Port.tcp(8072));
```

#### 数据库安全组
```typescript
const dbSecurityGroup = new ec2.SecurityGroup(this, 'DatabaseSecurityGroup', {
  vpc,
  description: 'Security group for Aurora database',
  allowAllOutbound: false,
});

dbSecurityGroup.addIngressRule(ecsSecurityGroup, ec2.Port.tcp(5432));
```

### WAF规则配置

#### 核心规则集
```typescript
const managedRules = [
  {
    name: 'AWS-AWSManagedRulesCommonRuleSet',
    priority: 1,
    statement: {
      managedRuleGroupStatement: {
        vendorName: 'AWS',
        name: 'AWSManagedRulesCommonRuleSet',
      },
    },
  },
  {
    name: 'AWS-AWSManagedRulesKnownBadInputsRuleSet',
    priority: 2,
    statement: {
      managedRuleGroupStatement: {
        vendorName: 'AWS',
        name: 'AWSManagedRulesKnownBadInputsRuleSet',
      },
    },
  },
];
```

#### 自定义规则
- **IP白名单**: 允许特定IP段访问
- **地理封锁**: 阻止特定国家/地区
- **频率限制**: 每5分钟2000请求限制

## 性能优化

### 1. CDN缓存优化
- **静态资源缓存**: 1年 (max-age=31536000)
- **动态内容缓存**: 1小时 (max-age=3600)
- **API响应**: 不缓存 (no-cache, no-store)

### 2. 数据库连接优化
- **连接池**: PgPool配置最大200连接
- **读写分离**: 读操作路由到只读副本
- **连接复用**: 减少连接建立开销

### 3. ECS服务优化
- **自动扩缩容**: 基于CPU/内存使用率
- **健康检查**: 优化检查间隔和超时
- **资源限制**: 根据负载调整CPU/内存

## 监控和告警

### CloudWatch指标
- **ALB指标**: 请求数、响应时间、错误率
- **ECS指标**: CPU使用率、内存使用率、任务数
- **Aurora指标**: 连接数、查询响应时间、存储使用

### 日志收集
- **ALB访问日志**: S3存储，生命周期管理
- **VPC Flow Logs**: 网络流量分析
- **WAF日志**: 安全事件监控
- **应用日志**: CloudWatch Logs集中管理

### 告警配置
```typescript
new cloudwatch.Alarm(this, 'HighErrorRateAlarm', {
  metric: alb.metricHttpCodeTarget(elbv2.HttpCodeTarget.TARGET_5XX_COUNT),
  threshold: 10,
  evaluationPeriods: 2,
  treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
});
```

## 环境配置差异

### 开发环境 (dev)
- 单可用区部署
- 基础WAF规则
- 不启用CloudFront
- 简化的监控配置

### 生产环境 (prod)
- 多可用区高可用
- 完整WAF规则集
- CloudFront CDN加速
- 完整监控和告警体系

## 故障排除

### 常见问题
1. **域名解析失败**
   - 检查Route53托管区域配置
   - 验证NS记录设置
   - 清除本地DNS缓存

2. **SSL证书问题**
   - 验证DNS验证记录
   - 检查证书状态和有效期
   - 重新申请或导入证书

3. **负载均衡器连接超时**
   - 检查目标组健康状态
   - 验证安全组规则
   - 调整健康检查参数

4. **WAF误拦截**
   - 分析WAF访问日志
   - 调整规则优先级
   - 添加白名单例外

### 诊断命令
```bash
# 检查目标组健康
aws elbv2 describe-target-health --target-group-arn <arn>

# 查看WAF日志
aws logs filter-log-events \
  --log-group-name "/aws/wafv2/yuanhui-prod" \
  --filter-pattern "BLOCK"

# 验证SSL证书
openssl s_client -connect dp.kh2u.com:443 -servername dp.kh2u.com
```

## 相关文档

- [ECS服务架构](ecs-services.md)
- [数据库架构](database.md)
- [部署指南](../deployment/README.md)
- [安全配置](../security/README.md)