# 数据库架构设计

## 概述

本文档描述了元晖Odoo应用的数据库架构设计，采用Amazon Aurora Serverless v2 PostgreSQL数据库，支持自动扩缩容、高可用和读写分离。

## 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    Aurora Cluster                          │
│                                                             │
│  ┌─────────────────┐              ┌─────────────────┐       │
│  │   Primary       │    Sync      │   Read Replica  │       │
│  │   Instance      │◄────────────►│   Instance      │       │
│  │  (Writer)       │              │   (Reader)      │       │
│  │                 │              │                 │       │
│  │ ┌─────────────┐ │              │ ┌─────────────┐ │       │
│  │ │   Odoo DB   │ │              │ │   Odoo DB   │ │       │
│  │ │   (RW)      │ │              │ │   (RO)      │ │       │
│  │ └─────────────┘ │              │ └─────────────┘ │       │
│  │                 │              │                 │       │
│  │ ┌─────────────┐ │              │ ┌─────────────┐ │       │
│  │ │ Airflow DB  │ │              │ │ Airflow DB  │ │       │
│  │ │   (RW)      │ │              │ │   (RO)      │ │       │
│  │ └─────────────┘ │              │ └─────────────┘ │       │
│  └─────────────────┘              └─────────────────┘       │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              Cluster Storage                        │   │
│  │         (Auto-scaling, Encrypted)                   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│                 Application Layer                           │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Yherp    │  │   Khmall    │  │   Airflow   │         │
│  │             │  │             │  │             │         │
│  │ Write: RW   │  │ Write: RW   │  │ Write: RW   │         │
│  │ Read:  RO   │  │ Read:  RO   │  │ Read:  RO   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. Aurora Serverless v2集群

#### 基础配置
- **引擎**: PostgreSQL 15.4
- **模式**: Aurora Serverless v2
- **计算**: 自动扩缩容 (0.5 - 16 ACU)
- **存储**: 自动扩展，最大128TB
- **备份**: 自动备份，保留7天

#### 实例配置
```typescript
const dbCluster = new rds.DatabaseCluster(this, 'AuroraCluster', {
  engine: rds.DatabaseClusterEngine.auroraPostgres({
    version: rds.AuroraPostgresEngineVersion.VER_15_4,
  }),
  serverlessV2MinCapacity: 0.5,
  serverlessV2MaxCapacity: config.database.maxCapacity,
  writer: rds.ClusterInstance.serverlessV2('writer'),
  readers: [
    rds.ClusterInstance.serverlessV2('reader', { 
      scaleWithWriter: true 
    }),
  ],
  vpc,
  subnets: {
    subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
  },
});
```

### 2. 数据库结构

#### 主数据库 (Odoo)
```sql
-- 主要表结构
CREATE DATABASE odoo_prod;
CREATE USER odoo_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE odoo_prod TO odoo_user;

-- 设置连接限制
ALTER USER odoo_user CONNECTION LIMIT 100;

-- 创建读写权限
GRANT CONNECT ON DATABASE odoo_prod TO odoo_user;
GRANT USAGE ON SCHEMA public TO odoo_user;
GRANT CREATE ON SCHEMA public TO odoo_user;
```

#### Airflow数据库
```sql
-- Airflow专用数据库
CREATE DATABASE airflow_prod;
CREATE USER airflow_user WITH PASSWORD 'airflow_password';
GRANT ALL PRIVILEGES ON DATABASE airflow_prod TO airflow_user;

-- Airflow特定权限
ALTER USER airflow_user CONNECTION LIMIT 50;
```

### 3. 读写分离配置

#### 连接端点
- **写端点**: `writer.cluster-xxx.region.rds.amazonaws.com:5432`
- **读端点**: `reader.cluster-xxx.region.rds.amazonaws.com:5432`
- **集群端点**: `cluster-xxx.region.rds.amazonaws.com:5432`

#### 应用连接配置
```python
# Odoo配置 (odoo.conf)
[database]
db_host = writer.cluster-xxx.region.rds.amazonaws.com
db_port = 5432
db_user = odoo_user
db_password = ${DB_PASSWORD}
db_name = odoo_prod

# 读分离配置
[reporting]
db_host = reader.cluster-xxx.region.rds.amazonaws.com
db_port = 5432
```

## 性能优化

### 1. 连接池配置

#### PgBouncer设置
```ini
[databases]
odoo_prod = host=writer.cluster-xxx.region.rds.amazonaws.com port=5432 dbname=odoo_prod
odoo_prod_ro = host=reader.cluster-xxx.region.rds.amazonaws.com port=5432 dbname=odoo_prod

[pgbouncer]
pool_mode = transaction
max_client_conn = 200
default_pool_size = 25
reserve_pool_size = 5
```

#### 应用层连接管理
```python
# 数据库连接配置
db_maxconn = 100
db_template = template0
max_cron_threads = 2
limit_memory_hard = **********
limit_memory_soft = **********
```

### 2. 查询优化

#### 索引策略
```sql
-- Odoo核心表索引
CREATE INDEX CONCURRENTLY idx_res_users_login ON res_users(login);
CREATE INDEX CONCURRENTLY idx_res_partner_name ON res_partner(name);
CREATE INDEX CONCURRENTLY idx_account_move_state ON account_move(state);

-- 复合索引
CREATE INDEX CONCURRENTLY idx_sale_order_partner_date 
ON sale_order(partner_id, date_order);
```

#### 查询分析
```sql
-- 启用查询统计
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- 查看慢查询
SELECT query, calls, total_time, mean_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;
```

### 3. 缓存配置

#### PostgreSQL参数调优
```sql
-- 连接参数
ALTER SYSTEM SET max_connections = 300;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- 写入优化
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET checkpoint_segments = 32;
ALTER SYSTEM SET checkpoint_completion_target = 0.7;
```

## 安全配置

### 1. 网络安全

#### 安全组配置
```typescript
const dbSecurityGroup = new ec2.SecurityGroup(this, 'DatabaseSG', {
  vpc,
  description: 'Security group for Aurora database',
  allowAllOutbound: false,
});

// 仅允许ECS服务访问
dbSecurityGroup.addIngressRule(
  ecsSecurityGroup,
  ec2.Port.tcp(5432),
  'Allow ECS services access'
);
```

#### VPC配置
- **子网**: 私有隔离子网
- **路由**: 无互联网网关路由
- **DNS**: 启用VPC DNS解析

### 2. 数据加密

#### 静态加密
```typescript
const dbCluster = new rds.DatabaseCluster(this, 'Cluster', {
  storageEncrypted: true,
  storageEncryptionKey: kmsKey,
  // 其他配置...
});
```

#### 传输加密
```typescript
// 强制SSL连接
const parameterGroup = new rds.ParameterGroup(this, 'DbParams', {
  engine: rds.DatabaseClusterEngine.auroraPostgres({
    version: rds.AuroraPostgresEngineVersion.VER_15_4,
  }),
  parameters: {
    'rds.force_ssl': '1',
    'log_statement': 'all',
    'log_min_duration_statement': '1000',
  },
});
```

### 3. 访问控制

#### 数据库用户权限
```sql
-- 应用用户（最小权限）
CREATE ROLE app_user LOGIN;
GRANT CONNECT ON DATABASE odoo_prod TO app_user;
GRANT USAGE ON SCHEMA public TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;

-- 只读用户（报表查询）
CREATE ROLE readonly_user LOGIN;
GRANT CONNECT ON DATABASE odoo_prod TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
```

#### IAM数据库认证
```typescript
const dbCluster = new rds.DatabaseCluster(this, 'Cluster', {
  iamAuthentication: true,
  // 其他配置...
});
```

## 备份和恢复

### 1. 自动备份

#### 备份配置
```typescript
const dbCluster = new rds.DatabaseCluster(this, 'Cluster', {
  backup: {
    retention: cdk.Duration.days(7),
    preferredWindow: '03:00-04:00',
  },
  preferredMaintenanceWindow: 'sun:04:00-sun:05:00',
  deletionProtection: true,
});
```

#### 快照策略
```typescript
// 手动快照
const snapshot = new rds.DatabaseClusterSnapshot(this, 'Snapshot', {
  databaseCluster: dbCluster,
  databaseClusterSnapshotIdentifier: 'manual-snapshot-20240101',
});
```

### 2. 灾难恢复

#### 跨区域复制
```typescript
// 生产环境跨区域备份
const crossRegionBackup = new rds.DatabaseCluster(this, 'CrossRegionCluster', {
  // 灾备集群配置
  region: 'ap-southeast-1', // 备用区域
  // 其他配置...
});
```

#### 恢复流程
```bash
# 从快照恢复
aws rds restore-db-cluster-from-snapshot \
  --db-cluster-identifier restored-cluster \
  --snapshot-identifier manual-snapshot-20240101

# 时间点恢复
aws rds restore-db-cluster-to-point-in-time \
  --db-cluster-identifier restored-cluster \
  --source-db-cluster-identifier source-cluster \
  --restore-to-time 2024-01-01T12:00:00Z
```

## 监控和告警

### 1. CloudWatch指标

#### 关键指标
- **DatabaseConnections**: 数据库连接数
- **CPUUtilization**: CPU使用率
- **ReadLatency/WriteLatency**: 读写延迟
- **FreeStorageSpace**: 可用存储空间
- **Aurora副本延迟**: 读副本同步延迟

#### 告警配置
```typescript
new cloudwatch.Alarm(this, 'DatabaseConnections', {
  metric: dbCluster.metricDatabaseConnections(),
  threshold: 80,
  evaluationPeriods: 2,
  comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
});

new cloudwatch.Alarm(this, 'HighCPUUtilization', {
  metric: dbCluster.metricCPUUtilization(),
  threshold: 80,
  evaluationPeriods: 3,
});
```

### 2. 性能洞察

#### Performance Insights配置
```typescript
const dbCluster = new rds.DatabaseCluster(this, 'Cluster', {
  monitoringInterval: cdk.Duration.seconds(60),
  performanceInsightRetention: rds.PerformanceInsightRetention.DEFAULT,
  enablePerformanceInsights: true,
});
```

#### 查询监控
```sql
-- 查看活动连接
SELECT pid, usename, application_name, client_addr, state, query
FROM pg_stat_activity
WHERE state = 'active';

-- 查看锁等待
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
  ON blocking_locks.locktype = blocked_locks.locktype
  AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

## 环境配置差异

### 开发环境 (dev)
- **最小ACU**: 0.5
- **最大ACU**: 4
- **读副本**: 0个
- **备份保留**: 3天
- **删除保护**: 关闭

### 生产环境 (prod)
- **最小ACU**: 2
- **最大ACU**: 16
- **读副本**: 1-2个
- **备份保留**: 7天
- **删除保护**: 启用
- **跨区域备份**: 启用

## 故障排除

### 常见问题

1. **连接数过多**
   ```sql
   -- 查看连接状态
   SELECT state, count(*) FROM pg_stat_activity GROUP BY state;
   
   -- 终止空闲连接
   SELECT pg_terminate_backend(pid)
   FROM pg_stat_activity
   WHERE state = 'idle' AND state_change < now() - interval '1 hour';
   ```

2. **查询性能差**
   ```sql
   -- 查看慢查询
   SELECT query, calls, total_time, mean_time
   FROM pg_stat_statements
   WHERE mean_time > 1000
   ORDER BY mean_time DESC;
   
   -- 分析查询计划
   EXPLAIN ANALYZE SELECT * FROM large_table WHERE condition;
   ```

3. **存储空间不足**
   ```bash
   # 检查表大小
   SELECT schemaname, tablename, 
          pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
   FROM pg_tables
   ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
   ```

### 诊断命令
```bash
# 检查集群状态
aws rds describe-db-clusters --db-cluster-identifier yuanhui-aurora-prod

# 检查实例状态
aws rds describe-db-cluster-endpoints --db-cluster-identifier yuanhui-aurora-prod

# 查看参数组
aws rds describe-db-cluster-parameters --db-cluster-parameter-group-name yuanhui-aurora-params

# 创建快照
aws rds create-db-cluster-snapshot \
  --db-cluster-identifier yuanhui-aurora-prod \
  --db-cluster-snapshot-identifier manual-backup-$(date +%Y%m%d)
```

## 相关文档

- [ECS服务架构](ecs-services.md)
- [网络架构](network.md)
- [部署指南](../deployment/README.md)
- [运维指南](../operations/README.md)