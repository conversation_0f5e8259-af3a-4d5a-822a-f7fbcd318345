# ECS服务架构设计

## 概述

本文档描述了基于Amazon ECS（Elastic Container Service）的容器化服务架构，采用EC2计算模式部署，支持有状态和无状态服务的统一管理。

## 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    ECS Cluster                             │
│  ┌───────────────┐  ┌───────────────┐  ┌───────────────┐   │
│  │   EC2 Host    │  │   EC2 Host    │  │   EC2 Host    │   │
│  │               │  │               │  │               │   │
│  │ ┌───────────┐ │  │ ┌───────────┐ │  │ ┌───────────┐ │   │
│  │ │   yherp   │ │  │ │  khmall   │ │  │ │   cron    │ │   │
│  │ │ (Stateless)│ │  │ │(Stateless)│ │  │ │(Stateless)│ │   │
│  │ └───────────┘ │  │ └───────────┘ │  │ └───────────┘ │   │
│  │               │  │               │  │               │   │
│  │ ┌───────────┐ │  │ ┌───────────┐ │  │ ┌───────────┐ │   │
│  │ │  Redis    │ │  │ │ RabbitMQ  │ │  │ │ Airflow   │ │   │
│  │ │(Stateful) │ │  │ │(Stateful) │ │  │ │Scheduler  │ │   │
│  │ └───────────┘ │  │ └───────────┘ │  │ │(Stateless)│ │   │
│  │               │  │               │  │ └───────────┘ │   │
│  └───────────────┘  └───────────────┘  └───────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. ECS集群配置

#### 计算资源
- **实例类型**: t3.medium (开发) / c5.large (生产)
- **操作系统**: Amazon Linux 2 ECS-optimized
- **自动扩缩容**: 基于CPU和内存利用率
- **可用区**: 多AZ部署确保高可用

#### 容量提供者
```typescript
const capacityProvider = new ecs.AsgCapacityProvider(this, 'CapacityProvider', {
  autoScalingGroup: asg,
  enableManagedScaling: true,
  targetCapacityPercent: 80,
  enableManagedTerminationProtection: false,
});
```

### 2. 服务类型设计

#### 无状态服务 (Stateless Services)
**特征:**
- 多实例运行，支持水平扩展
- 无持久化状态，重启后不影响功能
- 使用动态端口映射
- 支持滚动更新部署

**服务列表:**
- **yherp**: Odoo ERP主应用
- **khmall**: Odoo电商应用
- **cron**: Odoo定时任务服务
- **airflow-webserver**: Airflow Web界面
- **airflow-scheduler**: Airflow调度器

**部署配置:**
```typescript
const statelessService = new ecs.Ec2Service(this, 'StatelessService', {
  cluster,
  taskDefinition,
  desiredCount: 2,
  deploymentConfiguration: {
    maximumPercent: 200,
    minimumHealthyPercent: 50,
  },
  enableExecuteCommand: true,
});
```

#### 有状态服务 (Stateful Services)
**特征:**
- 单实例运行，持久化数据
- 需要固定节点放置
- 使用EBS卷进行数据持久化
- 采用先停止后启动的部署策略

**服务列表:**
- **redis**: 缓存服务
- **rabbitmq**: 消息队列服务

**部署配置:**
```typescript
const statefulService = new ecs.Ec2Service(this, 'StatefulService', {
  cluster,
  taskDefinition,
  desiredCount: 1,
  deploymentConfiguration: {
    maximumPercent: 100,
    minimumHealthyPercent: 0,
  },
  placementStrategies: [
    ecs.PlacementStrategy.packedByMemory(),
    ecs.PlacementStrategy.packedByCpu(),
  ],
});
```

### 3. 任务定义配置

#### 资源分配
| 服务 | CPU (单位) | 内存 (MB) | 端口 | 存储 |
|------|------------|-----------|------|------|
| yherp | 1024 | 2048 | 8069, 8072 | - |
| khmall | 1024 | 2048 | 8069, 8072 | - |
| cron | 512 | 1024 | - | - |
| redis | 256 | 512 | 6379 | 5GB EBS |
| rabbitmq | 512 | 1024 | 5672, 15672 | 10GB EBS |
| airflow-webserver | 512 | 1024 | 8080 | - |
| airflow-scheduler | 256 | 512 | - | - |

#### 环境变量管理
```typescript
const environment = {
  // 数据库配置
  DB_HOST: dbCluster.clusterEndpoint.hostname,
  DB_PORT: '5432',
  DB_USER: 'odoo_user',
  
  // Redis配置
  REDIS_HOST: 'redis.yuanhui.local',
  REDIS_PORT: '6379',
  
  // 应用配置
  ODOO_RC: '/etc/odoo/odoo.conf',
  WITHOUT_DEMO: 'True',
};

const secrets = {
  DB_PASSWORD: ecs.Secret.fromSecretsManager(dbSecret, 'password'),
  ADMIN_PASSWD: ecs.Secret.fromSecretsManager(adminSecret),
};
```

### 4. 服务发现和通信

#### Service Connect配置
```typescript
const serviceConnectConfiguration = {
  namespace: serviceConnectNamespace.namespaceName,
  services: [{
    portMappingName: 'web',
    dnsName: `${serviceName}.yuanhui.local`,
  }],
};
```

#### 内部DNS映射
- **yherp.yuanhui.local**: Yherp应用服务
- **khmall.yuanhui.local**: Khmall应用服务
- **redis.yuanhui.local**: Redis缓存服务
- **rabbitmq.yuanhui.local**: RabbitMQ消息队列
- **airflow.yuanhui.local**: Airflow Web界面

### 5. 存储配置

#### EFS共享存储 (Airflow)
```typescript
const fileSystem = new efs.FileSystem(this, 'AirflowEFS', {
  vpc,
  encrypted: true,
  performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
  throughputMode: efs.ThroughputMode.BURSTING,
});

// 挂载配置
const volumeConfiguration = {
  name: 'airflow-dags',
  efsVolumeConfiguration: {
    fileSystemId: fileSystem.fileSystemId,
    rootDirectory: '/opt/airflow/dags',
  },
};
```

#### EBS持久化存储 (有状态服务)
```typescript
// Redis数据持久化
const redisVolume = {
  name: 'redis-data',
  dockerVolumeConfiguration: {
    scope: ecs.Scope.SHARED,
    autoprovision: true,
    driver: 'local',
  },
};
```

## 部署策略

### 1. 滚动更新 (无状态服务)
- **最大运行百分比**: 200%
- **最小健康百分比**: 50%
- **部署超时**: 15分钟
- **健康检查**: HTTP GET /web/health

### 2. 蓝绿部署 (有状态服务)
- **最大运行百分比**: 100%
- **最小健康百分比**: 0%
- **部署策略**: 先停止旧任务，再启动新任务
- **数据迁移**: 自动挂载持久化存储

### 3. 健康检查配置
```typescript
const healthCheck = {
  command: [
    'CMD-SHELL',
    'curl -f http://localhost:8069/web/health || exit 1'
  ],
  interval: cdk.Duration.seconds(30),
  timeout: cdk.Duration.seconds(5),
  retries: 3,
  startPeriod: cdk.Duration.seconds(60),
};
```

## 自动扩缩容

### 1. 服务级扩缩容
```typescript
const scalableTarget = service.autoScaleTaskCount({
  minCapacity: 1,
  maxCapacity: 10,
});

scalableTarget.scaleOnCpuUtilization('CpuScaling', {
  targetUtilizationPercent: 70,
  scaleInCooldown: cdk.Duration.seconds(300),
  scaleOutCooldown: cdk.Duration.seconds(60),
});
```

### 2. 集群级扩缩容
```typescript
asg.scaleOnCpuUtilization('ClusterCpuScaling', {
  targetUtilizationPercent: 75,
  scaleInCooldown: cdk.Duration.seconds(300),
  scaleOutCooldown: cdk.Duration.seconds(300),
});
```

## 监控和日志

### 1. CloudWatch指标
- **服务指标**: CPU使用率、内存使用率、任务数量
- **集群指标**: 实例数量、资源利用率
- **应用指标**: 请求数、响应时间、错误率

### 2. 日志配置
```typescript
const logging = ecs.LogDriver.awsLogs({
  streamPrefix: `${serviceName}-logs`,
  logGroup: new logs.LogGroup(this, 'LogGroup', {
    logGroupName: `/aws/ecs/${serviceName}`,
    retention: logs.RetentionDays.ONE_WEEK,
  }),
});
```

### 3. 告警配置
```typescript
new cloudwatch.Alarm(this, 'HighCpuAlarm', {
  metric: service.metricCpuUtilization(),
  threshold: 80,
  evaluationPeriods: 2,
  treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
});
```

## 安全配置

### 1. IAM角色和权限
```typescript
// 任务执行角色
const taskExecutionRole = new iam.Role(this, 'TaskExecutionRole', {
  assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
  managedPolicies: [
    iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
  ],
});

// 任务角色
const taskRole = new iam.Role(this, 'TaskRole', {
  assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
});
```

### 2. 安全组配置
```typescript
const serviceSecurityGroup = new ec2.SecurityGroup(this, 'ServiceSG', {
  vpc,
  description: `Security group for ${serviceName}`,
});

// 仅允许ALB访问应用端口
serviceSecurityGroup.addIngressRule(
  albSecurityGroup,
  ec2.Port.tcp(8069),
  'Allow ALB access to Odoo'
);
```

### 3. 密钥管理
```typescript
const dbSecret = new secretsmanager.Secret(this, 'DatabaseSecret', {
  description: 'Database credentials',
  generateSecretString: {
    secretStringTemplate: JSON.stringify({ username: 'odoo_user' }),
    generateStringKey: 'password',
    excludeCharacters: '"@/\\',
  },
});
```

## 故障排除

### 常见问题
1. **服务启动失败**
   - 检查任务定义配置
   - 验证容器镜像可用性
   - 查看任务日志

2. **健康检查失败**
   - 确认健康检查端点可访问
   - 调整超时和重试参数
   - 检查安全组规则

3. **资源不足**
   - 监控集群资源利用率
   - 调整实例类型或数量
   - 优化任务资源配置

4. **网络连接问题**
   - 验证Service Connect配置
   - 检查DNS解析
   - 确认安全组规则

### 诊断命令
```bash
# 查看服务状态
aws ecs describe-services --cluster yuanhui-odoo-dev --services yherp-dev

# 查看任务详情
aws ecs describe-tasks --cluster yuanhui-odoo-dev --tasks <task-id>

# 查看任务日志
aws logs tail /aws/ecs/yherp-dev --follow

# 执行容器内命令
aws ecs execute-command --cluster yuanhui-odoo-dev --task <task-id> --container yherp --interactive --command "/bin/bash"
```

## 相关文档

- [网络架构](network.md)
- [数据库架构](database.md)
- [部署指南](../deployment/README.md)
- [监控配置](../operations/README.md)