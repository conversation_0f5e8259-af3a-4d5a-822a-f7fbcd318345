# 架构设计文档

## 项目概述

本项目实现了一个完整的AWS云原生Odoo应用服务架构，采用基础设施即代码（IaC）的方式，使用AWS CDK进行部署和管理。架构采用容器化部署，支持多域名路由、零信任网络访问、数据库读写分离、自动扩容和全面的安全防护。

## 核心设计原则

- **容器化优先**: 所有服务均采用容器化部署
- **基础设施即代码**: 使用AWS CDK (TypeScript) 实现完整的基础设施定义
- **多层安全防护**: 网络隔离 + WAF防护 + 零信任网络 + 数据加密
- **高可用性**: 多可用区部署 + 自动故障转移 + 健康检查
- **可观测性**: 全面的监控、日志和告警体系

## 架构组件概览

```
┌─────────────────────────────────────────────────────────────┐
│                      Internet                               │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                 Route 53 + CDN                             │
│     WAF Protection + SSL Certificates                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│                    VPC Network                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Public    │  │   Private   │  │  Database   │         │
│  │   Subnets   │  │   Subnets   │  │   Subnets   │         │
│  │    (ALB)    │  │   (ECS)     │  │  (Aurora)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 架构文档结构

### 1. [网络架构设计](network.md)
- VPC网络设计和子网规划
- 多域名路由配置
- 安全防护体系（WAF、OpenZiti、SSL）
- 负载均衡策略
- CDN和缓存配置

### 2. [ECS服务架构设计](ecs-services.md)
- ECS集群配置和容量提供者
- 有状态和无状态服务设计
- 任务定义和资源分配
- 服务发现和通信
- 自动扩缩容策略

### 3. [数据库架构设计](database.md)
- Aurora Serverless v2配置
- 读写分离实现
- 性能优化和连接池
- 备份和恢复策略
- 监控和故障排除

### 4. 专业构造组件

#### ECS服务构造
- [ECS服务构造设计](ecs-service-construct.md)
- [ECS服务构造使用指南](ecs-service-construct-usage.md)
- [故障排除指南](ecs-service-construct-troubleshooting.md)

#### 有状态节点管理
详见 `lib/constructs/stateful-node-manager.ts` 实现。

## 技术栈概览

### 基础设施
- **AWS CDK**: TypeScript实现的IaC
- **Amazon VPC**: 网络隔离和安全
- **Application Load Balancer**: 7层负载均衡
- **AWS WAF**: Web应用防火墙
- **Route 53**: DNS管理
- **CloudFront**: 全球CDN

### 计算和容器
- **Amazon ECS**: 容器编排服务
- **EC2实例**: ECS集群计算节点
- **Docker容器**: 应用容器化
- **Service Connect**: 服务发现

### 数据存储
- **Aurora Serverless v2**: PostgreSQL数据库
- **Amazon EFS**: 共享文件存储
- **Amazon EBS**: 持久化块存储

### 安全和监控
- **AWS Secrets Manager**: 密钥管理
- **AWS Certificate Manager**: SSL证书
- **CloudWatch**: 监控和日志
- **OpenZiti**: 零信任网络

## 环境支持

### 开发环境 (dev)
- 单可用区部署
- 最小资源配置
- 简化的安全规则
- 快速部署和测试

### 生产环境 (prod)
- 多可用区高可用部署
- 完整的安全防护
- 自动扩缩容
- 全面监控和告警

## 服务清单

### 主要应用服务
- **yherp**: Odoo ERP主应用
- **khmall**: Odoo电商应用  
- **cron**: Odoo定时任务服务

### 基础设施服务
- **redis**: 缓存服务
- **rabbitmq**: 消息队列服务
- **airflow**: 工作流引擎

## 快速开始

1. **环境准备**
   ```bash
   npm install
   aws configure
   export NODE_ENV=dev
   ```

2. **部署基础设施**
   ```bash
   ./scripts/deploy.sh dev --group core
   ```

3. **部署应用服务**
   ```bash
   ./scripts/deploy.sh dev --group apps
   ```

4. **验证部署**
   ```bash
   ./scripts/test-network-routing.sh dev
   ```

## 相关文档

- [部署指南](../deployment/README.md)
- [运维指南](../operations/README.md)
- [安全配置](../security/README.md)
- [故障排除](../troubleshooting/)
