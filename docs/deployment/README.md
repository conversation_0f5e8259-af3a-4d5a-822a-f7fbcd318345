# 部署指南

## 快速开始

### 前置条件
- Node.js 18.x+ 
- AWS CLI 2.x
- AWS CDK 2.x
- 有效的AWS账户和权限

### 一键部署
```bash
# 克隆项目
git clone <repository-url> && cd yuanhui-odoo-iac

# 安装依赖
npm install

# 配置环境
export NODE_ENV=dev
aws configure

# 部署基础设施
./scripts/deploy.sh dev --group core

# 部署应用服务  
./scripts/deploy.sh dev --group apps
```

## 部署方式对比

### 推荐：脚本部署
```bash
# 智能部署（推荐）
./scripts/deploy.sh dev                    # 部署所有栈
./scripts/deploy.sh prod --dry-run         # 预览生产部署
./scripts/deploy.sh dev --parallel --logs  # 并行部署+实时日志
```

**优势**：
- 自动依赖解析
- 并行部署支持
- 智能错误处理
- 实时日志显示
- 自动回滚支持

### 传统：CDK直接部署
```bash
# 传统方式
export NODE_ENV=dev
npm run build
cdk deploy --all --require-approval never
```

**适用场景**：单栈部署、调试、自定义参数

## 环境配置

### 环境变量
```bash
# 必需环境变量
export NODE_ENV=dev                    # dev | prod
export CDK_DEFAULT_ACCOUNT=*********  # AWS账户ID
export CDK_DEFAULT_REGION=ap-east-2   # AWS区域

# 可选配置
export DEPLOY_TIMEOUT=3600     # 部署超时（秒）
export DEPLOY_PARALLEL=true    # 启用并行部署
export DEPLOY_LOGS=true        # 显示实时日志
```

### 配置验证
```bash
# 检查权限
./scripts/check-permissions.sh

# 验证配置
aws sts get-caller-identity
aws configure list
```

## 部署栈组织

### Core栈组（基础设施）
- **Network**: VPC、安全组、WAF
- **Ecs**: ECS集群和容量提供者
- **ServiceConnect**: ECS服务发现
- **AuroraDatabase**: PostgreSQL数据库
- **Redis**: 缓存服务
- **RabbitMQ**: 消息队列
- **LoadBalancer**: 应用负载均衡器
- **Airflow**: 工作流引擎
- **Security**: 安全配置

### Apps栈组（应用服务）
- **Application**: Odoo应用服务
- **ClaudeRelay**: Claude代理服务  
- **CloudFront**: CDN（生产环境）
- **OpenZiti**: 零信任网络（生产环境）
- **Monitoring**: 监控告警

## 部署命令详解

### 基本命令
```bash
# 部署指定环境的所有栈
./scripts/deploy.sh <env>

# 部署指定栈组
./scripts/deploy.sh <env> --group <core|apps|all>

# 部署指定栈
./scripts/deploy.sh <env> --stacks <stack1,stack2>
```

### 高级选项
```bash
# 预览模式
./scripts/deploy.sh prod --dry-run

# 并行部署
./scripts/deploy.sh dev --parallel

# 显示实时日志
./scripts/deploy.sh dev --logs

# 自动回滚
./scripts/deploy.sh prod --auto-rollback

# 超时设置
./scripts/deploy.sh dev --timeout 3600

# 跳过确认
./scripts/deploy.sh dev --no-confirm
```

### NPM快捷命令
```bash
# 预定义快捷命令
npm run deploy:dev              # 开发环境
npm run deploy:prod             # 生产环境
npm run deploy:dev:dry          # 预览开发环境
npm run deploy:core             # 仅基础设施
npm run deploy:apps             # 仅应用服务
npm run deploy:fast             # 快速并行部署
```

## 分阶段部署

### 阶段1：网络和基础设施
```bash
./scripts/deploy.sh dev --stacks Network,Ecs,ServiceConnect
```

### 阶段2：数据存储服务
```bash  
./scripts/deploy.sh dev --stacks AuroraDatabase,Redis,RabbitMQ
```

### 阶段3：应用和负载均衡
```bash
./scripts/deploy.sh dev --stacks LoadBalancer,Application
```

### 阶段4：监控和安全
```bash
./scripts/deploy.sh dev --stacks Monitoring,Security
```

## 环境差异

### 开发环境 (dev)
- **实例规格**: t3.medium (ECS)
- **数据库**: 最小0.5 ACU，最大4 ACU
- **可用区**: 2个AZ
- **备份**: 3天保留
- **监控**: 基础监控
- **安全**: 简化WAF规则

### 生产环境 (prod)
- **实例规格**: c5.large (ECS)  
- **数据库**: 最小2 ACU，最大16 ACU
- **可用区**: 3个AZ
- **备份**: 7天保留+跨区域
- **监控**: 完整监控+告警
- **安全**: 完整WAF+OpenZiti+CloudFront

## 部署验证

### 自动验证
部署完成后自动检查：
- CloudFormation栈状态
- ECS服务健康
- 数据库连接
- 负载均衡器状态

### 手动验证
```bash
# 检查ECS服务
aws ecs describe-services \
  --cluster yuanhui-odoo-dev \
  --services yherp-dev khmall-dev

# 检查数据库
aws rds describe-db-clusters \
  --db-cluster-identifier yuanhui-aurora-dev

# 检查负载均衡器
aws elbv2 describe-load-balancers \
  --names yuanhui-application-dev

# 测试应用访问
curl -I https://dp-dev.kh2u.com/web/health
```

### 健康检查
```bash
# 使用内置测试脚本
./scripts/test-network-routing.sh dev
./scripts/verify-ssl-certificates.sh dev
./scripts/test-airflow-deployment.sh dev
```

## 故障排除

### 常见部署错误

1. **权限不足**
   ```bash
   # 检查IAM权限
   ./scripts/check-permissions.sh
   
   # 查看错误详情
   aws cloudformation describe-stack-events \
     --stack-name <failed-stack-name>
   ```

2. **资源冲突**
   ```bash
   # 检查现有资源
   aws cloudformation list-stacks \
     --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE
   
   # 删除冲突栈
   cdk destroy <stack-name>
   ```

3. **超时错误**
   ```bash
   # 增加超时时间
   ./scripts/deploy.sh dev --timeout 7200
   
   # 检查资源创建状态
   aws cloudformation describe-stack-resources \
     --stack-name <stack-name>
   ```

4. **依赖错误**
   ```bash
   # 按依赖顺序部署
   ./scripts/deploy.sh dev --group core
   sleep 300  # 等待资源就绪
   ./scripts/deploy.sh dev --group apps
   ```

### 调试模式
```bash
# 启用详细日志
export CDK_DEBUG=true
./scripts/deploy.sh dev --verbose

# 跳过失败栈继续部署
./scripts/deploy.sh dev --continue-on-error

# 单栈调试
cdk deploy <stack-name> --verbose
```

### 回滚操作
```bash
# 自动回滚到上一版本
./scripts/deploy.sh prod --rollback

# 手动回滚指定栈
aws cloudformation cancel-update-stack \
  --stack-name <stack-name>

# 删除失败的栈
cdk destroy <failed-stack-name>
```

## 成本监控

### 部署前成本预估
```bash
# 查看将要创建的资源
cdk diff --all

# 使用成本计算器
# https://calculator.aws/#/
```

### 部署后成本监控
```bash
# 运行成本检查脚本
./scripts/quick-cost-check.sh
./scripts/monitor-cloudwatch-costs.sh

# 设置预算告警
aws budgets create-budget --account-id <id> --budget file://budget.json
```

### 成本优化建议
- **开发环境**: 使用较小实例，单AZ部署
- **测试完后**: 及时停止或删除不必要的资源
- **生产环境**: 使用预留实例，启用自动扩缩容
- **监控设置**: 配置成本告警，定期审查

## 维护操作

### 更新部署
```bash
# 更新应用代码
./scripts/deploy.sh dev --stacks Application

# 更新基础设施
./scripts/deploy.sh dev --group core

# 滚动更新
./scripts/deploy.sh prod --rolling-update
```

### 备份和恢复
```bash
# 创建快照
aws rds create-db-cluster-snapshot \
  --db-cluster-identifier yuanhui-aurora-prod \
  --db-cluster-snapshot-identifier manual-backup-$(date +%Y%m%d)

# 备份CloudFormation模板
aws cloudformation get-template \
  --stack-name YuanhuiApplication-prod > template-backup.json
```

### 清理资源
```bash
# 删除开发环境
./scripts/deploy.sh dev --destroy

# 删除指定栈
cdk destroy <stack-name>

# 清理所有环境（谨慎使用）
cdk destroy --all
```

## CI/CD集成

### GitHub Actions
```yaml
name: Deploy Infrastructure
on:
  push:
    branches: [main]
    
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: ./scripts/deploy.sh prod --no-confirm
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
```

### 其他文档

- [快速开始指南](quick-start.md) - 新手入门
- [环境配置](../reference/environment-config.md) - 详细配置说明  
- [故障排除](../troubleshooting/README.md) - 问题解决方案
- [架构文档](../architecture/README.md) - 系统架构说明
- [运维指南](../operations/README.md) - 日常运维管理