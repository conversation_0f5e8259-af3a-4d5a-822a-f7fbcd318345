# 快速开始指南

## 新手入门

### 5分钟快速部署

如果你是第一次使用该项目，按照以下步骤可以快速部署一个完整的开发环境。

```bash
# 1. 克隆项目
git clone <repository-url>
cd yuanhui-odoo-iac

# 2. 安装依赖
npm install

# 3. 配置AWS
aws configure
# 输入你的 Access Key ID、Secret Access Key 和区域

# 4. 设置环境变量
export NODE_ENV=dev
export CDK_DEFAULT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
export CDK_DEFAULT_REGION=ap-east-2

# 5. 检查权限
./scripts/check-permissions.sh

# 6. 一键部署
./scripts/deploy.sh dev
```

部署完成后，你将拥有：
- ✅ 完整的VPC网络环境
- ✅ ECS集群和服务
- ✅ Aurora数据库
- ✅ Redis缓存
- ✅ RabbitMQ消息队列
- ✅ Airflow工作流引擎
- ✅ Odoo应用服务

## 部署后验证

### 检查服务状态
```bash
# 检查所有ECS服务
aws ecs describe-services --cluster yuanhui-odoo-dev --services yherp-dev khmall-dev cron-dev redis-dev rabbitmq-dev airflow-webserver-dev airflow-scheduler-dev

# 检查数据库
aws rds describe-db-clusters --db-cluster-identifier yuanhui-aurora-dev

# 测试网络连通性
./scripts/test-network-routing.sh dev
```

### 访问应用

#### 获取访问地址
```bash
# 获取负载均衡器地址
aws elbv2 describe-load-balancers \
  --names yuanhui-application-dev \
  --query 'LoadBalancers[0].DNSName' \
  --output text
```

#### 访问Odoo应用
- **Yherp开发环境**: `http://<alb-dns>` (Host: dp-dev.kh2u.com)
- **Khmall开发环境**: `http://<alb-dns>` (Host: j2mall.tw)

#### 访问Airflow
- **Web界面**: `http://<internal-alb-dns>:8080`
- **用户名**: admin
- **密码**: 查看Secrets Manager中的值

## 常见问题

### 1. 权限错误
**错误**: `AccessDenied` 或 `UnauthorizedOperation`

**解决**:
```bash
# 检查当前用户权限
aws sts get-caller-identity

# 确保用户有以下权限：
# - CloudFormation: *
# - EC2: *
# - ECS: *
# - RDS: *
# - IAM: *
# - Secrets Manager: *
```

### 2. 区域配置错误
**错误**: `InvalidParameterValue` 或资源不可用

**解决**:
```bash
# 检查当前区域
aws configure get region

# 设置正确的区域
export CDK_DEFAULT_REGION=ap-east-2
aws configure set region ap-east-2
```

### 3. CDK Bootstrap未完成
**错误**: `No stack named CDKToolkit found`

**解决**:
```bash
# Bootstrap CDK环境
cdk bootstrap aws://$CDK_DEFAULT_ACCOUNT/$CDK_DEFAULT_REGION
```

### 4. Node.js版本不兼容
**错误**: `Unsupported engine`

**解决**:
```bash
# 安装Node.js 18+
nvm install 18
nvm use 18

# 或使用系统包管理器
brew install node@18  # macOS
apt install nodejs    # Ubuntu
```

## 下一步

部署完成后，你可以：

1. **学习架构**: 阅读[架构文档](../architecture/README.md)了解系统设计
2. **配置域名**: 根据需要修改域名配置
3. **部署生产环境**: 使用`./scripts/deploy.sh prod`部署生产环境
4. **设置监控**: 配置CloudWatch告警和通知
5. **自定义配置**: 修改`lib/config/`下的环境配置

## 清理资源

如果你只是在测试，可以删除所有资源避免费用：

```bash
# 删除开发环境所有资源
./scripts/deploy.sh dev --destroy

# 确认删除
aws cloudformation list-stacks --stack-status-filter DELETE_COMPLETE
```

**注意**: 删除操作不可逆，请确保你不再需要这些资源。

## 获取帮助

遇到问题？可以：

1. 查看[故障排除文档](../troubleshooting/README.md)
2. 检查CloudFormation控制台中的错误信息
3. 运行`./scripts/check-permissions.sh`检查权限
4. 查看CloudWatch日志获取详细错误信息