# 环境配置说明

## 概述

本项目支持开发（dev）和生产（prod）两种环境配置，每种环境都有不同的资源配置、安全设置和功能特性。

## 配置文件结构

```
lib/config/
├── index.ts                    # 主配置入口
├── base.ts                     # 基础配置接口
├── environments/
│   ├── dev.ts                  # 开发环境配置
│   ├── prod.ts                 # 生产环境配置
│   └── common.ts               # 通用配置
└── stacks/
    ├── network.ts              # 网络栈配置
    ├── ecs.ts                  # ECS栈配置
    ├── database.ts             # 数据库栈配置
    └── ...                     # 其他栈配置
```

## 环境变量设置

### 必需环境变量

```bash
# 环境标识
export NODE_ENV=dev|prod

# AWS配置
export CDK_DEFAULT_ACCOUNT=************
export CDK_DEFAULT_REGION=ap-east-2

# 可选配置
export DEPLOY_TIMEOUT=3600      # 部署超时（秒）
export DEPLOY_PARALLEL=true     # 并行部署
export DEPLOY_LOGS=true         # 实时日志
```

### 环境检查

```bash
# 检查当前环境配置
echo "Environment: $NODE_ENV"
echo "Account: $CDK_DEFAULT_ACCOUNT" 
echo "Region: $CDK_DEFAULT_REGION"

# 验证AWS凭证
aws sts get-caller-identity
```

## 开发环境配置（dev）

### 资源配置
- **VPC CIDR**: 10.0.0.0/16
- **可用区数量**: 2个
- **NAT网关**: 单个（成本优化）
- **ECS实例类型**: t3.medium
- **Aurora配置**: 最小0.5 ACU，最大4 ACU
- **备份保留**: 3天

### 域名配置
```typescript
domains: {
  yherp: {
    internal: "yh-dev.kh2u.com",
    external: "dp-dev.kh2u.com"
  },
  khmall: {
    primary: "j2mall.tw"
  }
}
```

### 安全特性
- **WAF**: 禁用（开发便利性）
- **OpenZiti**: 禁用
- **CloudFront**: 禁用
- **SSL证书**: 基本SSL配置

### 监控配置
- **日志保留**: 7天
- **指标收集**: 基础监控
- **告警**: 仅关键告警

## 生产环境配置（prod）

### 资源配置
- **VPC CIDR**: 10.0.0.0/16
- **可用区数量**: 3个（高可用）
- **NAT网关**: 每个AZ一个
- **ECS实例类型**: c5.large
- **Aurora配置**: 最小2 ACU，最大16 ACU
- **备份保留**: 7天，跨区域备份

### 域名配置
```typescript
domains: {
  yherp: {
    internal: "yh.kh2u.com",     // OpenZiti + Longpolling
    external: "dp.kh2u.com"      // WAF + Longpolling
  },
  khmall: {
    primary: "j2mall.com"        // CloudFront CDN
  }
}
```

### 安全特性
- **WAF**: 启用完整规则集
- **OpenZiti**: 零信任网络访问
- **CloudFront**: CDN加速和安全
- **SSL证书**: 完整SSL配置和HSTS

### 监控配置
- **日志保留**: 30天
- **指标收集**: 详细监控
- **告警**: 完整告警策略
- **仪表板**: 环境专用仪表板

## 配置差异对比

| 配置项 | 开发环境 | 生产环境 | 说明 |
|-------|----------|----------|------|
| **基础设施** |  |  |  |
| 可用区 | 2个 | 3个 | 生产环境高可用 |
| NAT网关 | 1个 | 3个 | 成本vs可用性平衡 |
| **计算资源** |  |  |  |
| ECS实例 | t3.medium | c5.large | 生产环境更高性能 |
| 最小任务数 | 1 | 2 | 生产环境冗余 |
| 最大任务数 | 5 | 20 | 生产环境扩展性 |
| **数据库** |  |  |  |
| 最小ACU | 0.5 | 2 | 生产环境性能保证 |
| 最大ACU | 4 | 16 | 生产环境扩展能力 |
| 备份保留 | 3天 | 7天 | 生产环境数据保护 |
| **安全特性** |  |  |  |
| WAF | ❌ | ✅ | 生产环境安全保护 |
| OpenZiti | ❌ | ✅ | 生产环境零信任 |
| CloudFront | ❌ | ✅ | 生产环境CDN |
| **监控** |  |  |  |
| 日志保留 | 7天 | 30天 | 生产环境审计需求 |
| 详细监控 | 基础 | 完整 | 生产环境可观察性 |

## 配置切换

### 开发到生产切换

```bash
# 1. 切换环境变量
export NODE_ENV=prod

# 2. 确认配置变更
cdk diff --all

# 3. 部署到生产环境
./scripts/deploy.sh prod --dry-run  # 预览
./scripts/deploy.sh prod             # 执行部署
```

### 生产到开发切换

```bash
# 1. 切换环境变量
export NODE_ENV=dev

# 2. 确认配置变更
cdk diff --all

# 3. 部署到开发环境
./scripts/deploy.sh dev
```

## 环境特定配置

### 开发环境专用设置

```typescript
// 开发环境禁用成本较高的功能
const devConfig = {
  enableWAF: false,
  enableOpenZiti: false,
  enableCloudFront: false,
  
  // 使用较小的资源规格
  ecsInstanceType: "t3.medium",
  auroraMinCapacity: 0.5,
  auroraMaxCapacity: 4,
  
  // 简化的备份策略
  backupRetentionDays: 3,
  enableCrossRegionBackup: false
}
```

### 生产环境专用设置

```typescript
// 生产环境启用全部安全和性能功能
const prodConfig = {
  enableWAF: true,
  enableOpenZiti: true,
  enableCloudFront: true,
  
  // 使用高性能资源规格
  ecsInstanceType: "c5.large",
  auroraMinCapacity: 2,
  auroraMaxCapacity: 16,
  
  // 完整的备份和灾难恢复
  backupRetentionDays: 7,
  enableCrossRegionBackup: true,
  
  // 多可用区部署
  availabilityZones: 3,
  enableMultiAZDatabase: true
}
```

## 配置验证

### 环境配置检查脚本

```bash
#!/bin/bash
# scripts/check-environment-config.sh

echo "=== Environment Configuration Check ==="
echo "NODE_ENV: ${NODE_ENV}"
echo "CDK_DEFAULT_ACCOUNT: ${CDK_DEFAULT_ACCOUNT}"
echo "CDK_DEFAULT_REGION: ${CDK_DEFAULT_REGION}"

# 检查AWS权限
echo -e "\n=== AWS Credentials Check ==="
aws sts get-caller-identity

# 检查配置差异
echo -e "\n=== Configuration Diff ==="
cdk diff --all --require-approval never | grep -E "(Stack|Resources)"

echo -e "\n=== Environment Summary ==="
case "${NODE_ENV}" in
  "dev")
    echo "Development environment - optimized for cost and development speed"
    ;;
  "prod")
    echo "Production environment - optimized for performance and security"
    ;;
  *)
    echo "ERROR: Unknown environment ${NODE_ENV}"
    exit 1
    ;;
esac
```

### 配置合规检查

```bash
# 检查生产环境必需配置
./scripts/validate-prod-config.sh

# 检查开发环境配置优化
./scripts/validate-dev-config.sh

# 检查跨环境配置一致性
./scripts/validate-config-consistency.sh
```

## 故障排除

### 常见配置问题

1. **环境变量未设置**
   ```bash
   # 错误: NODE_ENV未设置
   Error: Environment variable NODE_ENV is not set
   
   # 解决: 设置环境变量
   export NODE_ENV=dev
   ```

2. **账户/区域不匹配**
   ```bash
   # 错误: 账户不匹配
   Error: Account mismatch
   
   # 解决: 检查并设置正确的账户ID
   export CDK_DEFAULT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
   ```

3. **配置文件语法错误**
   ```bash
   # 错误: TypeScript编译失败
   Error: TypeScript compilation failed
   
   # 解决: 检查配置文件语法
   npm run build
   ```

### 配置重置

```bash
# 清理并重新初始化配置
rm -rf cdk.out/
npm run clean
npm run build
cdk bootstrap
```

## 最佳实践

### 配置管理
1. **版本控制**: 所有配置变更都要经过代码审查
2. **环境隔离**: 开发和生产环境完全隔离
3. **敏感信息**: 使用AWS Secrets Manager存储密钥
4. **配置验证**: 部署前验证配置正确性

### 安全考虑
1. **最小权限**: IAM角色遵循最小权限原则
2. **网络隔离**: 使用VPC和安全组隔离网络
3. **加密传输**: 全部使用TLS 1.2+加密
4. **访问日志**: 启用所有服务的访问日志

### 成本优化
1. **资源规格**: 根据实际需求选择实例类型
2. **自动扩缩容**: 启用自动扩缩容节省成本
3. **预留实例**: 生产环境使用预留实例
4. **定期审查**: 定期审查资源使用情况