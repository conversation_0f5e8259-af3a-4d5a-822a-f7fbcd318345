# 开发工具和配置

## 概述

本文档介绍Yuan Hui基础设施项目中使用的开发工具、配置文件和最佳实践，帮助开发团队保持一致的开发环境和工作流程。

## 核心开发工具

### 1. Node.js和包管理

#### Node.js版本管理
```bash
# 推荐使用nvm管理Node.js版本
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 安装项目所需版本
nvm install 18
nvm use 18
nvm alias default 18

# 验证版本
node --version  # v18.x.x
npm --version   # 9.x.x
```

#### NPM配置
```bash
# 设置npm配置
npm config set save-exact true          # 精确版本锁定
npm config set engine-strict true       # 严格引擎检查
npm config set audit-level moderate     # 安全审计级别

# 查看当前配置
npm config list
```

### 2. AWS工具链

#### AWS CLI配置
```bash
# 安装AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# 配置凭证和默认区域
aws configure
# AWS Access Key ID: [输入访问密钥]
# AWS Secret Access Key: [输入密钥]
# Default region name: ap-east-2
# Default output format: json

# 验证配置
aws sts get-caller-identity
```

#### CDK工具配置
```bash
# 全局安装CDK（可选）
npm install -g aws-cdk

# 项目本地CDK（推荐）
npm install aws-cdk-lib constructs

# 验证CDK版本
npx cdk --version
```

### 3. 代码编辑器配置

#### VS Code设置

创建`.vscode/settings.json`：
```json
{
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "typescript.suggest.autoImports": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.yaml": "yaml",
    "*.yml": "yaml"
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/cdk.out": true,
    "**/.git": true,
    "**/dist": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/cdk.out": true,
    "**/dist": true
  },
  "emmet.includeLanguages": {
    "typescript": "javascript"
  }
}
```

推荐扩展`.vscode/extensions.json`：
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-json",
    "amazonwebservices.aws-toolkit-vscode",
    "ms-vscode.vscode-yaml",
    "redhat.vscode-yaml",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

#### IntelliJ IDEA / WebStorm配置

TypeScript配置：
```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "declaration": true,
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "noImplicitThis": true,
    "alwaysStrict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": false,
    "inlineSourceMap": true,
    "inlineSources": true,
    "experimentalDecorators": true,
    "strictPropertyInitialization": false,
    "typeRoots": ["./node_modules/@types"]
  },
  "exclude": ["cdk.out", "node_modules"]
}
```

## 代码质量工具

### 1. ESLint配置

创建`.eslintrc.js`：
```javascript
module.exports = {
  env: {
    browser: false,
    es6: true,
    node: true,
  },
  extends: [
    '@typescript-eslint/recommended',
    'prettier'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    project: './tsconfig.json'
  },
  plugins: [
    '@typescript-eslint',
    'import'
  ],
  rules: {
    // TypeScript特定规则
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-non-null-assertion': 'warn',
    
    // 导入规则
    'import/order': ['error', {
      'groups': ['builtin', 'external', 'internal', 'parent', 'sibling', 'index'],
      'newlines-between': 'always'
    }],
    
    // 通用规则
    'no-console': 'warn',
    'prefer-const': 'error',
    'no-var': 'error'
  },
  ignorePatterns: ['cdk.out/', 'node_modules/', '*.js']
};
```

### 2. Prettier配置

创建`.prettierrc`：
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

创建`.prettierignore`：
```
node_modules/
cdk.out/
dist/
*.md
*.yaml
*.yml
```

### 3. Husky Git钩子

安装和配置：
```bash
npm install --save-dev husky lint-staged

# 安装husky钩子
npx husky install
npm pkg set scripts.prepare="husky install"

# 添加pre-commit钩子
npx husky add .husky/pre-commit "npx lint-staged"

# 添加commit-msg钩子
npx husky add .husky/commit-msg 'npx --no-install commitlint --edit "$1"'
```

配置`package.json`中的lint-staged：
```json
{
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ],
    "*.{json,yaml,yml,md}": [
      "prettier --write"
    ]
  }
}
```

## 测试工具配置

### 1. Jest配置

创建`jest.config.js`：
```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/test'],
  testMatch: ['**/*.test.ts'],
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  collectCoverageFrom: [
    'lib/**/*.ts',
    '!lib/**/*.d.ts',
    '!lib/**/index.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

### 2. Playwright配置

创建`playwright.config.ts`：
```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './test/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: process.env.BASE_URL || 'https://dp-dev.kh2u.com',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ],
  webServer: {
    command: 'npm run start:test',
    url: 'http://127.0.0.1:3000',
    reuseExistingServer: !process.env.CI
  }
});
```

## 构建和部署工具

### 1. NPM脚本配置

`package.json`中的scripts：
```json
{
  "scripts": {
    "build": "tsc",
    "watch": "tsc -w",
    "clean": "rm -rf cdk.out dist",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext .ts",
    "lint:fix": "eslint . --ext .ts --fix",
    "format": "prettier --write .",
    "format:check": "prettier --check .",
    "cdk": "cdk",
    "deploy:dev": "./scripts/deploy.sh dev",
    "deploy:prod": "./scripts/deploy.sh prod",
    "deploy:core": "./scripts/deploy.sh dev --group core",
    "deploy:apps": "./scripts/deploy.sh dev --group apps"
  }
}
```

### 2. Makefile配置

创建`Makefile`：
```makefile
.PHONY: help install build test deploy clean

help: ## 显示帮助信息
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

install: ## 安装依赖
	npm ci

build: ## 构建项目
	npm run build

test: ## 运行测试
	npm run test

lint: ## 代码检查
	npm run lint

format: ## 代码格式化
	npm run format

deploy-dev: ## 部署到开发环境
	npm run deploy:dev

deploy-prod: ## 部署到生产环境
	npm run deploy:prod

clean: ## 清理构建文件
	npm run clean
	rm -rf node_modules

setup: install build ## 初始化项目

ci: install lint test build ## CI流程
```

## 环境配置

### 1. 环境变量管理

创建`.env.example`：
```bash
# AWS配置
NODE_ENV=dev
CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=ap-east-2

# 部署配置
DEPLOY_TIMEOUT=2400
DEPLOY_PARALLEL=true
DEPLOY_LOGS=true

# 调试配置
CDK_DEBUG=false
AWS_PROFILE=default

# 测试配置
BASE_URL=https://dp-dev.kh2u.com
```

### 2. EditorConfig

创建`.editorconfig`：
```ini
root = true

[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.md]
trim_trailing_whitespace = false

[Makefile]
indent_style = tab
```

## Docker开发环境

### 1. 开发容器配置

创建`Dockerfile.dev`：
```dockerfile
FROM node:18-alpine

# 安装必要的工具
RUN apk add --no-cache \
    aws-cli \
    git \
    bash \
    curl \
    jq

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci

# 设置用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

COPY --chown=nextjs:nodejs . .

# 构建项目
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### 2. Docker Compose开发环境

创建`docker-compose.dev.yml`：
```yaml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - CDK_DEFAULT_REGION=ap-east-2
    ports:
      - "3000:3000"
    networks:
      - dev-network

  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: yuanhui_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - dev-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - dev-network

networks:
  dev-network:
    driver: bridge

volumes:
  postgres_data:
```

## CI/CD工具配置

### 1. GitHub Actions

创建`.github/workflows/ci.yml`：
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linter
        run: npm run lint

      - name: Run tests
        run: npm run test:coverage

      - name: Upload coverage
        uses: codecov/codecov-action@v3

      - name: Build project
        run: npm run build

  deploy-dev:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/develop'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-east-2

      - name: Deploy to development
        run: ./scripts/deploy.sh dev --no-confirm
```

### 2. Pre-commit配置

创建`.pre-commit-config.yaml`：
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      
  - repo: https://github.com/prettier/prettier
    rev: v3.0.0
    hooks:
      - id: prettier

  - repo: local
    hooks:
      - id: eslint
        name: eslint
        entry: npx eslint --fix
        language: node
        types: [typescript]
```

## 调试工具配置

### 1. VS Code调试配置

创建`.vscode/launch.json`：
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug CDK App",
      "skipFiles": ["<node_internals>/**"],
      "program": "${workspaceFolder}/bin/iac.ts",
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "dev"
      },
      "console": "integratedTerminal"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Tests",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": ["--runInBand"],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    }
  ]
}
```

### 2. Chrome DevTools调试

```bash
# 启用Node.js调试
node --inspect-brk=0.0.0.0:9229 ./bin/iac.ts

# 或使用ts-node
node --inspect-brk -r ts-node/register ./bin/iac.ts
```

## 性能监控工具

### 1. 构建性能分析

```bash
# 分析NPM包大小
npm run build -- --analyze

# 使用webpack-bundle-analyzer（如果使用webpack）
npx webpack-bundle-analyzer dist/stats.json
```

### 2. 运行时性能监控

```javascript
// lib/utils/performance.ts
export class PerformanceMonitor {
  private static timers = new Map<string, number>();

  static start(label: string): void {
    this.timers.set(label, performance.now());
  }

  static end(label: string): number {
    const start = this.timers.get(label);
    if (!start) {
      throw new Error(`Timer ${label} not found`);
    }
    
    const duration = performance.now() - start;
    console.log(`${label}: ${duration}ms`);
    this.timers.delete(label);
    
    return duration;
  }
}
```

## 最佳实践

### 1. 工具链版本管理

```json
{
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=8.0.0"
  },
  "volta": {
    "node": "18.17.0",
    "npm": "9.6.7"
  }
}
```

### 2. 依赖管理

```bash
# 定期更新依赖
npm update
npm audit fix

# 检查过时的包
npm outdated

# 清理不需要的依赖
npm prune
```

### 3. 配置文件组织

```
.
├── .vscode/           # VS Code配置
├── .github/           # GitHub Actions配置
├── scripts/           # 构建和部署脚本
├── config/           # 应用配置
├── test/             # 测试配置和文件
└── docs/             # 文档
```

这个工具配置指南涵盖了现代JavaScript/TypeScript项目的完整工具链，确保开发团队使用一致的工具和配置。