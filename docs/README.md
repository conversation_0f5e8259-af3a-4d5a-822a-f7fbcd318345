# 技术文档中心

欢迎使用Yuan Hui基础设施代码项目的技术文档中心。本项目使用AWS CDK构建了完整的云原生Odoo应用栈，支持企业级ERP和电商平台。

## 🏗️ 系统架构

### 核心架构文档
- [📋 **架构总览**](architecture/README.md) - 完整系统架构概述
- [🌐 **网络架构**](architecture/network.md) - VPC、安全组、WAF、零信任网络
- [⚙️ **ECS服务架构**](architecture/ecs-services.md) - 容器编排、服务发现、自动扩缩容
- [🗄️ **数据库架构**](architecture/database.md) - Aurora Serverless v2、读写分离、备份策略

### 基础设施栈组织
```
Core栈组（基础设施）        Apps栈组（应用服务）
├─ Network                 ├─ ClaudeRelay
├─ Ecs                     ├─ WikiJS（可选）
├─ ServiceConnect          ├─ Turnkey（可选）
├─ AuroraDatabase          ├─ CloudFront（生产环境）
├─ RabbitMQ                ├─ OpenZiti（生产环境）
├─ LoadBalancer            ├─ Swarm（可选）
├─ Airflow                 └─ Monitoring
└─ Security
```

## 🚀 快速部署

### 新手入门
- [⚡ **5分钟快速部署**](deployment/quick-start.md) - 一键部署完整开发环境
- [📘 **部署指南**](deployment/README.md) - 详细部署说明和故障排除

### 常用部署命令
```bash
# 开发环境一键部署
npm run deploy:dev

# 生产环境部署
npm run deploy:prod

# 分组部署（推荐）
npm run deploy:core      # 基础设施
npm run deploy:apps      # 应用服务
```

## 📦 服务文档

### 核心服务
- [🔄 **Airflow工作流引擎**](services/airflow/README.md) - 任务调度和工作流管理
- [📬 **RabbitMQ消息队列**](services/rabbitmq/README.md) - 异步消息处理
- [🗄️ **数据库服务**](services/database/README.md) - Aurora PostgreSQL数据库管理
- [⚡ **Redis缓存服务**](services/redis/README.md) - 会话存储和缓存

### 应用服务
- [🤖 **Claude Relay服务**](services/claude-relay/README.md) - Claude API中转服务
- [📚 **WikiJS文档系统**](services/wikijs/README.md) - 企业知识库（可选）
- [🧾 **Turnkey电子发票**](services/turnkey/README.md) - 电子发票系统（可选）

### 服务特性对比
| 服务类型 | 实例数量 | 存储类型 | 健康检查 | 自动扩缩容 |
|----------|----------|----------|----------|------------|
| ClaudeRelay | 1-3个 | 有状态(EFS) | ✅ | ✅ |
| RabbitMQ | 1个 | 有状态(EFS) | ✅ | ❌ |
| Airflow | 多个 | 有状态(EFS) | ✅ | ✅ |
| WikiJS | 1个 | 无状态 | ✅ | ❌ |
| Turnkey | 1个 | 无状态 | ✅ | ❌ |

## 🛠️ 运维管理

### 日常运维
- [📊 **运维指南**](operations/README.md) - 监控、日志、性能调优
- [🔒 **安全管理**](security/README.md) - 安全配置、访问控制、合规检查

### 故障排除
- [🔍 **故障排除指南**](troubleshooting/README.md) - 常见问题和解决方案

## 🔧 开发支持

### 开发环境
- [💻 **本地开发环境**](development/local-setup.md) - 开发环境搭建和配置
- [🧪 **测试指南**](development/testing.md) - 单元测试和集成测试

### 脚本和工具
- [📜 **脚本文档**](scripts/README.md) - 自动化脚本使用说明
- [🔧 **工具配置**](reference/tools-config.md) - 开发工具和配置

## 📚 参考资料

### 技术规范
- [📋 **API文档**](reference/api-docs.md) - REST API接口文档
- [🏗️ **编码规范**](reference/coding-standards.md) - 代码风格和最佳实践
- [📦 **版本管理**](reference/version-control.md) - Git工作流和发布流程

### 配置参考
- [⚙️ **环境配置**](reference/environment-config.md) - 开发/生产环境配置说明
- [🔐 **密钥管理**](reference/secrets-management.md) - 密钥和证书管理

## 🌍 环境信息

### 开发环境
- **区域**: ap-east-2（香港）
- **域名**: `*-dev.kh2u.com`, `j2mall.tw`
- **特点**: 小规格资源、单可用区、简化安全配置

### 生产环境
- **区域**: ap-east-2（香港）
- **域名**: `kh2u.com`, `j2mall.com`
- **特点**: 多可用区、完整安全配置、CDN加速、零信任网络

## 🔍 快速导航

### 按角色分类
- **👨‍💻 开发人员** → [架构文档](architecture/) + [开发环境](development/)
- **🚀 部署人员** → [部署指南](deployment/) + [脚本文档](scripts/)
- **🛠️ 运维人员** → [运维指南](operations/) + [故障排除](troubleshooting/)
- **🔒 安全人员** → [安全管理](security/) + [合规检查](security/compliance.md)

### 按任务分类
- **🆕 首次部署** → [快速开始](deployment/quick-start.md)
- **🔧 问题排查** → [故障排除](troubleshooting/)
- **📈 性能优化** → [性能调优](operations/performance-tuning.md)
- **🔄 版本升级** → [升级指南](operations/upgrade-guide.md)

### 按服务分类
- **数据库相关** → [数据库文档](services/database/) + [Aurora管理](services/database/aurora-management.md)
- **工作流相关** → [Airflow文档](services/airflow/) + [消息队列文档](services/rabbitmq/)
- **应用相关** → [Claude Relay](services/claude-relay/) + [WikiJS文档](services/wikijs/)

## 📝 文档维护

### 贡献指南
本文档使用Markdown格式编写，欢迎团队成员贡献和维护。

### 文档结构说明
- `architecture/` - 系统架构和设计文档
- `deployment/` - 部署和配置指南
- `services/` - 各个服务的详细文档
- `operations/` - 运维和监控指南
- `troubleshooting/` - 问题排查和解决方案
- `security/` - 安全配置和最佳实践
- `development/` - 开发环境和工具
- `scripts/` - 自动化脚本文档
- `reference/` - 技术参考和规范

### 最后更新
文档最后更新时间：2025年9月

---

💡 **提示**: 如果您是第一次使用本系统，建议从[快速开始指南](deployment/quick-start.md)开始。