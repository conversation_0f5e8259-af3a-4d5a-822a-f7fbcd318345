# 故障排除指南

## 概述

本指南帮助解决Yuan Hui基础设施项目中的常见问题，涵盖部署、运维和开发过程中可能遇到的各种故障情况。

## 故障分类

### 🚀 部署相关问题
- [部署失败排查](#部署失败排查)
- [权限问题解决](#权限问题解决)
- [网络连接问题](#网络连接问题)
- [资源冲突处理](#资源冲突处理)

### ⚙️ 服务运行问题
- [ECS任务启动失败](#ecs任务启动失败)
- [数据库连接问题](#数据库连接问题)
- [负载均衡器健康检查](#负载均衡器健康检查)
- [DNS和SSL证书问题](#dns和ssl证书问题)

### 📊 性能和监控问题
- [应用性能问题](#应用性能问题)
- [日志和监控问题](#日志和监控问题)
- [成本异常诊断](#成本异常诊断)

## 快速诊断工具

### 自动诊断脚本
```bash
# 权限检查
./scripts/check-permissions.sh

# 网络连通性测试
./scripts/test-network-routing.sh

# SSL证书验证
./scripts/verify-ssl-certificates.sh

# 服务健康检查
./scripts/test-airflow-deployment.sh dev
```

### 手动检查命令
```bash
# 检查AWS身份
aws sts get-caller-identity

# 查看栈状态
aws cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE

# 检查ECS服务
aws ecs describe-services --cluster yuanhui-odoo-dev --services yherp-dev khmall-dev

# 查看任务状态
aws ecs describe-tasks --cluster yuanhui-odoo-dev --tasks $(aws ecs list-tasks --cluster yuanhui-odoo-dev --query 'taskArns[0]' --output text)
```

## 部署失败排查

### 1. CloudFormation栈失败

#### 常见错误
- **资源名称冲突**: `AlreadyExistsException`
- **权限不足**: `AccessDenied`
- **资源限制**: `LimitExceeded`
- **依赖缺失**: `ResourceNotFound`

#### 排查步骤
```bash
# 1. 查看失败的栈事件
aws cloudformation describe-stack-events --stack-name <stack-name> \
  --query 'StackEvents[?ResourceStatus==`CREATE_FAILED` || ResourceStatus==`UPDATE_FAILED`]' \
  --output table

# 2. 查看详细错误信息
aws cloudformation describe-stack-events --stack-name <stack-name> \
  --query 'StackEvents[0].ResourceStatusReason'

# 3. 查看栈资源状态
aws cloudformation describe-stack-resources --stack-name <stack-name> \
  --query 'StackResources[?ResourceStatus!=`CREATE_COMPLETE` && ResourceStatus!=`UPDATE_COMPLETE`]'
```

#### 解决方案
```bash
# 删除失败的栈
cdk destroy <stack-name>

# 清理冲突资源
aws ec2 delete-vpc --vpc-id vpc-xxxxxx  # 如果VPC冲突

# 重新部署
./scripts/deploy.sh dev --stacks <stack-name>
```

### 2. ECS部署失败

#### 常见问题
- 镜像拉取失败
- 任务定义配置错误
- 资源不足
- 网络配置问题

#### 排查步骤
```bash
# 查看服务事件
aws ecs describe-services --cluster yuanhui-odoo-dev --services <service-name> \
  --query 'services[0].events[0:5]'

# 查看任务失败原因
aws ecs describe-tasks --cluster yuanhui-odoo-dev --tasks <task-arn> \
  --query 'tasks[0].stoppedReason'

# 查看容器日志
aws logs tail /aws/ecs/<service-name>-dev --follow
```

## 权限问题解决

### 常见权限错误

1. **CloudFormation权限不足**
```bash
# 错误信息
User: arn:aws:iam::123456789012:user/username is not authorized to perform: cloudformation:CreateStack

# 解决方案
# 1. 检查IAM策略
aws iam list-attached-user-policies --user-name <username>

# 2. 添加必需权限
aws iam attach-user-policy --user-name <username> \
  --policy-arn arn:aws:iam::aws:policy/PowerUserAccess
```

2. **ECS权限问题**
```bash
# 错误信息
AccessDenied: User is not authorized to perform ecs:CreateService

# 解决方案
# 确保用户有以下权限：
# - ecs:*
# - ec2:*
# - iam:PassRole
```

### 权限检查清单

运行权限检查脚本：
```bash
./scripts/check-permissions.sh
```

手动验证关键权限：
```bash
# CloudFormation权限
aws cloudformation list-stacks --max-items 1

# ECS权限
aws ecs list-clusters --max-items 1

# RDS权限
aws rds describe-db-clusters --max-items 1

# IAM权限
aws iam list-roles --max-items 1
```

## 网络连接问题

### 1. 负载均衡器无法访问

#### 症状
- 域名无法解析
- 连接超时
- SSL证书错误

#### 排查步骤
```bash
# 1. 检查ALB状态
aws elbv2 describe-load-balancers --names yuanhui-application-dev

# 2. 检查目标组健康
aws elbv2 describe-target-health --target-group-arn <target-group-arn>

# 3. 检查安全组规则
aws ec2 describe-security-groups --group-ids <security-group-id>

# 4. 测试DNS解析
nslookup dp-dev.kh2u.com
dig dp-dev.kh2u.com

# 5. 测试SSL证书
echo | openssl s_client -connect dp-dev.kh2u.com:443 -servername dp-dev.kh2u.com
```

### 2. 服务间通信问题

#### 症状
- 应用无法连接数据库
- Redis连接失败
- RabbitMQ连接超时

#### 排查步骤
```bash
# 1. 检查Service Connect配置
aws ecs describe-services --cluster yuanhui-odoo-dev --services <service-name> \
  --query 'services[0].serviceConnectConfiguration'

# 2. 检查安全组规则
aws ec2 describe-security-groups --filters "Name=tag:Name,Values=*yuanhui*"

# 3. 测试内部连通性（在容器内）
aws ecs execute-command --cluster yuanhui-odoo-dev --task <task-arn> \
  --container <container-name> --interactive --command "/bin/bash"

# 容器内测试
ping redis.yuanhui.local
telnet rabbitmq.yuanhui.local 5672
```

## ECS任务启动失败

### 常见原因分析

1. **镜像拉取失败**
```bash
# 查看任务失败详情
aws ecs describe-tasks --cluster yuanhui-odoo-dev --tasks <task-arn> \
  --query 'tasks[0].containers[0].reason'

# 可能输出：
# "CannotPullContainerError: pull image error: image pull failed"

# 解决方案：
# 1. 检查镜像是否存在
# 2. 检查ECR权限
# 3. 检查网络连接
```

2. **资源不足**
```bash
# 错误信息：
# "RESOURCE:MEMORY" or "RESOURCE:CPU"

# 解决方案：
# 1. 增加集群容量
aws ecs put-cluster-capacity-providers --cluster yuanhui-odoo-dev \
  --capacity-providers EC2 --default-capacity-provider-strategy \
  capacityProvider=EC2,weight=1

# 2. 调整任务资源配置
# 修改 lib/config/environments/dev.ts 中的资源配置
```

3. **健康检查失败**
```bash
# 查看健康检查配置
aws elbv2 describe-target-groups --target-group-arns <target-group-arn>

# 修改健康检查配置
aws elbv2 modify-target-group --target-group-arn <arn> \
  --health-check-interval-seconds 30 \
  --health-check-timeout-seconds 10 \
  --healthy-threshold-count 2 \
  --unhealthy-threshold-count 5
```

## 数据库连接问题

### Aurora连接失败

#### 常见问题
- 连接超时
- 认证失败
- SSL连接问题

#### 排查步骤
```bash
# 1. 检查Aurora集群状态
aws rds describe-db-clusters --db-cluster-identifier yuanhui-aurora-dev

# 2. 检查数据库实例状态
aws rds describe-db-instances --filters "Name=db-cluster-id,Values=yuanhui-aurora-dev"

# 3. 检查安全组配置
aws ec2 describe-security-groups --filters "Name=tag:Name,Values=*aurora*"

# 4. 测试数据库连接（在ECS任务中）
aws ecs execute-command --cluster yuanhui-odoo-dev --task <task-arn> \
  --container odoo --interactive --command "/bin/bash"

# 容器内测试
pg_isready -h writer.cluster-xxx.region.rds.amazonaws.com -p 5432
```

#### 解决方案
```bash
# 1. 重置数据库密码
aws secretsmanager update-secret --secret-id odoo-db-password \
  --secret-string '{"password":"new-password"}'

# 2. 更新安全组规则
aws ec2 authorize-security-group-ingress --group-id <sg-id> \
  --protocol tcp --port 5432 --source-group <ecs-sg-id>

# 3. 重启Aurora集群（如果需要）
aws rds stop-db-cluster --db-cluster-identifier yuanhui-aurora-dev
aws rds start-db-cluster --db-cluster-identifier yuanhui-aurora-dev
```

## 应用性能问题

### 1. 响应时间过长

#### 诊断工具
```bash
# 1. 查看ALB指标
aws cloudwatch get-metric-statistics \
  --namespace AWS/ApplicationELB \
  --metric-name ResponseTime \
  --dimensions Name=LoadBalancer,Value=<alb-full-name> \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T01:00:00Z \
  --period 300 \
  --statistics Average

# 2. 查看ECS服务CPU/内存使用率
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name CPUUtilization \
  --dimensions Name=ServiceName,Value=yherp-dev Name=ClusterName,Value=yuanhui-odoo-dev \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T01:00:00Z \
  --period 300 \
  --statistics Average

# 3. 查看数据库性能
aws cloudwatch get-metric-statistics \
  --namespace AWS/RDS \
  --metric-name DatabaseConnections \
  --dimensions Name=DBClusterIdentifier,Value=yuanhui-aurora-dev \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-01T01:00:00Z \
  --period 300 \
  --statistics Average
```

### 2. 内存泄漏问题

#### 监控和诊断
```bash
# 1. 查看容器内存使用趋势
aws logs filter-log-events \
  --log-group-name /aws/ecs/yherp-dev \
  --filter-pattern "memory" \
  --start-time 1640995200000

# 2. 重启有问题的任务
aws ecs stop-task --cluster yuanhui-odoo-dev --task <task-arn> --reason "Memory leak investigation"

# 3. 调整内存限制
# 修改任务定义中的内存配置
```

## 日志和监控问题

### 1. 日志丢失或不完整

#### 排查步骤
```bash
# 1. 检查日志组配置
aws logs describe-log-groups --log-group-name-prefix "/aws/ecs/"

# 2. 检查日志保留策略
aws logs describe-log-groups --log-group-name-prefix "/aws/ecs/" \
  --query 'logGroups[*].[logGroupName,retentionInDays]'

# 3. 查看日志驱动配置
aws ecs describe-task-definition --task-definition <task-def> \
  --query 'taskDefinition.containerDefinitions[0].logConfiguration'
```

### 2. CloudWatch费用异常

使用专门的诊断脚本：
```bash
# 运行费用诊断
./scripts/diagnose-cloudwatch-costs.sh

# 手动检查高费用项目
aws logs describe-log-groups --query 'sort_by(logGroups, &storedBytes)[*].[logGroupName,storedBytes]' --output table
```

## 成本异常诊断

### 费用监控脚本
```bash
# 快速成本检查
./scripts/quick-cost-check.sh

# 详细费用分析
./scripts/monitor-cloudwatch-costs.sh --days 30
```

### 成本优化建议
1. **调整日志保留期**：短期开发环境使用7天保留
2. **优化实例规格**：根据实际使用情况调整ECS实例大小
3. **清理未使用资源**：定期清理测试环境资源
4. **使用预留实例**：生产环境考虑购买预留实例

## 紧急恢复程序

### 1. 生产环境故障

#### 立即响应步骤
```bash
# 1. 评估影响范围
aws ecs describe-services --cluster yuanhui-odoo-prod --services yherp-prod khmall-prod

# 2. 检查负载均衡器状态
aws elbv2 describe-target-health --target-group-arn <prod-target-group-arn>

# 3. 查看最近的部署和更改
aws cloudformation describe-stacks --stack-name YuanhuiApplication-prod \
  --query 'Stacks[0].LastUpdatedTime'

# 4. 如果需要回滚
cdk deploy YuanhuiApplication-prod --previous-parameters
```

### 2. 数据库故障

#### 恢复步骤
```bash
# 1. 检查Aurora集群状态
aws rds describe-db-clusters --db-cluster-identifier yuanhui-aurora-prod

# 2. 如果需要恢复到时间点
aws rds restore-db-cluster-to-point-in-time \
  --source-db-cluster-identifier yuanhui-aurora-prod \
  --db-cluster-identifier yuanhui-aurora-restore \
  --restore-to-time 2024-01-01T12:00:00Z

# 3. 切换到备份集群
# 修改应用配置指向新集群
```

## 联系支持

### 内部支持
1. **检查文档**: 查看相关服务文档
2. **运行诊断脚本**: 使用自动诊断工具
3. **收集日志**: 准备详细的错误日志和配置信息

### 日志收集脚本
```bash
#!/bin/bash
# 收集故障排除信息
echo "=== 系统信息 ===" > troubleshoot-$(date +%Y%m%d_%H%M%S).log
echo "Environment: $NODE_ENV" >> troubleshoot.log
echo "Account: $(aws sts get-caller-identity --query Account --output text)" >> troubleshoot.log

echo "=== CloudFormation栈状态 ===" >> troubleshoot.log
aws cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE ROLLBACK_COMPLETE >> troubleshoot.log

echo "=== ECS服务状态 ===" >> troubleshoot.log
aws ecs describe-services --cluster yuanhui-odoo-$NODE_ENV --services yherp-$NODE_ENV khmall-$NODE_ENV >> troubleshoot.log

echo "=== 最近的CloudWatch错误 ===" >> troubleshoot.log
aws logs filter-log-events --log-group-name /aws/ecs/yherp-$NODE_ENV --filter-pattern "ERROR" --start-time $(date -d '1 hour ago' +%s)000 >> troubleshoot.log
```

这个故障排除指南涵盖了项目中可能遇到的大部分问题，提供了系统化的诊断和解决方案。