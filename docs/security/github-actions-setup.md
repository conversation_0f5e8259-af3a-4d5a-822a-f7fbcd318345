# GitHub Actions CI/CD 安全配置指南

## 概述

本文档介绍如何使用新创建的安全栈（Security Stack）为GitHub Actions CI/CD流程配置AWS权限。安全栈遵循最小权限原则，仅授予完成Docker镜像构建和ECS服务重启所需的最低权限。

## 安全栈功能

### 创建的资源

1. **IAM用户**: `yuanhui-github-actions-{environment}`
   - 专门用于GitHub Actions CI/CD流程
   - 位于 `/ci-cd/` 路径下，便于管理

2. **访问密钥**: AWS_ACCESS_KEY_ID 和 AWS_SECRET_ACCESS_KEY
   - 用于GitHub Actions中的AWS认证

3. **ECR权限策略**: `yuanhui-github-actions-ecr-{environment}`
   - ECR认证权限
   - 镜像推送和拉取权限
   - 仅限访问预定义的ECR仓库

4. **ECS权限策略**: `yuanhui-github-actions-ecs-{environment}`
   - ECS集群和服务查询权限
   - ECS服务更新权限
   - 仅限操作指定的ECS集群和服务

### 权限范围

#### ECR权限
- **认证权限**: `ecr:GetAuthorizationToken`
- **镜像操作权限**:
  - `ecr:BatchCheckLayerAvailability`
  - `ecr:GetDownloadUrlForLayer`
  - `ecr:BatchGetImage`
  - `ecr:PutImage`
  - `ecr:InitiateLayerUpload`
  - `ecr:UploadLayerPart`
  - `ecr:CompleteLayerUpload`
  - `ecr:DescribeRepositories`
  - `ecr:DescribeImages`
  - `ecr:ListImages`

**限制范围**: 仅限访问以下ECR仓库：
- `yherp`
- `yherp-airflow`

#### ECS权限
- **查询权限**:
  - `ecs:DescribeClusters`
  - `ecs:DescribeServices`
  - `ecs:DescribeTasks`
  - `ecs:DescribeTaskDefinition`
  - `ecs:ListServices`
  - `ecs:ListTasks`

- **更新权限**:
  - `ecs:UpdateService`

**限制范围**: 仅限操作以下资源：
- ECS集群: `yuanhui-odoo-{environment}`
- ECS服务: 集群内的所有服务（如 `yherp-{env}`, `khmall-{env}` 等）

## 部署安全栈

### 1. 部署命令

```bash
# 设置环境变量
export NODE_ENV=dev  # 或 prod
export CDK_DEFAULT_ACCOUNT=************
export CDK_DEFAULT_REGION=ap-east-2

# 编译项目
npm run build

# 部署安全栈
cdk deploy YuanhuiSecurity-dev --require-approval never
```

### 2. 获取凭证

部署完成后，CDK会输出以下信息：

```
Outputs:
YuanhuiSecurity-dev.GitHubActionsAccessKeyId = AKIA...
YuanhuiSecurity-dev.GitHubActionsSecretAccessKey = wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
YuanhuiSecurity-dev.GitHubActionsUserName = yuanhui-github-actions-dev
YuanhuiSecurity-dev.GitHubActionsUserArn = arn:aws:iam::************:user/ci-cd/yuanhui-github-actions-dev
YuanhuiSecurity-dev.EcrRepositories = yherp, yherp-airflow
YuanhuiSecurity-dev.EcsClusterName = yuanhui-odoo-dev
YuanhuiSecurity-dev.GitHubSecretsSetup = AWS_ACCESS_KEY_ID 和 AWS_SECRET_ACCESS_KEY
```

**⚠️ 重要**: 请立即复制并安全保存 `AWS_SECRET_ACCESS_KEY`，因为它只会显示一次。

## GitHub Actions 配置

### 1. 添加GitHub Secrets

在GitHub仓库的设置中添加以下Secrets：

1. 进入GitHub仓库 → Settings → Secrets and variables → Actions
2. 点击 "New repository secret"
3. 添加以下两个密钥：

| Secret名称 | 值 | 描述 |
|-----------|---|------|
| `AWS_ACCESS_KEY_ID` | 从CDK输出获取 | AWS访问密钥ID |
| `AWS_SECRET_ACCESS_KEY` | 从CDK输出获取 | AWS秘密访问密钥 |

### 2. GitHub Actions工作流示例

创建 `.github/workflows/deploy.yml` 文件：

```yaml
name: Build and Deploy

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  AWS_REGION: ap-east-2
  ECR_REGISTRY: ************.dkr.ecr.ap-east-2.amazonaws.com

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build and push Docker image
      env:
        ECR_REPOSITORY: yherp
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # 构建Docker镜像
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:latest .
        
        # 推送镜像到ECR
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

    - name: Update ECS service
      env:
        CLUSTER_NAME: yuanhui-odoo-dev
        SERVICE_NAME: yherp-dev
      run: |
        # 强制更新ECS服务以使用新镜像
        aws ecs update-service \
          --cluster $CLUSTER_NAME \
          --service $SERVICE_NAME \
          --force-new-deployment
        
        # 等待服务稳定
        aws ecs wait services-stable \
          --cluster $CLUSTER_NAME \
          --services $SERVICE_NAME
```

## 安全最佳实践

### 1. 权限最小化
- 安全栈严格限制权限范围，仅授予必要的操作权限
- ECR权限仅限于预定义的仓库
- ECS权限仅限于指定的集群和服务

### 2. 凭证管理
- 使用GitHub Secrets安全存储AWS凭证
- 定期轮换访问密钥
- 监控IAM用户的使用情况

### 3. 环境隔离
- 开发环境和生产环境使用不同的IAM用户
- 每个环境的权限严格隔离
- 使用不同的ECR仓库和ECS集群

### 4. 审计和监控
- 启用CloudTrail记录API调用
- 监控IAM用户的活动
- 设置异常访问告警

## 故障排除

### 常见问题

#### 1. ECR推送失败
```bash
# 检查ECR权限
aws ecr describe-repositories --repository-names yherp

# 检查认证状态
aws ecr get-authorization-token
```

#### 2. ECS服务更新失败
```bash
# 检查ECS权限
aws ecs describe-clusters --clusters yuanhui-odoo-dev

# 检查服务状态
aws ecs describe-services --cluster yuanhui-odoo-dev --services yherp-dev
```

#### 3. 权限不足错误
- 确认IAM用户已正确创建
- 检查策略是否正确附加到用户
- 验证资源ARN是否正确

### 验证命令

```bash
# 验证IAM用户
aws iam get-user --user-name yuanhui-github-actions-dev

# 验证用户策略
aws iam list-attached-user-policies --user-name yuanhui-github-actions-dev

# 测试ECR访问
aws ecr describe-repositories --repository-names yherp

# 测试ECS访问
aws ecs describe-clusters --clusters yuanhui-odoo-dev
```

## 清理资源

如果需要删除安全栈：

```bash
# 删除安全栈
cdk destroy YuanhuiSecurity-dev

# 确认删除
# 注意：这将删除IAM用户和访问密钥，请确保GitHub Actions不再使用这些凭证
```

## 扩展配置

### 添加新的ECR仓库权限

1. 在 `lib/config/environment.ts` 中的 `imageBuild.repositories` 数组中添加新仓库名称
2. 重新部署安全栈：`cdk deploy YuanhuiSecurity-dev`

### 添加新的ECS服务权限

安全栈已配置为允许访问指定集群内的所有服务，无需额外配置。

### 多环境部署

```bash
# 生产环境部署
export NODE_ENV=prod
cdk deploy YuanhuiSecurity-prod
```

生产环境将创建独立的IAM用户和权限，确保环境隔离。
