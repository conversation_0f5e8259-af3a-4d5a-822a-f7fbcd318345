# 安全栈 (Security Stack) 文档

## 概述

安全栈是元晖基础设施项目中专门用于管理外部应用和基础设施IAM权限的CDK栈。它遵循最小权限原则，为GitHub Actions CI/CD流程提供精确的AWS权限控制。

## 功能特性

### 🔐 权限管理
- **最小权限原则**: 仅授予完成指定任务所需的最低权限
- **环境隔离**: 开发和生产环境使用独立的IAM用户和权限
- **精确范围控制**: 权限限制在特定的ECR仓库和ECS集群

### 🚀 GitHub Actions集成
- **专用IAM用户**: 为CI/CD流程创建专门的IAM用户
- **访问密钥管理**: 自动生成AWS访问密钥对
- **策略自动化**: 自动创建和附加必要的权限策略

### 📦 ECR权限
- **镜像推送**: 支持Docker镜像构建和推送到ECR
- **仓库管理**: 查看和管理指定的ECR仓库
- **认证支持**: 完整的ECR认证和授权流程

### ⚙️ ECS权限
- **服务管理**: 查询和更新ECS服务
- **集群访问**: 访问指定的ECS集群资源
- **部署支持**: 支持强制部署和服务重启

## 架构设计

```
GitHub Actions
      ↓
  IAM User (CI/CD)
      ↓
┌─────────────────┐    ┌─────────────────┐
│   ECR Policy    │    │   ECS Policy    │
│                 │    │                 │
│ • 认证权限      │    │ • 查询权限      │
│ • 镜像推送      │    │ • 服务更新      │
│ • 仓库管理      │    │ • 集群访问      │
└─────────────────┘    └─────────────────┘
      ↓                        ↓
┌─────────────────┐    ┌─────────────────┐
│  ECR Repository │    │  ECS Cluster    │
│                 │    │                 │
│ • yherp         │    │ yuanhui-odoo-   │
│ • yherp-airflow │    │   {environment} │
└─────────────────┘    └─────────────────┘
```

## 权限矩阵

### ECR权限详情

| 权限 | 资源范围 | 用途 |
|------|----------|------|
| `ecr:GetAuthorizationToken` | `*` | ECR认证 |
| `ecr:BatchCheckLayerAvailability` | 指定仓库 | 检查镜像层 |
| `ecr:GetDownloadUrlForLayer` | 指定仓库 | 下载镜像层 |
| `ecr:BatchGetImage` | 指定仓库 | 获取镜像 |
| `ecr:PutImage` | 指定仓库 | 推送镜像 |
| `ecr:InitiateLayerUpload` | 指定仓库 | 开始上传 |
| `ecr:UploadLayerPart` | 指定仓库 | 上传镜像层 |
| `ecr:CompleteLayerUpload` | 指定仓库 | 完成上传 |
| `ecr:DescribeRepositories` | 指定仓库 | 查看仓库信息 |
| `ecr:DescribeImages` | 指定仓库 | 查看镜像信息 |
| `ecr:ListImages` | 指定仓库 | 列出镜像 |

### ECS权限详情

| 权限 | 资源范围 | 用途 |
|------|----------|------|
| `ecs:DescribeClusters` | 指定集群 | 查看集群状态 |
| `ecs:DescribeServices` | 指定服务 | 查看服务状态 |
| `ecs:DescribeTasks` | 指定任务 | 查看任务状态 |
| `ecs:DescribeTaskDefinition` | 所有任务定义 | 查看任务定义 |
| `ecs:ListServices` | 指定集群 | 列出服务 |
| `ecs:ListTasks` | 指定集群 | 列出任务 |
| `ecs:UpdateService` | 指定服务 | 更新服务 |

## 快速开始

### 1. 部署安全栈

```bash
# 使用部署脚本（推荐）
./scripts/deploy-security-stack.sh

# 或手动部署
export NODE_ENV=dev
npm run build
cdk deploy YuanhuiSecurity-dev
```

### 2. 配置GitHub Secrets

从CDK输出中获取访问密钥，并在GitHub仓库中添加：
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`

### 3. 配置GitHub Actions

参考 [GitHub Actions配置指南](github-actions-setup.md) 创建工作流。

## 文件结构

```
docs/security/
├── README.md                    # 本文档
├── github-actions-setup.md      # GitHub Actions详细配置指南
lib/stacks/
├── security-stack.ts           # 安全栈实现
scripts/
├── deploy-security-stack.sh    # 部署脚本
```

## 环境配置

### 开发环境 (dev)
- **IAM用户**: `yuanhui-github-actions-dev`
- **ECR仓库**: `yherp`, `yherp-airflow`
- **ECS集群**: `yuanhui-odoo-dev`
- **权限范围**: 开发环境资源

### 生产环境 (prod)
- **IAM用户**: `yuanhui-github-actions-prod`
- **ECR仓库**: `yherp`, `yherp-airflow`
- **ECS集群**: `yuanhui-odoo-prod`
- **权限范围**: 生产环境资源

## 安全最佳实践

### ✅ 已实施的安全措施
1. **最小权限原则**: 仅授予必要权限
2. **资源范围限制**: 权限限制在特定资源
3. **环境隔离**: 不同环境使用独立权限
4. **条件访问**: ECS更新权限包含集群条件限制

### 🔒 推荐的安全措施
1. **定期轮换密钥**: 建议每90天轮换一次访问密钥
2. **监控使用情况**: 启用CloudTrail记录API调用
3. **异常检测**: 设置异常访问告警
4. **权限审计**: 定期审查IAM权限

## 故障排除

### 常见问题

#### 1. 部署失败
```bash
# 检查AWS凭证
aws sts get-caller-identity

# 检查CDK Bootstrap
cdk bootstrap

# 查看详细错误
cdk deploy YuanhuiSecurity-dev --verbose
```

#### 2. 权限不足
```bash
# 验证IAM用户
aws iam get-user --user-name yuanhui-github-actions-dev

# 检查附加的策略
aws iam list-attached-user-policies --user-name yuanhui-github-actions-dev
```

#### 3. ECR推送失败
```bash
# 测试ECR访问
aws ecr describe-repositories --repository-names yherp

# 检查认证
aws ecr get-authorization-token
```

### 调试命令

```bash
# 查看栈状态
cdk list
cdk diff YuanhuiSecurity-dev

# 查看生成的模板
cdk synth YuanhuiSecurity-dev

# 查看栈输出
aws cloudformation describe-stacks --stack-name YuanhuiSecurity-dev
```

## 扩展和定制

### 添加新的ECR仓库

1. 编辑 `lib/config/environment.ts`：
```typescript
imageBuild: {
  repositories: ['yherp', 'yherp-airflow', 'new-repo'],
  // ...
}
```

2. 重新部署：
```bash
cdk deploy YuanhuiSecurity-dev
```

### 添加新的权限

1. 编辑 `lib/stacks/security-stack.ts`
2. 在相应的策略中添加新的权限
3. 重新部署栈

### 多区域支持

安全栈支持部署到不同区域，只需设置相应的环境变量：

```bash
export CDK_DEFAULT_REGION=us-west-2
./scripts/deploy-security-stack.sh
```

## 相关文档

- [GitHub Actions配置指南](github-actions-setup.md)
- [项目总体架构](../PROJECT_SUMMARY.md)
- [部署指南](../deployment/README.md)
- [运维指南](../operations/README.md)

## 支持和反馈

如有问题或建议，请：
1. 查看故障排除部分
2. 检查相关文档
3. 提交Issue或联系项目维护者
