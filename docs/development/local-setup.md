# 本地开发环境搭建

## 概述

本指南帮助开发人员在本地搭建Yuan Hui基础设施项目的开发环境，包括必要的工具安装、配置和验证。

## 系统要求

### 基础要求
- **操作系统**: macOS 10.15+, Ubuntu 20.04+, 或 Windows 10+ (WSL2)
- **内存**: 最少8GB，推荐16GB+
- **存储**: 可用空间10GB+
- **网络**: 稳定的互联网连接

### 必需软件版本
- **Node.js**: 18.x或20.x
- **npm**: 8.x+
- **Git**: 2.30+
- **AWS CLI**: 2.x
- **Docker**: 20.x+（可选，用于本地测试）

## 环境搭建步骤

### 1. 安装Node.js和npm

#### macOS (使用Homebrew)
```bash
# 安装Homebrew（如果未安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装Node.js
brew install node@18
brew link node@18 --force
```

#### macOS/Linux (使用nvm)
```bash
# 安装nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc

# 安装和使用Node.js 18
nvm install 18
nvm use 18
nvm alias default 18
```

#### Windows (WSL2)
```bash
# 在WSL2中使用nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18 && nvm use 18
```

### 2. 安装AWS CLI

#### macOS
```bash
# 使用Homebrew
brew install awscli

# 或者使用官方安装器
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install awscli

# 或使用官方安装器
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

#### Windows (WSL2)
```bash
# 在WSL2中安装
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

### 3. 安装Git和其他工具

```bash
# macOS
brew install git jq tree

# Ubuntu/Debian
sudo apt install git jq tree

# 验证安装
git --version
aws --version
node --version
npm --version
```

### 4. 克隆项目代码

```bash
# 克隆代码库
git clone <repository-url> yuanhui-iac
cd yuanhui-iac

# 检查项目结构
tree -L 2
```

### 5. 安装项目依赖

```bash
# 安装npm依赖
npm install

# 验证CDK安装
npx cdk --version
```

## AWS配置

### 1. 配置AWS凭证

```bash
# 配置AWS凭证
aws configure

# 输入以下信息：
# AWS Access Key ID: 您的访问密钥ID
# AWS Secret Access Key: 您的密钥
# Default region: ap-east-2
# Default output format: json
```

### 2. 验证AWS访问权限

```bash
# 验证身份
aws sts get-caller-identity

# 检查必需权限
./scripts/check-permissions.sh
```

### 3. Bootstrap CDK环境

```bash
# 设置环境变量
export NODE_ENV=dev
export CDK_DEFAULT_ACCOUNT=$(aws sts get-caller-identity --query Account --output text)
export CDK_DEFAULT_REGION=ap-east-2

# Bootstrap CDK环境（首次使用）
npx cdk bootstrap aws://$CDK_DEFAULT_ACCOUNT/$CDK_DEFAULT_REGION
```

## 开发环境配置

### 1. 环境变量设置

创建本地环境配置文件：

```bash
# 创建 .env.local 文件
cat > .env.local << 'EOF'
# 开发环境配置
NODE_ENV=dev
CDK_DEFAULT_ACCOUNT=************
CDK_DEFAULT_REGION=ap-east-2

# 部署配置
DEPLOY_TIMEOUT=2400
DEPLOY_PARALLEL=true
DEPLOY_LOGS=true

# 可选：调试配置
CDK_DEBUG=false
AWS_PROFILE=default
EOF

# 加载环境变量
source .env.local
```

### 2. IDE配置

#### VS Code配置
创建 `.vscode/settings.json`:

```json
{
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.exclude": {
    "**/node_modules": true,
    "**/cdk.out": true,
    "**/.git": true
  }
}
```

创建 `.vscode/extensions.json`:

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-json"
  ]
}
```

### 3. 开发脚本设置

```bash
# 添加便捷脚本到 ~/.bashrc 或 ~/.zshrc
cat >> ~/.bashrc << 'EOF'

# Yuan Hui项目便捷命令
alias yh-dev='cd /path/to/yuanhui-iac && source .env.local'
alias yh-build='npm run build'
alias yh-deploy='./scripts/deploy.sh dev'
alias yh-logs='aws logs tail /aws/ecs/yherp-dev --follow'
alias yh-status='aws ecs describe-services --cluster yuanhui-odoo-dev --services yherp-dev khmall-dev'

# 快速环境切换
yh-env() {
  if [ "$1" = "dev" ] || [ "$1" = "prod" ]; then
    export NODE_ENV=$1
    echo "Environment switched to: $NODE_ENV"
  else
    echo "Current environment: $NODE_ENV"
    echo "Usage: yh-env [dev|prod]"
  fi
}
EOF

# 重新加载配置
source ~/.bashrc
```

## 验证开发环境

### 1. 构建项目

```bash
# 清理并构建
npm run clean
npm run build

# 检查构建输出
ls -la cdk.out/
```

### 2. 运行CDK命令

```bash
# 列出所有栈
npx cdk list

# 显示配置差异（不部署）
npx cdk diff

# 生成CloudFormation模板
npx cdk synth > template.yaml
```

### 3. 运行开发脚本

```bash
# 检查权限
./scripts/check-permissions.sh

# 测试部署脚本（预览模式）
./scripts/deploy.sh dev --dry-run

# 测试网络脚本
./scripts/test-network-routing.sh
```

## 开发工作流

### 1. 日常开发流程

```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装/更新依赖
npm install

# 3. 构建项目
npm run build

# 4. 进行开发...

# 5. 测试更改
npx cdk diff

# 6. 部署到开发环境
./scripts/deploy.sh dev --stacks <修改的栈>
```

### 2. 代码提交流程

```bash
# 1. 检查代码质量
npm run lint
npm run test

# 2. 构建确认
npm run build

# 3. 提交代码
git add .
git commit -m "feat: describe your changes"
git push origin feature-branch

# 4. 创建Pull Request
```

### 3. 分支管理

```bash
# 创建功能分支
git checkout -b feature/new-feature

# 切换分支
git checkout main
git checkout develop

# 合并分支
git checkout main
git merge feature/new-feature
```

## 常用开发命令

### CDK命令

```bash
# 基础命令
npx cdk list                    # 列出所有栈
npx cdk diff                    # 显示差异
npx cdk synth                   # 合成模板
npx cdk deploy --all            # 部署所有栈
npx cdk destroy --all           # 删除所有栈

# 调试命令
npx cdk doctor                  # 诊断环境问题
npx cdk context --clear         # 清理上下文缓存
```

### AWS命令

```bash
# ECS相关
aws ecs list-clusters
aws ecs list-services --cluster yuanhui-odoo-dev
aws ecs describe-tasks --cluster yuanhui-odoo-dev --tasks <task-id>

# 日志相关
aws logs describe-log-groups
aws logs tail /aws/ecs/yherp-dev --follow

# CloudFormation相关
aws cloudformation list-stacks
aws cloudformation describe-stacks --stack-name <stack-name>
```

### 项目特定命令

```bash
# 部署命令
npm run deploy:dev              # 部署开发环境
npm run deploy:core             # 部署基础设施
npm run deploy:apps             # 部署应用服务

# 监控命令
npm run logs:yherp              # 查看Yherp日志
npm run logs:airflow            # 查看Airflow日志
npm run status:all              # 查看所有服务状态
```

## 故障排除

### 常见问题

1. **Node.js版本不兼容**
   ```bash
   # 错误: Node版本不支持
   Error: Node.js version not supported
   
   # 解决: 安装正确版本
   nvm install 18
   nvm use 18
   ```

2. **AWS权限不足**
   ```bash
   # 错误: 权限被拒绝
   Error: AccessDenied
   
   # 解决: 检查权限
   ./scripts/check-permissions.sh
   aws sts get-caller-identity
   ```

3. **CDK Bootstrap失败**
   ```bash
   # 错误: Bootstrap失败
   Error: CDK bootstrap failed
   
   # 解决: 清理后重试
   npx cdk context --clear
   npx cdk bootstrap --force
   ```

4. **依赖安装问题**
   ```bash
   # 错误: npm install失败
   Error: npm ERR! code ERESOLVE
   
   # 解决: 清理后重装
   rm -rf node_modules package-lock.json
   npm install
   ```

### 环境重置

```bash
# 完全重置开发环境
rm -rf node_modules cdk.out .env.local
npm install
npm run build
npx cdk bootstrap
```

### 获取帮助

1. **查看文档**: `docs/`目录下的相关文档
2. **运行诊断**: `./scripts/check-permissions.sh`
3. **查看日志**: CloudWatch日志或本地构建日志
4. **团队支持**: 联系项目维护人员

## 高级配置

### 开发环境优化

```bash
# 启用并行构建
export MAKEFLAGS="-j$(nproc)"

# 启用CDK缓存
export CDK_CONTEXT_WARNING=false

# 设置更大的Node.js堆内存
export NODE_OPTIONS="--max-old-space-size=4096"
```

### 调试配置

```bash
# 启用详细日志
export CDK_DEBUG=true
export AWS_DEBUG=true

# 启用构建调试
export DEBUG="*"
```

### 性能优化

```bash
# 使用本地缓存
npm config set cache ~/.npm-cache

# 使用国内镜像（可选）
npm config set registry https://registry.npmmirror.com/
```

这个开发环境搭建指南涵盖了从基础工具安装到高级配置的完整流程，帮助开发人员快速上手项目开发。