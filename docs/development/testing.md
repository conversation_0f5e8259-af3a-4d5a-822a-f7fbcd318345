# 测试指南

## 概述

本指南涵盖Yuan Hui基础设施项目的测试策略、工具和最佳实践，包括基础设施测试、集成测试和端到端测试。

## 测试策略

### 测试金字塔

```
        /\
       /E2E\      端到端测试（少量）
      /____\
     /      \
    /集成测试 \    集成测试（适量）
   /__________\
  /            \
 /   单元测试    \  单元测试（大量）
/________________\
```

### 测试分类

1. **基础设施测试**: CDK栈配置验证
2. **集成测试**: 服务间通信验证
3. **端到端测试**: 完整用户场景验证
4. **性能测试**: 负载和性能基准测试
5. **安全测试**: 安全配置和漏洞扫描

## 基础设施测试

### CDK单元测试

使用AWS CDK的测试框架验证栈配置：

```typescript
// test/network-stack.test.ts
import { App } from 'aws-cdk-lib';
import { Template } from 'aws-cdk-lib/assertions';
import { NetworkStack } from '../lib/stacks/network-stack';
import { getConfig } from '../lib/config';

describe('NetworkStack', () => {
  test('creates VPC with correct CIDR', () => {
    const app = new App();
    const config = getConfig('test');
    
    const stack = new NetworkStack(app, 'TestNetworkStack', {
      config: config.network,
      env: { account: '************', region: 'us-east-1' }
    });

    const template = Template.fromStack(stack);

    // 验证VPC CIDR
    template.hasResourceProperties('AWS::EC2::VPC', {
      CidrBlock: '10.0.0.0/16'
    });

    // 验证子网数量
    template.resourceCountIs('AWS::EC2::Subnet', 6);
  });

  test('creates security groups with correct rules', () => {
    // 测试安全组配置...
  });
});
```

### 快照测试

验证CloudFormation模板的变更：

```typescript
// test/template-snapshots.test.ts
describe('Template Snapshots', () => {
  test('NetworkStack template matches snapshot', () => {
    const app = new App();
    const config = getConfig('dev');
    
    const stack = new NetworkStack(app, 'NetworkStack', {
      config: config.network,
      env: { account: '************', region: 'ap-east-2' }
    });

    const template = Template.fromStack(stack);
    expect(template.toJSON()).toMatchSnapshot();
  });
});
```

### 配置验证测试

```typescript
// test/config.test.ts
import { getConfig } from '../lib/config';

describe('Configuration', () => {
  test('dev environment has correct settings', () => {
    const config = getConfig('dev');
    
    expect(config.environment).toBe('dev');
    expect(config.vpc.cidr).toBe('10.0.0.0/16');
    expect(config.ecs.enableWAF).toBe(false);
  });

  test('prod environment enables security features', () => {
    const config = getConfig('prod');
    
    expect(config.ecs.enableWAF).toBe(true);
    expect(config.ecs.enableCloudFront).toBe(true);
    expect(config.ecs.enableOpenZiti).toBe(true);
  });
});
```

## 集成测试

### 服务连通性测试

验证部署后的服务间连接：

```bash
#!/bin/bash
# test/integration/service-connectivity.sh

set -e

ENVIRONMENT=${1:-dev}
CLUSTER_NAME="yuanhui-odoo-${ENVIRONMENT}"

echo "=== Testing Service Connectivity ==="

# 1. 测试数据库连接
echo "Testing database connection..."
TASK_ARN=$(aws ecs run-task \
  --cluster $CLUSTER_NAME \
  --task-definition test-db-connection \
  --network-configuration awsvpcConfiguration="{subnets=[subnet-xxx],securityGroups=[sg-xxx]}" \
  --query 'tasks[0].taskArn' --output text)

# 等待任务完成
aws ecs wait tasks-stopped --cluster $CLUSTER_NAME --tasks $TASK_ARN

# 检查任务退出码
EXIT_CODE=$(aws ecs describe-tasks \
  --cluster $CLUSTER_NAME \
  --tasks $TASK_ARN \
  --query 'tasks[0].containers[0].exitCode')

if [ "$EXIT_CODE" -ne 0 ]; then
  echo "❌ Database connection test failed"
  exit 1
else
  echo "✅ Database connection test passed"
fi

# 2. 测试Redis连接
echo "Testing Redis connection..."
# 类似的测试逻辑...

# 3. 测试RabbitMQ连接
echo "Testing RabbitMQ connection..."
# 类似的测试逻辑...
```

### API集成测试

```python
# test/integration/test_api_integration.py
import pytest
import requests
import time

class TestAPIIntegration:
    def setup_class(self):
        self.base_url = "https://dp-dev.kh2u.com"
        
    def test_health_endpoint(self):
        """测试健康检查端点"""
        response = requests.get(f"{self.base_url}/web/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "ok"
        assert "database" in data
        assert "cache" in data
        
    def test_login_flow(self):
        """测试登录流程"""
        # 获取登录页面
        response = requests.get(f"{self.base_url}/web/login")
        assert response.status_code == 200
        
        # 提交登录表单
        login_data = {
            "login": "admin",
            "password": "test_password"
        }
        response = requests.post(
            f"{self.base_url}/web/login",
            data=login_data,
            allow_redirects=False
        )
        assert response.status_code in [200, 302]
        
    def test_database_operations(self):
        """测试数据库操作"""
        # 创建测试数据
        # 查询测试数据
        # 更新测试数据
        # 删除测试数据
        pass
```

## 端到端测试

### Playwright测试

使用Playwright进行浏览器自动化测试：

```typescript
// test/e2e/login.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Login Flow', () => {
  test('should login successfully', async ({ page }) => {
    // 访问登录页面
    await page.goto('https://dp-dev.kh2u.com/web/login');
    
    // 填写登录表单
    await page.fill('#login', 'admin');
    await page.fill('#password', 'admin');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 验证登录成功
    await expect(page).toHaveURL(/.*\/web$/);
    await expect(page.locator('.oe_topbar_name')).toBeVisible();
  });
  
  test('should handle invalid credentials', async ({ page }) => {
    await page.goto('https://dp-dev.kh2u.com/web/login');
    
    await page.fill('#login', 'invalid');
    await page.fill('#password', 'invalid');
    await page.click('button[type="submit"]');
    
    // 验证错误信息
    await expect(page.locator('.alert-danger')).toBeVisible();
  });
});
```

### 用户场景测试

```typescript
// test/e2e/user-scenarios.spec.ts
import { test, expect } from '@playwright/test';

test.describe('User Scenarios', () => {
  test('complete purchase flow', async ({ page }) => {
    // 1. 登录
    await page.goto('https://j2mall.tw');
    // ... 登录逻辑
    
    // 2. 浏览商品
    await page.click('[data-testid="product-category"]');
    await expect(page.locator('.product-item')).toHaveCount.greaterThan(0);
    
    // 3. 添加到购物车
    await page.click('.product-item:first-child');
    await page.click('[data-testid="add-to-cart"]');
    
    // 4. 结账流程
    await page.click('[data-testid="cart-icon"]');
    await page.click('[data-testid="checkout"]');
    
    // 5. 验证订单创建
    await expect(page.locator('[data-testid="order-success"]')).toBeVisible();
  });
});
```

## 性能测试

### 负载测试

使用K6进行负载测试：

```javascript
// test/performance/load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export const options = {
  stages: [
    { duration: '5m', target: 10 }, // 预热
    { duration: '10m', target: 50 }, // 正常负载
    { duration: '5m', target: 100 }, // 峰值负载
    { duration: '10m', target: 50 }, // 恢复
    { duration: '5m', target: 0 }, // 冷却
  ],
  thresholds: {
    http_req_duration: ['p(95)<2000'], // 95%请求<2秒
    http_req_failed: ['rate<0.1'], // 错误率<10%
  },
};

export default function () {
  // 测试主页
  const response = http.get('https://dp-dev.kh2u.com');
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 2s': (r) => r.timings.duration < 2000,
  });

  // 测试API端点
  const healthResponse = http.get('https://dp-dev.kh2u.com/web/health');
  check(healthResponse, {
    'health check returns ok': (r) => JSON.parse(r.body).status === 'ok',
  });

  sleep(1);
}
```

### 数据库性能测试

```sql
-- test/performance/database-performance.sql
-- 测试数据库查询性能

-- 1. 创建测试数据
INSERT INTO res_partner (name, email, is_company) 
SELECT 
    'Test Partner ' || generate_series,
    'test' || generate_series || '@example.com',
    false
FROM generate_series(1, 10000);

-- 2. 测试查询性能
EXPLAIN ANALYZE SELECT * FROM res_partner WHERE email LIKE '%test%';

-- 3. 测试索引效果
CREATE INDEX CONCURRENTLY idx_partner_email ON res_partner(email);
EXPLAIN ANALYZE SELECT * FROM res_partner WHERE email LIKE '%test%';

-- 4. 清理测试数据
DELETE FROM res_partner WHERE name LIKE 'Test Partner %';
```

## 安全测试

### 安全扫描

```bash
#!/bin/bash
# test/security/security-scan.sh

echo "=== Security Scanning ==="

# 1. 网络端口扫描
echo "Scanning open ports..."
nmap -sS -O dp-dev.kh2u.com

# 2. SSL证书检查
echo "Checking SSL certificates..."
echo | openssl s_client -connect dp-dev.kh2u.com:443 -servername dp-dev.kh2u.com 2>/dev/null | \
openssl x509 -noout -dates

# 3. HTTP安全头检查
echo "Checking security headers..."
curl -I https://dp-dev.kh2u.com | grep -i "security\|x-frame\|x-content\|strict-transport"

# 4. 依赖漏洞扫描
echo "Scanning dependencies..."
npm audit
```

### 权限验证测试

```python
# test/security/test_permissions.py
import boto3
import pytest

class TestAWSPermissions:
    def setup_class(self):
        self.ecs_client = boto3.client('ecs')
        self.rds_client = boto3.client('rds')
        
    def test_ecs_permissions(self):
        """测试ECS权限配置"""
        # 验证任务角色权限
        # 验证执行角色权限
        # 验证安全组配置
        pass
        
    def test_database_permissions(self):
        """测试数据库权限"""
        # 验证数据库用户权限
        # 验证网络访问限制
        pass
```

## 测试自动化

### GitHub Actions工作流

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run test:coverage
      
  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    if: github.event_name == 'push'
    steps:
      - uses: actions/checkout@v3
      - name: Run integration tests
        run: ./scripts/run-integration-tests.sh
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          
  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    steps:
      - uses: actions/checkout@v3
      - name: Install Playwright
        run: npx playwright install
      - name: Run E2E tests
        run: npx playwright test
```

### 本地测试命令

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:unit": "jest test/unit",
    "test:integration": "./scripts/run-integration-tests.sh",
    "test:e2e": "playwright test",
    "test:performance": "k6 run test/performance/load-test.js",
    "test:security": "./test/security/security-scan.sh"
  }
}
```

## 测试最佳实践

### 1. 测试组织

- **按层级组织**: 单元测试、集成测试、E2E测试分别放在不同目录
- **按功能组织**: 每个服务/组件有对应的测试文件
- **命名规范**: 使用描述性的测试名称

### 2. 测试数据管理

```typescript
// test/fixtures/test-data.ts
export const testUsers = {
  admin: {
    login: 'admin',
    password: 'admin',
    email: '<EMAIL>'
  },
  user: {
    login: 'demo',
    password: 'demo',
    email: '<EMAIL>'
  }
};

export const testProducts = [
  {
    name: 'Test Product 1',
    price: 100.00,
    category: 'Test Category'
  }
];
```

### 3. Mock和Stub

```typescript
// test/mocks/aws-mocks.ts
import { mockClient } from 'aws-sdk-client-mock';
import { ECSClient, DescribeServicesCommand } from '@aws-sdk/client-ecs';

const ecsClientMock = mockClient(ECSClient);

export const mockECSService = (serviceName: string, status: string) => {
  ecsClientMock.on(DescribeServicesCommand).resolves({
    services: [{
      serviceName,
      status,
      runningCount: 1,
      desiredCount: 1
    }]
  });
};
```

### 4. 测试环境管理

```bash
#!/bin/bash
# scripts/setup-test-environment.sh

set -e

echo "Setting up test environment..."

# 1. 创建测试数据库
aws rds create-db-instance \
  --db-instance-identifier test-db \
  --db-instance-class db.t3.micro \
  --engine postgres \
  --allocated-storage 20

# 2. 部署测试栈
export NODE_ENV=test
cdk deploy TestNetworkStack TestDatabaseStack

# 3. 初始化测试数据
./scripts/seed-test-data.sh

echo "Test environment ready!"
```

## 故障排除

### 常见测试问题

1. **测试超时**
   ```typescript
   // 增加超时时间
   test('long running test', async () => {
     // 测试逻辑
   }, 30000); // 30秒超时
   ```

2. **网络连接失败**
   ```bash
   # 检查网络配置
   aws ec2 describe-security-groups --filters "Name=tag:Name,Values=*test*"
   ```

3. **权限错误**
   ```bash
   # 检查测试用户权限
   aws iam list-attached-user-policies --user-name test-user
   ```

### 调试技巧

```typescript
// 启用详细日志
process.env.DEBUG = '*';

// 使用Jest调试模式
// npm test -- --runInBand --detectOpenHandles
```

## 持续改进

### 测试指标监控

- **覆盖率目标**: 单元测试 >90%, 集成测试 >70%
- **性能基准**: P95响应时间 <2秒
- **可靠性**: 测试通过率 >95%

### 定期审查

1. **每周**: 检查测试覆盖率和失败率
2. **每月**: 审查和更新测试用例
3. **每季度**: 性能基准测试和安全扫描

这个测试指南提供了完整的测试策略和实施方案，确保基础设施和应用的质量和可靠性。