# 文档规范指南

## 概述

本文档定义了元晖Odoo应用服务AWS基础设施项目的文档编写规范，确保所有文档的一致性、可读性和维护性。

## 文档结构规范

### 1. 文件命名规范

- **主要文档**: 使用 `README.md`
- **专题文档**: 使用小写字母和连字符，如 `deployment-guide.md`
- **配置文档**: 以功能命名，如 `odoo-longpolling-guide.md`

### 2. 目录结构

```
docs/
├── README.md                     # 项目总览（根目录）
├── DOCUMENTATION_STANDARDS.md   # 文档规范（本文件）
├── architecture/                 # 架构设计
│   └── README.md
├── deployment/                   # 部署指南
│   └── README.md
├── network/                      # 网络配置
│   ├── README.md
│   ├── deployment-guide.md
│   ├── odoo-longpolling-guide.md
│   ├── security-best-practices.md
│   └── troubleshooting-guide.md
└── operations/                   # 运维指南
    └── README.md
```

## Markdown格式规范

### 1. 标题层级

```markdown
# 一级标题（文档标题）
## 二级标题（主要章节）
### 三级标题（子章节）
#### 四级标题（详细说明）
```

### 2. 代码块格式

**Shell命令**:
```bash
# 注释说明
aws ecs describe-services --cluster yuanhui-odoo-prod
```

**配置文件**:
```typescript
// TypeScript代码
export const config: EnvironmentConfig = {
  region: 'ap-east-2',
  environment: 'prod'
};
```

**配置示例**:
```ini
# Odoo配置文件
[options]
workers = 2
longpolling_port = 8072
```

### 3. 表格格式

| 列名 | 描述 | 示例 | 必需 |
|------|------|------|------|
| 参数名 | 参数说明 | 示例值 | 是/否 |

### 4. 链接格式

- **内部链接**: `[文档名称](../path/to/document.md)`
- **外部链接**: `[AWS文档](https://docs.aws.amazon.com/)`
- **锚点链接**: `[章节名称](#章节标题)`

### 5. 列表格式

**无序列表**:
- 主要项目
  - 子项目
  - 另一个子项目

**有序列表**:
1. 第一步
2. 第二步
3. 第三步

**任务列表**:
- [x] 已完成任务
- [ ] 待完成任务

## 内容组织规范

### 1. 文档开头

每个文档应包含：
```markdown
# 文档标题

## 概述
简要说明文档内容和目标读者

## 目录（可选，长文档使用）
- [章节1](#章节1)
- [章节2](#章节2)
```

### 2. 章节组织

- **概述**: 简要介绍
- **前置条件**: 必要的准备工作
- **详细步骤**: 具体操作指南
- **验证方法**: 如何确认配置正确
- **故障排除**: 常见问题和解决方案
- **参考资料**: 相关文档链接

### 3. 代码示例规范

- 提供完整的可执行示例
- 包含必要的注释
- 使用实际的参数名称
- 标明环境变量替换位置

```bash
# 示例：部署特定环境
export NODE_ENV=prod  # 设置为 dev 或 prod
cdk deploy --all
```

## 技术写作规范

### 1. 语言风格

- 使用简体中文
- 保持简洁明了
- 避免过于技术化的术语
- 提供必要的背景说明

### 2. 术语一致性

| 术语 | 标准用法 | 避免使用 |
|------|----------|----------|
| 容器化 | 容器化部署 | Docker化 |
| 负载均衡器 | Application Load Balancer (ALB) | 负载均衡 |
| 零信任网络 | OpenZiti零信任网络 | ZT网络 |
| 数据库 | PostgreSQL数据库 | PG数据库 |

### 3. 版本信息

- 明确指出软件版本
- 更新时间戳
- 兼容性说明

```markdown
**版本信息**:
- Odoo: 16.x
- PostgreSQL: 15.x
- AWS CDK: 2.x
- 更新时间: 2024-01-15
```

## 维护规范

### 1. 更新频率

- **架构文档**: 架构变更时更新
- **部署指南**: 部署流程变更时更新
- **运维指南**: 月度审查和更新
- **故障排除**: 发现新问题时及时更新

### 2. 审查流程

1. 技术审查：确保技术准确性
2. 格式审查：检查Markdown格式
3. 链接检查：验证所有链接有效
4. 实际测试：验证操作步骤可行

### 3. 版本控制

- 使用Git跟踪文档变更
- 重大更新时创建标签
- 在commit信息中说明变更内容

## 工具和检查

### 1. Markdown检查工具

推荐使用以下工具检查文档格式：
- markdownlint
- Vale（写作风格检查）
- 链接检查器

### 2. 自动化检查

在CI/CD流程中集成文档检查：
```bash
# 检查Markdown格式
markdownlint docs/**/*.md

# 检查链接有效性
markdown-link-check docs/**/*.md
```

## 示例模板

### 配置指南模板

```markdown
# [功能名称]配置指南

## 概述
简要说明功能和用途

## 前置条件
- 条件1
- 条件2

## 配置步骤

### 1. 步骤标题
详细说明和代码示例

### 2. 验证配置
如何验证配置正确

## 故障排除
常见问题和解决方案

## 参考资料
相关文档链接
```

---

**注意**: 本规范是活文档，会根据项目需要持续更新。如有建议或问题，请提交Issue或联系文档维护团队。
