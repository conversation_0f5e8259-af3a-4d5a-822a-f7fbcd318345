# 元晖Odoo应用服务运维指南

## 日常运维任务

### 1. 系统监控

#### 关键指标监控
- **应用性能指标**
  - CPU使用率 < 70%
  - 内存使用率 < 80%
  - 响应时间 < 2秒
  - 错误率 < 1%

- **数据库性能指标**
  - CPU使用率 < 80%
  - 连接数 < 80%
  - 查询响应时间 < 100ms
  - 磁盘使用率 < 85%

- **网络指标**
  - 负载均衡器健康目标 > 80%
  - 网络延迟 < 50ms
  - 带宽使用率 < 80%

- **网络安全指标**
  - WAF阻止请求数: 监控恶意攻击
  - SSL证书有效期: 提前30天告警
  - CloudFront缓存命中率 > 80%
  - OpenZiti活跃连接数: 内部用户访问

- **域名和路由指标**
  - DNS解析成功率 > 99.9%
  - HTTPS连接成功率 > 99.5%
  - 域名路由正确性: yh/dp/jmall域名
  - 会话粘性效果: Odoo多数据库支持

#### 监控检查清单
```bash
# 每日检查
□ 查看CloudWatch仪表板
□ 检查告警状态
□ 验证备份完成状态
□ 查看应用日志错误
□ 检查WAF阻止的恶意请求
□ 验证SSL证书状态
□ 检查域名解析状态

# 每周检查
□ 审查性能趋势
□ 检查安全组变更
□ 验证备份恢复测试
□ 更新安全补丁
□ 分析CloudFront缓存效果
□ 检查OpenZiti连接状态
□ 验证负载均衡器路由规则

# 每月检查
□ 成本分析和优化
□ 容量规划评估
□ 安全审计
□ 灾难恢复演练
□ SSL证书续期检查
□ WAF规则优化
□ 网络安全评估
```

### 2. 日志管理

#### 日志位置
```bash
# 应用日志
/aws/ecs/yherp-{env}
/aws/ecs/khmall-{env}
/aws/ecs/odoo-cron-{env}

# 数据库日志
/aws/rds/cluster/{cluster-name}/postgresql

# 负载均衡器日志
s3://{bucket-name}/alb-logs/

# 网络安全日志
/aws/wafv2/yuanhui-{env}              # WAF日志
/aws/cloudfront/yuanhui-{env}         # CloudFront访问日志
/aws/ecs/ziti-controller-{env}        # OpenZiti控制器日志
/aws/ecs/ziti-router-{env}            # OpenZiti路由器日志

# VPC Flow Logs
/aws/vpc/flowlogs                     # VPC网络流量日志
```

#### 日志查询示例
```bash
# 查看最近的错误日志
aws logs filter-log-events \
  --log-group-name "/aws/ecs/yherp-prod" \
  --filter-pattern "ERROR" \
  --start-time $(date -d "1 hour ago" +%s)000

# 查看特定时间段的日志
aws logs filter-log-events \
  --log-group-name "/aws/ecs/yherp-prod" \
  --start-time 1640995200000 \
  --end-time 1641081600000

# 查看WAF阻止的请求
aws logs filter-log-events \
  --log-group-name "/aws/wafv2/yuanhui-prod" \
  --filter-pattern "BLOCK" \
  --start-time $(date -d "1 hour ago" +%s)000

# 查看SSL证书相关错误
aws logs filter-log-events \
  --log-group-name "/aws/ecs/yherp-prod" \
  --filter-pattern "SSL" \
  --start-time $(date -d "1 day ago" +%s)000

# 查看OpenZiti连接日志
aws logs filter-log-events \
  --log-group-name "/aws/ecs/ziti-controller-prod" \
  --filter-pattern "connection" \
  --start-time $(date -d "1 hour ago" +%s)000
```

### 3. Odoo Longpolling监控

#### Longpolling健康检查
```bash
# 检查longpolling端点状态
curl -I https://dp.kh2u.com/longpolling/poll
curl -I https://yh.kh2u.com/longpolling/poll
curl -I https://jmall.tw/longpolling/poll

# 检查ALB目标组健康状态
aws elbv2 describe-target-health \
  --target-group-arn $(aws elbv2 describe-target-groups \
    --names yherp-longpoll-tg-{env} \
    --query 'TargetGroups[0].TargetGroupArn' \
    --output text)

# 验证端口映射
aws ecs describe-tasks \
  --cluster yuanhui-odoo-{env} \
  --tasks $(aws ecs list-tasks \
    --cluster yuanhui-odoo-{env} \
    --service-name yherp-{env} \
    --query 'taskArns[0]' \
    --output text) \
  --query 'tasks[0].containers[0].networkBindings'
```

#### Longpolling性能监控
```bash
# 监控longpolling连接数
aws cloudwatch get-metric-statistics \
  --namespace AWS/ApplicationELB \
  --metric-name ActiveConnectionCount \
  --dimensions Name=TargetGroup,Value=yherp-longpoll-tg-{env} \
  --start-time $(date -d "1 hour ago" +%s) \
  --end-time $(date +%s) \
  --period 300 \
  --statistics Average

# 监控响应时间
aws cloudwatch get-metric-statistics \
  --namespace AWS/ApplicationELB \
  --metric-name TargetResponseTime \
  --dimensions Name=TargetGroup,Value=yherp-longpoll-tg-{env} \
  --start-time $(date -d "1 hour ago" +%s) \
  --end-time $(date +%s) \
  --period 300 \
  --statistics Average
```

### 4. 备份管理

#### 备份策略验证
```bash
# 检查备份任务状态
aws backup describe-backup-jobs \
  --by-backup-vault-name yuanhui-backup-vault-prod \
  --by-state COMPLETED

# 验证最近的备份
aws backup describe-recovery-points \
  --backup-vault-name yuanhui-backup-vault-prod
```

#### 恢复测试流程
1. **准备测试环境**
   ```bash
   # 创建测试VPC
   aws ec2 create-vpc --cidr-block 10.1.0.0/16
   ```

2. **执行恢复测试**
   ```bash
   # 从备份恢复数据库
   aws backup start-restore-job \
     --recovery-point-arn <recovery-point-arn> \
     --metadata <restore-metadata>
   ```

3. **验证恢复结果**
   ```bash
   # 连接测试数据库验证数据完整性
   psql -h <test-db-endpoint> -U odoo_admin -d odoo
   ```

### 4. 性能优化

#### 数据库优化
```sql
-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 查看数据库连接
SELECT count(*) as connections, state 
FROM pg_stat_activity 
GROUP BY state;

-- 查看表大小
SELECT schemaname,tablename,
  pg_size_pretty(size) as size,
  pg_size_pretty(total_size) as total_size
FROM (
  SELECT schemaname,tablename,
    pg_relation_size(schemaname||'.'||tablename) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as total_size
  FROM pg_tables
) AS TABLES
ORDER BY total_size DESC;
```

#### 应用优化
```bash
# 查看容器资源使用情况
aws ecs describe-services \
  --cluster yuanhui-odoo-prod \
  --services yherp-prod

# 调整服务容量
aws ecs update-service \
  --cluster yuanhui-odoo-prod \
  --service yherp-prod \
  --desired-count 3
```

### 5. 安全管理

#### 安全检查清单
```bash
# 检查安全组规则
aws ec2 describe-security-groups \
  --group-ids sg-xxxxxxxxx

# 查看IAM策略
aws iam list-attached-role-policies \
  --role-name YuanhuiEcsTaskRole

# 检查密钥轮换状态
aws secretsmanager describe-secret \
  --secret-id yuanhui-database-secret
```

#### 安全事件响应
1. **发现安全事件**
   - 监控CloudTrail日志
   - 检查异常登录活动
   - 验证资源访问模式

2. **事件响应流程**
   ```bash
   # 隔离受影响的资源
   aws ec2 modify-security-group-rules \
     --group-id sg-xxxxxxxxx \
     --security-group-rules GroupId=sg-xxxxxxxxx,SecurityGroupRuleId=sgr-xxxxxxxxx
   
   # 轮换受影响的密钥
   aws secretsmanager rotate-secret \
     --secret-id yuanhui-database-secret
   ```

### 6. 故障处理

#### 常见故障场景

**场景1：应用服务无响应**
```bash
# 1. 检查服务状态
aws ecs describe-services --cluster yuanhui-odoo-prod --services yherp-prod

# 2. 查看任务状态
aws ecs list-tasks --cluster yuanhui-odoo-prod --service-name yherp-prod

# 3. 检查任务日志
aws logs tail /aws/ecs/yherp-prod --follow

# 4. 重启服务
aws ecs update-service --cluster yuanhui-odoo-prod --service yherp-prod --force-new-deployment
```

**场景2：数据库连接问题**
```bash
# 1. 检查数据库状态
aws rds describe-db-clusters --db-cluster-identifier yuanhui-postgres-prod

# 2. 检查安全组
aws ec2 describe-security-groups --group-ids sg-database

# 3. 测试连接
telnet <db-endpoint> 5432

# 4. 检查数据库日志
aws logs tail /aws/rds/cluster/yuanhui-postgres-prod/postgresql
```

**场景3：负载均衡器问题**
```bash
# 1. 检查目标组健康状态
aws elbv2 describe-target-health --target-group-arn <target-group-arn>

# 2. 检查负载均衡器状态
aws elbv2 describe-load-balancers --load-balancer-arns <alb-arn>

# 3. 查看访问日志
aws s3 ls s3://alb-logs-bucket/AWSLogs/
```

### 7. 容量规划

#### 资源使用趋势分析
```bash
# 获取CPU使用率趋势
aws cloudwatch get-metric-statistics \
  --namespace AWS/ECS \
  --metric-name CPUUtilization \
  --dimensions Name=ServiceName,Value=yherp-prod \
  --start-time 2024-01-01T00:00:00Z \
  --end-time 2024-01-31T23:59:59Z \
  --period 3600 \
  --statistics Average
```

#### 扩容决策矩阵
| 指标 | 阈值 | 动作 |
|------|------|------|
| CPU > 70% (持续15分钟) | 自动扩容 | 增加1个实例 |
| 内存 > 80% (持续10分钟) | 自动扩容 | 增加1个实例 |
| 响应时间 > 3秒 | 手动检查 | 分析瓶颈原因 |
| 错误率 > 2% | 立即告警 | 紧急响应 |

### 8. 成本优化

#### 成本监控
```bash
# 查看月度成本
aws ce get-cost-and-usage \
  --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost \
  --group-by Type=DIMENSION,Key=SERVICE

# 查看资源使用情况
aws ce get-rightsizing-recommendation \
  --service EC2-Instance
```

#### 优化建议
1. **计算资源优化**
   - 使用Spot实例（非生产环境）
   - 购买预留实例（生产环境）
   - 定期审查实例规格

2. **存储优化**
   - 启用数据库存储自动扩容
   - 使用生命周期策略管理备份
   - 清理未使用的快照

3. **网络优化**
   - 优化数据传输路径
   - 使用CloudFront缓存静态内容
   - 监控跨区域数据传输

### 9. 应急响应

#### 紧急联系信息
- **技术负责人**: <EMAIL>
- **运维团队**: <EMAIL>
- **AWS支持**: 企业支持计划

#### 应急响应流程
1. **事件分类**
   - P1: 系统完全不可用
   - P2: 核心功能受影响
   - P3: 非核心功能问题
   - P4: 一般性问题

2. **响应时间要求**
   - P1: 15分钟内响应
   - P2: 1小时内响应
   - P3: 4小时内响应
   - P4: 24小时内响应

3. **升级路径**
   ```
   运维工程师 → 技术负责人 → CTO → AWS支持
   ```

## 运维工具

### 推荐工具
- **监控**: CloudWatch, Grafana
- **日志**: CloudWatch Logs, ELK Stack
- **部署**: AWS CDK, GitHub Actions
- **通信**: Slack, PagerDuty
- **文档**: Confluence, GitHub Wiki

### 自动化脚本
项目包含以下自动化脚本：
- `scripts/health-check.sh`: 健康检查脚本
- `scripts/backup-verify.sh`: 备份验证脚本
- `scripts/performance-report.sh`: 性能报告生成
- `scripts/cost-analysis.sh`: 成本分析脚本
