# 数据库服务

## 概述

本项目使用Amazon Aurora Serverless v2 PostgreSQL作为主要数据库服务，支持自动扩缩容、读写分离和高可用部署。

## 服务架构

详见：[数据库架构设计](../../architecture/database.md)

## 工具和组件

### 1. SQL执行构造 (SqlExecutorConstruct)

SQL执行构造是一个基于AWS CDK Custom Resource和Lambda函数的通用SQL执行方案，用于数据库初始化和维护任务。

详细文档：[SQL执行构造使用指南](sql-executor-guide.md)

**主要功能：**
- 数据库初始化
- 用户权限设置
- 表创建和修改
- 数据库维护任务

**使用示例：**
```typescript
const sqlExecutor = new SqlExecutorConstruct(this, 'DatabaseInit', {
  vpc,
  databaseCluster: auroraCluster,
  databaseCredentials: dbSecret,
  sqlCommands: [
    'CREATE DATABASE airflow_dev;',
    'CREATE USER airflow_user WITH PASSWORD \'secure_password\';',
    'GRANT ALL PRIVILEGES ON DATABASE airflow_dev TO airflow_user;'
  ],
});
```

### 2. 数据库连接管理

#### 连接端点
- **写端点**: 用于所有写操作和事务
- **读端点**: 用于只读查询和报表
- **集群端点**: 自动路由到可用实例

#### 连接池配置
```typescript
// 应用连接配置
const poolConfig = {
  max: 20,           // 最大连接数
  min: 5,            // 最小连接数
  idle: 10000,       // 空闲超时
  acquire: 30000,    // 获取连接超时
};
```

### 3. 备份和恢复

#### 自动备份
- **备份窗口**: 凌晨3:00-4:00
- **保留期限**: 7天（开发环境3天）
- **跨区域复制**: 生产环境启用

#### 手动备份
```bash
# 创建手动快照
aws rds create-db-cluster-snapshot \
  --db-cluster-identifier yuanhui-aurora-prod \
  --db-cluster-snapshot-identifier manual-backup-$(date +%Y%m%d)

# 从快照恢复
aws rds restore-db-cluster-from-snapshot \
  --db-cluster-identifier restored-cluster \
  --snapshot-identifier manual-backup-20240101
```

## 数据库管理

### 1. 用户管理

#### 创建应用用户
```sql
-- Odoo应用用户
CREATE USER odoo_user WITH PASSWORD 'secure_password';
GRANT CONNECT ON DATABASE odoo_prod TO odoo_user;
GRANT ALL PRIVILEGES ON DATABASE odoo_prod TO odoo_user;

-- Airflow应用用户
CREATE USER airflow_user WITH PASSWORD 'airflow_password';
GRANT CONNECT ON DATABASE airflow_prod TO airflow_user;
GRANT ALL PRIVILEGES ON DATABASE airflow_prod TO airflow_user;

-- 只读用户（报表和查询）
CREATE USER readonly_user WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE odoo_prod TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;
```

#### 权限管理
```sql
-- 限制连接数
ALTER USER odoo_user CONNECTION LIMIT 100;
ALTER USER airflow_user CONNECTION LIMIT 50;

-- 设置超时
ALTER USER odoo_user SET statement_timeout = '300s';
ALTER USER readonly_user SET statement_timeout = '60s';
```

### 2. 性能优化

#### 索引管理
```sql
-- 查看缺失索引
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
  AND n_distinct > 100
  AND correlation < 0.1;

-- 创建索引
CREATE INDEX CONCURRENTLY idx_table_column ON table_name(column_name);

-- 查看索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

#### 查询优化
```sql
-- 启用查询统计
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- 查看慢查询
SELECT query, calls, total_time, mean_time, stddev_time
FROM pg_stat_statements
WHERE mean_time > 1000
ORDER BY mean_time DESC
LIMIT 10;

-- 分析查询计划
EXPLAIN (ANALYZE, BUFFERS) SELECT * FROM large_table WHERE condition;
```

### 3. 监控和维护

#### 连接监控
```sql
-- 查看活动连接
SELECT pid, usename, application_name, client_addr, state, query_start, query
FROM pg_stat_activity
WHERE state = 'active'
ORDER BY query_start;

-- 查看锁等待
SELECT 
  blocked_locks.pid AS blocked_pid,
  blocked_activity.usename AS blocked_user,
  blocking_locks.pid AS blocking_pid,
  blocking_activity.usename AS blocking_user,
  blocked_activity.query AS blocked_statement,
  blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
  ON blocking_locks.locktype = blocked_locks.locktype
  AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
  AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
  AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
  AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
  AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
  AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
  AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
  AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
  AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
  AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

#### 空间使用
```sql
-- 查看数据库大小
SELECT datname, pg_size_pretty(pg_database_size(datname)) as size
FROM pg_database
ORDER BY pg_database_size(datname) DESC;

-- 查看表大小
SELECT schemaname, tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 故障排除

### 常见问题

1. **连接数过多**
   ```sql
   -- 查看当前连接数
   SELECT count(*) FROM pg_stat_activity;
   
   -- 按用户查看连接
   SELECT usename, count(*) FROM pg_stat_activity GROUP BY usename;
   
   -- 终止空闲连接
   SELECT pg_terminate_backend(pid)
   FROM pg_stat_activity
   WHERE state = 'idle' AND state_change < now() - interval '30 minutes';
   ```

2. **查询性能差**
   ```sql
   -- 查看正在运行的慢查询
   SELECT pid, usename, query_start, state, query
   FROM pg_stat_activity
   WHERE state = 'active' AND query_start < now() - interval '5 minutes';
   
   -- 终止长时间运行的查询
   SELECT pg_cancel_backend(pid) FROM pg_stat_activity WHERE pid = <pid>;
   ```

3. **磁盘空间问题**
   ```sql
   -- 查看最大的表
   SELECT nspname||'.'||relname AS relation,
     pg_size_pretty(pg_total_relation_size(C.oid)) AS total_size
   FROM pg_class C
   LEFT JOIN pg_namespace N ON (N.oid = C.relnamespace)
   WHERE nspname NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
     AND C.relkind <> 'i'
     AND nspname !~ '^pg_temp'
   ORDER BY pg_total_relation_size(C.oid) DESC
   LIMIT 20;
   ```

### 诊断命令
```bash
# 检查集群状态
aws rds describe-db-clusters --db-cluster-identifier yuanhui-aurora-prod

# 检查集群端点
aws rds describe-db-cluster-endpoints --db-cluster-identifier yuanhui-aurora-prod

# 查看CloudWatch指标
aws cloudwatch get-metric-statistics \
  --namespace AWS/RDS \
  --metric-name DatabaseConnections \
  --dimensions Name=DBClusterIdentifier,Value=yuanhui-aurora-prod \
  --statistics Maximum \
  --start-time $(date -d '1 hour ago' -u +%Y-%m-%dT%H:%M:%SZ) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%SZ) \
  --period 300
```

## 维护任务

### 日常维护
1. **统计信息更新**
   ```sql
   -- 更新所有表的统计信息
   ANALYZE;
   
   -- 更新特定表的统计信息
   ANALYZE table_name;
   ```

2. **索引维护**
   ```sql
   -- 重建索引（谨慎使用）
   REINDEX INDEX index_name;
   
   -- 重建表的所有索引
   REINDEX TABLE table_name;
   ```

3. **清理无用数据**
   ```sql
   -- 查看需要清理的表
   SELECT schemaname, tablename, n_dead_tup, n_live_tup,
     round(n_dead_tup::numeric / (n_live_tup + n_dead_tup) * 100, 2) as dead_ratio
   FROM pg_stat_user_tables
   WHERE n_dead_tup > 0
   ORDER BY dead_ratio DESC;
   
   -- 手动清理（通常由autovacuum自动处理）
   VACUUM table_name;
   ```

### 定期任务
- **每日**: 检查连接数和慢查询
- **每周**: 分析表统计信息和索引使用情况
- **每月**: 检查存储使用情况和性能趋势
- **每季度**: 评估扩缩容配置和成本优化

## 相关文档

- [SQL执行构造使用指南](sql-executor-guide.md)
- [数据库架构设计](../../architecture/database.md)
- [ECS服务架构](../../architecture/ecs-services.md)
- [监控配置](../../operations/README.md)