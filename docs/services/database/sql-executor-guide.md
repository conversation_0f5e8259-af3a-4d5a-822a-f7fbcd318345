# SQL执行构造使用指南

## 概述

SQL执行构造（SqlExecutorConstruct）是一个基于AWS CDK Custom Resource和Lambda函数的通用SQL执行方案，用于在AWS RDS/Aurora数据库中执行SQL操作。该构造支持数据库初始化、表创建、用户权限设置等各种SQL操作。

## 特性

- **运行时环境**: Python 3.12 Lambda函数
- **无依赖层**: 直接在Lambda函数代码中包含所需依赖，简化部署
- **双重执行模式**: 支持RDS Data API（Aurora Serverless）和直接数据库连接
- **安全性**: 遵循最小权限原则的IAM权限配置
- **错误处理**: 完善的错误处理和日志记录机制
- **SQL验证**: 内置SQL命令安全性验证
- **环境隔离**: 支持多环境部署和资源标签

## 架构组件

### 1. Lambda函数
- **运行时**: Python 3.12
- **超时**: 15分钟
- **内存**: 512MB
- **网络**: 部署在VPC私有子网中
- **日志保留**: 1周

### 2. IAM权限
- **Secrets Manager**: 读取数据库凭证
- **RDS Data API**: 执行SQL命令（Aurora Serverless）
- **CloudWatch Logs**: 日志记录
- **STS**: 获取账户信息

### 3. Custom Resource
- **生命周期管理**: Create/Update/Delete操作
- **属性传递**: SQL命令和配置参数
- **依赖管理**: 确保在数据库创建后执行

## 使用方法

### 基本用法

```typescript
import { SqlExecutorConstruct } from '../constructs/sql-executor-construct';

const sqlExecutor = new SqlExecutorConstruct(this, 'DatabaseSetup', {
  config: environmentConfig,
  vpc: vpc,
  databaseCluster: auroraCluster, // 或 databaseInstance: rdsInstance
  databaseSecret: databaseSecret,
  databaseSecurityGroup: databaseSecurityGroup,
  resourceName: 'my-database-setup',
  description: 'Initialize database schema and data',
  sqlCommands: [
    'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
    'CREATE SCHEMA IF NOT EXISTS app_data;',
    'CREATE TABLE IF NOT EXISTS app_data.users (id UUID PRIMARY KEY DEFAULT uuid_generate_v4(), name VARCHAR(255));',
  ],
});
```

### 高级用法示例

#### 1. 数据库初始化
```typescript
const databaseInit = new SqlExecutorConstruct(this, 'DatabaseInit', {
  config,
  vpc,
  databaseCluster: auroraCluster,
  databaseSecret: masterSecret,
  databaseSecurityGroup,
  resourceName: 'database-initialization',
  description: 'Initialize database with extensions and schemas',
  sqlCommands: [
    // 创建扩展
    'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
    'CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";',
    'CREATE EXTENSION IF NOT EXISTS "pg_trgm";',
    
    // 创建schema
    'CREATE SCHEMA IF NOT EXISTS app_data;',
    'CREATE SCHEMA IF NOT EXISTS audit_logs;',
    'CREATE SCHEMA IF NOT EXISTS reporting;',
    
    // 设置默认权限
    'ALTER DEFAULT PRIVILEGES IN SCHEMA app_data GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO app_user;',
  ],
});
```

#### 2. 用户权限管理
```typescript
const userManagement = new SqlExecutorConstruct(this, 'UserManagement', {
  config,
  vpc,
  databaseCluster: auroraCluster,
  databaseSecret: masterSecret,
  databaseSecurityGroup,
  resourceName: 'user-management',
  description: 'Create application users and set permissions',
  sqlCommands: [
    // 创建应用用户
    "CREATE USER app_user WITH PASSWORD 'secure_password_123';",
    "CREATE USER readonly_user WITH PASSWORD 'readonly_password_123';",
    
    // 授予权限
    'GRANT USAGE ON SCHEMA app_data TO app_user;',
    'GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA app_data TO app_user;',
    'GRANT USAGE ON SCHEMA app_data TO readonly_user;',
    'GRANT SELECT ON ALL TABLES IN SCHEMA app_data TO readonly_user;',
  ],
});
```

#### 3. 数据迁移
```typescript
const dataMigration = new SqlExecutorConstruct(this, 'DataMigration', {
  config,
  vpc,
  databaseCluster: auroraCluster,
  databaseSecret: masterSecret,
  databaseSecurityGroup,
  resourceName: 'data-migration-v1',
  description: 'Migrate data to new schema version',
  sqlCommands: [
    // 创建新表
    `CREATE TABLE IF NOT EXISTS app_data.user_profiles (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID REFERENCES app_data.users(id),
      profile_data JSONB,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );`,
    
    // 迁移现有数据
    `INSERT INTO app_data.user_profiles (user_id, profile_data)
     SELECT id, '{"migrated": true}'::jsonb
     FROM app_data.users
     WHERE id NOT IN (SELECT user_id FROM app_data.user_profiles WHERE user_id IS NOT NULL);`,
    
    // 创建索引
    'CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON app_data.user_profiles(user_id);',
    'CREATE INDEX IF NOT EXISTS idx_user_profiles_created_at ON app_data.user_profiles(created_at);',
  ],
});
```

## 配置参数

### SqlExecutorConstructProps

| 参数 | 类型 | 必需 | 描述 |
|------|------|------|------|
| config | EnvironmentConfig | ✓ | 环境配置 |
| vpc | ec2.Vpc | ✓ | VPC网络 |
| databaseCluster | rds.DatabaseCluster | - | Aurora集群（与databaseInstance二选一） |
| databaseInstance | rds.DatabaseInstance | - | RDS实例（与databaseCluster二选一） |
| databaseSecret | secretsmanager.Secret | ✓ | 数据库凭证 |
| databaseSecurityGroup | ec2.ISecurityGroup | ✓ | 数据库安全组 |
| sqlCommands | string[] | ✓ | 要执行的SQL命令列表 |
| resourceName | string | ✓ | 资源名称（用于标识和日志） |
| description | string | - | 资源描述 |

## 执行模式

### 1. RDS Data API模式（推荐）
- **适用于**: Aurora Serverless v2集群
- **优势**: 无需管理数据库连接，自动处理连接池
- **权限**: 需要RDS Data API权限
- **使用条件**: 设置了CLUSTER_IDENTIFIER环境变量

### 2. 直接连接模式
- **适用于**: 标准RDS实例
- **要求**: 需要psycopg2库（当前版本会提示错误）
- **建议**: 在生产环境中使用RDS Data API模式

## 安全考虑

### 1. IAM权限
- 遵循最小权限原则
- 使用资源标签进行条件访问控制
- 限制Secrets Manager访问范围

### 2. SQL验证
- 检查危险SQL关键字
- 限制SQL命令长度
- 记录潜在危险操作

### 3. 网络安全
- Lambda函数部署在私有子网
- 通过安全组控制数据库访问
- 所有流量在VPC内部

## 监控和日志

### CloudWatch日志
- **日志组**: `/aws/lambda/*sql-executor*`
- **保留期**: 1周
- **内容**: 执行状态、错误信息、性能指标

### 关键日志信息
- SQL命令执行状态
- 执行时间和性能
- 错误详情和堆栈跟踪
- 安全警告和验证结果

## 故障排除

### 常见问题

#### 1. 权限错误
```
Error: User does not have permission to access secret
```
**解决方案**: 检查IAM权限和资源标签配置

#### 2. 网络连接问题
```
Error: Could not connect to database
```
**解决方案**: 
- 检查安全组规则
- 确认Lambda在正确的子网中
- 验证数据库端点可达性

#### 3. SQL执行失败
```
Error: relation "table_name" does not exist
```
**解决方案**:
- 检查SQL语法
- 确认表/schema存在
- 验证执行顺序

#### 4. 超时错误
```
Error: Task timed out after 15.00 seconds
```
**解决方案**:
- 减少SQL命令数量
- 优化SQL性能
- 分批执行大量操作

### 调试步骤

1. **检查CloudWatch日志**
   ```bash
   aws logs tail /aws/lambda/YuanhuiAuroraDatabase-dev-SqlExecutorExample --follow
   ```

2. **验证IAM权限**
   ```bash
   aws iam simulate-principal-policy \
     --policy-source-arn <lambda-role-arn> \
     --action-names secretsmanager:GetSecretValue \
     --resource-arns <secret-arn>
   ```

3. **测试数据库连接**
   ```bash
   aws rds-data execute-statement \
     --resource-arn <cluster-arn> \
     --secret-arn <secret-arn> \
     --database postgres \
     --sql "SELECT version();"
   ```

## 最佳实践

### 1. SQL命令组织
- 将相关的SQL命令分组
- 使用事务确保数据一致性
- 添加适当的错误处理

### 2. 资源命名
- 使用描述性的resourceName
- 包含版本信息用于迁移
- 遵循项目命名约定

### 3. 依赖管理
- 明确设置资源依赖关系
- 确保执行顺序正确
- 避免循环依赖

### 4. 环境管理
- 使用环境特定的配置
- 区分开发和生产环境的SQL
- 实施适当的测试策略

## 快速开始

### 1. 导入构造
```typescript
import { SqlExecutorConstruct } from '../constructs/sql-executor-construct';
```

### 2. 基本配置
```typescript
const sqlExecutor = new SqlExecutorConstruct(this, 'MyDatabaseSetup', {
  config: environmentConfig,
  vpc: vpc,
  databaseCluster: auroraCluster,
  databaseSecret: databaseSecret,
  databaseSecurityGroup: databaseSecurityGroup,
  resourceName: 'my-setup',
  sqlCommands: ['CREATE EXTENSION IF NOT EXISTS "uuid-ossp";'],
});
```

### 3. 部署
```bash
npm run build
npx cdk deploy YourStackName-dev
```

### 4. 验证
检查CloudWatch日志确认执行成功：
```bash
aws logs tail /aws/lambda/*sql-executor* --follow
```

## 相关文档

- [使用示例](../examples/sql-executor-examples.ts)
- [AWS RDS Data API文档](https://docs.aws.amazon.com/AmazonRDS/latest/AuroraUserGuide/data-api.html)
- [AWS CDK Custom Resources](https://docs.aws.amazon.com/cdk/v2/guide/custom_resource.html)
- [PostgreSQL文档](https://www.postgresql.org/docs/)
- [项目架构文档](./architecture/README.md)
