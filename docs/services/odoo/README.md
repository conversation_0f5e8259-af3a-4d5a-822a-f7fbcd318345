# Odoo 应用服务

## 概述

Odoo是一个开源的企业资源规划（ERP）软件套件，包含CRM、销售、项目管理、制造和库存管理等应用。本项目部署了多个Odoo服务实例以支持不同的业务需求。

## 服务架构

```
┌─────────────────────────────────────────────────────────────┐
│                 Odoo Services Architecture                 │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │    Yherp    │  │   Khmall    │  │  Odoo Cron  │         │
│  │   Service   │  │   Service   │  │   Service   │         │
│  │             │  │             │  │             │         │
│  │ Port 8069   │  │ Port 8069   │  │ Background  │         │
│  │ Port 8072   │  │ Port 8072   │  │   Tasks     │         │
│  │(Longpoll)   │  │(Longpoll)   │  └─────────────┘         │
│  └─────────────┘  └─────────────┘                          │
│         │                │                                 │
│         │                │                                 │
│         ▼                ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Shared Resources                           ││
│  │                                                         ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      ││
│  │  │  PostgreSQL │  │    Redis    │  │  RabbitMQ   │      ││
│  │  │  Database   │  │   Cache     │  │Message Queue│      ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘      ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 服务实例

### 1. Yherp 服务
**用途**: 内部员工ERP系统  
**域名**: 
- 内部访问: yh.kh2u.com (OpenZiti零信任网络)
- 外部访问: dp.kh2u.com (WAF防护)

**配置**:
- CPU: 1024 units
- 内存: 2048 MB
- 端口: 8069 (Web), 8072 (Longpolling)
- 实例数: 2个（可扩展）

### 2. Khmall 服务  
**用途**: 电商平台系统  
**域名**: j2mall.com (CloudFront CDN加速)

**配置**:
- CPU: 1024 units  
- 内存: 2048 MB
- 端口: 8069 (Web), 8072 (Longpolling)
- 实例数: 2个（可扩展）

### 3. Cron 服务
**用途**: 后台任务和定时作业处理  
**特点**: 无Web界面，专门处理异步任务

**配置**:
- CPU: 512 units
- 内存: 1024 MB  
- 实例数: 1个

## 部署配置

### 容器镜像
- **基础镜像**: 自定义Odoo镜像
- **版本**: Odoo 18
- **Python**: 3.11
- **操作系统**: Ubuntu 22.04

### 环境变量配置
```bash
# 数据库配置
DB_HOST=writer.cluster-xxx.region.rds.amazonaws.com
DB_PORT=5432
DB_USER=odoo_user  
DB_PASSWORD=${DB_SECRET}
DB_NAME=odoo_prod

# 服务配置
ODOO_RC=/etc/odoo/odoo.conf
WITHOUT_DEMO=True
RUNNING_ENV=prod

# 缓存配置
REDIS_HOST=redis.yuanhui.local
REDIS_PORT=6379

# 队列配置
RABBITMQ_HOST=rabbitmq.yuanhui.local
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=${RABBITMQ_SECRET}
```

### Odoo配置文件
```ini
# /etc/odoo/odoo.conf

[options]
# 数据库配置
db_host = writer.cluster-xxx.region.rds.amazonaws.com
db_port = 5432
db_user = odoo_user
db_password = ${DB_PASSWORD}
db_maxconn = 64
db_template = template0

# 服务器配置  
http_port = 8069
longpolling_port = 8072
proxy_mode = True
workers = 4
max_cron_threads = 1
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_time_cpu = 600
limit_time_real = 1200

# 会话存储
session_store = redis
session_redis_host = redis.yuanhui.local
session_redis_port = 6379

# 日志配置
log_level = info
log_handler = :INFO

# 安全配置
admin_passwd = ${ADMIN_PASSWD}
list_db = False
```

## Longpolling 支持

### 架构设计
Odoo 18支持实时通信功能，需要独立的longpolling端口：

- **主应用端口**: 8069 (HTTP请求)
- **Longpolling端口**: 8072 (WebSocket连接)

### 负载均衡配置
```typescript
// ALB监听器配置
const webListener = alb.addListener('WebListener', {
  port: 80,
  defaultAction: elbv2.ListenerAction.redirect({
    port: '443',
    protocol: 'HTTPS',
  }),
});

const httpsListener = alb.addListener('HttpsListener', {
  port: 443,
  certificates: [certificate],
  defaultAction: elbv2.ListenerAction.fixedResponse(404),
});

// Longpolling监听器
const longpollingListener = alb.addListener('LongpollingListener', {
  port: 8072,
  defaultTargetGroups: [longpollingTargetGroup],
});
```

### 客户端配置
浏览器会自动建立到8072端口的WebSocket连接，实现：
- **实时通知**: 系统消息、任务完成通知
- **协作功能**: 多用户同时编辑文档
- **状态更新**: 实时显示记录状态变化

## 多数据库支持

### 数据库隔离
每个服务实例可以连接不同的数据库：

```python
# Yherp服务配置
DATABASE_MAPPING = {
    'yh.kh2u.com': 'yherp_prod',
    'dp.kh2u.com': 'yherp_prod', 
}

# Khmall服务配置  
DATABASE_MAPPING = {
    'j2mall.com': 'khmall_prod',
}
```

### 会话粘性
通过ALB配置会话粘性确保用户会话一致性：

```typescript
const targetGroup = new elbv2.ApplicationTargetGroup(this, 'TargetGroup', {
  port: 8069,
  vpc,
  targetType: elbv2.TargetType.IP,
  healthCheck: {
    path: '/web/health',
    healthyHttpCodes: '200',
  },
  stickinessCookieDuration: cdk.Duration.hours(8),
});
```

## 自动扩缩容

### 扩容策略
```typescript
const scalableTarget = service.autoScaleTaskCount({
  minCapacity: 1,
  maxCapacity: 10,
});

// CPU扩容
scalableTarget.scaleOnCpuUtilization('CpuScaling', {
  targetUtilizationPercent: 70,
  scaleInCooldown: cdk.Duration.seconds(300),
  scaleOutCooldown: cdk.Duration.seconds(60),
});

// 内存扩容
scalableTarget.scaleOnMemoryUtilization('MemoryScaling', {
  targetUtilizationPercent: 80,
  scaleInCooldown: cdk.Duration.seconds(300),
  scaleOutCooldown: cdk.Duration.seconds(60),
});
```

### 负载测试
```bash
# 使用Apache Bench进行负载测试
ab -n 1000 -c 10 http://dp.kh2u.com/web/login

# 监控扩容情况
aws ecs describe-services \
  --cluster yuanhui-odoo-dev \
  --services yherp-dev \
  --query 'services[0].deployments[0].runningCount'
```

## 监控和日志

### 健康检查
所有Odoo服务都配置了健康检查端点：

```python
# /web/health端点返回服务状态
{
  "status": "ok",
  "database": "connected", 
  "cache": "connected",
  "version": "18.0"
}
```

### 关键指标
- **响应时间**: 页面加载速度
- **错误率**: HTTP 4xx/5xx错误
- **并发用户**: 同时在线用户数
- **数据库连接**: 连接池使用情况
- **内存使用**: 工作进程内存消耗

### 日志收集
```bash
# 查看应用日志
aws logs tail /aws/ecs/yherp-dev --follow

# 查看特定时间段日志
aws logs filter-log-events \
  --log-group-name /aws/ecs/yherp-dev \
  --start-time 1640995200000 \
  --end-time 1641081600000 \
  --filter-pattern "ERROR"
```

## 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查任务定义
   aws ecs describe-task-definition \
     --task-definition yherp-dev:latest
   
   # 查看任务失败原因
   aws ecs describe-tasks \
     --cluster yuanhui-odoo-dev \
     --tasks <task-id>
   ```

2. **数据库连接错误**
   ```bash
   # 测试数据库连接
   pg_isready -h writer.cluster-xxx.region.rds.amazonaws.com -p 5432
   
   # 检查数据库密钥
   aws secretsmanager get-secret-value \
     --secret-id odoo-db-password
   ```

3. **Longpolling连接失败**
   ```bash
   # 检查8072端口监听
   curl -I http://dp.kh2u.com:8072/longpolling/health
   
   # 检查WebSocket连接
   wscat -c ws://dp.kh2u.com:8072/websocket
   ```

4. **性能问题**
   ```python
   # Odoo性能分析
   # 在Odoo shell中执行
   import time
   import psutil
   
   # CPU使用率
   print(f"CPU Usage: {psutil.cpu_percent()}%")
   
   # 内存使用率  
   memory = psutil.virtual_memory()
   print(f"Memory Usage: {memory.percent}%")
   
   # 数据库连接数
   cr = self.env.cr
   cr.execute("SELECT count(*) FROM pg_stat_activity")
   connections = cr.fetchone()[0]
   print(f"DB Connections: {connections}")
   ```

### 调试模式
```bash
# 启用调试模式
docker run -it \
  -e ODOO_RC=/etc/odoo/odoo.conf \
  -e LOG_LEVEL=debug \
  odoo:latest \
  --dev=all --log-level=debug
```

## 性能优化

### 数据库优化
```sql  
-- 创建索引加速查询
CREATE INDEX CONCURRENTLY idx_res_users_login ON res_users(login);
CREATE INDEX CONCURRENTLY idx_res_partner_name ON res_partner USING gin(to_tsvector('simple', name));

-- 分析表统计信息
ANALYZE res_users;
ANALYZE res_partner;
```

### 缓存优化
```python
# 启用查询缓存
from odoo.tools import ormcache

class ResPartner(models.Model):
    _inherit = 'res.partner'
    
    @api.model
    @ormcache('self._uid', 'partner_id')
    def get_partner_info(self, partner_id):
        # 缓存partner信息
        return self.browse(partner_id).read(['name', 'email'])
```

### 资源配置优化
```ini
# odoo.conf优化配置
workers = 4                    # CPU核心数
max_cron_threads = 1          # cron线程数
db_maxconn = 64               # 数据库连接池
limit_memory_hard = 2684354560 # 硬内存限制2.5GB
limit_memory_soft = 2147483648 # 软内存限制2GB
limit_time_cpu = 600          # CPU时间限制10分钟
limit_time_real = 1200        # 真实时间限制20分钟
```

## 备份和恢复

### 数据备份
```bash
# 数据库备份
pg_dump -h writer.cluster-xxx.region.rds.amazonaws.com \
        -U odoo_user \
        -d odoo_prod \
        -f odoo-backup-$(date +%Y%m%d).sql

# 文件存储备份（如果有）
tar -czf filestore-backup-$(date +%Y%m%d).tar.gz /var/lib/odoo/filestore/
```

### 数据恢复
```bash
# 恢复数据库
createdb -h writer.cluster-xxx.region.rds.amazonaws.com \
         -U odoo_user \
         odoo_restored

psql -h writer.cluster-xxx.region.rds.amazonaws.com \
     -U odoo_user \
     -d odoo_restored \
     -f odoo-backup-20240101.sql

# 恢复文件存储
tar -xzf filestore-backup-20240101.tar.gz -C /var/lib/odoo/
```

## 相关文档

- [ECS服务架构](../../architecture/ecs-services.md)
- [数据库架构](../../architecture/database.md)  
- [网络架构](../../architecture/network.md)
- [部署指南](../../deployment/README.md)
- [Redis缓存服务](../redis/README.md)
- [RabbitMQ消息队列](../rabbitmq/README.md)