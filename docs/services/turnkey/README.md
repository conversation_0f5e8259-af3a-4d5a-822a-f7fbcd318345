# Turnkey 电子发票系统

## 服务概述

Turnkey 是台湾地区的电子发票服务系统，提供发票开立、传输和管理功能。

## 架构特点

### 容器配置
- **CPU**: 256 vCPU
- **内存**: 512 MB
- **端口**: 8080
- **存储**: EFS共享文件系统
- **自动扩缩容**: 不支持（单实例）

### 主要功能
- 电子发票开立
- 发票数据传输
- 发票查询和管理
- 合规性验证
- 报表生成

## 部署配置

### 环境变量
- `NODE_ENV`: 运行环境
- `PORT`: 服务监听端口（默认8080）
- `TURNKEY_API_ENDPOINT`: Turnkey API端点
- `TURNKEY_API_KEY`: API密钥

### 文件存储
使用EFS共享文件系统存储：
- 发票PDF文件
- 临时处理文件
- 日志文件
- 配置文件

### 安全配置
- 敏感配置存储在Secrets Manager
- API密钥自动轮换
- 文件访问权限控制
- 网络访问限制

## 网络配置

### 内部通信
- **Service Connect**: 服务发现和内部通信
- **数据库连接**: 通过Aurora PostgreSQL（如需要）
- **文件存储**: EFS NFS挂载

### 外部接入
- **负载均衡器**: 可选的外部访问
- **API网关**: RESTful API接口
- **健康检查**: `/health` 端点

## 监控指标

### 业务指标
- 发票开立数量
- 传输成功率
- 处理时延
- 错误率

### 系统指标
- CPU和内存使用率
- 磁盘I/O
- 网络延迟
- 文件系统使用率

## 合规性要求

### 台湾税务规定
- 符合台湾财政部电子发票规范
- 数据保留期限要求
- 安全传输协议
- 审计日志记录

### 数据保护
- 敏感数据加密存储
- 访问日志记录
- 定期安全评估
- 备份和恢复机制

## 运维操作

### 服务管理
```bash
# 查看服务状态
aws ecs describe-services --cluster yuanhui-odoo-dev --services turnkey-dev

# 查看日志
aws logs tail /aws/ecs/turnkey-dev --follow

# 检查文件系统使用
df -h /mnt/efs/turnkey
```

### 数据维护
```bash
# 清理临时文件
find /mnt/efs/turnkey/temp -type f -mtime +7 -delete

# 检查发票文件
ls -la /mnt/efs/turnkey/invoices/
```

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查网络连接
   - 验证API密钥
   - 确认服务端点可用性

2. **文件处理错误**
   - 检查EFS挂载状态
   - 验证文件权限
   - 监控磁盘空间

3. **合规性问题**
   - 审查配置参数
   - 检查证书有效期
   - 验证数据格式

### 应急处理
1. 服务故障时的业务连续性
2. 数据丢失的恢复流程
3. 合规性问题的处理程序

## 最后更新
文档更新时间：2025年9月