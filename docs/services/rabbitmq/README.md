# RabbitMQ 消息队列服务

## 概述

RabbitMQ是一个开源的消息代理软件，用于处理应用之间的异步消息传递。本项目部署RabbitMQ作为Odoo应用的消息队列服务。

## 服务架构

```
┌─────────────────────────────────────────────────────────────┐
│                 RabbitMQ Architecture                      │
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐│
│  │               RabbitMQ Container                        ││
│  │                                                         ││
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      ││
│  │  │   AMQP      │  │ Management  │  │   HTTP      │      ││
│  │  │ Port 5672   │  │ Port 15672  │  │   API       │      ││
│  │  └─────────────┘  └─────────────┘  └─────────────┘      ││
│  │                                                         ││
│  │  ┌─────────────────────────────────────────────────────┐││
│  │  │               Data Directory                        │││
│  │  │              /var/lib/rabbitmq                     │││
│  │  │            (EBS Volume Mount)                      │││
│  │  └─────────────────────────────────────────────────────┘││
│  └─────────────────────────────────────────────────────────┘│
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                ECS Service                              ││
│  │            (Stateful Deployment)                       ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 部署配置

### 容器配置
- **镜像**: rabbitmq:3.12-management
- **部署模式**: 有状态服务（单实例）
- **存储**: EBS卷持久化数据
- **网络**: Service Connect内部发现

### 端口映射
- **5672**: AMQP协议端口（应用连接）
- **15672**: Web管理界面端口

### 资源配置
- **CPU**: 512 CPU units
- **内存**: 1024 MB
- **存储**: 10 GB EBS GP3卷

### 环境变量
```bash
# 默认用户配置
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=${ADMIN_PASSWORD}

# 节点配置
RABBITMQ_NODENAME=rabbit@rabbitmq
RABBITMQ_NODE_IP_ADDRESS=0.0.0.0

# 数据目录
RABBITMQ_MNESIA_BASE=/var/lib/rabbitmq/mnesia
RABBITMQ_LOG_BASE=/var/lib/rabbitmq/log
```

## 服务发现

### 内部DNS
- **服务名**: rabbitmq.yuanhui.local
- **AMQP端口**: 5672
- **管理端口**: 15672

### 连接字符串
```python
# Python应用连接示例
import pika

connection = pika.BlockingConnection(
    pika.ConnectionParameters(
        host='rabbitmq.yuanhui.local',
        port=5672,
        virtual_host='/',
        credentials=pika.PlainCredentials('admin', 'password')
    )
)
```

## 管理和监控

### Web管理界面
访问地址：`http://<internal-alb>:15672`

> ⚠️ **注意**: 仅能从VPC内部访问

**默认凭据**:
- 用户名: admin
- 密码: 存储在Secrets Manager中

### 主要功能
- **队列管理**: 创建、删除、监控队列
- **交换器管理**: 配置消息路由
- **用户管理**: 添加用户和设置权限
- **监控**: 查看消息统计和性能指标

### CLI管理
```bash
# 进入容器
aws ecs execute-command \
  --cluster yuanhui-odoo-dev \
  --task <rabbitmq-task-id> \
  --container rabbitmq \
  --interactive \
  --command "/bin/bash"

# 查看集群状态
rabbitmqctl cluster_status

# 查看队列
rabbitmqctl list_queues

# 查看用户
rabbitmqctl list_users

# 添加用户
rabbitmqctl add_user myuser mypassword
rabbitmqctl set_user_tags myuser administrator
rabbitmqctl set_permissions -p / myuser ".*" ".*" ".*"
```

## Odoo集成

### 配置方法
在Odoo配置文件中启用消息队列：

```ini
# odoo.conf
[queue_job]
channels = root:4

[queue_job.wsgi]
server = rabbitmq
host = rabbitmq.yuanhui.local
port = 5672
user = admin
password = ${RABBITMQ_PASSWORD}
virtual_host = /
```

### 队列作业
RabbitMQ主要用于处理：
- **异步任务**: 邮件发送、报表生成
- **定时作业**: 数据同步、清理任务
- **长时间运行的操作**: 大批量数据处理

### 监控队列状态
```python
# 在Odoo中监控队列
from odoo.addons.queue_job.models.queue_job import Job

# 查看待处理作业
pending_jobs = self.env['queue.job'].search([('state', '=', 'pending')])

# 查看失败作业
failed_jobs = self.env['queue.job'].search([('state', '=', 'failed')])
```

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查ECS任务状态
   aws ecs describe-tasks --cluster yuanhui-odoo-dev --tasks <task-id>
   
   # 查看容器日志
   aws logs tail /aws/ecs/rabbitmq-dev --follow
   ```

2. **连接被拒绝**
   ```bash
   # 检查端口监听
   netstat -tlnp | grep :5672
   
   # 测试连接
   telnet rabbitmq.yuanhui.local 5672
   ```

3. **Web界面无法访问**
   ```bash
   # 检查管理插件状态
   rabbitmq-plugins list
   
   # 启用管理插件
   rabbitmq-plugins enable rabbitmq_management
   ```

4. **内存不足**
   ```bash
   # 查看内存使用
   rabbitmqctl status | grep memory
   
   # 设置内存限制
   rabbitmqctl set_vm_memory_high_watermark 0.6
   ```

### 环境变量问题

参见：[RabbitMQ环境变量问题排查](RABBITMQ_DEPRECATED_ENV_VARS.md)

**已弃用的环境变量**:
- `RABBITMQ_DEFAULT_VHOST` → 使用配置文件或运行时命令
- 某些`RABBITMQ_*`变量在新版本中已更改

### 数据持久化问题
```bash
# 检查EBS卷挂载
df -h | grep rabbitmq

# 检查数据目录权限
ls -la /var/lib/rabbitmq/

# 修复权限（如需要）
chown -R rabbitmq:rabbitmq /var/lib/rabbitmq/
```

## 性能优化

### 内存管理
```bash
# 设置内存阈值
rabbitmqctl set_vm_memory_high_watermark 0.6

# 启用内存映射
echo "vm_memory_high_watermark.relative = 0.6" >> /etc/rabbitmq/rabbitmq.conf
```

### 队列优化
```python
# 声明持久化队列
channel.queue_declare(
    queue='task_queue',
    durable=True,
    arguments={'x-max-priority': 10}
)

# 设置预取数量
channel.basic_qos(prefetch_count=1)
```

### 监控指标
- **队列长度**: 避免消息积压
- **消息率**: 监控生产和消费速度
- **连接数**: 监控客户端连接
- **内存使用**: 防止内存溢出

## 备份和恢复

### 数据备份
```bash
# 导出定义（队列、交换器、绑定）
rabbitmqadmin export backup.json

# 备份数据目录
tar -czf rabbitmq-backup-$(date +%Y%m%d).tar.gz /var/lib/rabbitmq/
```

### 数据恢复
```bash
# 恢复定义
rabbitmqadmin import backup.json

# 恢复数据目录
tar -xzf rabbitmq-backup-20240101.tar.gz -C /
chown -R rabbitmq:rabbitmq /var/lib/rabbitmq/
```

## 监控和告警

### CloudWatch指标
- **ECS服务健康**: 任务运行状态
- **容器资源**: CPU、内存使用率
- **存储使用**: EBS卷使用情况

### 应用级监控
```python
# RabbitMQ监控脚本
import pika
import json

def check_queue_depth(queue_name, threshold=100):
    connection = pika.BlockingConnection(
        pika.ConnectionParameters('rabbitmq.yuanhui.local')
    )
    channel = connection.channel()
    
    method = channel.queue_declare(queue=queue_name, passive=True)
    queue_depth = method.method.message_count
    
    if queue_depth > threshold:
        print(f"WARNING: Queue {queue_name} has {queue_depth} messages")
    
    connection.close()
    return queue_depth
```

### 告警配置
- **队列长度告警**: 超过阈值时通知
- **连接失败告警**: 无法连接到RabbitMQ
- **内存使用告警**: 内存使用率过高

## 相关文档

- [故障排查：环境变量问题](RABBITMQ_DEPRECATED_ENV_VARS.md)
- [ECS服务架构](../../architecture/ecs-services.md)
- [有状态服务部署](../../deployment/README.md)
- [监控配置](../../operations/README.md)