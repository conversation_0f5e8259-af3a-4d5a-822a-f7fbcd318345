# RabbitMQ已弃用环境变量修复指南

## 问题描述

在RabbitMQ 4.x版本中，某些环境变量已被弃用，会导致容器启动时出现警告错误：

```
error: RABBITMQ_VM_MEMORY_HIGH_WATERMARK is set but deprecated
error: deprecated environment variables detected
Please use a configuration file instead; visit https://www.rabbitmq.com/configure.html to learn more
```

## 已弃用的环境变量

以下环境变量在RabbitMQ 4.x中已被弃用：

- `RABBITMQ_VM_MEMORY_HIGH_WATERMARK`
- `RABBITMQ_DISK_FREE_LIMIT`
- `RABBITMQ_LOGS`
- `RABBITMQ_SASL_LOGS`

## 解决方案

### 1. 使用配置文件替代环境变量

我们已经修改了RabbitMQ栈配置，使用`rabbitmq.conf`配置文件替代已弃用的环境变量。

### 2. 配置文件映射

| 已弃用环境变量 | 新配置文件选项 |
|---------------|---------------|
| `RABBITMQ_VM_MEMORY_HIGH_WATERMARK=0.6` | `vm_memory_high_watermark.relative = 0.6` |
| `RABBITMQ_DISK_FREE_LIMIT=2GB` | `disk_free_limit.absolute = 2GB` |
| `RABBITMQ_LOGS=/var/log/rabbitmq/rabbit.log` | `log.file = /var/log/rabbitmq/rabbit.log` |
| `RABBITMQ_SASL_LOGS=/var/log/rabbitmq/rabbit-sasl.log` | `log.file.sasl = /var/log/rabbitmq/rabbit-sasl.log` |

### 3. 实现方式

#### 3.1 添加配置文件卷

```typescript
// 添加配置文件卷
taskDefinition.addVolume({
  name: 'rabbitmq-config',
  dockerVolumeConfiguration: {
    scope: ecs.Scope.SHARED,
    autoprovision: true,
    driver: 'local',
  },
});
```

#### 3.2 初始化容器创建配置文件

使用busybox初始化容器在启动时创建配置文件：

```typescript
const initContainer = taskDefinition.addContainer('RabbitMQConfigInit', {
  image: ecs.ContainerImage.fromRegistry('busybox:1.36'),
  essential: false,
  command: [
    'sh', '-c',
    'mkdir -p /etc/rabbitmq && cat > /etc/rabbitmq/rabbitmq.conf << "EOF" ...'
  ],
});
```

#### 3.3 容器依赖关系

确保RabbitMQ容器在配置文件创建完成后启动：

```typescript
container.addContainerDependencies({
  container: initContainer,
  condition: ecs.ContainerDependencyCondition.SUCCESS,
});
```

### 4. 配置文件内容

新的`rabbitmq.conf`包含以下主要配置：

```ini
# 内存管理
vm_memory_high_watermark.relative = 0.6
vm_memory_high_watermark_paging_ratio = 0.5

# 磁盘管理
disk_free_limit.absolute = 2GB

# 日志配置
log.file = /var/log/rabbitmq/rabbit.log
log.file.level = info

# 网络配置
listeners.tcp.default = 5672
management.tcp.port = 15672

# 性能优化
channel_max = 2047
frame_max = 131072
heartbeat = 60
```

## 验证修复

### 1. 重新部署RabbitMQ栈

```bash
npx cdk deploy YuanhuiRabbitMQ-dev --require-approval never
```

### 2. 检查容器日志

```bash
# 获取任务ARN
aws ecs list-tasks --cluster yuanhui-odoo-dev --service-name rabbitmq-dev

# 查看容器日志
aws logs get-log-events --log-group-name /aws/ecs/rabbitmq-dev --log-stream-name ecs/RabbitMQContainer/TASK_ID
```

### 3. 验证服务状态

```bash
# 检查ECS服务状态
aws ecs describe-services --cluster yuanhui-odoo-dev --services rabbitmq-dev

# 检查负载均衡器目标组健康状态
aws elbv2 describe-target-health --target-group-arn TARGET_GROUP_ARN
```

### 4. 验证配置生效

- ✅ **没有已弃用环境变量错误**
- ✅ **RabbitMQ 4.1.2 成功启动**
- ✅ **配置文件正确加载：`/etc/rabbitmq/rabbitmq.conf`**
- ✅ **管理插件启用：3个插件加载**
- ✅ **负载均衡器健康检查通过**
- ✅ **目标组正确注册到端口15672**

## 修复结果

### 部署信息
- **栈状态**: CREATE_COMPLETE
- **服务状态**: ACTIVE (1/1 运行中)
- **健康检查**: healthy
- **管理界面**: http://internal-Yuanhu-Rabbi-ivtcwDt7cPtp-**********.ap-east-2.elb.amazonaws.com:15672

### 连接信息
- **AMQP端点**: `rabbitmq.dev-services:5672`
- **管理界面**: 通过内部负载均衡器访问
- **凭证**: 存储在AWS Secrets Manager中
  - 用户名: `admin`
  - 密码: 通过Secrets Manager获取

### 解决的问题
1. **已弃用环境变量错误** - 完全消除
2. **健康检查失败** - 修复负载均衡器端口映射
3. **管理插件未启用** - 通过配置文件启用
4. **配置文件缺失** - 使用初始化容器创建

## 参考资料

- [RabbitMQ Configuration Guide](https://www.rabbitmq.com/configure.html)
- [RabbitMQ 4.x Migration Guide](https://www.rabbitmq.com/upgrade.html)
- [AWS ECS Container Dependencies](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definition_parameters.html#container_definition_dependson)

## 注意事项

1. **向后兼容性**: 此修复确保与RabbitMQ 4.x版本的兼容性
2. **配置持久化**: 配置文件存储在Docker卷中，重启后保持不变
3. **监控**: 建议监控RabbitMQ的内存和磁盘使用情况，确保配置参数适合您的工作负载
4. **集群配置**: 如果启用集群模式，可能需要额外的配置调整
