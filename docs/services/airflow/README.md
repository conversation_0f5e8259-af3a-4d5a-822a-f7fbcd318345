# Apache Airflow 服务

## 概述

Apache Airflow 是一个开源的工作流编排平台，用于开发、调度和监控工作流。本项目采用 Airflow 3.0.2 版本，部署在 AWS ECS 集群中。

## 服务架构

```
┌─────────────────────────────────────────────────────────────┐
│                 Airflow Architecture                       │
│                                                             │
│  ┌─────────────────┐              ┌─────────────────┐       │
│  │   Webserver     │              │   Scheduler     │       │
│  │   Container     │              │   Container     │       │
│  │   (Port 8080)   │              │                 │       │
│  └─────────────────┘              └─────────────────┘       │
│           │                                │                │
│           │                                │                │
│           ▼                                ▼                │
│  ┌─────────────────────────────────────────────────────────┐│
│  │                 EFS Storage                             ││
│  │   /opt/airflow/dags    /opt/airflow/logs              ││
│  └─────────────────────────────────────────────────────────┘│
│                           │                                 │
│                           ▼                                 │
│  ┌─────────────────────────────────────────────────────────┐│
│  │              Aurora PostgreSQL                         ││
│  │           (Airflow Metadata DB)                        ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. Webserver 服务
- **功能**: 提供Web界面，用于监控和管理工作流
- **端口**: 8080
- **健康检查**: HTTP GET /health
- **资源配置**: 512 CPU units, 1024 MB memory

### 2. Scheduler 服务  
- **功能**: 调度和执行工作流任务
- **资源配置**: 256 CPU units, 512 MB memory
- **执行模式**: LocalExecutor

### 3. 存储系统

#### EFS 共享文件系统
- **DAGs目录**: `/opt/airflow/dags` - 工作流定义文件
- **日志目录**: `/opt/airflow/logs` - 任务执行日志
- **挂载**: 两个服务共享同一EFS卷

#### 数据库
- **类型**: Aurora PostgreSQL Serverless v2
- **数据库**: `airflow` (生产) / `airflow-dev` (开发)
- **用户**: `airflow_user`
- **初始化**: 自动创建数据库和用户

## 部署配置

### 环境变量
```bash
# 数据库配置
AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql://airflow_user:${PASSWORD}@${HOST}:5432/airflow

# 执行器配置
AIRFLOW__CORE__EXECUTOR=LocalExecutor

# Web服务配置
AIRFLOW__WEBSERVER__WEB_SERVER_PORT=8080
AIRFLOW__WEBSERVER__EXPOSE_CONFIG=True

# 安全配置
AIRFLOW__WEBSERVER__SECRET_KEY=${SECRET_KEY}

# 日志配置
AIRFLOW__LOGGING__REMOTE_LOGGING=False
AIRFLOW__LOGGING__BASE_LOG_FOLDER=/opt/airflow/logs
```

### 初始用户
- **用户名**: admin
- **邮箱**: <EMAIL>
- **密码**: 自动生成（存储在 Secrets Manager）

## 访问方式

### Web界面访问
```bash
# 获取内部负载均衡器地址
aws elbv2 describe-load-balancers \
  --names Yuanhu-Airfl-* \
  --query 'LoadBalancers[0].DNSName' \
  --output text
```

访问地址：`http://<internal-alb-dns>:8080`

> ⚠️ **注意**: 这是内部负载均衡器，只能从VPC内部访问

### CLI访问
```bash
# 进入Webserver容器
aws ecs execute-command \
  --cluster yuanhui-odoo-dev \
  --task <webserver-task-id> \
  --container airflow-webserver \
  --interactive \
  --command "/bin/bash"

# 执行Airflow命令
airflow dags list
airflow tasks list example_dag
```

## 工作流开发

### DAG文件结构
```python
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import BashOperator
from airflow.operators.python import PythonOperator

default_args = {
    'owner': 'yuanhui',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'example_workflow',
    default_args=default_args,
    description='示例工作流',
    schedule_interval=timedelta(days=1),
    catchup=False,
    tags=['example'],
)

task1 = BashOperator(
    task_id='print_date',
    bash_command='date',
    dag=dag,
)

def hello_world():
    print("Hello World from Airflow!")

task2 = PythonOperator(
    task_id='hello_task',
    python_callable=hello_world,
    dag=dag,
)

task1 >> task2
```

### 上传DAG文件
```bash
# 将DAG文件上传到EFS
# 方法1: 通过容器内操作
aws ecs execute-command \
  --cluster yuanhui-odoo-dev \
  --task <task-id> \
  --container airflow-webserver \
  --interactive \
  --command "/bin/bash"

# 在容器内创建DAG文件
cat > /opt/airflow/dags/my_dag.py << 'EOF'
# DAG内容
EOF

# 方法2: 通过EFS挂载点（如果有EC2实例）
# 挂载EFS并直接上传文件
```

## 监控和日志

### CloudWatch指标
- **服务状态**: ECS服务健康状态
- **任务执行**: DAG运行成功/失败率
- **资源使用**: CPU、内存使用率

### 日志查看
```bash
# 查看Webserver日志
aws logs tail /aws/ecs/airflow-webserver-dev --follow

# 查看Scheduler日志  
aws logs tail /aws/ecs/airflow-scheduler-dev --follow

# 查看特定任务日志（在Web界面中）
# 访问 Web UI > DAGs > 选择DAG > Graph View > 点击任务 > View Log
```

### 告警配置
- **服务停止告警**: ECS服务desired count与running count不匹配
- **数据库连接失败**: 数据库连接异常
- **磁盘空间不足**: EFS使用率过高

## 故障排除

### 常见问题

1. **Webserver无法访问**
   ```bash
   # 检查服务状态
   aws ecs describe-services \
     --cluster yuanhui-odoo-dev \
     --services airflow-webserver-dev
   
   # 检查任务健康
   aws ecs describe-tasks \
     --cluster yuanhui-odoo-dev \
     --tasks <task-id>
   ```

2. **DAG不显示**
   ```bash
   # 检查DAG文件语法
   python -m py_compile /opt/airflow/dags/my_dag.py
   
   # 检查Scheduler日志
   aws logs tail /aws/ecs/airflow-scheduler-dev --follow
   ```

3. **数据库连接错误**
   ```bash
   # 验证数据库连接
   aws rds describe-db-clusters --db-cluster-identifier yuanhui-aurora-dev
   
   # 检查Secrets Manager中的密码
   aws secretsmanager get-secret-value --secret-id airflow-db-password
   ```

4. **EFS挂载问题**
   ```bash
   # 检查EFS状态
   aws efs describe-file-systems --file-system-id fs-xxxxx
   
   # 检查挂载目标
   aws efs describe-mount-targets --file-system-id fs-xxxxx
   ```

### 诊断命令
```bash
# 检查所有Airflow服务
aws ecs list-services \
  --cluster yuanhui-odoo-dev \
  --query 'serviceArns[?contains(@, `airflow`)]'

# 检查EFS文件系统
aws efs describe-file-systems \
  --query 'FileSystems[?contains(Tags[?Key==`Name`].Value, `airflow`)]'

# 测试数据库连接
aws rds describe-db-cluster-endpoints \
  --db-cluster-identifier yuanhui-aurora-dev
```

## 维护任务

### 定期维护
1. **日志清理**: 定期清理过期的任务日志
2. **数据库维护**: 清理过期的任务实例和日志记录
3. **DAG审查**: 定期审查和优化工作流定义

### 升级流程
1. **备份当前配置**: 备份DAG文件和数据库
2. **测试新版本**: 在开发环境测试新版本
3. **滚动更新**: 使用ECS滚动更新部署新版本
4. **验证功能**: 确保所有功能正常运行

## 相关文档

- [部署指南](airflow-deployment-guide.md) - 详细部署说明
- [部署总结](airflow-deployment-summary.md) - 快速部署总结
- [ECS服务架构](../../architecture/ecs-services.md) - ECS整体架构
- [数据库配置](../../architecture/database.md) - 数据库架构说明