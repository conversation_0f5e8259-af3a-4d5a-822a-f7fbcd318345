# Apache Airflow 部署验证与使用指南

## 部署状态验证

### ✅ 部署成功确认

Apache Airflow 3.0.2 已成功部署到 AWS ECS 集群，包含以下组件：

#### 1. 基础设施状态
- **CloudFormation Stack**: `YuanhuiAirflow-dev` - 部署成功
- **ECS 集群**: `yuanhui-odoo-dev` - 运行正常
- **VPC**: `vpc-0febfc7bbf8918e97` - 网络配置正确

#### 2. 服务状态
| 服务名称 | 状态 | 运行实例 | 期望实例 | 健康状态 |
|---------|------|----------|----------|----------|
| airflow-webserver-dev | ACTIVE | 1 | 1 | HEALTHY |
| airflow-scheduler-dev | ACTIVE | 1 | 1 | RUNNING |

#### 3. 存储系统
- **EFS 文件系统**: `fs-049450351bc9eb558` - 可用状态
- **挂载目标**: 2个（覆盖所有可用区）
- **DAGs 目录**: `/opt/airflow/dags` (EFS 共享)
- **日志目录**: `/opt/airflow/logs` (EFS 共享)

#### 4. 数据库连接
- **PostgreSQL**: Aurora Serverless v2 集群
- **数据库初始化**: 通过 init container 自动完成
- **连接状态**: 正常

## 访问信息

### Web 界面访问
- **URL**: http://internal-Yuanhu-Airfl-YDSoIKmsSpTq-342213428.ap-east-2.elb.amazonaws.com:8080
- **用户名**: admin
- **密码**: `Ik{<9`?)h>UOUXbPC8-XT6h:tWfSA?60`
- **邮箱**: <EMAIL>

> **注意**: 这是内部负载均衡器，只能从 VPC 内部访问。

### 安全凭证
- **Secret ARN**: `arn:aws:secretsmanager:ap-east-2:138264596682:secret:AirflowSecret05E4E7B3-blDPyjlQWM2J-JiNsEX`
- **Fernet Key**: 已配置（用于加密敏感数据）
- **Webserver Secret Key**: 已配置（用于会话管理）

## 使用指南

### 1. 创建和部署 DAGs

#### 方法一：通过 ECS Exec 上传
```bash
# 连接到 webserver 容器
aws ecs execute-command \
  --cluster yuanhui-odoo-dev \
  --task f257faf1147f47729c9ffba3e6e2f3e8 \
  --container AirflowWebserverContainer \
  --interactive \
  --command "/bin/bash"

# 在容器内创建示例 DAG
cat > /opt/airflow/dags/example_dag.py << 'EOF'
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import BashOperator

default_args = {
    'owner': 'yuanhui',
    'depends_on_past': False,
    'start_date': datetime(2025, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'example_yuanhui_dag',
    default_args=default_args,
    description='元晖示例 DAG',
    schedule_interval=timedelta(days=1),
    catchup=False,
)

task1 = BashOperator(
    task_id='print_date',
    bash_command='date',
    dag=dag,
)

task2 = BashOperator(
    task_id='sleep',
    depends_on_past=False,
    bash_command='sleep 5',
    dag=dag,
)

task1 >> task2
EOF
```

#### 方法二：通过 EFS 挂载点（推荐）
如果您有 EC2 实例或其他可以挂载 EFS 的资源：
```bash
# 挂载 EFS 文件系统
sudo mount -t efs fs-049450351bc9eb558:/ /mnt/airflow-efs

# 将 DAG 文件复制到 dags 目录
cp your_dag.py /mnt/airflow-efs/dags/
```

### 2. 监控和日志

#### 查看服务日志
```bash
# Webserver 日志
aws logs tail /aws/ecs/airflow-webserver-dev --region ap-east-2 --follow

# Scheduler 日志
aws logs tail /aws/ecs/airflow-scheduler-dev --region ap-east-2 --follow

# Init container 日志
aws logs tail /aws/ecs/airflow-webserver-init-dev --region ap-east-2
```

#### 任务执行日志
- 任务日志存储在 EFS: `/opt/airflow/logs`
- 可通过 Web 界面查看
- 也可以通过 EFS 挂载点直接访问

### 3. 数据库管理

#### 连接数据库
```bash
# 获取数据库密码
DB_PASSWORD=$(aws secretsmanager get-secret-value \
  --secret-id arn:aws:secretsmanager:ap-east-2:138264596682:secret:AuroraDatabaseSecret3BAE587-pPe0oRoDTUhC-jxZ7r9 \
  --region ap-east-2 \
  --query 'SecretString' --output text | jq -r '.password')

# 连接到数据库
PGPASSWORD=$DB_PASSWORD psql -h yuanhuiauroradatabase-dev-aurorapostgresqlcluster3-hjxdqrwozxa7.cluster-c3c8ccka8h7t.ap-east-2.rds.amazonaws.com -U odoo_admin -d airflow
```

#### 数据库维护命令
```bash
# 在 Airflow 容器中执行
airflow db check          # 检查数据库连接
airflow db upgrade        # 升级数据库架构
airflow db reset          # 重置数据库（谨慎使用）
```

### 4. 用户管理

#### 创建新用户
```bash
# 在 Airflow 容器中执行
airflow users create \
  --username newuser \
  --firstname New \
  --lastname User \
  --role Admin \
  --email <EMAIL> \
  --password newpassword
```

#### 列出用户
```bash
airflow users list
```

### 5. 配置管理

#### 查看当前配置
```bash
airflow config list
```

#### 重要配置项
- **Executor**: LocalExecutor（单机模式）
- **DAGs 目录**: `/opt/airflow/dags`
- **日志目录**: `/opt/airflow/logs`
- **数据库**: PostgreSQL (Aurora Serverless v2)
- **并发任务数**: 32
- **DAG 并发数**: 16

## 故障排除

### 常见问题

#### 1. Web 界面无法访问
```bash
# 检查服务状态
aws ecs describe-services --cluster yuanhui-odoo-dev --services airflow-webserver-dev --region ap-east-2

# 检查任务健康状态
aws ecs describe-tasks --cluster yuanhui-odoo-dev --tasks <task-id> --region ap-east-2
```

#### 2. DAG 不显示
- 检查 DAG 文件语法错误
- 确认文件放在正确的目录 `/opt/airflow/dags`
- 查看 scheduler 日志

#### 3. 任务执行失败
- 查看任务日志（Web 界面或 EFS 日志目录）
- 检查资源限制
- 验证数据库连接

#### 4. EFS 挂载问题
```bash
# 检查 EFS 挂载目标
aws efs describe-mount-targets --file-system-id fs-049450351bc9eb558 --region ap-east-2

# 检查安全组规则
aws ec2 describe-security-groups --group-ids <efs-security-group-id> --region ap-east-2
```

## 扩展和优化

### 1. 启用 CeleryExecutor（分布式执行）
如需要分布式任务执行，可以修改配置启用 CeleryExecutor 和 Worker 节点。

### 2. 自动扩缩容
可以配置 ECS 服务的自动扩缩容策略，根据 CPU/内存使用率自动调整实例数量。

### 3. 监控告警
建议配置 CloudWatch 告警监控：
- ECS 服务健康状态
- 任务失败率
- 资源使用率
- 数据库连接数

### 4. 备份策略
- EFS 文件系统自动备份
- Aurora 数据库自动备份
- DAG 文件版本控制

## 安全建议

1. **更换默认密钥**: 生产环境中应更换 Fernet Key 和 Webserver Secret Key
2. **网络隔离**: 当前使用内部负载均衡器，确保只有授权网络可访问
3. **访问控制**: 配置适当的 IAM 角色和权限
4. **加密传输**: EFS 和数据库连接已启用加密
5. **定期更新**: 定期更新 Airflow 版本和安全补丁

---

**部署完成时间**: 2025-07-05 21:36:35
**版本**: Apache Airflow 3.0.2-python3.11
**环境**: dev
**区域**: ap-east-2
