# WikiJS 文档系统

## 服务概述

WikiJS 是一个现代化的企业级知识库系统，提供团队协作文档管理功能。

## 架构特点

### 容器配置
- **CPU**: 256 vCPU
- **内存**: 512 MB
- **端口**: 3000
- **存储**: Aurora PostgreSQL数据库
- **自动扩缩容**: 不支持（单实例）

### 主要功能
- Markdown文档编辑
- 用户权限管理
- 搜索功能
- 版本控制
- 多语言支持

## 部署配置

### 环境变量
- `NODE_ENV`: 运行环境
- `PORT`: 服务监听端口（默认3000）
- `DATABASE_URL`: 数据库连接地址
- `JWT_SECRET`: JWT令牌密钥

### 数据库配置
使用 Aurora PostgreSQL 作为后端数据库，自动创建专用数据库和用户。

### 网络配置
- **内部访问**: 通过Service Connect
- **外部访问**: 通过Application Load Balancer
- **健康检查**: `/healthz` 端点

## 初始设置

### 首次访问
1. 通过负载均衡器访问WikiJS
2. 完成初始管理员账户设置
3. 配置认证方式
4. 设置站点基本信息

### 推荐配置
- 启用Markdown编辑器
- 配置LDAP/OAuth认证
- 设置适当的权限组
- 配置备份策略

## 监控指标

### 关键指标
- 服务可用性
- 响应时间
- 数据库连接状态
- 用户活跃度

### 告警配置
- 服务不可用告警
- 数据库连接失败告警
- 磁盘空间不足告警

## 运维操作

### 查看服务状态
```bash
aws ecs describe-services --cluster yuanhui-odoo-dev --services wikijs-dev
```

### 数据备份
WikiJS数据存储在Aurora数据库中，通过数据库自动备份机制保护数据。

### 版本升级
1. 更新容器镜像版本
2. 重新部署服务
3. 验证功能正常

## 故障排除

### 常见问题

1. **无法访问服务**
   - 检查ECS服务状态
   - 验证负载均衡器配置
   - 确认安全组规则

2. **数据库连接失败**
   - 检查Aurora数据库状态
   - 验证数据库凭据
   - 确认网络连通性

3. **性能问题**
   - 监控资源使用情况
   - 优化数据库查询
   - 考虑增加资源配置

## 最后更新
文档更新时间：2025年9月