# Claude Relay 服务文档

## 服务概述

<PERSON> Relay 是一个 Claude API 中转服务，提供 API 代理、缓存和负载均衡功能。

## 架构特点

### 容器配置
- **CPU**: 256 vCPU
- **内存**: 512 MB  
- **端口**: 3000
- **存储**: EFS共享文件系统
- **自动扩缩容**: 支持（1-3个实例）

### 主要功能
- Claude API请求中转
- 响应缓存机制
- 内置Redis缓存
- 健康检查端点

## 部署配置

### 环境变量
服务通过以下环境变量进行配置：
- `NODE_ENV`: 运行环境
- `PORT`: 服务监听端口（默认3000）
- `CLAUDE_API_KEY`: Claude API密钥
- `REDIS_URL`: Redis连接地址

### 网络配置
- **内部访问**: 通过Service Connect（服务发现）
- **外部访问**: 通过Application Load Balancer
- **健康检查**: `/health` 端点

## 监控指标

### 关键指标
- CPU使用率
- 内存使用率
- 响应时间
- 请求成功率
- 缓存命中率

### 告警配置
- CPU > 70% 持续5分钟
- 内存 > 80% 持续5分钟
- 错误率 > 5% 持续3分钟

## 运维操作

### 查看服务状态
```bash
aws ecs describe-services --cluster yuanhui-odoo-dev --services claude-relay-dev
```

### 查看日志
```bash
aws logs tail /aws/ecs/claude-relay-dev --follow
```

### 手动扩缩容
```bash
aws ecs update-service --cluster yuanhui-odoo-dev --service claude-relay-dev --desired-count 2
```

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查环境变量配置
   - 验证Claude API密钥
   - 查看容器日志

2. **缓存问题**
   - 检查Redis连接
   - 验证EFS挂载
   - 重启服务清除缓存

3. **性能问题**
   - 监控CPU和内存使用
   - 检查网络延迟
   - 调整实例数量

## 最后更新
文档更新时间：2025年9月