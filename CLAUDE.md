# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a comprehensive AWS CDK infrastructure-as-code project for deploying a cloud-native Odoo application stack. The project manages the complete infrastructure for Yuan Hui's enterprise applications, including containerized deployment of databases, Odoo application servers, and supporting services using ECS.

## Core Architecture

The project follows a multi-stack architecture with the following key components:

### Infrastructure Stacks
- **NetworkStack**: VPC, security groups, WAF, OpenZiti zero-trust network
- **EcsStack**: ECS EC2 cluster for container orchestration
- **ServiceConnectStack**: ECS Service Connect namespace for service discovery
- **AuroraDatabaseStack**: Aurora Serverless v2 PostgreSQL with read/write separation
- **RedisStack**: ECS-based single-instance Redis caching service
- **RabbitMQStack**: RabbitMQ message queue with management UI
- **AirflowStack**: Apache Airflow 3.x workflow engine
- **ApplicationStack**: Main Odoo application services (yherp, khmall, cron)
- **DnsStack**: DNS and SSL certificate management
- **CloudFrontStack**: CDN acceleration (production only)
- **OpenZitiStack**: Zero-trust network access (production only)
- **SecurityStack**: GitHub Actions CI/CD permissions management
- **MonitoringStack**: CloudWatch monitoring and alerting

### Application Services
- **yherp**: Main Odoo ERP application
- **khmall**: E-commerce platform
- **cron**: Scheduled task processing
- **airflow**: Workflow orchestration (webserver, scheduler, worker)
- **rabbitmq**: Message queue service
- **redis**: Caching service

## Common Development Commands

### Build and Test
```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Watch for changes
npm run watch

# Run tests
npm run test

# Clean build artifacts
npm run clean
```

### Deployment Commands

#### NPM Scripts (Recommended)
```bash
# Basic deployment
npm run deploy:dev              # Deploy to development environment
npm run deploy:prod             # Deploy to production environment

# Preview mode
npm run deploy:dev:dry          # Preview development deployment
npm run deploy:prod:dry         # Preview production deployment

# Stack group deployment
npm run deploy:core             # Deploy infrastructure stacks (dev)
npm run deploy:core:prod        # Deploy infrastructure stacks (prod)
npm run deploy:apps             # Deploy application service stacks (dev)
npm run deploy:apps:prod        # Deploy application service stacks (prod)

# Quick deployment
npm run deploy:fast             # Parallel deployment + real-time logs
npm run deploy:parallel         # Enable parallel deployment
npm run deploy:logs             # Show real-time logs
npm run deploy:safe             # Enable auto-rollback

# Single stack deployment
npm run deploy:network          # Deploy network stack only
npm run deploy:database         # Deploy database stack only
npm run deploy:application      # Deploy application stack only

# Advanced parameter passing
npm run deploy:dev -- --stacks Network,Database --timeout 3600
npm run deploy:prod -- --parallel --auto-rollback
npm run deploy:dev -- --logs --stacks Application
```

#### Direct Script Commands
```bash
# Basic usage
./scripts/deploy.sh dev                                    # Deploy all stacks to dev
./scripts/deploy.sh prod --dry-run                         # Preview production deployment

# Specify stack deployment
./scripts/deploy.sh dev --stacks Network,Database          # Deploy only network and database stacks
./scripts/deploy.sh dev --group core                       # Deploy infrastructure stack group

# Advanced options
./scripts/deploy.sh dev --parallel --logs                  # Parallel deployment with real-time logs
./scripts/deploy.sh prod --timeout 3600 --auto-rollback    # Production deployment, 1 hour timeout, auto-rollback

# Get help
./scripts/deploy.sh --help
npm run deploy:help
```

#### Configuration Management

**Configuration File** (`deploy.config.json`):
```json
{
  "dev": {
    "timeout": 2400,
    "parallel": true,
    "logs": true,
    "autoRollback": false,
    "defaultGroup": "core"
  },
  "prod": {
    "timeout": 3600,
    "parallel": true,
    "logs": false,
    "autoRollback": true,
    "defaultGroup": "all",
    "requireConfirmation": true
  }
}
```

**Environment Variables**:
```bash
# Configure deployment parameters
export DEPLOY_TIMEOUT=3600
export DEPLOY_PARALLEL=true
export DEPLOY_LOGS=true
export DEPLOY_AUTO_ROLLBACK=true

# Use configuration
npm run deploy:dev
```

**NPM Config**:
```bash
# Set npm configuration
npm config set yuanhui:deploy:timeout 3600
npm config set yuanhui:deploy:parallel true
npm config set yuanhui:deploy:logs true
npm config set yuanhui:deploy:autoRollback true

# View configuration
npm config get yuanhui:deploy:timeout
```

#### Configuration Priority
1. **CLI Arguments** (highest priority)
2. **Environment Variables** (DEPLOY_*)
3. **NPM Configuration** (yuanhui:deploy:*)
4. **Configuration File** (deploy.config.json)
5. **Default Values** (lowest priority)

### Stack Organization

#### Core Stack Group (Infrastructure)
- **Network**: VPC, security groups, WAF
- **Ecs**: ECS cluster
- **ServiceConnect**: Service discovery
- **AuroraDatabase**: Aurora database
- **Redis**: Redis cache
- **RabbitMQ**: Message queue
- **LoadBalancer**: Application load balancer
- **Airflow**: Workflow engine
- **Security**: Security configuration

#### Apps Stack Group (Application Services)
- **Application**: Odoo application services
- **ClaudeRelay**: Claude relay service
- **CloudFront**: CDN (production environment)
- **OpenZiti**: Zero-trust network (production environment)
- **Monitoring**: Monitoring and alerting

### Deployment Features

#### Smart Deployment
- **Dependency Management**: Automatically resolve stack dependencies and deploy in correct order
- **Parallel Deployment**: Support parallel deployment of independent stacks for efficiency
- **Smart Timeout**: Set different timeout periods based on stack complexity
- **Real-time Monitoring**: Display CloudFormation events and ECS service status

#### Error Handling
- **Failure Diagnosis**: Automatically analyze deployment failure causes and provide solutions
- **Smart Retry**: Support automatic retry for temporary errors
- **Progressive Rollback**: Rollback in reverse dependency order when deployment fails
- **State Recovery**: Record and recover deployment state

#### Security Features
- **Production Confirmation**: Production environment deployment requires user confirmation
- **Permission Check**: Automatically verify AWS permissions and configuration
- **Security Validation**: Perform security checks before deployment

### CDK Commands
```bash
# Set environment (dev/prod)
export NODE_ENV=dev
export CDK_DEFAULT_ACCOUNT=************
export CDK_DEFAULT_REGION=ap-east-2

# Bootstrap CDK (first time only)
cdk bootstrap

# List all stacks
cdk list

# Preview changes
cdk diff

# Deploy all stacks
cdk deploy --all

# Deploy specific stack
cdk deploy YuanhuiNetwork-dev --require-approval never

# Generate CloudFormation template
cdk synth

# Destroy stacks
cdk destroy --all
```

### Script Operations

#### Permission and Deployment Scripts
```bash
# Check AWS permissions (must run before deployment)
./scripts/check-permissions.sh

# Deploy network infrastructure
./scripts/deploy-network-infrastructure.sh dev  # Development environment
./scripts/deploy-network-infrastructure.sh prod # Production environment

# Deploy GitHub Actions security stack
./scripts/deploy-security-stack.sh -e dev       # Development environment
./scripts/deploy-security-stack.sh -e prod      # Production environment
./scripts/deploy-security-stack.sh -d           # Preview mode (no deployment)
```

#### Validation and Testing Scripts
```bash
# Verify network routing and SSL certificates
./scripts/test-network-routing.sh
./scripts/verify-ssl-certificates.sh

# Airflow related operations
./scripts/run-airflow-init.sh                   # Initialize Airflow database
./scripts/test-airflow-deployment.sh            # Verify Airflow deployment
./scripts/verify-airflow-database.sh            # Verify Airflow database configuration
```

#### Cost Monitoring Scripts
```bash
# Quick cost check
./scripts/quick-cost-check.sh

# Detailed cost analysis
./scripts/monitor-cloudwatch-costs.sh

# Diagnose CloudWatch cost issues
./scripts/diagnose-cloudwatch-costs.sh
```

### Operational Commands
```bash
# Check ECS services
aws ecs list-services --cluster yuanhui-odoo-dev

# View container logs
aws logs tail /aws/ecs/yherp-dev --follow

# Describe Aurora cluster
aws rds describe-db-clusters

# Check target group health
aws elbv2 describe-target-health --target-group-arn <arn>
```

## Environment Configuration

Configuration is managed through the `lib/config/` directory, supporting separate configurations for development and production environments:

### Configuration File Structure
- `lib/config/index.ts` - Main configuration entry, automatically selects environment based on NODE_ENV
- `lib/config/base.ts` - Base configuration interface definitions
- `lib/config/environments/dev.ts` - Development environment configuration
- `lib/config/environments/prod.ts` - Production environment configuration
- `lib/config/stacks/` - Configuration interface definitions for each stack

### Key Configuration Areas
- **VPC and Networking**: CIDR blocks, AZs, NAT gateways
- **Domain Management**: Multi-domain routing with SSL certificates
- **Security**: WAF rules, OpenZiti, IP whitelisting
- **Database**: Aurora Serverless v2 scaling and performance settings
- **Container Resources**: CPU/memory allocation for all services
- **Monitoring**: CloudWatch settings and log retention

### Environment Differences
- **Development**: Smaller resources, single AZ, disabled WAF/CDN/OpenZiti
- **Production**: Multi-AZ, full security features, CDN acceleration, zero-trust network

## Domain and Routing Architecture

The project supports multi-domain routing with environment-specific configurations:

### Production Domains
- `yh.kh2u.com` - Internal Yherp access (OpenZiti + Longpolling)
- `dp.kh2u.com` - Public Yherp access (WAF + Longpolling)
- `j2mall.com` - E-commerce platform (CloudFront CDN + Longpolling)

### Development Domains
- `yh-dev.kh2u.com` - Internal Yherp access
- `dp-dev.kh2u.com` - Public Yherp access
- `j2mall.tw` - E-commerce platform

## Odoo Longpolling Support

The infrastructure fully supports Odoo 18 longpolling functionality with:
- Dual port configuration (8069 main, 8072 longpolling)
- Multi-worker process support
- Proper load balancer routing for both ports

## Stack Dependencies

The deployment follows a specific dependency order:
1. NetworkStack (foundation)
2. AuroraDatabaseStack (database initialization using Lambda direct connection, no ECS dependency)
3. EcsStack → ServiceConnectStack
4. LoadBalancerStack, RedisStack, RabbitMQStack, AirflowStack
5. ApplicationStack (depends on all services)
6. CloudFrontStack, OpenZitiStack (production only)
7. MonitoringStack (depends on all application stacks)

## Security Features

- **Network Isolation**: VPC with private subnets and security groups
- **Zero-Trust Network**: OpenZiti for internal access (production)
- **Web Application Firewall**: AWS WAF v2 with custom rules
- **Encryption**: TLS 1.2+ for transport, EBS/Aurora encryption at rest
- **Access Control**: IAM roles with least privilege principle
- **Certificate Management**: AWS Certificate Manager with auto-renewal

## Monitoring and Alerting

- **CloudWatch Dashboards**: Environment-specific dashboards
- **Key Metrics**: CPU, memory, response times, database performance
- **Alerting**: SNS notifications for critical thresholds
- **Log Management**: Centralized logging with configurable retention

## Cost Optimization

- **Environment-specific sizing**: Smaller resources for development
- **Auto-scaling**: Dynamic scaling based on load
- **Resource cleanup**: Automated cleanup of old images and backups
- **Reserved instances**: Production uses reserved instances for cost savings

## Codebase Usage Guide

### Adding New Services
1. Create new stack file in `lib/stacks/`
2. Add configuration interface in `lib/config/stacks/`
3. Update environment configuration in `lib/config/environments/`
4. Modify `bin/iac.ts` to add stack instantiation and dependencies
5. Update monitoring stack to include new services

### Airflow Database Initialization Architecture
- **Lambda Direct Connection**: Use Lambda function to directly connect to PostgreSQL, no ECS task required
- **psycopg2 Layer**: Provide database connection support through Lambda Layer
- **Simplified Dependencies**: Database initialization no longer depends on ECS cluster, executed early after network stack
- **Custom Resource**: Ensure database and users are created before Airflow deployment

### Modifying Existing Services
1. Update configuration in corresponding stack file
2. Modify environment configuration if needed
3. Use `cdk diff` to test changes
4. Use `cdk deploy` to deploy

### Troubleshooting
- Check ECS task logs to troubleshoot container issues
- Verify security group rules to ensure connectivity
- Monitor CloudWatch metrics to evaluate performance
- Check WAF logs to view blocked requests
- Verify SSL certificates and domain configuration

## Region and Compliance

- **Primary Region**: ap-east-2 (Hong Kong)
- **Multi-AZ**: Production uses 3 AZs, development uses 2 AZs
- **Compliance**: Follows AWS best practices for security and monitoring

## Documentation Maintenance Policy

When making code changes, always ensure documentation is kept in sync:

### 📋 Documentation Update Requirements

**After ANY code modification, check if these documents need updates:**

1. **Architecture Changes**:
   - Adding/removing/modifying stacks → Update `docs/architecture/README.md` and related architecture docs
   - Service configuration changes → Update corresponding `docs/services/*/README.md`
   - New infrastructure components → Create new service documentation

2. **Deployment Process Changes**:
   - New/modified deployment scripts → Update `docs/deployment/README.md`
   - Environment configuration changes → Update `docs/deployment/quick-start.md`
   - Stack dependency changes → Update deployment sequence documentation

3. **Service Function Changes**:
   - Adding new services → Create `docs/services/{service-name}/README.md`
   - Modifying service configuration → Update service-specific documentation
   - Removing services → Delete related documentation and update main README

4. **Operational Changes**:
   - Monitoring configuration changes → Update `docs/operations/README.md`
   - Troubleshooting procedures → Update `docs/troubleshooting/README.md`
   - New scripts or tools → Update `docs/scripts/README.md`

### 🎯 Documentation Sync Process

**Before committing code changes:**
1. Review what documentation might be affected
2. Update relevant documentation files
3. Test documentation links and references
4. Include documentation updates in the same commit
5. Update the main `docs/README.md` if new services or major changes are introduced

### 📝 Documentation Standards

- Keep documentation concise but comprehensive
- Use consistent formatting and structure
- Include practical examples and commands
- Update timestamps in modified documents
- Remove obsolete information immediately
- Ensure all internal links are working

**Remember**: Outdated documentation is worse than no documentation. Always keep docs current with code changes.