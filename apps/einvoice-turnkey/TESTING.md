# 电子发票Turnkey系统 - 测试指南

本文档说明如何使用简化的测试环境来验证电子发票Turnkey系统的Docker镜像。

## 快速开始

### 1. 构建镜像并验证

```bash
# 构建Docker镜像
make build

# 验证镜像是否正确构建
make verify
```

### 2. 快速测试

```bash
# 一步完成构建和快速验证
make quick-test

# 完整的构建和测试套件
make full-test
```

## 详细说明

### 构建命令

- `make build` - 构建Docker镜像
- `make build-no-cache` - 不使用缓存重新构建
- `make rebuild` - 清理后重新构建

### 验证命令

- `make verify` - 完整的镜像验证（推荐）
- `make verify-quick` - 快速验证基本功能
- `make test-compose` - 测试docker-compose配置
- `make test-xml-update` - 测试XML配置更新工具

### 调试命令

- `make shell` - 进入容器shell进行调试
- `make logs` - 查看测试容器日志
- `make check` - 检查环境和文件

### 清理命令

- `make clean` - 清理Docker构建缓存
- `make clean-all` - 清理所有相关资源（危险操作）

## 验证脚本

### verify-image.sh

轻量级验证脚本，快速检查Docker镜像是否正确构建：

```bash
# 使用默认镜像
./verify-image.sh

# 指定镜像名称
./verify-image.sh -i yuanhui/einvoice-turnkey:v1.0

# 使用环境变量
IMAGE_NAME=custom:tag ./verify-image.sh
```

**验证项目**：
- 镜像存在性检查
- 目录结构验证
- 文件权限检查
- 配置文件验证
- Java环境验证
- 脚本功能验证
- 用户权限设置

## XML配置更新改进

### 问题解决

原来的sed命令无法正确处理XML空元素，现已修复：

**支持的XML格式**：
```xml
<!-- 空元素 -->
<db-type/>

<!-- 空内容 -->
<db-type></db-type>

<!-- 有内容 -->
<db-type>oracle</db-type>

<!-- 多行格式 -->
<db-type>
  postgresql
</db-type>
```

### 智能更新工具

新增`docker/update-xml-config.sh`工具，提供：
- 智能XML元素识别和更新
- 备份和恢复机制
- XML格式验证
- 配置更改日志

**entrypoint.sh改进**：
1. 优先使用智能XML更新工具
2. 失败时回退到改进的sed方式
3. 更好的错误处理和日志记录

## 测试环境

### docker-compose.test.yml

简化的测试配置，仅包含PostgreSQL数据库：

```bash
# 启动测试数据库
docker compose -f docker-compose.test.yml up -d

# 测试数据库连接
docker compose -f docker-compose.test.yml exec postgres pg_isready -U einvoice_user -d einvoice

# 清理测试环境
docker compose -f docker-compose.test.yml down -v
```

**特点**：
- 使用内存存储（tmpfs）
- 不同端口避免冲突（5433）
- 测试后自动清理数据

### 环境变量

复制`.env.example`到`.env`并修改配置：

```bash
cp .env.example .env
# 编辑.env文件设置数据库密码等
```

## CI/CD集成

### GitHub Actions示例

```yaml
- name: Build and Test
  run: |
    make ci-build
    make ci-test
```

### 命令说明

- `make ci-build` - CI环境优化的构建
- `make ci-test` - CI环境测试流程

## 故障排除

### 常见问题

1. **镜像构建失败**
   ```bash
   make check  # 检查环境和文件
   make build-no-cache  # 清理缓存重建
   ```

2. **验证失败**
   ```bash
   make shell  # 进入容器检查
   ./verify-image.sh -i $(IMAGE_NAME)  # 详细验证报告
   ```

3. **XML配置问题**
   ```bash
   make test-xml-update  # 测试XML更新工具
   ```

4. **数据库连接失败**
   ```bash
   make test-compose  # 测试数据库配置
   ```

### 日志查看

```bash
# 构建日志
make ci-build

# 运行时日志
make logs

# 健康检查日志
docker logs einvoice-test-postgres
```

## 性能优化

### 验证速度

- 完整验证：< 30秒
- 快速验证：< 10秒
- 测试环境启动：< 15秒

### 资源使用

- 内存占用：< 512MB
- 磁盘空间：< 2GB
- CPU使用：低

## 部署集成

该测试环境与生产部署完全兼容：
- 相同的镜像可用于CDK部署
- 配置更新机制与ECS环境一致
- 支持相同的环境变量和挂载点

## 总结

简化后的测试环境提供：
- ✅ 快速可靠的镜像验证
- ✅ 智能XML配置更新
- ✅ 轻量级测试环境
- ✅ 完整的故障排除工具
- ✅ CI/CD友好的接口

专注于核心功能验证，去除了复杂的PgAdmin、Redis等辅助服务，提高了测试效率和可维护性。