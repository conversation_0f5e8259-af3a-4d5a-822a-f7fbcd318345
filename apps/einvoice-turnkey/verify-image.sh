#!/bin/bash

# 电子发票Turnkey系统 - 镜像验证脚本
# 轻量级验证脚本，快速检查Docker镜像是否正确构建

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME=${IMAGE_NAME:-"yuanhui/einvoice-turnkey:latest"}
CONTAINER_NAME="einvoice-verify-$$"
VERIFICATION_TIMEOUT=30

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%H:%M:%S')] INFO: $1${NC}"
}

# 检查结果统计
check_result() {
    local result=$1
    local description=$2
    
    ((TOTAL_CHECKS++))
    
    if [ $result -eq 0 ]; then
        echo -e "  ${GREEN}✓${NC} $description"
        ((PASSED_CHECKS++))
        return 0
    else
        echo -e "  ${RED}✗${NC} $description"
        ((FAILED_CHECKS++))
        return 1
    fi
}

# 清理函数
cleanup() {
    if docker ps -a -q -f name=$CONTAINER_NAME | grep -q .; then
        log "清理验证容器..."
        docker rm -f $CONTAINER_NAME >/dev/null 2>&1 || true
    fi
}

# 设置清理钩子
trap cleanup EXIT

# 主验证函数
verify_image() {
    log "开始验证Docker镜像: $IMAGE_NAME"
    echo ""
    
    # 1. 检查镜像是否存在
    info "1. 检查镜像存在性..."
    docker image inspect $IMAGE_NAME >/dev/null 2>&1
    check_result $? "镜像存在检查"
    
    # 2. 创建并启动临时容器进行文件系统检查
    info "2. 创建验证容器..."
    docker run -d --name $CONTAINER_NAME --entrypoint /bin/bash $IMAGE_NAME -c "sleep 300" >/dev/null 2>&1
    check_result $? "容器创建并启动"
    
    # 等待容器完全启动
    sleep 2
    
    # 3. 验证目录结构
    info "3. 验证目录结构..."
    
    # 应用根目录
    docker exec $CONTAINER_NAME test -d /opt/EINVTurnkey 2>/dev/null
    check_result $? "应用根目录 /opt/EINVTurnkey 存在"
    
    # 关键文件检查
    docker exec $CONTAINER_NAME test -f /opt/EINVTurnkey/run_start.sh 2>/dev/null
    check_result $? "启动脚本 run_start.sh 存在"
    
    docker exec $CONTAINER_NAME test -f /opt/EINVTurnkey/einvUserConfig.xml 2>/dev/null
    check_result $? "配置文件 einvUserConfig.xml 存在"
    
    docker exec $CONTAINER_NAME test -f /opt/EINVTurnkey/javahome 2>/dev/null
    check_result $? "Java标识文件 javahome 存在"
    
    # 关键目录检查
    docker exec $CONTAINER_NAME test -d /opt/EINVTurnkey/lib 2>/dev/null
    check_result $? "依赖库目录 lib/ 存在"
    
    docker exec $CONTAINER_NAME test -d /opt/EINVTurnkey/modules 2>/dev/null
    check_result $? "模块目录 modules/ 存在"
    
    # so目录是可选的，不是所有版本都有
    if docker exec $CONTAINER_NAME test -d /opt/EINVTurnkey/so 2>/dev/null; then
        check_result 0 "本地库目录 so/ 存在"
    else
        warn "本地库目录 so/ 不存在（可选）"
        # 不计入失败统计
    fi
    
    # 数据库脚本检查
    docker exec $CONTAINER_NAME test -f /app/sql/PostgreSQL.sql 2>/dev/null
    check_result $? "数据库初始化脚本 PostgreSQL.sql 存在"
    
    # 4. 验证文件权限
    info "4. 验证文件权限..."
    
    # 检查turnkey用户(1001)权限
    TURNKEY_OWNER=$(docker exec $CONTAINER_NAME stat -c "%u" /opt/EINVTurnkey 2>/dev/null || echo "unknown")
    if [ "$TURNKEY_OWNER" = "1001" ]; then
        check_result 0 "turnkey用户(1001)拥有应用目录权限"
    else
        check_result 1 "turnkey用户(1001)应该拥有应用目录权限 (当前: $TURNKEY_OWNER)"
    fi
    
    # 检查shell脚本执行权限
    docker exec $CONTAINER_NAME test -x /opt/EINVTurnkey/run_start.sh 2>/dev/null
    check_result $? "启动脚本具有执行权限"
    
    docker exec $CONTAINER_NAME test -x /app/entrypoint.sh 2>/dev/null
    check_result $? "入口脚本具有执行权限"
    
    docker exec $CONTAINER_NAME test -x /app/health-check.sh 2>/dev/null
    check_result $? "健康检查脚本具有执行权限"
    
    # 5. 验证配置文件内容
    info "5. 验证配置文件内容..."
    
    # 检查XML文件格式
    if docker exec $CONTAINER_NAME xmllint --noout /opt/EINVTurnkey/einvUserConfig.xml 2>/dev/null; then
        check_result 0 "XML配置文件格式正确"
    else
        # 如果xmllint不可用，使用基本检查
        if docker exec $CONTAINER_NAME grep -q "<?xml" /opt/EINVTurnkey/einvUserConfig.xml 2>/dev/null; then
            check_result 0 "XML配置文件包含XML声明"
        else
            check_result 1 "XML配置文件格式问题"
        fi
    fi
    
    # 检查关键配置节点（使用更灵活的匹配）
    if docker exec $CONTAINER_NAME grep -E -q "(db-type|dbType)" /opt/EINVTurnkey/einvUserConfig.xml 2>/dev/null; then
        check_result 0 "XML包含数据库类型配置节点"
    else
        check_result 1 "XML包含数据库类型配置节点"
    fi
    
    if docker exec $CONTAINER_NAME grep -E -q "(jdbc-url|jdbcUrl)" /opt/EINVTurnkey/einvUserConfig.xml 2>/dev/null; then
        check_result 0 "XML包含JDBC URL配置节点"
    else
        check_result 1 "XML包含JDBC URL配置节点"
    fi
    
    # 6. 验证Java环境
    info "6. 验证Java环境..."
    
    # 检查Java可执行性
    docker exec $CONTAINER_NAME java -version >/dev/null 2>&1
    check_result $? "Java环境可用"
    
    # 获取Java版本信息
    if docker exec $CONTAINER_NAME java -version >/dev/null 2>&1; then
        JAVA_VERSION=$(docker exec $CONTAINER_NAME java -version 2>&1 | head -n 1 | cut -d'"' -f2)
        info "  Java版本: $JAVA_VERSION"
    fi
    
    # 7. 验证入口点和健康检查
    info "7. 验证脚本功能..."
    
    # 检查entrypoint脚本语法
    docker exec $CONTAINER_NAME bash -n /app/entrypoint.sh 2>/dev/null
    check_result $? "entrypoint.sh 脚本语法正确"
    
    # 检查健康检查脚本语法
    docker exec $CONTAINER_NAME bash -n /app/health-check.sh 2>/dev/null
    check_result $? "health-check.sh 脚本语法正确"
    
    # 8. 验证用户和权限设置
    info "8. 验证用户设置..."
    
    # 检查turnkey用户是否存在
    docker exec $CONTAINER_NAME id turnkey >/dev/null 2>&1
    check_result $? "turnkey用户存在"
    
    # 检查工作目录
    WORKDIR=$(docker exec $CONTAINER_NAME pwd 2>/dev/null || echo "unknown")
    if [ "$WORKDIR" = "/opt/EINVTurnkey" ]; then
        check_result 0 "工作目录设置正确"
    else
        check_result 1 "工作目录应为 /opt/EINVTurnkey (当前: $WORKDIR)"
    fi
}

# 显示验证结果摘要
show_summary() {
    echo ""
    echo "=========================================="
    log "验证结果摘要"
    echo "=========================================="
    echo -e "总检查项: ${BLUE}$TOTAL_CHECKS${NC}"
    echo -e "通过检查: ${GREEN}$PASSED_CHECKS${NC}"
    echo -e "失败检查: ${RED}$FAILED_CHECKS${NC}"
    
    if [ $FAILED_CHECKS -eq 0 ]; then
        echo ""
        echo -e "${GREEN}🎉 所有验证检查均通过！镜像构建正确。${NC}"
        return 0
    else
        echo ""
        echo -e "${RED}❌ 发现 $FAILED_CHECKS 个问题，请检查镜像构建过程。${NC}"
        return 1
    fi
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -i, --image IMAGE_NAME    指定要验证的镜像名称 (默认: yuanhui/einvoice-turnkey:latest)"
    echo "  -h, --help               显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  IMAGE_NAME               要验证的Docker镜像名称"
    echo ""
    echo "示例:"
    echo "  $0                                          # 使用默认镜像"
    echo "  $0 -i my-registry/turnkey:v1.0            # 指定镜像"
    echo "  IMAGE_NAME=custom:tag $0                   # 使用环境变量"
}

# 参数解析
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--image)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            error "未知参数: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 带超时的验证执行函数
run_verification_with_timeout() {
    local temp_file=$(mktemp)
    local start_time=$(date +%s)
    
    # 在后台运行验证函数，将输出和统计写入临时文件
    (
        verify_image
        local result=$?
        echo "VERIFICATION_EXIT_CODE=$result" >> "$temp_file"
        echo "TOTAL_CHECKS=$TOTAL_CHECKS" >> "$temp_file"
        echo "PASSED_CHECKS=$PASSED_CHECKS" >> "$temp_file"
        echo "FAILED_CHECKS=$FAILED_CHECKS" >> "$temp_file"
    ) &
    local verify_pid=$!
    
    # 等待验证完成或超时
    local count=0
    while [ $count -lt $VERIFICATION_TIMEOUT ]; do
        if ! kill -0 $verify_pid 2>/dev/null; then
            # 进程已结束
            wait $verify_pid 2>/dev/null
            break
        fi
        sleep 1
        ((count++))
    done
    
    # 如果超时，杀死验证进程
    if kill -0 $verify_pid 2>/dev/null; then
        error "验证超时，正在终止验证进程..."
        kill -TERM $verify_pid 2>/dev/null
        sleep 2
        kill -KILL $verify_pid 2>/dev/null || true
        rm -f "$temp_file"
        return 1
    fi
    
    # 检查验证结果并读取统计信息
    if [ -f "$temp_file" ]; then
        # 读取统计信息
        if grep -q "TOTAL_CHECKS=" "$temp_file"; then
            TOTAL_CHECKS=$(grep "TOTAL_CHECKS=" "$temp_file" | cut -d'=' -f2)
            PASSED_CHECKS=$(grep "PASSED_CHECKS=" "$temp_file" | cut -d'=' -f2)
            FAILED_CHECKS=$(grep "FAILED_CHECKS=" "$temp_file" | cut -d'=' -f2)
        fi
        
        if grep -q "VERIFICATION_EXIT_CODE=0" "$temp_file"; then
            rm -f "$temp_file"
            return 0
        fi
    fi
    
    rm -f "$temp_file"
    return 1
}

# 主程序
main() {
    echo "=========================================="
    log "电子发票Turnkey镜像验证工具"
    echo "=========================================="
    echo ""
    
    # 检查Docker是否可用
    if ! command -v docker >/dev/null 2>&1; then
        error "Docker未安装或不可用"
        exit 1
    fi
    
    # 检查Docker daemon是否运行
    if ! docker info >/dev/null 2>&1; then
        error "Docker daemon未运行"
        exit 1
    fi
    
    info "目标镜像: $IMAGE_NAME"
    info "验证超时: ${VERIFICATION_TIMEOUT}秒"
    echo ""
    
    # 开始验证
    local start_time=$(date +%s)
    
    if run_verification_with_timeout; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        show_summary
        
        echo ""
        log "验证完成，耗时: ${duration}秒"
        
        if [ $FAILED_CHECKS -eq 0 ]; then
            exit 0
        else
            exit 1
        fi
    else
        error "验证超时或失败"
        cleanup
        exit 1
    fi
}

# 运行主程序
main "$@"