# 数据库初始化目录

此目录包含PostgreSQL数据库的初始化脚本，这些脚本会在数据库容器首次启动时自动执行。

## 文件执行顺序

脚本按文件名的字典序执行：

1. `00-init-database.sql` - 基础数据库配置和系统表
2. `01-turnkey-schema.sql` - Turnkey应用的数据库结构（由应用自动生成）
3. `99-final-setup.sql` - 最终配置和数据

## 脚本说明

### 00-init-database.sql
- 设置数据库时区
- 优化PostgreSQL参数
- 创建系统监控表
- 创建操作日志表

### 01-turnkey-schema.sql
这个文件通常由Turnkey应用自动生成。如果需要手动创建：

```bash
# 从Turnkey容器复制数据库初始化脚本
make db-init
```

## 使用方法

### 首次部署
脚本会在首次启动PostgreSQL容器时自动执行，无需手动干预。

### 重新初始化
如果需要重新初始化数据库：

```bash
# 方法1：删除数据卷并重启
make down-volumes
make up

# 方法2：使用重置命令（危险操作）
make db-reset
```

### 添加自定义初始化脚本

1. 在此目录创建 `.sql` 或 `.sh` 文件
2. 使用数字前缀控制执行顺序
3. 确保脚本是幂等的（可重复执行）

示例：
```sql
-- 02-custom-tables.sql
CREATE TABLE IF NOT EXISTS custom_table (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 注意事项

1. **执行顺序**：文件按字典序执行，使用数字前缀控制顺序
2. **幂等性**：脚本应该是幂等的，使用 `IF NOT EXISTS` 等条件
3. **权限**：脚本以 `postgres` 超级用户身份执行
4. **编码**：确保脚本文件使用 UTF-8 编码
5. **日志**：初始化日志会显示在容器启动日志中

## 故障排除

### 初始化失败
```bash
# 查看初始化日志
docker compose logs postgres

# 检查脚本语法
psql -U postgres -d einvoice -f init-db/00-init-database.sql --echo-all --set ON_ERROR_STOP=1
```

### 重新执行脚本
```bash
# 连接到数据库
make psql

# 手动执行脚本
\i /docker-entrypoint-initdb.d/00-init-database.sql
```

### 检查执行结果
```sql
-- 检查系统信息
SELECT * FROM system_info;

-- 检查操作日志
SELECT * FROM operation_logs ORDER BY operation_time DESC LIMIT 10;
```