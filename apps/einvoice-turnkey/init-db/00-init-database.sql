-- 电子发票Turnkey系统 - 数据库初始化脚本
-- 此脚本在PostgreSQL容器首次启动时自动执行

-- 设置数据库编码和排序规则
ALTER DATABASE einvoice SET timezone = 'Asia/Taipei';

-- 创建扩展（如果需要）
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 设置数据库参数优化
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- 创建监控表（可选）
CREATE TABLE IF NOT EXISTS system_info (
    id SERIAL PRIMARY KEY,
    info_key VARCHAR(100) NOT NULL UNIQUE,
    info_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入系统信息
INSERT INTO system_info (info_key, info_value) VALUES 
('system_name', 'EInvoice Turnkey System'),
('database_version', version()),
('initialized_at', CURRENT_TIMESTAMP::TEXT),
('timezone', 'Asia/Taipei')
ON CONFLICT (info_key) DO UPDATE SET 
    info_value = EXCLUDED.info_value,
    updated_at = CURRENT_TIMESTAMP;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 应用触发器到system_info表
DROP TRIGGER IF EXISTS update_system_info_updated_at ON system_info;
CREATE TRIGGER update_system_info_updated_at
    BEFORE UPDATE ON system_info
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 创建日志表（用于记录系统操作）
CREATE TABLE IF NOT EXISTS operation_logs (
    id SERIAL PRIMARY KEY,
    operation_type VARCHAR(50),
    operation_desc TEXT,
    operation_user VARCHAR(100),
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    additional_info JSONB
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_operation_logs_time ON operation_logs(operation_time);
CREATE INDEX IF NOT EXISTS idx_operation_logs_type ON operation_logs(operation_type);

-- 记录初始化日志
INSERT INTO operation_logs (operation_type, operation_desc, operation_user, additional_info) 
VALUES (
    'SYSTEM_INIT', 
    'Database initialized for EInvoice Turnkey System',
    'system',
    '{"database": "einvoice", "version": "1.0", "environment": "docker"}'::jsonb
);

-- 显示初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '===========================================';
    RAISE NOTICE 'EInvoice Turnkey Database Initialization';
    RAISE NOTICE '===========================================';
    RAISE NOTICE 'Database: %', current_database();
    RAISE NOTICE 'User: %', current_user;
    RAISE NOTICE 'Time: %', CURRENT_TIMESTAMP;
    RAISE NOTICE 'Timezone: %', current_setting('timezone');
    RAISE NOTICE '===========================================';
END $$;