# 开发者指南

本文档为开发者提供详细的开发环境设置和工作流程指导。

## 🏗️ 项目架构

### 系统组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   PgAdmin       │    │   Turnkey App   │    │  PostgreSQL     │
│   (Web UI)      │    │   (Java App)    │    │  (Database)     │
│   Port: 8080    │    │   Debug: 5005   │    │   Port: 5432    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Docker        │
                    │   Network       │
                    │ einvoice-network│
                    └─────────────────┘
```

### 数据流

```
用户操作 → Docker Compose → 容器编排 → 服务间通信
    ↓
Turnkey应用 → PostgreSQL数据库 → 数据持久化
    ↓
PgAdmin → 数据库管理 → 可视化界面
```

## 🛠️ 开发环境设置

### 必备工具

1. **Docker & Docker Compose**
   ```bash
   # 安装Docker Desktop (推荐)
   # 或分别安装Docker Engine和Docker Compose
   
   # 验证安装
   docker --version
   docker compose version
   ```

2. **开发IDE**
   - IntelliJ IDEA (推荐Java开发)
   - VS Code + Java扩展包
   - Eclipse IDE

3. **数据库客户端**
   - PgAdmin (已集成在Docker环境中)
   - DBeaver
   - DataGrip

### 环境配置

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd einvoice-turnkey
   ```

2. **快速启动**
   ```bash
   # 方式1：使用快速启动脚本
   ./quick-start.sh
   
   # 方式2：使用Make命令
   make env-init
   make up
   
   # 方式3：使用Docker Compose
   cp .env.example .env
   docker compose --profile dev up -d
   ```

## 📝 开发工作流

### 日常开发循环

1. **启动开发环境**
   ```bash
   make debug  # 启动调试模式
   ```

2. **代码修改**
   - 修改Java代码（如果支持热重载）
   - 修改配置文件
   - 修改数据库结构

3. **测试变更**
   ```bash
   make health     # 健康检查
   make logs       # 查看日志
   make test       # 运行测试
   ```

4. **调试问题**
   ```bash
   make shell      # 进入容器
   make psql       # 连接数据库
   ```

### Java远程调试

1. **启动调试模式**
   ```bash
   make debug  # 或 docker compose --profile dev up -d
   ```

2. **IDE配置**
   - **IntelliJ IDEA:**
     - Run → Edit Configurations
     - Add → Remote JVM Debug
     - Host: localhost, Port: 5005
   
   - **VS Code:**
     ```json
     // launch.json
     {
       "type": "java",
       "request": "attach",
       "name": "Debug Turnkey",
       "hostName": "localhost",
       "port": 5005
     }
     ```

3. **设置断点并调试**

### 数据库开发

1. **连接数据库**
   ```bash
   # 方式1：通过PgAdmin Web界面
   # 访问 http://localhost:8080
   
   # 方式2：命令行
   make psql
   
   # 方式3：外部客户端
   # Host: localhost, Port: 5432
   # Database: einvoice, User: einvoice_user
   ```

2. **数据库操作**
   ```sql
   -- 查看表结构
   \dt
   
   -- 查看系统信息
   SELECT * FROM system_info;
   
   -- 查看操作日志
   SELECT * FROM operation_logs ORDER BY operation_time DESC LIMIT 10;
   ```

3. **备份和恢复**
   ```bash
   make db-backup              # 创建备份
   make db-restore BACKUP_FILE=backup.sql  # 恢复备份
   ```

## 🧪 测试策略

### 测试层次

1. **单元测试**
   - Java应用内部逻辑测试
   - 数据库操作测试

2. **集成测试**
   - 应用与数据库集成测试
   - 配置文件解析测试

3. **系统测试**
   - 完整系统功能测试
   - 性能测试

### 测试执行

```bash
# 完整测试套件
make test

# 单独测试组件
make test-build      # 构建测试
make test-services   # 服务测试
make test-database   # 数据库测试
make test-turnkey    # 应用测试
```

### 测试数据

```bash
# 准备测试数据
docker compose exec postgres psql -U einvoice_user -d einvoice -c "
INSERT INTO test_table (name, value) VALUES ('test1', 'value1');
"

# 清理测试数据
make db-reset  # 注意：这会删除所有数据
```

## 📊 监控和调试

### 日志查看

```bash
# 实时日志
make logs               # 所有服务
make logs-turnkey       # Turnkey应用
make logs-postgres      # PostgreSQL

# 日志文件
docker compose exec turnkey tail -f null/-SYS.log
```

### 性能监控

```bash
# 资源使用
docker stats

# 系统监控
make watch

# 容器内监控
docker compose exec turnkey top
```

### 故障排除

1. **服务启动失败**
   ```bash
   make status          # 查看服务状态
   make health          # 健康检查
   docker compose logs  # 查看启动日志
   ```

2. **数据库连接问题**
   ```bash
   make test-database   # 测试数据库
   docker compose exec postgres pg_isready
   ```

3. **应用程序错误**
   ```bash
   make shell           # 进入容器
   cat einvUserConfig.xml  # 检查配置
   ```

## 🔧 配置管理

### 环境变量

核心配置在 `.env` 文件中：

```bash
# 数据库配置
DB_NAME=einvoice_dev
DB_USER=dev_user
DB_PASSWORD=dev_password

# 开发配置
DEBUG=true
LOG_LEVEL=DEBUG
JAVA_OPTS=-Xmx2g -Xms1g -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
```

### 配置优先级

1. 环境变量 (最高)
2. `.env` 文件
3. `docker-compose.override.yml`
4. `docker-compose.yml` (默认)

### 配置热重载

某些配置支持热重载：

```bash
# 重新加载配置
docker compose restart turnkey

# 或重新构建
make rebuild
```

## 🚀 部署准备

### 构建优化

```bash
# 优化构建
make build-no-cache     # 无缓存构建
docker system prune     # 清理未使用资源
```

### 镜像管理

```bash
# 标记版本
docker tag yuanhui/einvoice-turnkey:latest yuanhui/einvoice-turnkey:v1.0.0

# 推送到仓库
docker push yuanhui/einvoice-turnkey:v1.0.0
```

### 环境迁移

```bash
# 导出配置
docker compose config > production-compose.yml

# 数据迁移
make db-backup
# 传输备份文件到目标环境
make db-restore BACKUP_FILE=production-backup.sql
```

## 📚 开发资源

### 相关文档

- [Docker Compose官方文档](https://docs.docker.com/compose/)
- [PostgreSQL官方文档](https://www.postgresql.org/docs/)
- 电子发票Turnkey使用说明书 (docs/目录)

### 开发工具

- **Makefile**: `make help` 查看所有命令
- **快速启动**: `./quick-start.sh help`
- **健康检查**: `docker compose exec turnkey /app/health-check.sh`

### 社区资源

- GitHub Issues: 报告问题和请求功能
- 技术文档: docs/目录中的PDF文档
- 开发团队: Yuan Hui Development Team

## 🤝 贡献指南

### 代码风格

1. **提交信息格式**
   ```
   type(scope): description
   
   例如:
   feat(database): add user authentication table
   fix(docker): resolve container startup issue
   docs(readme): update installation guide
   ```

2. **分支策略**
   - `main`: 稳定版本
   - `develop`: 开发版本
   - `feature/*`: 功能分支
   - `hotfix/*`: 紧急修复

3. **代码审查**
   - 提交PR前运行完整测试
   - 确保Docker构建成功
   - 更新相关文档

### 发布流程

1. **版本标记**
   ```bash
   git tag -a v1.0.0 -m "Release version 1.0.0"
   git push origin v1.0.0
   ```

2. **镜像发布**
   ```bash
   docker build -t yuanhui/einvoice-turnkey:v1.0.0 .
   docker push yuanhui/einvoice-turnkey:v1.0.0
   ```

---

**维护团队**: Yuan Hui Development Team  
**最后更新**: 2024-12-01