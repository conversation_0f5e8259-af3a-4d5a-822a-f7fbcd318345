#!/bin/bash

# Turnkey证书管理功能测试脚本
# 用于验证SSM Parameter Store证书获取和解析功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[TEST-INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[TEST-WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[TEST-ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[TEST-DEBUG]${NC} $1"
}

# 测试配置
TEST_DIR="/tmp/turnkey_cert_test"
TEST_CERT_DIR="$TEST_DIR/cert"
TEST_SSM_PARAMETER="/turnkey/test/certificates"

# 清理函数
cleanup() {
    log_info "Cleaning up test environment..."
    rm -rf "$TEST_DIR"
    
    # 清理测试SSM参数（可选）
    if [ "$CLEANUP_SSM" = "true" ]; then
        log_info "Removing test SSM parameter..."
        aws ssm delete-parameter --name "$TEST_SSM_PARAMETER" 2>/dev/null || true
    fi
}

# 创建测试证书数据
create_test_certificates() {
    log_info "Creating test certificate data..."
    
    # 创建模拟的base64编码证书数据（实际应用中这应该是真正的PFX证书）
    local test_cert_1="VGVzdCBQRlggQ2VydGlmaWNhdGUgMQ=="  # "Test PFX Certificate 1" 的base64
    local test_cert_2="VGVzdCBQRlggQ2VydGlmaWNhdGUgMg=="  # "Test PFX Certificate 2" 的base64
    local test_cert_3="VGVzdCBQRlggQ2VydGlmaWNhdGUgMw=="  # "Test PFX Certificate 3" 的base64
    
    # 创建测试参数内容
    cat > "$TEST_DIR/test_ssm_data.txt" << EOF
pfx_base64_1=$test_cert_1
pfx_password_1=test_password_1
sign_id_1=company_seal
sign_type_1=corporate
pfx_base64_2=$test_cert_2
pfx_password_2=test_password_2
sign_id_2=legal_representative
sign_type_2=personal
pfx_base64_3=$test_cert_3
pfx_password_3=test_password_3
sign_id_3=finance_dept
sign_type_3=departmental
EOF

    log_info "Test certificate data created at: $TEST_DIR/test_ssm_data.txt"
}

# 创建测试SSM参数
create_test_ssm_parameter() {
    log_info "Creating test SSM parameter: $TEST_SSM_PARAMETER"
    
    local ssm_value
    ssm_value=$(cat "$TEST_DIR/test_ssm_data.txt")
    
    # 创建或更新SSM参数
    if aws ssm put-parameter \
        --name "$TEST_SSM_PARAMETER" \
        --type "SecureString" \
        --value "$ssm_value" \
        --overwrite \
        --description "Test certificates for Turnkey certificate management" \
        >/dev/null 2>&1; then
        log_info "Test SSM parameter created successfully"
    else
        log_error "Failed to create test SSM parameter"
        return 1
    fi
}

# 测试证书提取函数
test_extract_value_from_kv() {
    log_info "Testing extract_value_from_kv function..."
    
    local test_data="pfx_base64_1=VGVzdA==
pfx_password_1=password123
sign_id_1=test_cert
sign_type_1=test_type"
    
    # 导入entrypoint.sh中的函数
    source_entrypoint_functions
    
    local extracted_value
    extracted_value=$(extract_value_from_kv "$test_data" "pfx_password_1")
    
    if [ "$extracted_value" = "password123" ]; then
        log_info "✅ extract_value_from_kv test passed"
    else
        log_error "❌ extract_value_from_kv test failed. Expected: password123, Got: $extracted_value"
        return 1
    fi
}

# 导入entrypoint.sh中的函数
source_entrypoint_functions() {
    # 从entrypoint.sh中提取函数定义
    local script_dir
    script_dir=$(dirname "$0")
    local entrypoint_script="$script_dir/entrypoint.sh"
    
    if [ ! -f "$entrypoint_script" ]; then
        log_error "entrypoint.sh not found at: $entrypoint_script"
        return 1
    fi
    
    # 提取并定义需要的函数
    eval "$(sed -n '/^extract_value_from_kv()/,/^}/p' "$entrypoint_script")"
    eval "$(sed -n '/^get_certificates_from_ssm()/,/^}/p' "$entrypoint_script")"
    eval "$(sed -n '/^process_single_certificate()/,/^}/p' "$entrypoint_script")"
}

# 测试SSM获取功能
test_ssm_retrieval() {
    log_info "Testing SSM certificate retrieval..."
    
    # 获取AWS区域
    local aws_region
    aws_region=$(aws configure get region || echo "ap-east-2")
    
    source_entrypoint_functions
    
    local ssm_data
    if ssm_data=$(get_certificates_from_ssm "$TEST_SSM_PARAMETER" "$aws_region"); then
        log_info "✅ SSM retrieval test passed"
        log_debug "Retrieved data (first 100 chars): ${ssm_data:0:100}..."
        return 0
    else
        log_error "❌ SSM retrieval test failed"
        return 1
    fi
}

# 测试证书处理功能
test_certificate_processing() {
    log_info "Testing certificate processing..."
    
    mkdir -p "$TEST_CERT_DIR"
    
    # 获取测试数据
    local test_data
    test_data=$(cat "$TEST_DIR/test_ssm_data.txt")
    
    source_entrypoint_functions
    
    # 测试处理第一个证书
    if process_single_certificate "1" "$test_data" "$TEST_CERT_DIR"; then
        log_info "✅ Certificate 1 processing test passed"
        
        # 验证文件是否创建
        local expected_file="$TEST_CERT_DIR/company_seal_corporate.pfx"
        if [ -f "$expected_file" ]; then
            log_info "✅ Certificate file created: $expected_file"
            
            # 验证文件内容
            local file_content
            file_content=$(cat "$expected_file")
            if [ "$file_content" = "Test PFX Certificate 1" ]; then
                log_info "✅ Certificate content verification passed"
            else
                log_error "❌ Certificate content verification failed"
                return 1
            fi
        else
            log_error "❌ Certificate file not created: $expected_file"
            return 1
        fi
        
        # 验证环境变量
        if [ "$PFX_PASSWORD_1" = "test_password_1" ] && [ "$SIGN_ID_1" = "company_seal" ]; then
            log_info "✅ Environment variables test passed"
        else
            log_error "❌ Environment variables test failed"
            return 1
        fi
    else
        log_error "❌ Certificate processing test failed"
        return 1
    fi
}

# 测试错误处理
test_error_handling() {
    log_info "Testing error handling..."
    
    source_entrypoint_functions
    
    # 测试处理不存在的证书
    local empty_data=""
    if ! process_single_certificate "1" "$empty_data" "$TEST_CERT_DIR"; then
        log_info "✅ Empty certificate data handling test passed"
    else
        log_error "❌ Empty certificate data should fail but didn't"
        return 1
    fi
    
    # 测试无效base64数据
    local invalid_data="pfx_base64_1=invalid_base64_data
pfx_password_1=password
sign_id_1=test
sign_type_1=test"
    
    if ! process_single_certificate "1" "$invalid_data" "$TEST_CERT_DIR"; then
        log_info "✅ Invalid base64 data handling test passed"
    else
        log_error "❌ Invalid base64 data should fail but didn't"
        return 1
    fi
}

# 完整集成测试
test_full_integration() {
    log_info "Running full integration test..."
    
    # 设置测试环境变量
    export CERT_SSM_PARAMETER="$TEST_SSM_PARAMETER"
    export CERT_DIRECTORY="$TEST_CERT_DIR"
    
    # 创建测试证书目录
    mkdir -p "$TEST_CERT_DIR"
    
    # 模拟完整的证书设置过程
    local aws_region
    aws_region=$(aws configure get region || echo "ap-east-2")
    
    source_entrypoint_functions
    
    # 获取SSM数据
    local ssm_data
    if ssm_data=$(get_certificates_from_ssm "$TEST_SSM_PARAMETER" "$aws_region"); then
        log_info "SSM data retrieved successfully"
        
        # 处理所有证书
        local cert_index=1
        local processed_count=0
        
        while [ $cert_index -le 5 ]; do
            if process_single_certificate "$cert_index" "$ssm_data" "$TEST_CERT_DIR"; then
                processed_count=$((processed_count + 1))
                log_info "Certificate $cert_index processed successfully"
            else
                break
            fi
            cert_index=$((cert_index + 1))
        done
        
        if [ $processed_count -eq 3 ]; then
            log_info "✅ Full integration test passed - processed $processed_count certificates"
            
            # 列出生成的证书文件
            log_info "Generated certificate files:"
            ls -la "$TEST_CERT_DIR"/*.pfx 2>/dev/null || log_warn "No PFX files found"
            
        else
            log_error "❌ Full integration test failed - expected 3 certificates, got $processed_count"
            return 1
        fi
    else
        log_error "❌ Failed to retrieve SSM data for integration test"
        return 1
    fi
}

# 主测试流程
main() {
    log_info "Starting Turnkey certificate management tests..."
    
    # 设置信号处理
    trap cleanup EXIT
    
    # 创建测试目录
    mkdir -p "$TEST_DIR"
    
    # 运行测试
    local test_count=0
    local pass_count=0
    
    # 测试1: 创建测试数据
    test_count=$((test_count + 1))
    if create_test_certificates; then
        pass_count=$((pass_count + 1))
    fi
    
    # 测试2: 创建SSM参数
    test_count=$((test_count + 1))
    if create_test_ssm_parameter; then
        pass_count=$((pass_count + 1))
    fi
    
    # 测试3: 键值对提取函数
    test_count=$((test_count + 1))
    if test_extract_value_from_kv; then
        pass_count=$((pass_count + 1))
    fi
    
    # 测试4: SSM获取功能
    test_count=$((test_count + 1))
    if test_ssm_retrieval; then
        pass_count=$((pass_count + 1))
    fi
    
    # 测试5: 证书处理功能
    test_count=$((test_count + 1))
    if test_certificate_processing; then
        pass_count=$((pass_count + 1))
    fi
    
    # 测试6: 错误处理
    test_count=$((test_count + 1))
    if test_error_handling; then
        pass_count=$((pass_count + 1))
    fi
    
    # 测试7: 完整集成测试
    test_count=$((test_count + 1))
    if test_full_integration; then
        pass_count=$((pass_count + 1))
    fi
    
    # 测试结果汇总
    log_info "==================== Test Results ===================="
    log_info "Total tests: $test_count"
    log_info "Passed: $pass_count"
    log_info "Failed: $((test_count - pass_count))"
    
    if [ $pass_count -eq $test_count ]; then
        log_info "🎉 All tests passed!"
        exit 0
    else
        log_error "❌ Some tests failed!"
        exit 1
    fi
}

# 检查参数
case "${1:-}" in
    "--help"|"-h")
        echo "Usage: $0 [--cleanup-ssm]"
        echo "Options:"
        echo "  --cleanup-ssm    Remove test SSM parameter after testing"
        echo "  --help, -h       Show this help message"
        exit 0
        ;;
    "--cleanup-ssm")
        export CLEANUP_SSM=true
        ;;
esac

# 执行主函数
main "$@"