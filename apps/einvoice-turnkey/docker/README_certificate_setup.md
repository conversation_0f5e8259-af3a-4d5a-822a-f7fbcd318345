# Turnkey 证书管理系统使用指南

## 概述

Turnkey启动脚本现在支持两种证书获取方式：
1. **SSM Parameter Store**（推荐）- 键值对格式存储多个证书
2. **AWS Secrets Manager**（向后兼容）- JSON格式存储证书文件

## SSM Parameter Store 证书配置

### 环境变量配置

在ECS任务定义或Docker运行环境中设置以下环境变量：

```bash
# SSM参数路径（必需）
CERT_SSM_PARAMETER="/turnkey/certificates/prod"

# 证书目录（可选，默认：/opt/EINVTurnkey/cert）
CERT_DIRECTORY="/opt/EINVTurnkey/cert"
```

### SSM Parameter Store 数据格式

在SSM Parameter Store中，证书数据应存储为键值对格式，支持多个证书：

```
# 证书1
pfx_base64_1=MIIKpAIBAzCCCl4GCSqGSIb3DQEHAaCCCk8EggpLMIIKRzCC...（base64编码的PFX证书）
pfx_password_1=your_certificate_password_1
sign_id_1=company_seal
sign_type_1=corporate

# 证书2
pfx_base64_2=MIILqAIBAzCCCl4GCSqGSIb3DQEHAaCCCm8EggprMIIKZzCC...（base64编码的PFX证书）
pfx_password_2=your_certificate_password_2
sign_id_2=legal_representative
sign_type_2=personal

# 证书3
pfx_base64_3=MIIMrAIBAzCCCl4GCSqGSIb3DQEHAaCCCn8EggpzMIIKbzCC...（base64编码的PFX证书）
pfx_password_3=your_certificate_password_3
sign_id_3=finance_dept
sign_type_3=departmental
```

### 数据字段说明

对于每个证书（索引从1开始），需要提供以下字段：

| 字段名 | 格式 | 必需 | 描述 |
|--------|------|------|------|
| `pfx_base64_N` | Base64字符串 | ✅ | PFX证书的base64编码数据 |
| `pfx_password_N` | 明文字符串 | ✅ | PFX证书密码 |
| `sign_id_N` | 字符串 | ✅ | 签名标识符（用于文件命名） |
| `sign_type_N` | 字符串 | ✅ | 签名类型（用于文件命名） |

其中 `N` 是证书编号，从1开始递增。

### 证书文件命名规则

处理后的证书文件将按以下格式命名：
```
{sign_id}_{sign_type}.pfx
```

例如：
- `company_seal_corporate.pfx`
- `legal_representative_personal.pfx`
- `finance_dept_departmental.pfx`

### 环境变量输出

脚本处理成功后会设置以下环境变量：

```bash
# 证书计数
CERT_COUNT=3

# 每个证书的详细信息
PFX_BASE64_1="base64_certificate_data"
PFX_PASSWORD_1="certificate_password"
SIGN_ID_1="company_seal"
SIGN_TYPE_1="corporate"
CERT_FILE_1="/opt/EINVTurnkey/cert/company_seal_corporate.pfx"

PFX_BASE64_2="base64_certificate_data"
PFX_PASSWORD_2="certificate_password"
SIGN_ID_2="legal_representative"
SIGN_TYPE_2="personal"
CERT_FILE_2="/opt/EINVTurnkey/cert/legal_representative_personal.pfx"

# ... 更多证书
```

## AWS CLI 设置示例

### 创建SSM参数

```bash
# 创建加密的SSM参数
aws ssm put-parameter \
  --name "/turnkey/certificates/prod" \
  --type "SecureString" \
  --value "pfx_base64_1=MIIKpAIBAzCC...
pfx_password_1=your_password_1
sign_id_1=company_seal
sign_type_1=corporate
pfx_base64_2=MIILqAIBAzCC...
pfx_password_2=your_password_2
sign_id_2=legal_representative
sign_type_2=personal" \
  --description "Turnkey PFX certificates configuration"
```

### 更新SSM参数

```bash
aws ssm put-parameter \
  --name "/turnkey/certificates/prod" \
  --type "SecureString" \
  --value "$(cat certificates.txt)" \
  --overwrite
```

## 证书转换工具

如果你有现有的证书文件，可以使用以下命令转换为base64格式：

```bash
# 将PFX证书转换为base64
base64 -i your_certificate.pfx | tr -d '\n' > certificate_base64.txt

# 查看base64编码结果
cat certificate_base64.txt
```

## 日志和调试

启动脚本会产生详细的日志输出，帮助你调试证书配置问题：

```
[INFO] Setting up Turnkey certificates...
[INFO] Using SSM Parameter Store for certificate retrieval
[INFO] Attempting to retrieve certificates from SSM Parameter Store: /turnkey/certificates/prod
[INFO] Successfully retrieved certificate data from SSM Parameter Store
[INFO] Certificate 1 saved: company_seal_corporate.pfx
[INFO] Environment variables set for certificate 1
[INFO] Certificate 2 saved: legal_representative_personal.pfx
[INFO] Environment variables set for certificate 2
[INFO] Successfully processed 2 certificates from SSM Parameter Store
[INFO] Total certificates available: 2
[INFO] Certificate setup completed
```

## 错误处理

### 常见错误场景

1. **SSM参数不存在**
   ```
   [WARN] Failed to retrieve certificates from SSM Parameter Store
   [INFO] Falling back to AWS Secrets Manager for certificate retrieval
   ```

2. **Base64解码失败**
   ```
   [ERROR] Failed to decode base64 certificate data for certificate 1
   ```

3. **证书字段缺失**
   ```
   [WARN] Certificate 1 missing sign_id or sign_type, using default names
   ```

### 回退机制

1. 如果SSM Parameter Store获取失败，脚本会自动回退到AWS Secrets Manager
2. 如果所有证书获取方法都失败，脚本会创建空的占位符文件
3. 应用程序可以根据实际需求处理证书缺失的情况

## 安全考虑

1. **权限控制**：确保ECS任务角色具有读取SSM参数的权限
2. **加密存储**：使用SecureString类型的SSM参数进行加密存储
3. **文件权限**：证书文件权限设置为600，所有者为turnkey用户
4. **日志安全**：敏感信息不会记录到日志中

## 迁移指南

### 从Secrets Manager迁移到SSM

1. **准备SSM参数**：按照上述格式创建SSM参数
2. **更新ECS配置**：添加`CERT_SSM_PARAMETER`环境变量
3. **验证功能**：在测试环境验证证书加载正常
4. **删除旧配置**：确认新方式工作正常后，移除`CERT_SECRET_ARN`配置

### 向后兼容性

- 现有的Secrets Manager配置将继续工作
- 可以同时配置两种方式，SSM具有更高优先级
- 渐进式迁移，无需停机