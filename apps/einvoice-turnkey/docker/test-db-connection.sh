#!/bin/bash

# 电子发票Turnkey系统数据库连接测试脚本
# 验证数据库连接配置和连接性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEST_CONTAINER="db-connection-test-$$"
DB_CONTAINER="test-postgres-$$"
TEST_NETWORK="db-test-network-$$"

# 默认配置
DEFAULT_DB_HOST="localhost"
DEFAULT_DB_PORT="5432"
DEFAULT_DB_NAME="einvoice"
DEFAULT_DB_USER="einvoice"
DEFAULT_DB_PASSWORD="test_password"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    
    # 停止并删除容器
    for container in "$TEST_CONTAINER" "$DB_CONTAINER"; do
        if docker ps -a --format "table {{.Names}}" | grep -q "$container"; then
            docker stop "$container" 2>/dev/null || true
            docker rm "$container" 2>/dev/null || true
        fi
    done
    
    # 删除测试网络
    if docker network ls --format "table {{.Name}}" | grep -q "$TEST_NETWORK"; then
        docker network rm "$TEST_NETWORK" 2>/dev/null || true
    fi
}

# 信号处理
trap cleanup EXIT

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v nc >/dev/null 2>&1; then
        log_warn "nc (netcat) 未安装，某些连接测试可能不可用"
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行"
        exit 1
    fi
    
    log_info "系统依赖检查通过"
}

# 创建测试网络
create_test_network() {
    log_step "创建测试网络..."
    
    if docker network create "$TEST_NETWORK" --driver bridge >/dev/null 2>&1; then
        log_info "测试网络已创建: $TEST_NETWORK"
    else
        log_error "无法创建测试网络"
        exit 1
    fi
}

# 启动测试PostgreSQL数据库
start_test_database() {
    log_step "启动测试PostgreSQL数据库..."
    
    # 启动PostgreSQL容器
    docker run -d \
        --name "$DB_CONTAINER" \
        --network "$TEST_NETWORK" \
        -e POSTGRES_DB="$DEFAULT_DB_NAME" \
        -e POSTGRES_USER="$DEFAULT_DB_USER" \
        -e POSTGRES_PASSWORD="$DEFAULT_DB_PASSWORD" \
        -e POSTGRES_HOST_AUTH_METHOD=md5 \
        -p 5433:5432 \
        postgres:13 >/dev/null
    
    if ! docker ps --format "table {{.Names}}" | grep -q "$DB_CONTAINER"; then
        log_error "无法启动PostgreSQL容器"
        return 1
    fi
    
    log_info "PostgreSQL容器已启动"
    
    # 等待数据库启动
    log_info "等待数据库完全启动..."
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec "$DB_CONTAINER" pg_isready -U "$DEFAULT_DB_USER" -d "$DEFAULT_DB_NAME" >/dev/null 2>&1; then
            log_info "数据库已准备就绪"
            break
        fi
        
        if [ $((attempt % 10)) -eq 0 ]; then
            log_info "等待数据库启动... ($attempt/$max_attempts)"
        fi
        
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "数据库启动超时"
        show_database_logs
        return 1
    fi
    
    return 0
}

# 创建测试应用容器
create_test_container() {
    log_step "创建测试应用容器..."
    
    # 使用postgres客户端工具镜像进行连接测试
    docker run -d \
        --name "$TEST_CONTAINER" \
        --network "$TEST_NETWORK" \
        --entrypoint="/bin/sleep" \
        postgres:13 \
        300 >/dev/null
    
    if ! docker ps --format "table {{.Names}}" | grep -q "$TEST_CONTAINER"; then
        log_error "无法创建测试容器"
        return 1
    fi
    
    log_info "测试容器已创建"
    return 0
}

# 测试基本网络连接
test_network_connectivity() {
    log_step "测试网络连接..."
    
    # 测试容器到数据库的网络连接
    if docker exec "$TEST_CONTAINER" nc -z "$DB_CONTAINER" 5432 >/dev/null 2>&1; then
        log_info "✓ 网络连接正常 ($DB_CONTAINER:5432)"
    else
        log_error "✗ 网络连接失败"
        return 1
    fi
    
    # 测试DNS解析
    if docker exec "$TEST_CONTAINER" nslookup "$DB_CONTAINER" >/dev/null 2>&1; then
        log_info "✓ DNS解析正常"
    else
        log_warn "⚠ DNS解析可能存在问题"
    fi
    
    return 0
}

# 测试PostgreSQL连接
test_postgresql_connection() {
    log_step "测试PostgreSQL连接..."
    
    local connection_tests=0
    local passed_tests=0
    
    # 测试1: 基本连接测试
    log_info "测试基本数据库连接..."
    connection_tests=$((connection_tests + 1))
    
    if docker exec "$TEST_CONTAINER" psql \
        -h "$DB_CONTAINER" \
        -p 5432 \
        -U "$DEFAULT_DB_USER" \
        -d "$DEFAULT_DB_NAME" \
        -c "SELECT version();" >/dev/null 2>&1; then
        log_info "✓ 基本数据库连接成功"
        passed_tests=$((passed_tests + 1))
    else
        log_error "✗ 基本数据库连接失败"
    fi
    
    # 测试2: 权限测试
    log_info "测试数据库权限..."
    connection_tests=$((connection_tests + 1))
    
    if docker exec "$TEST_CONTAINER" psql \
        -h "$DB_CONTAINER" \
        -p 5432 \
        -U "$DEFAULT_DB_USER" \
        -d "$DEFAULT_DB_NAME" \
        -c "CREATE TABLE test_permissions (id INTEGER); DROP TABLE test_permissions;" >/dev/null 2>&1; then
        log_info "✓ 数据库权限正常 (可创建/删除表)"
        passed_tests=$((passed_tests + 1))
    else
        log_warn "⚠ 数据库权限受限或连接问题"
    fi
    
    # 测试3: 事务测试
    log_info "测试事务支持..."
    connection_tests=$((connection_tests + 1))
    
    if docker exec "$TEST_CONTAINER" psql \
        -h "$DB_CONTAINER" \
        -p 5432 \
        -U "$DEFAULT_DB_USER" \
        -d "$DEFAULT_DB_NAME" \
        -c "BEGIN; SELECT 1; COMMIT;" >/dev/null 2>&1; then
        log_info "✓ 事务支持正常"
        passed_tests=$((passed_tests + 1))
    else
        log_error "✗ 事务支持异常"
    fi
    
    # 测试4: 编码测试
    log_info "测试字符编码..."
    connection_tests=$((connection_tests + 1))
    
    if docker exec "$TEST_CONTAINER" psql \
        -h "$DB_CONTAINER" \
        -p 5432 \
        -U "$DEFAULT_DB_USER" \
        -d "$DEFAULT_DB_NAME" \
        -c "SELECT '测试中文字符';" >/dev/null 2>&1; then
        log_info "✓ 字符编码支持正常"
        passed_tests=$((passed_tests + 1))
    else
        log_warn "⚠ 字符编码可能存在问题"
    fi
    
    log_info "数据库连接测试完成: $passed_tests/$connection_tests 通过"
    
    if [ $passed_tests -ge $((connection_tests * 3 / 4)) ]; then
        return 0
    else
        return 1
    fi
}

# 模拟Turnkey应用的数据库连接行为
test_turnkey_database_behavior() {
    log_step "模拟Turnkey应用数据库连接行为..."
    
    # 创建Turnkey可能需要的基本表结构（简化版本）
    log_info "创建测试数据库结构..."
    
    docker exec "$TEST_CONTAINER" psql \
        -h "$DB_CONTAINER" \
        -p 5432 \
        -U "$DEFAULT_DB_USER" \
        -d "$DEFAULT_DB_NAME" \
        -c "
        CREATE SCHEMA IF NOT EXISTS turnkey;
        
        CREATE TABLE IF NOT EXISTS turnkey.config (
            id SERIAL PRIMARY KEY,
            key VARCHAR(255) NOT NULL,
            value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        INSERT INTO turnkey.config (key, value) VALUES 
            ('app_version', '3.2.0'),
            ('database_initialized', 'true');
        " >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        log_info "✓ 测试数据库结构创建成功"
    else
        log_error "✗ 测试数据库结构创建失败"
        return 1
    fi
    
    # 测试数据查询
    log_info "测试数据查询..."
    
    local query_result
    query_result=$(docker exec "$TEST_CONTAINER" psql \
        -h "$DB_CONTAINER" \
        -p 5432 \
        -U "$DEFAULT_DB_USER" \
        -d "$DEFAULT_DB_NAME" \
        -t -c "SELECT COUNT(*) FROM turnkey.config;" 2>/dev/null | tr -d ' ')
    
    if [ "$query_result" = "2" ]; then
        log_info "✓ 数据查询正常 (找到 $query_result 条记录)"
    else
        log_warn "⚠ 数据查询结果异常: '$query_result'"
    fi
    
    # 测试连接池行为（模拟多个并发连接）
    log_info "测试并发连接..."
    
    local concurrent_success=0
    for i in {1..5}; do
        if docker exec "$TEST_CONTAINER" psql \
            -h "$DB_CONTAINER" \
            -p 5432 \
            -U "$DEFAULT_DB_USER" \
            -d "$DEFAULT_DB_NAME" \
            -c "SELECT NOW();" >/dev/null 2>&1 & then
            concurrent_success=$((concurrent_success + 1))
        fi
    done
    
    wait  # 等待所有后台任务完成
    
    log_info "✓ 并发连接测试: $concurrent_success/5 成功"
    
    return 0
}

# 测试连接错误处理
test_connection_error_scenarios() {
    log_step "测试连接错误处理场景..."
    
    # 场景1: 错误的主机名
    log_info "测试错误主机名处理..."
    if docker exec "$TEST_CONTAINER" timeout 10 psql \
        -h "nonexistent-host" \
        -p 5432 \
        -U "$DEFAULT_DB_USER" \
        -d "$DEFAULT_DB_NAME" \
        -c "SELECT 1;" >/dev/null 2>&1; then
        log_warn "⚠ 错误主机名测试意外成功"
    else
        log_info "✓ 错误主机名正确处理"
    fi
    
    # 场景2: 错误的端口
    log_info "测试错误端口处理..."
    if docker exec "$TEST_CONTAINER" timeout 10 psql \
        -h "$DB_CONTAINER" \
        -p 5433 \
        -U "$DEFAULT_DB_USER" \
        -d "$DEFAULT_DB_NAME" \
        -c "SELECT 1;" >/dev/null 2>&1; then
        log_warn "⚠ 错误端口测试意外成功"
    else
        log_info "✓ 错误端口正确处理"
    fi
    
    # 场景3: 错误的用户名
    log_info "测试错误用户名处理..."
    if docker exec "$TEST_CONTAINER" timeout 10 psql \
        -h "$DB_CONTAINER" \
        -p 5432 \
        -U "wrong_user" \
        -d "$DEFAULT_DB_NAME" \
        -c "SELECT 1;" >/dev/null 2>&1; then
        log_warn "⚠ 错误用户名测试意外成功"
    else
        log_info "✓ 错误用户名正确处理"
    fi
    
    return 0
}

# 生成连接测试配置文件
generate_connection_config() {
    log_step "生成连接测试配置..."
    
    local config_file="$SCRIPT_DIR/database-connection-test.conf"
    
    cat > "$config_file" << EOF
# 数据库连接测试配置文件
# 用于ECS部署前验证数据库连接参数

[database]
host = $DEFAULT_DB_HOST
port = $DEFAULT_DB_PORT  
database = $DEFAULT_DB_NAME
username = $DEFAULT_DB_USER
# password 应通过环境变量或secrets提供

[connection_pool]
min_connections = 1
max_connections = 20
connection_timeout = 30
idle_timeout = 300

[test_queries]
health_check = SELECT 1
version_check = SELECT version()
table_check = SELECT COUNT(*) FROM information_schema.tables

[ecs_environment]
# ECS环境中的数据库连接配置示例
# DB_HOST = your-aurora-cluster.amazonaws.com
# DB_PORT = 5432
# DB_NAME = einvoice
# DB_USER = einvoice_user
# DB_PASSWORD = (通过 ECS Secrets Manager 注入)
EOF

    log_info "连接测试配置已生成: $config_file"
}

# 显示数据库日志
show_database_logs() {
    log_step "显示数据库日志..."
    echo "=== PostgreSQL 日志 ==="
    docker logs "$DB_CONTAINER" 2>&1 | tail -20
    echo "======================="
}

# 显示连接统计信息
show_connection_statistics() {
    log_step "显示连接统计信息..."
    
    if docker exec "$DB_CONTAINER" psql -U "$DEFAULT_DB_USER" -d "$DEFAULT_DB_NAME" \
        -c "SELECT datname, numbackends, xact_commit, xact_rollback FROM pg_stat_database WHERE datname = '$DEFAULT_DB_NAME';" 2>/dev/null; then
        log_info "✓ 连接统计信息获取成功"
    else
        log_warn "⚠ 无法获取连接统计信息"
    fi
    
    # 显示当前连接
    log_info "当前数据库连接:"
    docker exec "$DB_CONTAINER" psql -U "$DEFAULT_DB_USER" -d "$DEFAULT_DB_NAME" \
        -c "SELECT client_addr, state, query FROM pg_stat_activity WHERE datname = '$DEFAULT_DB_NAME';" 2>/dev/null || true
}

# 主函数
main() {
    log_info "开始数据库连接测试..."
    echo "===================================="
    
    check_dependencies
    cleanup  # 清理可能存在的旧环境
    
    local test_results=()
    local passed_tests=0
    local total_tests=6
    
    # 执行各项测试
    if create_test_network && start_test_database && create_test_container; then
        test_results+=("环境准备: 成功")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("环境准备: 失败")
        log_error "测试环境准备失败"
        return 1
    fi
    
    if test_network_connectivity; then
        test_results+=("网络连接: 成功")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("网络连接: 失败")
    fi
    
    if test_postgresql_connection; then
        test_results+=("PostgreSQL连接: 成功")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("PostgreSQL连接: 失败")
    fi
    
    if test_turnkey_database_behavior; then
        test_results+=("Turnkey行为模拟: 成功")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("Turnkey行为模拟: 失败")
    fi
    
    if test_connection_error_scenarios; then
        test_results+=("错误场景处理: 成功")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("错误场景处理: 失败")
    fi
    
    # 生成配置和统计
    generate_connection_config
    show_connection_statistics
    test_results+=("配置生成: 成功")
    passed_tests=$((passed_tests + 1))
    
    # 显示测试结果
    echo -e "\n===================================="
    log_step "数据库连接测试结果汇总"
    echo "===================================="
    
    for result in "${test_results[@]}"; do
        if [[ $result == *"成功"* ]]; then
            echo -e "${GREEN}✓${NC} $result"
        else
            echo -e "${RED}✗${NC} $result"
        fi
    done
    
    echo -e "\n总计: ${passed_tests}/${total_tests} 项测试通过"
    
    if [ $passed_tests -eq $total_tests ]; then
        log_info "🎉 数据库连接测试全部通过！"
        log_info "数据库配置可用于ECS部署"
        return 0
    elif [ $passed_tests -ge $((total_tests * 3 / 4)) ]; then
        log_warn "⚠️ 数据库连接测试基本通过，注意少数问题"
        return 1
    else
        log_error "❌ 数据库连接测试失败，需要修复问题"
        show_database_logs
        return 1
    fi
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi