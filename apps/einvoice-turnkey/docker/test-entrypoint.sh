#!/bin/bash

# 测试重构后的entrypoint.sh证书处理逻辑

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

test_log() {
    echo -e "${GREEN}[TEST]${NC} $1"
}

test_error() {
    echo -e "${RED}[TEST ERROR]${NC} $1"
}

test_warn() {
    echo -e "${YELLOW}[TEST WARN]${NC} $1"
}

# 创建测试环境
setup_test_env() {
    test_log "Setting up test environment..."
    
    # 创建测试目录
    export TEST_DIR="/tmp/einvoice-test-$$"
    mkdir -p "$TEST_DIR/cert"
    
    # 设置证书目录
    export CERT_DIRECTORY="$TEST_DIR/cert"
    
    # 创建turnkey用户（如果不存在）
    if ! id turnkey >/dev/null 2>&1; then
        test_warn "turnkey user not found, using current user for testing"
    fi
}

# 清理测试环境
cleanup_test_env() {
    test_log "Cleaning up test environment..."
    rm -rf "$TEST_DIR"
}

# 测试1: JSON格式证书处理
test_json_certificates() {
    test_log "Test 1: JSON format certificate processing"
    
    # 创建测试证书数据（base64编码的示例数据）
    local test_cert_data="VGVzdCBjZXJ0aWZpY2F0ZSBkYXRhIGZvciB0ZXN0aW5nIHB1cnBvc2Vz"
    
    # 设置CERTIFICATES_JSON环境变量
    export CERTIFICATES_JSON='[
        {
            "pfx_base64": "'$test_cert_data'",
            "pfx_password": "test123",
            "sign_id": "testcert1",
            "sign_type": "invoice"
        },
        {
            "pfx_base64": "'$test_cert_data'",
            "pfx_password": "test456",
            "sign_id": "testcert2",
            "sign_type": "receipt"
        }
    ]'
    
    # 检查jq是否可用
    if ! command -v jq >/dev/null 2>&1; then
        test_error "jq not available, skipping JSON test"
        return 1
    fi
    
    # 源入测试函数
    source /Users/<USER>/code/yuan/iac/apps/einvoice-turnkey/docker/entrypoint.sh
    
    # 调用证书设置函数
    if setup_certificates; then
        test_log "JSON certificate processing succeeded"
        
        # 验证证书文件是否创建
        if [ -f "$CERT_DIRECTORY/testcert1_invoice.pfx" ] && [ -f "$CERT_DIRECTORY/testcert2_receipt.pfx" ]; then
            test_log "Certificate files created successfully"
            
            # 验证环境变量
            if [ "$CERT_COUNT" = "2" ] && [ -n "$PFX_BASE64_1" ] && [ -n "$PFX_BASE64_2" ]; then
                test_log "Environment variables set correctly"
                return 0
            else
                test_error "Environment variables not set correctly"
                return 1
            fi
        else
            test_error "Certificate files not created"
            return 1
        fi
    else
        test_error "JSON certificate processing failed"
        return 1
    fi
}

# 测试2: Legacy环境变量格式
test_legacy_env_vars() {
    test_log "Test 2: Legacy environment variable format"
    
    # 清理之前的测试
    unset CERTIFICATES_JSON
    unset CERT_SSM_DATA
    rm -rf "$CERT_DIRECTORY"/*
    
    # 创建测试证书数据
    local test_cert_data="VGVzdCBjZXJ0aWZpY2F0ZSBkYXRhIGZvciB0ZXN0aW5nIHB1cnBvc2Vz"
    
    # 设置Legacy环境变量
    export PFX_BASE64_1="$test_cert_data"
    export PFX_PASSWORD_1="legacy123"
    export SIGN_ID_1="legacycert1"
    export SIGN_TYPE_1="legacy"
    
    # 调用证书设置函数
    if setup_certificates; then
        test_log "Legacy certificate processing succeeded"
        
        # 验证证书文件
        if [ -f "$CERT_DIRECTORY/legacycert1_legacy.pfx" ]; then
            test_log "Legacy certificate file created successfully"
            
            # 验证环境变量
            if [ "$CERT_COUNT" = "1" ] && [ -n "$CERT_FILE_1" ]; then
                test_log "Legacy environment variables set correctly"
                return 0
            else
                test_error "Legacy environment variables not set correctly"
                return 1
            fi
        else
            test_error "Legacy certificate file not created"
            return 1
        fi
    else
        test_error "Legacy certificate processing failed"
        return 1
    fi
}

# 测试3: 无证书环境（占位符测试）
test_no_certificates() {
    test_log "Test 3: No certificates available (placeholder mode)"
    
    # 清理所有证书环境变量
    unset CERTIFICATES_JSON
    unset CERT_SSM_DATA
    unset PFX_BASE64_1
    unset PFX_PASSWORD_1
    rm -rf "$CERT_DIRECTORY"/*
    
    # 调用证书设置函数
    if setup_certificates; then
        test_log "No certificate processing succeeded"
        
        # 验证占位符文件是否创建
        if [ -f "$CERT_DIRECTORY/server.crt" ] && [ -f "$CERT_DIRECTORY/server.key" ]; then
            test_log "Placeholder certificate files created successfully"
            
            # 验证CERT_COUNT为0
            if [ "$CERT_COUNT" = "0" ]; then
                test_log "Certificate count correctly set to 0"
                return 0
            else
                test_error "Certificate count not set correctly"
                return 1
            fi
        else
            test_error "Placeholder certificate files not created"
            return 1
        fi
    else
        test_error "No certificate processing failed"
        return 1
    fi
}

# 主测试函数
main() {
    test_log "Starting entrypoint.sh certificate processing tests..."
    
    setup_test_env
    
    local failed_tests=0
    
    # 运行测试
    test_json_certificates || failed_tests=$((failed_tests + 1))
    test_legacy_env_vars || failed_tests=$((failed_tests + 1))
    test_no_certificates || failed_tests=$((failed_tests + 1))
    
    # 清理
    cleanup_test_env
    
    # 报告结果
    if [ $failed_tests -eq 0 ]; then
        test_log "All tests passed successfully!"
        exit 0
    else
        test_error "$failed_tests test(s) failed"
        exit 1
    fi
}

# 信号处理
trap 'cleanup_test_env; exit 1' SIGTERM SIGINT

# 运行测试
main "$@"