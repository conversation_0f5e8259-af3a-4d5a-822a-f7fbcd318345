# 电子发票Turnkey证书管理重构说明

## 概述

此文档说明了`entrypoint.sh`脚本中证书管理的重构变更，从依赖AWS CLI转向直接使用ECS注入的环境变量。

## 重构目标

1. **移除AWS CLI依赖**：不再需要容器内安装和配置AWS CLI
2. **简化部署**：直接使用ECS Secrets注入的环境变量
3. **提高安全性**：减少容器权限需求，无需AWS API调用权限
4. **向后兼容**：保持对旧证书格式的支持
5. **增强错误处理**：更好的日志记录和错误诊断

## 新的证书处理流程

### 1. 主要方式：CERTIFICATES_JSON 环境变量

容器启动时，ECS通过Secrets Manager注入`CERTIFICATES_JSON`环境变量，包含JSON数组格式的证书数据：

```json
[
  {
    "pfx_base64": "base64编码的PFX证书数据",
    "pfx_password": "证书密码",
    "sign_id": "证书标识符",
    "sign_type": "证书类型（如invoice、receipt）"
  },
  {
    "pfx_base64": "第二个证书的base64数据",
    "pfx_password": "第二个证书的密码",
    "sign_id": "cert2",
    "sign_type": "default"
  }
]
```

### 2. 证书处理逻辑

1. **JSON验证**：使用`jq`验证JSON格式
2. **证书解析**：提取每个证书的字段信息
3. **文件生成**：解码base64数据并保存为PFX文件
4. **权限设置**：设置正确的文件权限（600）和所有者（turnkey:turnkey）
5. **环境变量导出**：为每个证书设置编号后缀的环境变量

### 3. 生成的环境变量

对于每个证书（索引从1开始），脚本会设置以下环境变量：

```bash
PFX_BASE64_1=<base64证书数据>
PFX_PASSWORD_1=<证书密码>
SIGN_ID_1=<证书标识>
SIGN_TYPE_1=<证书类型>
CERT_FILE_1=<证书文件路径>
```

同时设置总证书数量：
```bash
CERT_COUNT=<证书总数>
```

## 向后兼容性

脚本保持了对旧格式的兼容性，按以下优先级处理：

1. **CERTIFICATES_JSON**（主要方式）
2. **CERT_SSM_DATA**（Legacy键值对格式）
3. **直接环境变量**（PFX_BASE64_1, PFX_PASSWORD_1等）
4. **占位符文件**（如果没有任何证书源）

## 错误处理和诊断

### jq工具检查

脚本会检查`jq`工具的可用性：
```bash
check_jq_available() {
    if ! command -v jq >/dev/null 2>&1; then
        log_error "jq is not installed or not available in PATH"
        return 1
    fi
    return 0
}
```

### JSON格式验证

验证JSON数据的格式是否正确：
```bash
if ! echo "$certificates_json" | jq . >/dev/null 2>&1; then
    log_error "Invalid JSON format in CERTIFICATES_JSON"
    return 1
fi
```

### 详细日志记录

脚本提供详细的日志记录，包括：
- 证书处理进度
- 文件权限设置
- 环境变量导出
- 错误诊断信息

## 部署配置

### ECS任务定义配置

在ECS任务定义中配置Secrets：

```json
{
  "secrets": [
    {
      "name": "CERTIFICATES_JSON",
      "valueFrom": "arn:aws:secretsmanager:region:account:secret:einvoice-certificates"
    }
  ]
}
```

### Secrets Manager数据格式

在AWS Secrets Manager中存储的证书数据应该是JSON数组格式：

```json
[
  {
    "pfx_base64": "MIIKqAIBAzCCCmQGCSqGSIb3DQEHAaCCClUEggpRMIIKTTCCBf...",
    "pfx_password": "your-certificate-password",
    "sign_id": "company-invoice-cert",
    "sign_type": "invoice"
  }
]
```

## 测试

使用提供的测试脚本验证证书处理功能：

```bash
./test-entrypoint.sh
```

测试覆盖：
1. JSON格式证书处理
2. Legacy环境变量格式
3. 无证书情况下的占位符创建

## 容器要求

### 必需工具
- `jq`：用于JSON解析
- `base64`：用于证书解码
- `chown`、`chmod`：用于权限设置

### 用户权限
- 容器需要创建`turnkey`用户
- 或者使用其他具有文件读写权限的用户

## 迁移指南

### 从旧版本迁移

1. **更新ECS任务定义**：添加CERTIFICATES_JSON secret
2. **更新Secrets Manager**：将证书数据转换为JSON数组格式
3. **测试部署**：在开发环境验证证书加载
4. **移除AWS CLI权限**：更新IAM角色，移除不必要的权限

### 验证迁移

部署后检查容器日志，确认看到类似输出：
```
[INFO] Using CERTIFICATES_JSON environment variable for certificate retrieval
[INFO] Successfully validated certificate JSON data
[INFO] Certificate 1 saved: company-invoice-cert_invoice.pfx
[INFO] Successfully processed 1 certificates from CERTIFICATES_JSON
[INFO] Total certificates available: 1
```

## 故障排除

### 常见问题

1. **jq not found**
   ```
   [ERROR] jq is not installed or not available in PATH
   ```
   解决：在Dockerfile中安装jq工具

2. **Invalid JSON format**
   ```
   [ERROR] Invalid JSON format in CERTIFICATES_JSON
   ```
   解决：验证Secrets Manager中的JSON格式

3. **Certificate decode failed**
   ```
   [ERROR] Failed to decode base64 certificate data for certificate 1
   ```
   解决：检查base64证书数据是否正确编码

4. **Permission errors**
   ```
   chown: cannot change ownership to 'turnkey:turnkey'
   ```
   解决：确保容器中存在turnkey用户，或修改为适当的用户

### 调试模式

启用详细日志记录：
```bash
export DEBUG_CERTIFICATES=1
./entrypoint.sh start
```

## 安全考虑

1. **最小权限原则**：容器不再需要AWS API访问权限
2. **证书保护**：PFX文件设置为600权限，只有所有者可读写
3. **内存安全**：敏感数据通过环境变量传递，避免写入日志
4. **清理机制**：容器退出时自动清理临时证书文件

## 性能优化

1. **减少启动时间**：无需AWS CLI调用，启动更快
2. **并行处理**：支持同时处理多个证书
3. **缓存机制**：避免重复的base64解码操作
4. **内存使用**：优化大证书文件的内存使用

---

此重构显著简化了证书管理流程，提高了安全性和可维护性，同时保持了向后兼容性。