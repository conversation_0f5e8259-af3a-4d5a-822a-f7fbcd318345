#!/bin/bash

# XML配置更新工具
# 智能处理XML元素的不同格式：空元素 <tag/>、空内容 <tag></tag>、有内容 <tag>content</tag>

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

# 智能更新XML元素的函数
# 参数: 文件路径 元素名 新值
update_xml_element() {
    local file="$1"
    local element="$2"
    local value="$3"
    
    if [ ! -f "$file" ]; then
        error "配置文件不存在: $file"
        return 1
    fi
    
    log "更新XML元素: $element = $value"
    
    # 创建临时文件
    local temp_file="${file}.tmp"
    
    # 使用awk脚本进行智能XML更新
    awk -v element="$element" -v value="$value" '
    BEGIN {
        # 构建匹配模式
        open_tag = "<" element ">"
        close_tag = "</" element ">"
        self_closing = "<" element "/>"
        empty_element = "<" element "></" element ">"
        replacement = "<" element ">" value "</" element ">"
    }
    {
        line = $0
        
        # 情况1: 自闭合标签 <element/>
        if (match(line, "<" element "\\s*/>")) {
            gsub("<" element "\\s*/>", replacement, line)
            updated = 1
        }
        # 情况2: 空内容标签 <element></element>
        else if (match(line, "<" element ">\\s*</" element ">")) {
            gsub("<" element ">\\s*</" element ">", replacement, line)
            updated = 1
        }
        # 情况3: 单行带内容 <element>content</element>
        else if (match(line, "<" element ">[^<]*</" element ">")) {
            gsub("<" element ">[^<]*</" element ">", replacement, line)
            updated = 1
        }
        # 情况4: 多行元素处理（开始标签）
        else if (match(line, "<" element ">")) {
            # 如果同一行有结束标签，按情况3处理
            if (match(line, "</" element ">")) {
                gsub("<" element ">.*</" element ">", replacement, line)
                updated = 1
            } else {
                # 多行元素：替换开始标签并标记跳过模式
                gsub("<" element ">", "<" element ">" value, line)
                skip_until_close = 1
                updated = 1
            }
        }
        # 情况5: 跳过多行元素的中间内容
        else if (skip_until_close && match(line, "</" element ">")) {
            # 到达结束标签，停止跳过
            skip_until_close = 0
            next  # 跳过这一行，因为内容已经在开始标签行添加了
        }
        # 情况6: 跳过多行元素的内容行
        else if (skip_until_close) {
            next  # 跳过内容行
        }
        
        print line
    }
    END {
        if (!updated) {
            print "<!-- WARNING: Element " element " not found or already updated -->"
        }
    }' "$file" > "$temp_file"
    
    # 检查是否成功生成临时文件
    if [ $? -eq 0 ] && [ -s "$temp_file" ]; then
        mv "$temp_file" "$file"
        log "✓ 元素 $element 更新完成"
        return 0
    else
        error "更新 $element 失败"
        rm -f "$temp_file"
        return 1
    fi
}

# 验证XML文件格式
validate_xml() {
    local file="$1"
    
    # 使用xmllint验证（如果可用）
    if command -v xmllint >/dev/null 2>&1; then
        if xmllint --noout "$file" 2>/dev/null; then
            log "✓ XML文件格式验证通过"
            return 0
        else
            error "XML文件格式验证失败"
            return 1
        fi
    else
        # 简单验证：检查XML声明和基本结构
        if grep -q "<?xml" "$file" && grep -q "<.*>" "$file"; then
            log "✓ XML文件基本格式正确"
            return 0
        else
            error "XML文件格式可能有问题"
            return 1
        fi
    fi
}

# 备份配置文件
backup_config() {
    local file="$1"
    local backup="${file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    if [ -f "$file" ]; then
        cp "$file" "$backup"
        log "✓ 配置文件已备份到: $backup"
        return 0
    else
        error "源文件不存在，无法备份"
        return 1
    fi
}

# 显示配置更改摘要（隐藏敏感信息）
show_config_summary() {
    local file="$1"
    
    log "当前数据库配置摘要:"
    
    # 显示关键配置（隐藏密码）
    if grep -q "<db-type>" "$file"; then
        local db_type=$(grep "<db-type>" "$file" | sed -e 's/<[^>]*>//g' | xargs)
        echo "  数据库类型: $db_type"
    fi
    
    if grep -q "<jdbc-driver>" "$file"; then
        local driver=$(grep "<jdbc-driver>" "$file" | sed -e 's/<[^>]*>//g' | xargs)
        echo "  JDBC驱动: $driver"
    fi
    
    if grep -q "<jdbc-url>" "$file"; then
        local url=$(grep "<jdbc-url>" "$file" | sed -e 's/<[^>]*>//g' | xargs)
        echo "  JDBC URL: $url"
    fi
    
    if grep -q "<db-user>" "$file"; then
        local user=$(grep "<db-user>" "$file" | sed -e 's/<[^>]*>//g' | xargs)
        echo "  数据库用户: $user"
    fi
    
    if grep -q "<db-password>" "$file"; then
        echo "  数据库密码: [已设置]"
    fi
    
    if grep -q "<db-config-ok>" "$file"; then
        local config_ok=$(grep "<db-config-ok>" "$file" | sed -e 's/<[^>]*>//g' | xargs)
        echo "  配置状态: $config_ok"
    fi
}

# 主更新函数
update_database_config() {
    local config_file="$1"
    local db_host="$2"
    local db_port="$3"
    local db_name="$4"
    local db_user="$5"
    local db_password="$6"
    local def_path="${7:-/var/EINVTurnkey}"  # 默认工作目录路径
    
    # 构造JDBC URL
    local jdbc_url="jdbc:postgresql://${db_host}:${db_port}/${db_name}"
    
    log "开始更新数据库配置..."
    log "配置文件: $config_file"
    log "JDBC URL: $jdbc_url"
    
    # 备份原始文件
    if ! backup_config "$config_file"; then
        return 1
    fi
    
    # 验证原始XML格式
    if ! validate_xml "$config_file"; then
        warn "原始XML格式验证失败，继续尝试更新..."
    fi
    
    # 更新各个配置项
    local success=0
    
    if update_xml_element "$config_file" "db-type" "postgresql"; then
        ((success++))
    fi
    
    if update_xml_element "$config_file" "jdbc-driver" "org.postgresql.Driver"; then
        ((success++))
    fi
    
    if update_xml_element "$config_file" "jdbc-url" "$jdbc_url"; then
        ((success++))
    fi
    
    if update_xml_element "$config_file" "db-user" "$db_user"; then
        ((success++))
    fi
    
    if update_xml_element "$config_file" "db-password" "$db_password"; then
        ((success++))
    fi
    
    # 设置工作目录路径
    if update_xml_element "$config_file" "def-path" "$def_path"; then
        ((success++))
        log "✓ 工作目录路径已设置: $def_path"
    fi
    
    # 设置数据库配置完成标志
    if update_xml_element "$config_file" "db-config-ok" "true"; then
        ((success++))
        log "✓ 数据库配置完成标志已设置"
    fi
    
    # 验证更新后的XML格式
    if ! validate_xml "$config_file"; then
        error "更新后的XML格式验证失败"
        return 1
    fi
    
    log "✓ 配置更新完成，共更新 $success 个配置项 (包括db-config-ok标志)"
    
    # 显示配置摘要
    show_config_summary "$config_file"
    
    return 0
}

# 使用说明
show_usage() {
    echo "用法: $0 <config_file> <db_host> <db_port> <db_name> <db_user> <db_password> [def_path]"
    echo ""
    echo "参数:"
    echo "  config_file    XML配置文件路径"
    echo "  db_host        数据库主机"
    echo "  db_port        数据库端口"
    echo "  db_name        数据库名称"
    echo "  db_user        数据库用户名"
    echo "  db_password    数据库密码"
    echo "  def_path       工作目录路径 (可选，默认: /var/EINVTurnkey)"
    echo ""
    echo "示例:"
    echo "  $0 /opt/EINVTurnkey/einvUserConfig.xml postgres 5432 einvoice einvoice_user password123"
    echo "  $0 /opt/EINVTurnkey/einvUserConfig.xml postgres 5432 einvoice einvoice_user password123 /var/EINVTurnkey"
}

# 主程序
main() {
    if [ $# -lt 6 ] || [ $# -gt 7 ]; then
        error "参数数量不正确"
        show_usage
        exit 1
    fi
    
    local config_file="$1"
    local db_host="$2"
    local db_port="$3"
    local db_name="$4"
    local db_user="$5"
    local db_password="$6"
    local def_path="${7:-/var/EINVTurnkey}"  # 可选参数，默认值
    
    log "=== XML配置更新工具 ==="
    
    if update_database_config "$config_file" "$db_host" "$db_port" "$db_name" "$db_user" "$db_password" "$def_path"; then
        log "✅ 配置更新成功完成"
        exit 0
    else
        error "❌ 配置更新失败"
        exit 1
    fi
}

# 运行主程序
main "$@"