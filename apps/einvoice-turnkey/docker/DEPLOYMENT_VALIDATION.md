# 电子发票Turnkey系统 - ECS部署验证指南

本指南提供了完整的Docker镜像验证流程，确保在部署到AWS ECS之前解决所有环境问题。

## 🎯 验证目标

在ECS部署前验证：
- Dockerfile编写正确性
- 启动脚本功能完整性
- 环境变量配置正确性
- 证书处理逻辑可靠性
- 数据库连接稳定性
- 健康检查机制有效性

## 📋 验证流程

### 1. 快速完整验证

```bash
# 进入Docker目录
cd apps/einvoice-turnkey/docker/

# 运行完整验证（推荐）
./local-validation.sh
```

这个脚本会：
- ✅ 构建Docker镜像
- ✅ 启动测试PostgreSQL数据库
- ✅ 创建模拟ECS环境
- ✅ 验证证书处理
- ✅ 测试数据库连接
- ✅ 执行健康检查
- ✅ 进行稳定性测试

### 2. 分项验证

#### 健康检查验证
```bash
./validate-health.sh
```

验证内容：
- 健康检查脚本语法正确性
- 容器环境集成测试
- 各检查函数功能测试
- 错误处理场景验证

#### 数据库连接验证
```bash
./test-db-connection.sh
```

验证内容：
- 网络连接测试
- PostgreSQL连接测试
- 权限和事务测试
- 并发连接测试
- 错误场景处理

### 3. 环境配置验证

#### 生产环境配置
```bash
# 检查生产环境模板
cat env-templates/production.env

# 验证必需的环境变量
grep -E "^[^#].*=" env-templates/production.env
```

#### 开发环境配置
```bash
# 使用开发环境配置进行本地测试
cp env-templates/development.env .env
docker run --env-file .env einvoice-turnkey:test
```

## 🔧 主要修复的问题

### 1. 用户权限问题
**问题**：容器以非root用户启动时权限不足
**解决方案**：
- 安装`su-exec`工具实现安全的用户切换
- 创建包装器脚本确保权限正确设置
- entrypoint.sh内部处理用户切换逻辑

### 2. 证书处理增强
**改进**：
- 支持ECS Secrets Manager的JSON格式注入
- 保持向后兼容的多种证书格式支持
- 增强错误处理和验证逻辑
- 支持最多20个证书的处理

### 3. 健康检查完善
**增强**：
- 多层健康检查机制
- 进程、文件、配置、Java进程全方位检查
- 智能的评分系统
- 详细的错误诊断信息

## 📊 验证结果解读

### 成功指标
- ✅ **构建成功**：Docker镜像构建无错误
- ✅ **启动成功**：容器能正常启动并保持运行
- ✅ **证书处理**：证书正确解码和存储
- ✅ **数据库连接**：能连接PostgreSQL数据库
- ✅ **健康检查**：健康检查逻辑正确响应
- ✅ **稳定性**：容器能持续运行不崩溃

### 警告指标
- ⚠️ **部分功能**：某些非关键功能可能有问题
- ⚠️ **性能**：运行正常但性能可能需要调优
- ⚠️ **配置**：配置可能需要根据实际环境调整

### 失败指标  
- ❌ **镜像构建失败**：Dockerfile存在问题
- ❌ **启动失败**：entrypoint.sh或应用启动存在问题
- ❌ **连接失败**：数据库连接配置错误
- ❌ **健康检查失败**：健康检查逻辑有缺陷

## 🚀 ECS部署准备

验证通过后，进行ECS部署：

### 1. 准备环境变量
```bash
# 基于production.env创建ECS任务定义中的环境变量
# 确保敏感信息通过ECS Secrets Manager注入
```

### 2. 证书配置
```json
{
  "name": "CERTIFICATES_JSON",
  "valueFrom": "arn:aws:secretsmanager:region:account:secret:turnkey-certificates"
}
```

### 3. 数据库配置
```bash
DB_HOST=your-aurora-endpoint.amazonaws.com
DB_PORT=5432
DB_NAME=einvoice
DB_USER=einvoice_user
# DB_PASSWORD 通过Secrets Manager注入
```

### 4. 健康检查配置
ECS任务定义中启用健康检查：
```json
{
  "healthCheck": {
    "command": ["/app/health-check.sh"],
    "interval": 30,
    "timeout": 10,
    "retries": 3,
    "startPeriod": 60
  }
}
```

## 🔍 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看容器日志
docker logs <container_name>

# 进入容器调试
docker exec -it <container_name> /bin/bash
```

#### 2. 数据库连接失败
- 检查网络配置
- 验证数据库凭据
- 确认数据库可访问性
- 检查防火墙设置

#### 3. 证书处理问题
- 验证JSON格式正确性
- 检查base64编码完整性
- 确认证书密码正确
- 验证文件权限设置

#### 4. 健康检查失败
- 检查应用启动状态
- 验证进程运行情况
- 确认配置文件存在
- 查看详细错误日志

### 调试工具

#### 进入容器调试模式
```bash
docker run -it --entrypoint=/bin/bash einvoice-turnkey:test
```

#### 查看详细日志
```bash
# 容器启动日志
docker logs -f <container_name>

# 应用日志
docker exec <container_name> tail -f /opt/EINVTurnkey/null/-SYS.log
```

## 📝 验证清单

部署前确认：

- [ ] `./local-validation.sh` 全部测试通过
- [ ] `./validate-health.sh` 健康检查验证通过  
- [ ] `./test-db-connection.sh` 数据库连接测试通过
- [ ] 环境变量配置完整且正确
- [ ] 证书数据格式正确且可访问
- [ ] 数据库连接参数正确
- [ ] ECS任务定义配置完成
- [ ] Secrets Manager配置完成
- [ ] 网络和安全组配置正确

## 🔄 持续验证

建议定期运行验证脚本：
- 代码更新后验证
- 环境配置变更后验证
- 部署前最终验证
- 生产问题排查时验证

通过这套验证体系，可以大大降低ECS部署失败的风险，确保Turnkey系统在云环境中稳定运行。