#!/bin/bash

# s5cmd集成测试脚本
# 验证s5cmd在容器中的功能和性能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 检查s5cmd是否安装
test_s5cmd_installation() {
    log_test "Testing s5cmd installation..."
    
    if command -v s5cmd >/dev/null 2>&1; then
        local version=$(s5cmd version 2>&1 || echo "unknown")
        log_info "✓ s5cmd found: $version"
        
        # 检查二进制文件大小
        local size=$(ls -lah $(which s5cmd) | awk '{print $5}')
        log_info "✓ s5cmd binary size: $size"
        
        return 0
    else
        log_error "✗ s5cmd not found in PATH"
        return 1
    fi
}

# 测试基本的s5cmd功能
test_s5cmd_basic_functionality() {
    log_test "Testing s5cmd basic functionality..."
    
    # 测试help命令
    if s5cmd --help >/dev/null 2>&1; then
        log_info "✓ s5cmd help command works"
    else
        log_error "✗ s5cmd help command failed"
        return 1
    fi
    
    # 测试version命令
    if s5cmd version >/dev/null 2>&1; then
        log_info "✓ s5cmd version command works"
    else
        log_error "✗ s5cmd version command failed"
        return 1
    fi
    
    return 0
}

# 测试AWS凭证配置（模拟）
test_aws_credentials_simulation() {
    log_test "Testing AWS credentials configuration simulation..."
    
    # 设置模拟区域
    export AWS_DEFAULT_REGION="ap-east-2"
    export AWS_REGION="ap-east-2"
    
    log_info "✓ AWS region set to: $AWS_DEFAULT_REGION"
    
    # 检查环境变量
    if [ -n "$AWS_DEFAULT_REGION" ] && [ -n "$AWS_REGION" ]; then
        log_info "✓ AWS region environment variables configured"
        return 0
    else
        log_error "✗ AWS region environment variables not set"
        return 1
    fi
}

# 测试证书下载逻辑（模拟）
test_certificate_download_simulation() {
    log_test "Testing certificate download simulation..."
    
    # 创建临时测试目录
    local test_dir="/tmp/s5cmd_test"
    mkdir -p "$test_dir"
    
    # 模拟证书URL
    local cert_urls="s3://test-bucket/certs/cert1.pfx,s3://test-bucket/certs/cert2.pfx"
    
    log_info "Simulating certificate download for URLs: $cert_urls"
    
    # 解析URL列表
    IFS=',' read -ra CERT_URLS <<< "$cert_urls"
    local processed_count=0
    
    for cert_url in "${CERT_URLS[@]}"; do
        local filename=$(basename "$cert_url")
        log_info "Would download: $cert_url -> $filename"
        
        # 创建模拟证书文件
        echo "Mock certificate content" > "$test_dir/$filename"
        
        if [ -f "$test_dir/$filename" ]; then
            chmod 600 "$test_dir/$filename"
            log_info "✓ Simulated download: $filename"
            processed_count=$((processed_count + 1))
        fi
    done
    
    log_info "✓ Processed $processed_count certificate files (simulated)"
    
    # 清理
    rm -rf "$test_dir"
    
    return 0
}

# 性能基准测试
test_performance_benchmark() {
    log_test "Testing s5cmd performance benchmark..."
    
    # 测试启动时间
    local start_time=$(date +%s%N)
    s5cmd version >/dev/null 2>&1
    local end_time=$(date +%s%N)
    local duration=$(((end_time - start_time) / 1000000))  # 转换为毫秒
    
    log_info "✓ s5cmd startup time: ${duration}ms"
    
    if [ "$duration" -lt 100 ]; then
        log_info "✓ Excellent startup performance (< 100ms)"
    elif [ "$duration" -lt 500 ]; then
        log_info "✓ Good startup performance (< 500ms)"
    else
        log_warn "⚠ Slow startup performance (> 500ms)"
    fi
    
    return 0
}

# 与系统资源的兼容性测试
test_system_compatibility() {
    log_test "Testing system compatibility..."
    
    # 检查依赖库
    if ldd $(which s5cmd) >/dev/null 2>&1; then
        log_info "✓ s5cmd dependencies check passed"
        ldd $(which s5cmd) | head -5
    else
        log_info "✓ s5cmd is a static binary (no dynamic dependencies)"
    fi
    
    # 检查文件权限
    local perms=$(stat -c "%a" $(which s5cmd) 2>/dev/null || stat -f "%A" $(which s5cmd) 2>/dev/null)
    if [ "$perms" -ge 755 ]; then
        log_info "✓ s5cmd has correct execution permissions: $perms"
    else
        log_error "✗ s5cmd has incorrect permissions: $perms"
        return 1
    fi
    
    return 0
}

# 主函数
main() {
    echo "======================================"
    echo "s5cmd Integration Test Suite"
    echo "======================================"
    
    local test_count=0
    local failed_count=0
    
    # 执行测试
    tests=(
        "test_s5cmd_installation"
        "test_s5cmd_basic_functionality"
        "test_aws_credentials_simulation"
        "test_certificate_download_simulation"
        "test_performance_benchmark"
        "test_system_compatibility"
    )
    
    for test in "${tests[@]}"; do
        test_count=$((test_count + 1))
        echo ""
        
        if $test; then
            log_info "Test $test_count passed: $test"
        else
            log_error "Test $test_count failed: $test"
            failed_count=$((failed_count + 1))
        fi
    done
    
    echo ""
    echo "======================================"
    echo "Test Summary"
    echo "======================================"
    log_info "Total tests: $test_count"
    log_info "Passed: $((test_count - failed_count))"
    
    if [ "$failed_count" -eq 0 ]; then
        log_info "✓ All tests passed!"
        echo "s5cmd integration is ready for production use."
        exit 0
    else
        log_error "✗ $failed_count tests failed!"
        echo "Please fix the issues before deploying."
        exit 1
    fi
}

# 如果脚本直接运行（而不是被source），执行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi