# 电子发票Turnkey系统 - 开发环境配置模板  
# 用于本地Docker开发和测试

# ==============================================================================
# 数据库配置
# ==============================================================================
DB_HOST=localhost
DB_PORT=5432
DB_NAME=einvoice_dev
DB_USER=einvoice_dev
DB_PASSWORD=dev_password

# ==============================================================================
# 证书配置 (开发环境 - 使用测试证书)
# ==============================================================================
# 开发环境使用简化的JSON格式
CERTIFICATES_JSON=[{"pfx_base64":"dGVzdCBjZXJ0aWZpY2F0ZSBjb250ZW50","pfx_password":"test_password","sign_id":"dev_cert_1","sign_type":"invoice"},{"pfx_base64":"dGVzdCBjZXJ0aWZpY2F0ZSBjb250ZW50Mg==","pfx_password":"test_password2","sign_id":"dev_cert_2","sign_type":"receipt"}]

# 证书存储目录
CERT_DIRECTORY=/opt/EINVTurnkey/cert

# ==============================================================================
# 应用程序配置
# ==============================================================================
TURNKEY_HOME=/opt/EINVTurnkey
TURNKEY_DATA=/data

# Java运行时配置（开发环境使用较小的内存）
JAVA_HOME=/usr/local/openjdk-17
JVM_OPTS=-Xmx1g -Xms512m -XX:+UseG1GC

# ==============================================================================
# 监控和日志配置
# ==============================================================================
# 开发环境启用详细日志
LOG_LEVEL=DEBUG
ENABLE_DEBUG_LOGS=true

# 健康检查配置
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=15

# ==============================================================================
# 开发环境特定配置
# ==============================================================================
# 开发模式标志
DEV_MODE=true
DEBUG_ENABLED=true

# 快速启动模式
FAST_STARTUP=true

# 禁用某些生产功能
PRODUCTION_FEATURES_ENABLED=false

# ==============================================================================
# 网络配置
# ==============================================================================
# 开发环境端口映射
APP_PORT=8080

# ==============================================================================
# 测试配置
# ==============================================================================
# 启用测试模式
TEST_MODE=true

# 模拟外部服务
MOCK_EXTERNAL_SERVICES=true

# 测试数据配置
USE_TEST_DATA=true