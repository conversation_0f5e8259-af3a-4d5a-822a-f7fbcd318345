# 环境变量配置模板

本目录包含不同环境的配置模板，用于配置电子发票Turnkey系统。

## 配置文件说明

### production.env
生产环境配置模板，包含：
- Aurora数据库连接配置
- ECS Secrets Manager证书注入格式
- 生产级别的JVM配置
- 安全和监控配置

### development.env  
开发环境配置模板，包含：
- 本地数据库配置
- 测试证书数据
- 开发友好的调试配置
- 快速启动选项

## 使用方式

### 1. ECS部署使用
```bash
# 将production.env中的配置添加到ECS任务定义的环境变量中
# 敏感信息（如DB_PASSWORD, CERTIFICATES_JSON）通过ECS Secrets Manager注入
```

### 2. 本地开发使用
```bash
# 复制开发模板
cp env-templates/development.env .env

# 编辑配置
vim .env

# 使用docker-compose或直接运行
docker run --env-file .env einvoice-turnkey:latest
```

### 3. 本地验证使用
```bash
# local-validation.sh脚本会自动创建测试环境变量
./local-validation.sh
```

## 证书配置格式

### 主要方式：CERTIFICATES_JSON (推荐)
```json
[
  {
    "pfx_base64": "base64编码的PFX证书内容",
    "pfx_password": "证书密码",
    "sign_id": "证书标识",
    "sign_type": "证书类型"
  }
]
```

### 备用方式：独立环境变量 (向后兼容)
```bash
PFX_BASE64_1=base64编码内容
PFX_PASSWORD_1=密码
SIGN_ID_1=证书标识
SIGN_TYPE_1=证书类型
```

## 安全注意事项

1. **生产环境**：
   - 绝不在配置文件中存储明文密码
   - 使用ECS Secrets Manager注入敏感信息
   - 启用所有安全功能

2. **开发环境**：
   - 可以使用简化的测试数据
   - 注意不要提交真实证书到版本控制

3. **配置验证**：
   - 使用`local-validation.sh`验证配置正确性
   - 部署前确保所有必需的环境变量已设置

## 故障排除

如果容器启动失败，检查：
1. 数据库连接配置是否正确
2. 证书JSON格式是否有效
3. 必需的环境变量是否已设置
4. 文件权限是否正确设置