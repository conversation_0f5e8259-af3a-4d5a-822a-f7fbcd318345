# 电子发票Turnkey系统 - 生产环境配置模板
# 用于ECS部署的环境变量配置

# ==============================================================================
# 数据库配置
# ==============================================================================
DB_HOST=your-aurora-cluster-endpoint.amazonaws.com
DB_PORT=5432
DB_NAME=einvoice
DB_USER=einvoice_user
# DB_PASSWORD 通过ECS Secrets Manager注入

# ==============================================================================
# 证书配置 (主要方式 - ECS Secrets Manager注入)
# ==============================================================================
# CERTIFICATES_JSON 通过ECS Secrets Manager注入，格式如下:
# [
#   {
#     "pfx_base64": "MIIKmw...base64编码的PFX证书内容",
#     "pfx_password": "certificate_password",
#     "sign_id": "company_invoice_cert",
#     "sign_type": "invoice"
#   },
#   {
#     "pfx_base64": "MIILpA...另一个证书的base64内容", 
#     "pfx_password": "another_password",
#     "sign_id": "company_receipt_cert",
#     "sign_type": "receipt"
#   }
# ]

# 证书存储目录
CERT_DIRECTORY=/opt/EINVTurnkey/cert

# ==============================================================================
# 应用程序配置
# ==============================================================================
TURNKEY_HOME=/opt/EINVTurnkey
TURNKEY_DATA=/data

# Java运行时配置
JAVA_HOME=/usr/local/openjdk-17
JVM_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC

# ==============================================================================
# 监控和日志配置
# ==============================================================================
# 启用详细日志记录
LOG_LEVEL=INFO
ENABLE_DEBUG_LOGS=false

# 健康检查配置
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30

# ==============================================================================
# 网络和安全配置
# ==============================================================================
# 应用监听端口（如果需要）
# APP_PORT=8080

# 安全配置
SECURITY_ENABLED=true

# ==============================================================================
# ECS特定配置
# ==============================================================================
# ECS任务配置
ECS_TASK_DEFINITION_FAMILY=turnkey-app
ECS_SERVICE_NAME=turnkey-service

# AWS区域（通过ECS任务元数据自动获取）
# AWS_REGION=ap-east-1

# ==============================================================================
# 备用配置 (向后兼容)
# ==============================================================================
# 如果主要的CERTIFICATES_JSON方式不可用，可以使用以下格式:

# 方式2: SSM Parameter格式（已弃用，保留兼容性）
# CERT_SSM_DATA="pfx_base64_1=MIIKmw... pfx_password_1=password123 sign_id_1=cert1 sign_type_1=invoice pfx_base64_2=MIILpA... pfx_password_2=password456 sign_id_2=cert2 sign_type_2=receipt"

# 方式3: 独立环境变量（已弃用，保留兼容性）
# PFX_BASE64_1=MIIKmw...
# PFX_PASSWORD_1=password123
# SIGN_ID_1=company_invoice_cert
# SIGN_TYPE_1=invoice
# PFX_BASE64_2=MIILpA...
# PFX_PASSWORD_2=password456
# SIGN_ID_2=company_receipt_cert
# SIGN_TYPE_2=receipt