#!/bin/bash

# 电子发票Turnkey系统本地Docker验证脚本
# 模拟ECS环境，验证Docker镜像在部署前的完整性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
IMAGE_NAME="einvoice-turnkey"
CONTAINER_NAME="turnkey-test-$$"
TEST_NETWORK="turnkey-test-network"
DB_CONTAINER="turnkey-test-db"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 清理函数
cleanup() {
    log_info "清理测试环境..."
    
    # 停止并删除容器
    if docker ps -a --format "table {{.Names}}" | grep -q "$CONTAINER_NAME"; then
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
    fi
    
    if docker ps -a --format "table {{.Names}}" | grep -q "$DB_CONTAINER"; then
        docker stop "$DB_CONTAINER" 2>/dev/null || true
        docker rm "$DB_CONTAINER" 2>/dev/null || true
    fi
    
    # 删除测试网络
    if docker network ls --format "table {{.Name}}" | grep -q "$TEST_NETWORK"; then
        docker network rm "$TEST_NETWORK" 2>/dev/null || true
    fi
}

# 信号处理
trap cleanup EXIT

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    if ! command -v docker >/dev/null 2>&1; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v jq >/dev/null 2>&1; then
        log_error "jq 未安装，请安装: brew install jq"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行"
        exit 1
    fi
    
    log_info "系统依赖检查通过"
}

# 构建Docker镜像
build_image() {
    log_step "构建Docker镜像..."
    
    cd "$SCRIPT_DIR"
    
    # 检查Dockerfile是否存在
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile 不存在"
        exit 1
    fi
    
    # 构建镜像
    if docker build -t "$IMAGE_NAME:test" .; then
        log_info "Docker镜像构建成功"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# 创建测试网络
create_test_network() {
    log_step "创建测试网络..."
    
    if ! docker network create "$TEST_NETWORK" --driver bridge >/dev/null 2>&1; then
        log_warn "网络可能已存在，继续..."
    fi
    
    log_info "测试网络已创建"
}

# 启动测试数据库
start_test_database() {
    log_step "启动测试PostgreSQL数据库..."
    
    # 启动PostgreSQL容器（使用5433端口避免与主机PostgreSQL冲突）
    docker run -d \
        --name "$DB_CONTAINER" \
        --network "$TEST_NETWORK" \
        -e POSTGRES_DB=einvoice \
        -e POSTGRES_USER=einvoice \
        -e POSTGRES_PASSWORD=test_password \
        -e POSTGRES_HOST_AUTH_METHOD=md5 \
        -p 5433:5432 \
        postgres:13 >/dev/null
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec "$DB_CONTAINER" pg_isready -U einvoice -d einvoice >/dev/null 2>&1; then
            log_info "数据库已启动"
            break
        fi
        
        log_warn "等待数据库启动... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        log_error "数据库启动超时"
        exit 1
    fi
}

# 验证容器启动
test_container_startup() {
    log_step "测试容器启动..."
    
    # 读取测试证书
    local certificates_json
    certificates_json=$(cat "$SCRIPT_DIR/test-certificates.json")
    
    # 启动容器（使用测试环境模式）
    docker run -d \
        --name "$CONTAINER_NAME" \
        --network "$TEST_NETWORK" \
        -e DB_HOST="$DB_CONTAINER" \
        -e DB_PORT=5432 \
        -e DB_NAME=einvoice \
        -e DB_USER=einvoice \
        -e DB_PASSWORD=test_password \
        -e CERTIFICATES_JSON="$certificates_json" \
        -e CERT_DIRECTORY="/opt/EINVTurnkey/cert" \
        -e TURNKEY_ENV="prod" \
        "$IMAGE_NAME:test" >/dev/null
    
    log_info "容器启动命令已执行"
    
    # 等待容器启动
    sleep 10
    
    # 检查容器状态
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$CONTAINER_NAME.*Up"; then
        log_info "容器启动成功"
        return 0
    else
        log_error "容器启动失败"
        return 1
    fi
}

# 验证容器健康状态
test_container_health() {
    log_step "验证容器健康状态..."
    
    # 检查容器是否仍在运行
    if ! docker ps --format "table {{.Names}}" | grep -q "$CONTAINER_NAME"; then
        log_error "容器未运行"
        show_container_logs
        return 1
    fi
    
    # 执行健康检查
    local health_attempts=5
    local attempt=1
    
    while [ $attempt -le $health_attempts ]; do
        log_info "执行健康检查 ($attempt/$health_attempts)..."
        
        if docker exec "$CONTAINER_NAME" /app/health-check.sh >/dev/null 2>&1; then
            log_info "健康检查通过"
            return 0
        fi
        
        log_warn "健康检查失败，等待重试..."
        sleep 15
        attempt=$((attempt + 1))
    done
    
    log_error "健康检查多次失败"
    return 1
}

# 验证证书处理
test_certificate_handling() {
    log_step "验证证书处理..."
    
    # 检查证书目录
    if docker exec "$CONTAINER_NAME" ls -la /opt/EINVTurnkey/cert/ >/dev/null 2>&1; then
        log_info "证书目录存在"
    else
        log_error "证书目录不存在"
        return 1
    fi
    
    # 检查证书文件
    local cert_count
    cert_count=$(docker exec "$CONTAINER_NAME" find /opt/EINVTurnkey/cert/ -name "*.pfx" -type f | wc -l)
    
    if [ "$cert_count" -gt 0 ]; then
        log_info "找到 $cert_count 个PFX证书文件"
    else
        log_warn "未找到PFX证书文件"
    fi
    
    # 检查证书环境变量
    if docker exec "$CONTAINER_NAME" printenv CERT_COUNT >/dev/null 2>&1; then
        local cert_env_count
        cert_env_count=$(docker exec "$CONTAINER_NAME" printenv CERT_COUNT)
        log_info "证书环境变量计数: $cert_env_count"
    else
        log_warn "证书环境变量未设置"
    fi
    
    return 0
}

# 验证数据库连接
test_database_connection() {
    log_step "验证数据库连接..."
    
    # 在容器内测试数据库连接
    if docker exec "$CONTAINER_NAME" nc -z "$DB_CONTAINER" 5432 >/dev/null 2>&1; then
        log_info "数据库连接测试通过"
        return 0
    else
        log_error "数据库连接测试失败"
        return 1
    fi
}

# 验证应用进程
test_application_processes() {
    log_step "验证应用进程..."
    
    # 在测试环境中，检查模拟进程
    if docker exec "$CONTAINER_NAME" pgrep -f "simulate_turnkey" >/dev/null 2>&1; then
        log_info "Turnkey模拟进程运行中"
        return 0
    fi
    
    # 检查Java进程（生产模式）
    if docker exec "$CONTAINER_NAME" pgrep -f "java" >/dev/null 2>&1; then
        log_info "Java进程运行中"
        return 0
    fi
    
    # 检查Turnkey特定进程（生产模式）
    if docker exec "$CONTAINER_NAME" pgrep -f "EINVTurnkey\|Turnkey" >/dev/null 2>&1; then
        log_info "Turnkey进程运行中"
        return 0
    fi
    
    # 检查bash进程（模拟进程可能以bash运行）
    if docker exec "$CONTAINER_NAME" pgrep -f "bash.*simulate" >/dev/null 2>&1; then
        log_info "Turnkey模拟进程运行中（bash）"
        return 0
    fi
    
    log_warn "未找到Turnkey相关进程"
    
    # 显示当前运行的进程以便调试
    log_info "当前运行的进程："
    docker exec "$CONTAINER_NAME" ps aux | head -10
    
    return 1
}

# 显示容器日志
show_container_logs() {
    log_step "显示容器日志..."
    echo "=== 容器启动日志 ==="
    docker logs "$CONTAINER_NAME" 2>&1 | tail -50
    echo "===================="
}

# 显示详细状态信息
show_container_status() {
    log_step "显示容器详细状态..."
    
    echo "=== 容器状态 ==="
    docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo -e "\n=== 容器资源使用 ==="
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
    
    echo -e "\n=== 证书文件 ==="
    docker exec "$CONTAINER_NAME" ls -la /opt/EINVTurnkey/cert/ 2>/dev/null || echo "证书目录不可访问"
    
    echo -e "\n=== 环境变量 ==="
    docker exec "$CONTAINER_NAME" env | grep -E "(CERT|DB|TURNKEY)" | sort
    
    echo -e "\n=== 进程列表 ==="
    docker exec "$CONTAINER_NAME" ps aux 2>/dev/null || echo "进程列表不可访问"
}

# 运行完整验证
run_full_validation() {
    log_step "开始完整验证流程..."
    
    local test_results=()
    local passed_tests=0
    local total_tests=6
    
    # 执行各项测试
    if test_container_startup; then
        test_results+=("容器启动: 通过")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("容器启动: 失败")
    fi
    
    if test_certificate_handling; then
        test_results+=("证书处理: 通过")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("证书处理: 失败")
    fi
    
    if test_database_connection; then
        test_results+=("数据库连接: 通过")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("数据库连接: 失败")
    fi
    
    if test_application_processes; then
        test_results+=("应用进程: 通过")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("应用进程: 失败")
    fi
    
    # 等待一段时间让应用充分启动
    log_info "等待应用完全启动..."
    sleep 30
    
    if test_container_health; then
        test_results+=("健康检查: 通过")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("健康检查: 失败")
    fi
    
    # 额外的稳定性测试
    log_info "进行稳定性测试..."
    sleep 30
    
    if docker ps --format "table {{.Names}}" | grep -q "$CONTAINER_NAME"; then
        test_results+=("稳定性测试: 通过")
        passed_tests=$((passed_tests + 1))
    else
        test_results+=("稳定性测试: 失败")
    fi
    
    # 显示测试结果
    log_step "验证结果汇总"
    echo "===================="
    for result in "${test_results[@]}"; do
        if [[ $result == *"通过"* ]]; then
            echo -e "${GREEN}✓${NC} $result"
        else
            echo -e "${RED}✗${NC} $result"
        fi
    done
    echo "===================="
    echo -e "总计: ${passed_tests}/${total_tests} 项测试通过"
    
    # 显示详细状态
    show_container_status
    
    if [ $passed_tests -eq $total_tests ]; then
        log_info "🎉 所有验证测试通过！Docker镜像可以部署到ECS"
        return 0
    elif [ $passed_tests -ge $((total_tests * 2 / 3)) ]; then
        log_warn "⚠️ 大部分测试通过，但存在一些问题需要关注"
        show_container_logs
        return 1
    else
        log_error "❌ 验证失败，需要修复问题后重新测试"
        show_container_logs
        return 1
    fi
}

# 主函数
main() {
    echo "=== 电子发票Turnkey系统Docker验证 ==="
    echo "模拟ECS环境，验证镜像部署准备情况"
    echo "========================================="
    
    check_dependencies
    cleanup  # 清理可能存在的旧环境
    create_test_network
    start_test_database
    build_image
    
    # 运行验证
    if run_full_validation; then
        log_info "验证完成 - 成功"
        exit 0
    else
        log_error "验证完成 - 失败"
        exit 1
    fi
}

# 如果是直接运行脚本
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi