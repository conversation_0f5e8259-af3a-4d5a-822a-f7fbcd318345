#!/bin/bash

# 电子发票Turnkey系统启动脚本
# 处理证书管理和应用程序启动
#
# 证书管理重构 (v2.0):
# - 主要方式：使用CERTIFICATES_JSON环境变量（ECS Secrets注入的JSON数组格式）
# - 移除AWS CLI依赖：不再调用aws secretsmanager、aws ssm命令
# - 移除AWS区域获取：不再使用curl获取EC2 metadata
# - 增强错误处理：添加jq可用性检查和JSON格式验证
# - 支持更多证书：最多20个证书（之前10个）
#
# 环境变量格式：
# CERT_FILES=s3://bucket/path/to/cert1.pfx,s3://bucket/path/to/cert2.pfx

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}


# S3证书下载函数 (使用s5cmd替代MinIO Client)
download_certificates_from_s3() {
    log_info "Downloading certificates from S3 using s5cmd..."
    
    local cert_dir="/opt/EINVTurnkey/cert"
    local cert_files="${CERT_FILES:-}"
    
    if [ -z "$cert_files" ]; then
        log_error "CERT_FILES environment variable not set or empty"
        log_error "This function should only be called when CERT_FILES is available"
        return 1
    fi
    
    mkdir -p "$cert_dir"
    
    # 配置s5cmd使用IAM角色认证
    # s5cmd会自动使用ECS/EC2上的IAM角色凭证，无需额外配置
    # 确保使用正确的AWS区域配置
    local aws_region="${AWS_DEFAULT_REGION:-ap-east-2}"
    
    # 尝试自动检测区域（从ECS元数据）
    if command -v curl >/dev/null 2>&1; then
        detected_region=$(curl -s --connect-timeout 2 http://169.254.169.254/latest/meta-data/placement/region 2>/dev/null || echo "")
        if [ -n "$detected_region" ]; then
            aws_region="$detected_region"
            log_info "Detected AWS region: $aws_region"
        fi
    fi
    
    # 设置区域环境变量，s5cmd会自动使用
    export AWS_DEFAULT_REGION="$aws_region"
    export AWS_REGION="$aws_region"
    
    # 验证s5cmd可用性
    if ! command -v s5cmd >/dev/null 2>&1; then
        log_error "s5cmd not found in PATH"
        return 1
    fi
    
    log_info "Using s5cmd for S3 access with region: $aws_region"
    
    # 分割URL列表并下载每个文件
    IFS=',' read -ra CERT_URLS <<< "$cert_files"
    local processed_count=0
    
    for cert_url in "${CERT_URLS[@]}"; do
        # 从URL提取文件名
        local filename=$(basename "$cert_url")
        local filepath="$cert_dir/$filename"
        
        # 使用s5cmd下载文件
        log_info "Downloading certificate: $cert_url -> $filepath"
        if s5cmd cp "$cert_url" "$filepath" 2>/tmp/s5cmd_error.log; then
            chmod 600 "$filepath"
            chown turnkey:root "$filepath" 2>/dev/null || true
            log_info "Successfully downloaded certificate: $filename"
            processed_count=$((processed_count + 1))
        else
            log_error "Failed to download certificate: $cert_url"
            if [ -f "/tmp/s5cmd_error.log" ]; then
                log_error "s5cmd Error details:"
                cat /tmp/s5cmd_error.log >&2
            fi
            
            # 提供故障排除提示
            log_error "Troubleshooting tips:"
            log_error "1. Verify IAM role has s3:GetObject and s3:ListBucket permissions"
            log_error "2. Check if certificate file exists: s5cmd ls $cert_url"
            log_error "3. Ensure AWS region is correctly set: $AWS_DEFAULT_REGION"
            log_error "4. Test s5cmd configuration: s5cmd ls s3://bucket-name/turnkey/certs/"
            return 1
        fi
    done
    
    export CERT_COUNT=$processed_count
    log_info "Successfully downloaded $processed_count certificates from S3"
    return 0
}



# 证书管理函数
setup_certificates() {
    log_info "Setting up certificates..."
    
    # 检查是否需要从S3下载证书
    if [ -n "${CERT_FILES:-}" ]; then
        log_info "CERT_FILES environment variable found, downloading from S3..."
        
        if download_certificates_from_s3; then
            # 设置证书目录权限
            local cert_dir="/opt/EINVTurnkey/cert"
            chown -R turnkey:root "$cert_dir" 2>/dev/null || true
            chmod 700 "$cert_dir"
            
            log_info "S3 certificate setup completed successfully"
            return 0
        else
            log_error "Failed to setup certificates from S3"
            return 1
        fi
    else
        log_warn "CERT_FILES environment variable not set, skipping S3 certificate download"
        log_info "Assuming certificates are already mounted or will be provided by other means"
        
        # 确保证书目录存在
        local cert_dir="/opt/EINVTurnkey/cert"
        mkdir -p "$cert_dir"
        chown -R turnkey:root "$cert_dir" 2>/dev/null || true
        chmod 700 "$cert_dir"
        
        # 检查是否已有证书文件存在
        if [ -d "$cert_dir" ] && [ "$(ls -A "$cert_dir" 2>/dev/null)" ]; then
            log_info "Found existing certificates in $cert_dir"
            return 0
        else
            log_warn "No certificates found in $cert_dir"
            log_warn "Application may fail if certificates are required"
            return 0  # 不阻止启动，让应用程序自己处理缺少证书的情况
        fi
    fi
}

# 健康检查函数
health_check() {
    # 检查应用程序是否运行
    if pgrep -f "EINVTurnkey" > /dev/null; then
        return 0
    fi
    
    # 检查数据库连接
    if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ]; then
        if nc -z "$DB_HOST" "$DB_PORT" > /dev/null 2>&1; then
            log_info "Database connection OK"
        else
            log_error "Database connection failed"
            return 1
        fi
    fi
    
    return 0
}

# 数据库连接检查
check_database_connection() {
    log_info "Checking database connection..."
    
    if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
        log_error "Database configuration incomplete"
        return 1
    fi
    
    # 等待数据库就绪
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$DB_HOST" "$DB_PORT" > /dev/null 2>&1; then
            log_info "Database connection successful"
            return 0
        fi
        
        log_warn "Waiting for database... (attempt $attempt/$max_attempts)"
        sleep 10
        attempt=$((attempt + 1))
    done
    
    log_error "Database connection timeout after $max_attempts attempts"
    return 1
}

# 应用程序初始化
initialize_application() {
    log_info "Initializing Turnkey application..."
    
    # 检查应用程序目录
    APP_DIR="/opt/EINVTurnkey"
    if [ ! -d "$APP_DIR" ]; then
        log_error "Application directory not found: $APP_DIR"
        return 1
    fi
    
    cd "$APP_DIR"
    
    # 设置应用程序权限
    chown -R turnkey:root "$APP_DIR"
    
    # 配置数据库连接信息
    configure_database
    
    # 如果存在初始化脚本，执行它
    if [ -f "$APP_DIR/init.sh" ]; then
        log_info "Running application initialization script..."
        bash "$APP_DIR/init.sh"
    fi
    
    return 0
}

# 配置数据库连接信息
configure_database() {
    log_info "Configuring database connection..."
    
    # 检查数据库环境变量
    if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
        log_warn "Database environment variables not fully configured, using defaults"
        export DB_HOST="${DB_HOST:-localhost}"
        export DB_PORT="${DB_PORT:-5432}"
        export DB_NAME="${DB_NAME:-einvoice}"
        export DB_USER="${DB_USER:-einvoice}"
        export DB_PASSWORD="${DB_PASSWORD:-}"
    fi
    
    log_info "Database configuration:"
    log_info "  Host: $DB_HOST"
    log_info "  Port: $DB_PORT"
    log_info "  Database: $DB_NAME"
    log_info "  User: $DB_USER"
    
    # 使用update-xml-config.sh更新数据库配置
    if [ -f "/app/update-xml-config.sh" ]; then
        log_info "Using update-xml-config.sh to configure database settings..."
        
        # 确保脚本有执行权限
        chmod +x /app/update-xml-config.sh
        
        # 定义配置文件路径
        local config_file="/opt/EINVTurnkey/einvUserConfig.xml"
        
        # 运行配置更新脚本，传递所需的参数（包括工作目录路径）
        local work_dir="${WORK_DIR:-/var/EINVTurnkey}"
        if /app/update-xml-config.sh "$config_file" "$DB_HOST" "$DB_PORT" "$DB_NAME" "$DB_USER" "${DB_PASSWORD:-}" "$work_dir"; then
            log_info "✓ Database configuration updated successfully"
            log_info "✓ Work directory path set to: $work_dir"
        else
            log_error "✗ Failed to update database configuration"
            return 1
        fi
    else
        log_warn "update-xml-config.sh not found, skipping automatic configuration"
        log_warn "Manual database configuration may be required"
    fi
    
    # 验证配置文件存在
    local config_file="/opt/EINVTurnkey/einvUserConfig.xml"
    if [ -f "$config_file" ]; then
        log_info "✓ Configuration file exists: $config_file"
        
        # 设置配置文件权限
        chown turnkey:root "$config_file" 2>/dev/null || true
        chmod 640 "$config_file" 2>/dev/null || true
        
        # 简单验证配置内容
        if grep -q -i "postgresql\|database" "$config_file"; then
            log_info "✓ Database configuration validation passed"
        else
            log_warn "⚠ Configuration file may not contain database settings"
        fi
    else
        log_warn "⚠ Configuration file not found, application may fail to start"
    fi
    
    return 0
}

# 创建健康检查脚本
create_health_check_script() {
    cat > /opt/EINVTurnkey/health-check.sh << 'EOF'
#!/bin/bash
# Turnkey健康检查脚本

# 检查主要进程（支持生产模式和模拟模式）
if ! pgrep -f "EINVTurnkey\|simulate_turnkey" > /dev/null; then
    echo "Turnkey process not running"
    exit 1
fi

# 检查证书文件
CERT_DIR="${CERT_DIRECTORY:-/opt/EINVTurnkey/cert}"
if [ -d "$CERT_DIR" ]; then
    # 检查是否存在PFX证书文件（新格式）
    pfx_count=$(find "$CERT_DIR" -name "*.pfx" -type f | wc -l)
    if [ "$pfx_count" -gt 0 ]; then
        echo "Found $pfx_count PFX certificate(s)"
        # 验证PFX证书文件的权限
        for pfx_file in "$CERT_DIR"/*.pfx; do
            if [ -f "$pfx_file" ]; then
                file_perms=$(stat -c "%a" "$pfx_file" 2>/dev/null || stat -f "%A" "$pfx_file" 2>/dev/null)
                if [ "$file_perms" != "600" ]; then
                    echo "Certificate file has incorrect permissions: $(basename "$pfx_file") ($file_perms)"
                    exit 1
                fi
            fi
        done
    else
        # 回退检查传统证书文件（保持向后兼容性）
        for cert_file in "server.crt" "server.key"; do
            if [ ! -f "$CERT_DIR/$cert_file" ]; then
                echo "Missing certificate file: $cert_file"
                exit 1
            fi
        done
    fi
fi

# 检查证书环境变量
if [ -n "$CERT_COUNT" ] && [ "$CERT_COUNT" -gt 0 ]; then
    echo "Certificate count: $CERT_COUNT"
    # 验证证书环境变量
    for i in $(seq 1 "$CERT_COUNT"); do
        cert_file_var="CERT_FILE_$i"
        eval cert_file=\$$cert_file_var
        if [ -n "$cert_file" ] && [ ! -f "$cert_file" ]; then
            echo "Certificate file not found: $cert_file (from $cert_file_var)"
            exit 1
        fi
    done
fi

# 检查工作目录
if [ -n "$WORK_DIR" ] && [ ! -d "$WORK_DIR" ]; then
    echo "Work directory not accessible: $WORK_DIR"
    exit 1
fi

echo "Health check passed"
exit 0
EOF

    chmod +x /opt/EINVTurnkey/health-check.sh
    chown turnkey:root /opt/EINVTurnkey/health-check.sh
}

# 模拟模式启动（用于开发和测试环境）
start_simulation_mode() {
    log_info "Starting Turnkey application in simulation mode..."
    
    # 创建模拟进程
    cat > /tmp/simulate_turnkey.sh << 'EOF'
#!/bin/bash
# Turnkey模拟进程

echo "Turnkey simulation process started (PID: $$)"
echo "Simulating EINVTurnkey application behavior..."

# 模拟应用程序日志
while true; do
    echo "$(date) [INFO] Turnkey simulation running - checking certificates..."
    sleep 30
    
    echo "$(date) [INFO] Turnkey simulation running - checking database connection..."
    sleep 30
    
    echo "$(date) [INFO] Turnkey simulation running - processing invoices..."
    sleep 60
done
EOF
    
    chmod +x /tmp/simulate_turnkey.sh
    chown turnkey:root /tmp/simulate_turnkey.sh
    
    # 记录模拟启动信息
    log_info "Simulation mode features:"
    log_info "  - Mock Turnkey process for health checks"
    log_info "  - Database connection testing"
    log_info "  - Certificate validation"
    log_info "  - No actual invoice processing"
    
    # 使用gosu安全地切换到turnkey用户并启动模拟
    if command -v gosu >/dev/null 2>&1; then
        log_info "Using gosu to start simulation as turnkey user..."
        exec gosu turnkey:root /tmp/simulate_turnkey.sh
    elif command -v su-exec >/dev/null 2>&1; then
        log_info "Using su-exec to start simulation as turnkey user..."
        exec su-exec turnkey:root /tmp/simulate_turnkey.sh
    else
        log_warn "gosu/su-exec not available, using su command..."
        exec su -c "/tmp/simulate_turnkey.sh" turnkey
    fi
}

# 生产模式启动（真实应用程序）
start_production_mode() {
    log_info "Starting Turnkey application in production mode..."
    
    # 检查启动脚本
    if [ ! -f "/opt/EINVTurnkey/run_start.sh" ]; then
        log_error "Application start script not found: /opt/EINVTurnkey/run_start.sh"
        log_info "Available files in /opt/EINVTurnkey:"
        ls -la /opt/EINVTurnkey/ || true
        
        # 在生产模式下，如果启动脚本不存在，这是严重错误
        log_error "Cannot start production application without run_start.sh"
        log_error "This may indicate an incomplete installation or corrupted image"
        exit 1
    fi
    
    # 确保运行脚本具有执行权限
    chmod +x /opt/EINVTurnkey/run_start.sh
    chown turnkey:root /opt/EINVTurnkey/run_start.sh
    
    # 记录生产启动信息
    log_info "Production mode features:"
    log_info "  - Full Turnkey application"
    log_info "  - Real invoice processing"
    log_info "  - Complete database operations"
    log_info "  - Certificate-based signing"
    
    # 使用gosu安全地切换到turnkey用户并启动应用程序
    if command -v gosu >/dev/null 2>&1; then
        log_info "Using gosu to start application as turnkey user..."
        exec gosu turnkey:root /opt/EINVTurnkey/run_start.sh
    elif command -v su-exec >/dev/null 2>&1; then
        log_info "Using su-exec to start application as turnkey user..."
        exec su-exec turnkey:root /opt/EINVTurnkey/run_start.sh
    else
        log_warn "gosu/su-exec not available, trying alternative user switch..."
        # 备用方案：使用su命令
        exec su -c "/opt/EINVTurnkey/run_start.sh" turnkey
    fi
}

# 主启动流程
main() {
    log_info "Starting Turnkey container initialization..."
    
    # 设置证书
    setup_certificates
    
    # 检查数据库连接
    check_database_connection || exit 1
    
    # 初始化应用程序
    initialize_application || exit 1
    
    # 创建健康检查脚本
    create_health_check_script
    
    log_info "Container initialization completed successfully"
    
    # 根据传入的参数决定启动方式
    case "$1" in
        "start")
            log_info "Starting Turnkey application..."
            # 切换到应用目录
            cd /opt/EINVTurnkey
            
            # 检测运行环境
            if [ "$TURNKEY_ENV" = "development" ] || [ "$TURNKEY_ENV" = "test" ]; then
                log_info "Running in $TURNKEY_ENV environment - using simulation mode"
                start_simulation_mode
            else
                log_info "Running in production environment - starting real application"
                start_production_mode
            fi
            ;;
        "health")
            health_check
            ;;
        "bash"|"sh")
            # 调试模式，以turnkey用户启动shell
            if command -v gosu >/dev/null 2>&1; then
                exec gosu turnkey:root /bin/bash
            elif command -v su-exec >/dev/null 2>&1; then
                exec su-exec turnkey:root /bin/bash
            else
                exec su - turnkey
            fi
            ;;
        *)
            log_info "Running custom command: $*"
            # 其他命令以turnkey用户执行
            if command -v gosu >/dev/null 2>&1; then
                exec gosu turnkey:root "$@"
            elif command -v su-exec >/dev/null 2>&1; then
                exec su-exec turnkey:root "$@"
            else
                exec su -c "$*" turnkey
            fi
            ;;
    esac
}

# 信号处理
trap 'log_info "Received termination signal, shutting down..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"