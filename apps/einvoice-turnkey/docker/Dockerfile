# ==============================================================================
# 台湾电子发票Turnkey系统Docker镜像 - 多阶段优化版本
# 基于OpenJDK 17，提供完整的电子发票交换服务
# ==============================================================================

# ------------------------------------------------------------------------------
# 阶段1: 下载和构建阶段
# ------------------------------------------------------------------------------
FROM ubuntu:18.04 AS downloader

# 构建参数 - 可选的升级包URL
ARG UPGRADE_URL=https://www.einvoice.nat.gov.tw/static/ptl/ein_upload/download/5480.zip

# 设置工作目录
WORKDIR /tmp

# 安装下载工具和解压工具
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    unzip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/*

# 下载、解压并安装电子发票Turnkey系统
RUN echo "Downloading E-Invoice Turnkey System..." && \
    wget -O turnkey.zip https://www.einvoice.nat.gov.tw/static/ptl/ein_upload/download/5420.zip && \
    (echo "A" | unzip -o turnkey.zip -d turnkey/ || true) && \
    ls -la turnkey/ && \
    # 提取数据库初始化脚本
    mkdir -p /app/sql && \
    find turnkey -name "PostgreSQL.sql" -exec cp {} /app/sql/ \; && \
    # 解压并准备应用程序
    find turnkey -name "EINVTurnkey_setup_*.tar.gz" -exec tar -xzf {} -C . \; && \
    find . -type d -name "EINVTurnkey" -exec cp -r {} /app/EINVTurnkey \; && \
    ls -la /app/EINVTurnkey/ && \
    # 清理下载文件
    rm -rf turnkey turnkey.zip EINVTurnkey_setup_*.tar.gz

# 检查并下载升级包（如果UPGRADE_URL存在）
RUN if [ -n "$UPGRADE_URL" ]; then \
        echo "Downloading upgrade package from: $UPGRADE_URL" && \
        wget -O turnkey_upgrade.zip "$UPGRADE_URL" && \
        (echo "A" | unzip -o turnkey_upgrade.zip -d /app/EINVTurnkey/EINVUPGRADE/ || true) && \
        ls -la /app/EINVTurnkey/EINVUPGRADE/ && \
        rm -f turnkey_upgrade.zip; \
    else \
        echo "No UPGRADE_URL specified, skipping upgrade download" && \
        echo "Creating empty placeholder in EINVUPGRADE directory" && \
        touch /app/EINVTurnkey/EINVUPGRADE/.placeholder; \
    fi

# ------------------------------------------------------------------------------
# 阶段2: 运行时镜像
# ------------------------------------------------------------------------------
FROM ubuntu:25.10 AS runtime

# 构建参数
ARG TURNKEY_VERSION=3.2.0
ARG BUILD_DATE
ARG VCS_REF
ARG ENVIRONMENT=dev

# 维护者信息和标签
LABEL maintainer="YuanHui Team <<EMAIL>>" \
      description="Taiwan E-Invoice Turnkey System - Optimized" \
      version="${TURNKEY_VERSION}" \
      org.opencontainers.image.title="Taiwan E-Invoice Turnkey System" \
      org.opencontainers.image.description="Taiwan Electronic Invoice Turnkey System for enterprise integration" \
      org.opencontainers.image.version="${TURNKEY_VERSION}" \
      org.opencontainers.image.created="${BUILD_DATE}" \
      org.opencontainers.image.revision="${VCS_REF}" \
      org.opencontainers.image.vendor="YuanHui Team" \
      org.opencontainers.image.licenses="Proprietary" \
      turnkey.version="${TURNKEY_VERSION}" \
      turnkey.environment="${ENVIRONMENT}" \
      turnkey.java.version="17"

# 安装运行时必要的系统依赖
RUN apt-get update && apt-get install -y \
    openjdk-17-jdk \
    curl \
    procps \
    jq \
    netcat-openbsd \
    gosu \
    unzip \
    ca-certificates \
    # 添加中文支持
      locales \
      fonts-noto-cjk-extra \
      fonts-wqy-zenhei \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/* \
    && update-ca-certificates \
    # 生成中文locale
      && locale-gen zh_TW.UTF-8 \
      && update-locale LANG=zh_TW.UTF-8 \
    && sync

# 安装s5cmd - 超轻量级高性能S3客户端（仅4.8MB vs MinIO 20MB vs AWS CLI 150MB）
# s5cmd提供12倍于AWS CLI的性能，Go编译单二进制无依赖
RUN ARCH=$(dpkg --print-architecture) \
    && if [ "$ARCH" = "arm64" ]; then \
        S5CMD_ARCH="Linux-arm64"; \
    else \
        S5CMD_ARCH="Linux-64bit"; \
    fi \
    && echo "Installing s5cmd for architecture: $ARCH (using $S5CMD_ARCH)" \
    && curl -L "https://github.com/peak/s5cmd/releases/download/v2.3.0/s5cmd_2.3.0_${S5CMD_ARCH}.tar.gz" | tar xz \
    && mv s5cmd /usr/local/bin/ \
    && chmod +x /usr/local/bin/s5cmd \
    && s5cmd version

# 创建应用用户和组
RUN useradd -r -u 101 -g root turnkey && \
    mkdir -p /opt/EINVTurnkey /var/EINVTurnkey /app && \
    chown -R turnkey:root /opt/EINVTurnkey /var/EINVTurnkey /app && \
    chmod -R g+rwX /opt/EINVTurnkey /var/EINVTurnkey /app

# 设置环境变量（自动检测Java路径）
ENV JAVA_HOME=/usr/lib/jvm/default-java \
    PATH=$JAVA_HOME/bin:$PATH \
    TURNKEY_HOME=/opt/EINVTurnkey \
    TURNKEY_WORK=/var/EINVTurnkey \
    TURNKEY_VERSION=${TURNKEY_VERSION} \
    TURNKEY_ENV=${ENVIRONMENT} \
    # 添加中文环境变量
      LANG=zh_TW.UTF-8 \
      LANGUAGE=zh_TW:en \
      LC_ALL=zh_TW.UTF-8


# 复制应用程序文件
COPY --from=downloader --chown=turnkey:root /app/EINVTurnkey /opt/EINVTurnkey
COPY --from=downloader --chown=turnkey:root /app/sql /app/sql

# 设置工作目录
WORKDIR /opt/EINVTurnkey

# 复制脚本文件
COPY --chown=turnkey:root entrypoint.sh /app/entrypoint.sh
COPY --chown=turnkey:root health-check.sh /app/health-check.sh
COPY --chown=turnkey:root update-xml-config.sh /app/update-xml-config.sh

# 设置权限和Java路径
RUN touch javahome && \
    find . -name "*.sh" -exec chmod +x {} \; && \
    chmod +x /app/entrypoint.sh /app/health-check.sh /app/update-xml-config.sh && \
    chown -R turnkey:root /opt/EINVTurnkey /var/EINVTurnkey /app && \
    chmod -R g+rwX /opt/EINVTurnkey /var/EINVTurnkey /app

# 设置Java路径并创建符号链接（确保跨架构兼容性）
RUN ARCH=$(dpkg --print-architecture) && \
    if [ "$ARCH" = "arm64" ]; then \
        JAVA_PATH="/usr/lib/jvm/java-17-openjdk-arm64"; \
    elif [ "$ARCH" = "amd64" ]; then \
        JAVA_PATH="/usr/lib/jvm/java-17-openjdk-amd64"; \
    else \
        JAVA_PATH=$(find /usr/lib/jvm -name "java-17-openjdk-*" -type d | head -1); \
    fi && \
    echo "Detected architecture: $ARCH, Java path: $JAVA_PATH" && \
    ln -sf "$JAVA_PATH" /usr/lib/jvm/default-java && \
    ls -la /usr/lib/jvm/default-java

# 执行升级脚本（如果存在）- 直接使用JAVA_HOME环境变量
RUN if [ -f "/opt/EINVTurnkey/EINVUPGRADE/upgrade.sh" ]; then \
        echo "Found upgrade script, executing upgrade..." && \
        cd /opt/EINVTurnkey && \
        # 创建 javahome 文件标记使用系统 Java
        echo "Using system Java from JAVA_HOME" > javahome && \
        # 设置权限
        chmod +x EINVUPGRADE/upgrade.sh && \
        # 验证JAVA_HOME设置
        echo "JAVA_HOME is set to: $JAVA_HOME" && \
        if [ -x "$JAVA_HOME/bin/java" ]; then \
            echo "Java found and verified:" && \
            $JAVA_HOME/bin/java -version && \
            # 直接使用已设置的JAVA_HOME执行升级脚本
            bash EINVUPGRADE/upgrade.sh && \
            echo "Upgrade completed successfully"; \
        else \
            echo "Java not found at JAVA_HOME: $JAVA_HOME" && \
            echo "Skipping upgrade script execution"; \
        fi && \
        # 清理升级文件以减少镜像大小
        rm -rf EINVUPGRADE; \
    else \
        echo "No upgrade script found, proceeding with standard installation"; \
    fi

# 健康检查配置
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /app/health-check.sh

# 创建优化的启动包装器脚本
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# 显示版本信息\n\
echo "=== Taiwan E-Invoice Turnkey System ==="\n\
echo "Version: ${TURNKEY_VERSION}"\n\
echo "Environment: ${TURNKEY_ENV}"\n\
echo "Java Version: $(java -version 2>&1 | head -1)"\n\
echo "Build Date: ${BUILD_DATE}"\n\
echo "VCS Ref: ${VCS_REF}"\n\
echo "========================================"\n\
\n\
# 确保目录权限正确\n\
echo "Setting up permissions..."\n\
chown -R turnkey:root /opt/EINVTurnkey /var/EINVTurnkey /app 2>/dev/null || true\n\
chmod -R g+rwX /opt/EINVTurnkey /var/EINVTurnkey 2>/dev/null || true\n\
\n\
# 验证Java环境\n\
echo "Verifying Java environment..."\n\
java -version || {\n\
    echo "ERROR: Java runtime not available"\n\
    exit 1\n\
}\n\
\n\
# 验证应用目录\n\
if [ ! -d "/opt/EINVTurnkey" ]; then\n\
    echo "ERROR: Turnkey application directory not found"\n\
    exit 1\n\
fi\n\
\n\
# 创建必要的工作目录\n\
mkdir -p /var/EINVTurnkey/logs /var/EINVTurnkey/temp /var/EINVTurnkey/backup\n\
chown -R turnkey:root /var/EINVTurnkey 2>/dev/null || true\n\
\n\
echo "Starting Turnkey system..."\n\
# 以root身份执行entrypoint.sh，内部会处理用户切换\n\
exec /app/entrypoint.sh "$@"' > /app/docker-entrypoint.sh && \
    chmod +x /app/docker-entrypoint.sh

# 创建证书目录
RUN mkdir -p /opt/EINVTurnkey/cert && \
    chown -R turnkey:root /opt/EINVTurnkey/cert && \
    chmod -R g+rwX /opt/EINVTurnkey/cert

# 声明数据卷
VOLUME ["/var/EINVTurnkey", "/opt/EINVTurnkey/cert"]

# 暴露可能使用的端口（预留，根据实际需要调整）
# EXPOSE 8080 8443

# 保持root权限，让entrypoint.sh内部处理用户切换
# 这样可以确保在需要时有足够权限修改文件权限

# 启动应用
ENTRYPOINT ["/app/docker-entrypoint.sh"]
CMD ["start"]

# 添加运行时验证步骤
RUN echo "=== Build Verification ===" && \
    ls -la /opt/EINVTurnkey && \
    ls -la /app && \
    java -version && \
    s5cmd version && \
    /app/health-check.sh --version || true && \
    echo "=== Build Complete ==="