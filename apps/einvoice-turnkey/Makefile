# 电子发票Turnkey系统 - 简化Makefile
# 专注于基本构建、测试和验证功能

# 默认配置
DOCKER_REGISTRY ?= yuanhui
IMAGE_NAME ?= einvoice-turnkey
IMAGE_TAG ?= latest
FULL_IMAGE_NAME = $(DOCKER_REGISTRY)/$(IMAGE_NAME):$(IMAGE_TAG)

# 颜色输出
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[1;33m
BLUE = \033[0;34m
NC = \033[0m # No Color

# 默认目标
.DEFAULT_GOAL := help

# 帮助信息
.PHONY: help
help: ## 显示帮助信息
	@echo "$(GREEN)电子发票Turnkey系统 - 构建和测试工具$(NC)"
	@echo ""
	@echo "$(YELLOW)构建命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / && /构建|build/ {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)测试和验证:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / && /测试|test|验证|verify/ {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(YELLOW)维护工具:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / && /清理|clean|检查|check/ {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(BLUE)环境变量:$(NC)"
	@echo "  IMAGE_TAG=latest     设置镜像标签"
	@echo "  DOCKER_REGISTRY      设置Docker注册表"

# =====================================
# 构建相关命令
# =====================================

.PHONY: build
build: ## 构建Docker镜像
	@echo "$(GREEN)构建Docker镜像: $(FULL_IMAGE_NAME)$(NC)"
	docker build -t $(FULL_IMAGE_NAME) -f docker/Dockerfile .
	@echo "$(GREEN)镜像构建完成$(NC)"

.PHONY: build-no-cache
build-no-cache: ## 构建Docker镜像(无缓存)
	@echo "$(GREEN)构建Docker镜像(无缓存): $(FULL_IMAGE_NAME)$(NC)"
	docker build --no-cache -t $(FULL_IMAGE_NAME) -f docker/Dockerfile .
	@echo "$(GREEN)镜像构建完成$(NC)"

# =====================================
# 测试和验证命令
# =====================================

.PHONY: test
test: verify test-compose ## 运行完整测试套件

.PHONY: verify
verify: ## 验证镜像构建正确性
	@echo "$(GREEN)验证Docker镜像$(NC)"
	./verify-image.sh -i $(FULL_IMAGE_NAME)

.PHONY: verify-quick
verify-quick: ## 快速验证镜像基本功能
	@echo "$(BLUE)快速验证镜像$(NC)"
	@docker image inspect $(FULL_IMAGE_NAME) >/dev/null 2>&1 && echo "$(GREEN)✓ 镜像存在$(NC)" || echo "$(RED)✗ 镜像不存在$(NC)"
	@docker create --name einvoice-quick-test $(FULL_IMAGE_NAME) >/dev/null 2>&1 || true
	@docker exec einvoice-quick-test test -f /opt/EINVTurnkey/einvUserConfig.xml 2>/dev/null && echo "$(GREEN)✓ 配置文件存在$(NC)" || echo "$(RED)✗ 配置文件缺失$(NC)"
	@docker rm -f einvoice-quick-test >/dev/null 2>&1 || true

.PHONY: test-xml-update
test-xml-update: ## 测试XML配置更新工具
	@echo "$(BLUE)测试XML配置更新工具$(NC)"
	@if [ -f "docker/update-xml-config.sh" ]; then \
		bash -n docker/update-xml-config.sh && echo "$(GREEN)✓ XML更新脚本语法正确$(NC)" || echo "$(RED)✗ XML更新脚本语法错误$(NC)"; \
	else \
		echo "$(RED)✗ XML更新脚本不存在$(NC)"; \
	fi

# =====================================
# 开发和调试
# =====================================

.PHONY: shell
shell: ## 进入容器shell（用于调试）
	@echo "$(GREEN)启动调试容器$(NC)"
	docker run --rm -it --name einvoice-debug $(FULL_IMAGE_NAME) /bin/bash

.PHONY: logs
logs: ## 查看测试容器日志
	@if [ -f "docker-compose.test.yml" ]; then \
		docker compose -f docker-compose.test.yml logs; \
	else \
		echo "$(YELLOW)⚠ docker-compose.test.yml 不存在$(NC)"; \
	fi

# =====================================
# 维护工具
# =====================================

.PHONY: clean
clean: ## 清理构建资源
	@echo "$(YELLOW)清理Docker资源$(NC)"
	docker system prune -f
	@echo "$(GREEN)清理完成$(NC)"

.PHONY: clean-all
clean-all: ## 清理所有相关资源(危险操作)
	@echo "$(RED)清理所有Docker资源$(NC)"
	@read -p "确认要删除所有相关资源吗? [y/N] " confirm && [ "$$confirm" = "y" ]
	docker rmi $(FULL_IMAGE_NAME) || true
	docker system prune -a -f
	docker volume prune -f
	@echo "$(GREEN)全部清理完成$(NC)"

.PHONY: check
check: ## 检查环境和依赖
	@echo "$(BLUE)环境检查$(NC)"
	@echo "Docker版本: $$(docker --version)"
	@echo "Docker Compose版本: $$(docker compose version)"
	@echo ""
	@echo "$(BLUE)检查必要文件$(NC)"
	@ls -la docker/Dockerfile && echo "$(GREEN)✓ Dockerfile存在$(NC)" || echo "$(RED)✗ Dockerfile缺失$(NC)"
	@ls -la docker/entrypoint.sh && echo "$(GREEN)✓ entrypoint.sh存在$(NC)" || echo "$(RED)✗ entrypoint.sh缺失$(NC)"
	@ls -la docker/health-check.sh && echo "$(GREEN)✓ health-check.sh存在$(NC)" || echo "$(RED)✗ health-check.sh缺失$(NC)"
	@ls -la docker/update-xml-config.sh && echo "$(GREEN)✓ update-xml-config.sh存在$(NC)" || echo "$(RED)✗ update-xml-config.sh缺失$(NC)"
	@ls -la verify-image.sh && echo "$(GREEN)✓ verify-image.sh存在$(NC)" || echo "$(RED)✗ verify-image.sh缺失$(NC)"
	@echo ""
	@echo "$(BLUE)当前配置$(NC)"
	@echo "镜像名称: $(FULL_IMAGE_NAME)"

# =====================================
# 快速命令别名
# =====================================

.PHONY: rebuild
rebuild: clean build ## 清理并重新构建

.PHONY: quick-test
quick-test: build verify-quick ## 构建并快速验证

.PHONY: full-test
full-test: build test ## 构建并完整测试

# =====================================
# CI/CD支持
# =====================================

.PHONY: ci-build
ci-build: ## CI环境构建
	@echo "$(GREEN)CI环境构建$(NC)"
	docker build -t $(FULL_IMAGE_NAME) -f docker/Dockerfile . --progress=plain

.PHONY: ci-test
ci-test: ci-build verify ## CI环境测试
	@echo "$(GREEN)CI环境测试完成$(NC)"