# 电子发票Turnkey系统 - 本地开发环境

Taiwan E-Invoice Turnkey System 的完整本地开发和测试环境，基于Docker Compose构建。

## 🚀 快速开始

### 1. 环境准备

**系统要求：**
- Docker >= 20.10
- Docker Compose >= 2.0
- Make (可选，用于便捷命令)
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

**检查环境：**
```bash
# 检查Docker版本
docker --version
docker compose version

# 检查系统资源
docker system df
```

### 2. 快速部署

```bash
# 克隆仓库（如果需要）
git clone <repository-url>
cd einvoice-turnkey

# 初始化环境配置
make env-init

# 编辑配置文件
vim .env

# 一键启动所有服务
make up

# 或使用Docker Compose直接启动
docker compose up -d
```

### 3. 验证部署

```bash
# 查看服务状态
make status

# 检查服务健康状态  
make health

# 查看服务日志
make logs
```

**访问地址：**
- 数据库: `localhost:5432`
- PgAdmin: `http://localhost:8080`
- Java调试端口: `5005` (开发模式)

## 📋 项目结构

```
einvoice-turnkey/
├── docker/                    # Docker构建文件
│   ├── Dockerfile             # 主Dockerfile
│   ├── entrypoint.sh          # 容器启动脚本
│   └── health-check.sh        # 健康检查脚本
├── docs/                      # 文档目录
├── config/                    # 本地配置覆盖
├── init-db/                   # 数据库初始化脚本
├── backups/                   # 数据库备份
├── logs/                      # 应用日志
├── pgadmin/                   # PgAdmin配置
├── docker-compose.yml         # 主要服务配置
├── docker-compose.override.yml # 开发环境配置
├── Makefile                   # 便捷命令
├── .env.example               # 环境变量模板
└── README.md                  # 本文档
```

## 🛠️ 开发工具

### Make命令

```bash
# 查看所有可用命令
make help

# 构建和启动
make build              # 构建Docker镜像
make up                 # 启动所有服务
make up-build           # 构建并启动
make down               # 停止所有服务
make restart            # 重启服务

# 开发工具
make debug              # 启动调试模式
make logs               # 查看所有日志
make logs-turnkey       # 查看Turnkey日志
make shell              # 进入Turnkey容器
make psql               # 连接数据库

# 测试
make test               # 运行完整测试
make health             # 健康检查
make status             # 服务状态

# 数据库管理
make db-init            # 初始化数据库
make db-backup          # 备份数据库
make db-restore BACKUP_FILE=xxx.sql # 恢复数据库

# 维护
make clean              # 清理Docker资源
make update             # 更新并重启
make monitoring         # 启动监控工具
```

### Docker Compose命令

```bash
# 基本操作
docker compose up -d                    # 后台启动
docker compose down                     # 停止服务
docker compose ps                       # 查看状态
docker compose logs -f turnkey          # 查看指定服务日志

# 开发模式
docker compose --profile dev up -d      # 启动开发配置
docker compose --profile tools up -d    # 启动工具服务

# 服务管理
docker compose exec turnkey bash        # 进入容器
docker compose exec postgres psql -U einvoice_user -d einvoice  # 连接数据库
```

## 🔧 配置说明

### 环境变量配置

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

**主要配置项：**

```bash
# 数据库配置
DB_NAME=einvoice
DB_USER=einvoice_user
DB_PASSWORD=your_secure_password

# 应用配置
TURNKEY_PASSWORD=         # Turnkey启动密码（可选）
JAVA_OPTS=-Xmx2g -Xms1g   # JVM配置
LOG_LEVEL=INFO            # 日志级别

# 开发配置
DEBUG=true                # 是否启用调试
DEBUG_PORT=5005           # Java调试端口
```

### 服务配置

**PostgreSQL配置：**
- 端口: 5432
- 数据库: einvoice
- 用户名/密码: 见.env文件
- 数据持久化: postgres_data卷

**Turnkey应用配置：**
- 工作目录: /opt/EINVTurnkey
- 数据目录: /data
- 日志目录: /opt/EINVTurnkey/null
- 健康检查: 每60秒检查一次

**PgAdmin配置：**
- 访问地址: http://localhost:8080
- 用户名/密码: 见.env文件
- 预配置数据库连接

## 🐛 调试指南

### 开发模式调试

```bash
# 启动开发模式
make debug

# 查看调试日志
make logs-turnkey

# 进入容器调试
make shell
```

**Java远程调试：**
1. 启动开发模式: `make debug`
2. IDE连接到 `localhost:5005`
3. 设置断点进行调试

### 常见问题排查

**服务启动失败：**
```bash
# 检查服务状态
make status

# 查看详细日志
make logs

# 检查Docker资源
docker system df
```

**数据库连接问题：**
```bash
# 测试数据库连接
make test-database

# 检查数据库日志
make logs-postgres

# 手动连接数据库
make psql
```

**Turnkey应用问题：**
```bash
# 检查应用健康状态
make health

# 查看应用配置
docker compose exec turnkey cat einvUserConfig.xml

# 重启应用服务
docker compose restart turnkey
```

### 日志分析

**查看实时日志：**
```bash
# 所有服务日志
make logs

# 特定服务日志
docker compose logs -f turnkey
docker compose logs -f postgres
```

**日志文件位置：**
- Turnkey应用日志: `turnkey_logs` 卷
- PostgreSQL日志: 容器标准输出
- Docker Compose日志: 系统Docker日志

## 🧪 测试指南

### 自动化测试

```bash
# 运行完整测试套件
make test

# 单独测试组件
make test-build          # 测试镜像构建
make test-services       # 测试服务启动
make test-database       # 测试数据库
make test-turnkey        # 测试Turnkey应用
```

### 手动测试

**数据库测试：**
```bash
# 连接数据库
make psql

# 检查表结构
\dt

# 测试查询
SELECT version();
```

**应用程序测试：**
```bash
# 进入应用容器
make shell

# 检查应用配置
cat einvUserConfig.xml

# 检查日志文件
ls -la null/
tail -f null/-SYS.log
```

### 性能测试

**资源监控：**
```bash
# 实时监控
make watch

# Docker资源统计
docker stats

# 容器资源使用
docker compose exec turnkey top
```

## 📊 监控和维护

### 服务监控

```bash
# 启动监控工具
make monitoring

# 实时状态监控
make watch

# 健康检查
make health
```

### 数据库维护

```bash
# 创建备份
make db-backup

# 恢复备份
make db-restore BACKUP_FILE=backup_20241201_120000.sql

# 重置数据库（危险操作）
make db-reset
```

### 系统维护

```bash
# 清理未使用资源
make clean

# 更新系统
make update

# 完全清理（危险）
make clean-all
```

## 🚀 生产环境部署

### 生产环境配置

1. **创建生产环境配置：**
```bash
cp .env.example .env.prod
```

2. **修改生产配置：**
```bash
# 生产环境关键配置
DEBUG=false
LOG_LEVEL=WARN
JAVA_OPTS=-Xmx4g -Xms2g -XX:+UseG1GC

# 使用强密码
DB_PASSWORD=production_strong_password
PGADMIN_PASSWORD=production_admin_password
```

3. **启动生产环境：**
```bash
# 使用生产配置
docker compose --env-file .env.prod up -d --profile prod

# 或创建专门的生产compose文件
docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### 安全建议

- 使用强密码和加密连接
- 定期更新镜像和依赖
- 启用防火墙和访问控制
- 配置SSL/TLS加密
- 定期备份数据库
- 监控系统日志和性能

## 🔍 故障排除

### 常见错误及解决方案

**1. 端口冲突**
```bash
# 检查端口占用
netstat -tulpn | grep :5432
lsof -i :5432

# 修改docker-compose.yml中的端口映射
ports:
  - "15432:5432"  # 改为其他端口
```

**2. 内存不足**
```bash
# 检查内存使用
free -h
docker stats

# 调整JVM内存配置
JAVA_OPTS=-Xmx1g -Xms512m  # 减少内存使用
```

**3. 磁盘空间不足**
```bash
# 清理Docker资源
make clean
docker system prune -a

# 检查磁盘使用
df -h
docker system df
```

**4. 网络连接问题**
```bash
# 检查网络配置
docker network ls
docker network inspect einvoice-network

# 重新创建网络
docker compose down
docker compose up -d
```

### 获取支持

- 检查日志文件获取错误信息
- 参考官方文档和用户手册
- 提交Issue时请包含：
  - 错误日志
  - 环境配置
  - 复现步骤
  - 系统信息

## 📚 相关文档

- [Docker Compose官方文档](https://docs.docker.com/compose/)
- [PostgreSQL官方文档](https://www.postgresql.org/docs/)
- [财政部电子发票平台](https://www.einvoice.nat.gov.tw/)
- 电子发票Turnkey使用说明书 (见docs目录)
- MIG40电子发票交换讯息指引 (见docs目录)

---

## 📝 更新日志

### v1.0.0 (2024-12-01)
- 初始版本发布
- 完整的Docker Compose配置
- 开发环境支持
- 自动化测试套件
- 详细的文档和故障排除指南

---

**维护团队**: Yuan Hui Development Team  
**联系方式**: <EMAIL>  
**最后更新**: 2024-12-01