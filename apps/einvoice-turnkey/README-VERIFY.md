# 电子发票Turnkey镜像验证指南

## 概述

本文档说明如何验证电子发票Turnkey系统Docker镜像的正确性。

## 验证脚本

### 完整验证 (推荐)
```bash
make verify
# 或
./verify-image.sh -i yuanhui/einvoice-turnkey:latest
```

完整验证包括21项检查：
- ✅ 镜像存在性检查
- ✅ 容器创建和启动
- ✅ 目录结构验证（7项）
- ✅ 文件权限检查（4项）
- ✅ 配置文件内容验证（3项）
- ✅ Java环境检查（2项）
- ✅ 脚本功能验证（2项）
- ✅ 用户设置检查（2项）

### 快速验证
```bash
make verify-quick
```

快速验证只检查基本功能：
- 镜像是否存在
- 关键文件是否存在

## 验证项说明

### 1. 镜像存在性
检查Docker镜像是否已正确构建并存在于本地。

### 2. 目录结构验证
- `/opt/EINVTurnkey/` - 应用根目录
- `run_start.sh` - 启动脚本
- `einvUserConfig.xml` - 配置文件
- `javahome` - Java标识文件
- `lib/` - 依赖库目录
- `modules/` - 模块目录
- `so/` - 本地库目录（可选）
- `/app/sql/PostgreSQL.sql` - 数据库初始化脚本

### 3. 文件权限检查
- turnkey用户(1001)对应用目录的所有权
- 关键脚本的执行权限：
  - `run_start.sh`
  - `/app/entrypoint.sh`
  - `/app/health-check.sh`

### 4. 配置文件内容验证
- XML格式正确性
- 包含必要的配置节点：
  - `<db-type>` - 数据库类型
  - `<jdbc-url>` - 数据库连接URL

### 5. Java环境检查
- Java可执行性
- 版本信息显示（预期：OpenJDK 17.0.2）

### 6. 脚本功能验证
- `entrypoint.sh` 脚本语法正确
- `health-check.sh` 脚本语法正确

### 7. 用户设置检查
- turnkey用户存在
- 工作目录设置为 `/opt/EINVTurnkey`

## 故障排除

### 常见错误和解决方案

#### 1. 镜像不存在
```
ERROR: 镜像存在检查失败
```
**解决方案**: 先构建镜像
```bash
make build
```

#### 2. Docker daemon未运行
```
ERROR: Docker daemon未运行
```
**解决方案**: 启动Docker服务
```bash
# macOS
open -a Docker

# Linux
sudo systemctl start docker
```

#### 3. 容器启动失败
```
ERROR: 容器创建并启动失败
```
**解决方案**: 检查Docker资源和镜像完整性
```bash
docker system df
make build-no-cache
```

#### 4. 权限问题
```
ERROR: turnkey用户权限检查失败
```
**解决方案**: 检查Dockerfile中的用户设置

#### 5. 配置文件问题
```
ERROR: XML配置文件格式问题
```
**解决方案**: 检查原始配置文件是否正确复制

#### 6. Java环境问题
```
ERROR: Java环境可用性检查失败
```
**解决方案**: 检查基础镜像和Java安装

## 验证脚本选项

### 使用说明
```bash
./verify-image.sh [选项]

选项:
  -i, --image IMAGE_NAME    指定要验证的镜像名称
  -h, --help               显示帮助信息

环境变量:
  IMAGE_NAME               要验证的Docker镜像名称

示例:
  ./verify-image.sh                                   # 使用默认镜像
  ./verify-image.sh -i my-registry/turnkey:v1.0      # 指定镜像
  IMAGE_NAME=custom:tag ./verify-image.sh             # 使用环境变量
```

### 超时设置
验证脚本默认超时时间为30秒。如需调整：
```bash
# 修改 verify-image.sh 中的 VERIFICATION_TIMEOUT 变量
VERIFICATION_TIMEOUT=60  # 设置为60秒
```

## 自动化集成

### CI/CD集成
```bash
# 在CI/CD管道中使用
make ci-test
```

### 监控脚本
可以将验证脚本集成到监控系统中：
```bash
#!/bin/bash
if ./verify-image.sh -i yuanhui/einvoice-turnkey:latest; then
    echo "镜像验证通过"
    exit 0
else
    echo "镜像验证失败"
    exit 1
fi
```

## 注意事项

1. **资源清理**: 验证脚本会自动清理测试容器
2. **网络要求**: 如果验证远程镜像，需要网络连接
3. **权限要求**: 需要Docker执行权限
4. **存储空间**: 确保有足够空间创建测试容器

## 最佳实践

1. **定期验证**: 在每次镜像构建后运行完整验证
2. **CI/CD集成**: 将验证作为构建流水线的一部分
3. **监控集成**: 定期运行验证检查生产镜像
4. **日志保存**: 保存验证结果用于问题追踪