# 电子发票 Turnkey 系统 CI/CD 文档

## 概述

本文档描述了电子发票 Turnkey 系统的持续集成和持续部署（CI/CD）流程，该流程通过 GitHub Actions 自动构建 Docker 镜像并推送到 AWS ECR。

## 架构概览

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Developer     │    │   GitHub Actions │    │   AWS ECR       │    │   ECS Cluster   │
│   Push Code     │───▶│   Build Image    │───▶│   Store Image   │───▶│   Deploy App    │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └─────────────────┘
```

## GitHub Actions 工作流

### 触发条件

工作流会在以下情况自动触发：

1. **代码推送**：当推送到 `main` 或 `develop` 分支且修改了以下路径时：
   - `apps/einvoice-turnkey/**`
   - `.github/workflows/build-turnkey-image.yml`

2. **版本发布**：推送 `turnkey-v*` 格式的标签时

3. **Pull Request**：针对 `main` 分支的 PR

4. **手动触发**：支持手动选择环境（dev/prod）和自定义标签

### 工作流阶段

#### 🏗️ 阶段1：镜像构建和推送

1. **代码检出**：获取最新代码
2. **环境设置**：根据分支自动选择环境（main=prod, 其他=dev）
3. **Docker Buildx 设置**：配置多平台构建支持
4. **AWS 认证**：使用 OIDC 或密钥认证到 AWS
5. **ECR 登录**：获取 ECR 访问权限
6. **元数据提取**：生成镜像标签和标注
7. **缓存配置**：设置构建缓存策略
8. **镜像构建**：使用多阶段 Dockerfile 构建
9. **推送到 ECR**：将镜像推送到 Amazon ECR

#### 🧪 阶段2：镜像功能测试

1. **基础功能测试**：Java 运行时、应用结构
2. **证书管理测试**：证书目录和更新脚本
3. **环境配置测试**：环境变量和数据目录

#### 🔒 阶段3：安全扫描

1. **Trivy 漏洞扫描**：扫描镜像安全漏洞
2. **结果上传**：上传扫描结果到 GitHub Security
3. **安全报告**：生成安全扫描摘要

#### 📬 阶段4：部署通知

1. **成功通知**：显示镜像信息和部署状态
2. **失败通知**：标记失败阶段和原因

#### 🔧 阶段5：CDK 配置更新（仅生产环境）

1. **配置更新**：自动更新生产环境 CDK 配置
2. **提交变更**：提交配置更新到代码库

## 镜像标签策略

### 开发环境
- `dev-latest`：最新开发版本
- `dev-{sha}`：特定提交版本
- `develop-{sha}`：develop 分支版本

### 生产环境
- `latest`：最新稳定版本
- `main-latest`：主分支最新版本
- `turnkey-v{version}`：语义化版本标签

### 特殊标签
- `pr-{number}`：Pull Request 预览版本
- `{custom-tag}`：手动指定的自定义标签

## 配置要求

### GitHub 仓库设置

#### Secrets（必需）
```
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=xxx...
```

#### Variables（必需）
```
AWS_REGION=ap-east-2
```

#### Environments
- `dev`：开发环境
- `prod`：生产环境（需要审批）

### AWS ECR 仓库

ECR 仓库命名：`kh2u/yhiac-turnkey`

确保 GitHub Actions 有以下 ECR 权限：
- `ecr:GetAuthorizationToken`
- `ecr:BatchCheckLayerAvailability`
- `ecr:BatchGetImage`
- `ecr:GetDownloadUrlForLayer`
- `ecr:PutImage`
- `ecr:InitiateLayerUpload`
- `ecr:UploadLayerPart`
- `ecr:CompleteLayerUpload`

## Docker 优化

### 多阶段构建

Dockerfile 采用多阶段构建优化：

1. **下载阶段**：下载和解压 Turnkey 系统
2. **运行时阶段**：创建精简的运行时镜像

### 缓存策略

- **GitHub Actions Cache**：缓存构建层和依赖
- **ECR Registry Cache**：利用 ECR 作为缓存后端
- **Docker Layer Cache**：优化 Docker 层缓存

### 安全优化

- 使用非 root 用户运行
- 最小化系统依赖
- 定期安全扫描
- 镜像签名和验证

## CDK 集成

### 环境配置

#### 开发环境 (`lib/config/environments/dev/turnkey.ts`)
```typescript
image: {
  useCustomImage: true,
  url: '{{ECR_REGISTRY}}/kh2u/yhiac-turnkey:dev-latest'
}
```

#### 生产环境 (`lib/config/environments/prod/turnkey.ts`)
```typescript
image: {
  useCustomImage: true,
  url: '{{ECR_REGISTRY}}/kh2u/yhiac-turnkey:latest'
}
```

### 动态替换

CDK 部署时会自动替换 `{{ECR_REGISTRY}}` 占位符为实际的 ECR 注册表地址：
```
{account}.dkr.ecr.{region}.amazonaws.com
```

## 本地开发

### 构建镜像
```bash
cd apps/einvoice-turnkey/docker
docker build -t turnkey-local .
```

### 使用优化版本
```bash
docker build -f Dockerfile.optimized -t turnkey-local-optimized .
```

### 运行容器
```bash
docker run -d \
  --name turnkey-test \
  -e TURNKEY_ENV=development \
  -v turnkey-data:/data \
  turnkey-local
```

## 监控和故障排除

### 构建日志

在 GitHub Actions 页面查看详细的构建日志：
1. 转到仓库的 Actions 页面
2. 选择对应的工作流运行
3. 查看各个阶段的详细日志

### 镜像验证

验证推送到 ECR 的镜像：
```bash
# 登录 ECR
aws ecr get-login-password --region ap-east-2 | \
  docker login --username AWS --password-stdin {account}.dkr.ecr.ap-east-2.amazonaws.com

# 拉取镜像
docker pull {account}.dkr.ecr.ap-east-2.amazonaws.com/kh2u/yhiac-turnkey:latest

# 验证镜像
docker run --rm {account}.dkr.ecr.ap-east-2.amazonaws.com/kh2u/yhiac-turnkey:latest java -version
```

### 常见问题

#### 1. 认证失败
- 确认 AWS 密钥配置正确
- 检查 IAM 权限是否足够
- 验证 ECR 仓库是否存在

#### 2. 构建失败
- 检查 Dockerfile 语法
- 验证基础镜像可用性
- 确认构建上下文正确

#### 3. 推送失败
- 检查 ECR 权限
- 验证镜像大小是否超限
- 确认网络连接正常

#### 4. 安全扫描失败
- 查看 Trivy 扫描报告
- 更新基础镜像版本
- 修复已知漏洞

## 最佳实践

### 版本管理
- 使用语义化版本标签
- 为重要版本创建 GitHub Release
- 保持镜像标签的一致性

### 安全
- 定期更新基础镜像
- 监控安全扫描结果
- 使用最小权限原则

### 性能
- 优化 Dockerfile 层结构
- 利用构建缓存
- 定期清理旧镜像

### 监控
- 设置构建失败通知
- 监控镜像大小变化
- 跟踪部署频率和成功率

## 联系支持

如有问题或需要支持，请联系：
- **技术支持**：<EMAIL>
- **GitHub Issues**：在项目仓库创建 Issue
- **文档更新**：提交 Pull Request 改进文档