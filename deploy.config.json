{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Deploy Configuration", "description": "元晖Odoo应用服务部署配置文件", "type": "object", "properties": {"dev": {"type": "object", "description": "开发环境配置", "properties": {"timeout": {"type": "number", "description": "全局超时时间（秒）", "default": 1800, "minimum": 300, "maximum": 7200}, "stackTimeout": {"type": "number", "description": "单栈超时时间（秒）", "default": 600, "minimum": 180, "maximum": 3600}, "parallel": {"type": "boolean", "description": "启用并行部署", "default": false}, "logs": {"type": "boolean", "description": "显示实时部署日志", "default": true}, "autoRollback": {"type": "boolean", "description": "部署失败时自动回滚", "default": false}, "defaultGroup": {"type": "string", "description": "默认栈组", "enum": ["core", "apps", "all"], "default": "all"}, "retryEnabled": {"type": "boolean", "description": "启用智能重试", "default": true}, "maxRetries": {"type": "number", "description": "最大重试次数", "default": 2, "minimum": 0, "maximum": 5}}}, "prod": {"type": "object", "description": "生产环境配置", "properties": {"timeout": {"type": "number", "description": "全局超时时间（秒）", "default": 3600, "minimum": 600, "maximum": 10800}, "stackTimeout": {"type": "number", "description": "单栈超时时间（秒）", "default": 900, "minimum": 300, "maximum": 3600}, "parallel": {"type": "boolean", "description": "启用并行部署", "default": true}, "logs": {"type": "boolean", "description": "显示实时部署日志", "default": false}, "autoRollback": {"type": "boolean", "description": "部署失败时自动回滚", "default": true}, "defaultGroup": {"type": "string", "description": "默认栈组", "enum": ["core", "apps", "all"], "default": "all"}, "retryEnabled": {"type": "boolean", "description": "启用智能重试", "default": true}, "maxRetries": {"type": "number", "description": "最大重试次数", "default": 2, "minimum": 0, "maximum": 3}, "requireConfirmation": {"type": "boolean", "description": "生产环境需要确认", "default": true}}}}, "examples": [{"dev": {"timeout": 2400, "parallel": true, "logs": true, "autoRollback": false, "defaultGroup": "core"}, "prod": {"timeout": 3600, "parallel": true, "logs": false, "autoRollback": true, "defaultGroup": "all", "requireConfirmation": true}}]}