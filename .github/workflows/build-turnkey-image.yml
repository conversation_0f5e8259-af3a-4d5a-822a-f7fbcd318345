name: 🚀 Build Turnkey E-Invoice Docker Image

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/einvoice-turnkey/**'
      - '.github/workflows/build-turnkey-image.yml'
    tags: [ 'turnkey-v*' ]
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/einvoice-turnkey/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        type: choice
        options:
          - dev
          - prod
        default: 'dev'
      tag:
        description: 'Custom image tag'
        required: false
        default: ''
      force_rebuild:
        description: 'Force rebuild without cache'
        required: false
        type: boolean
        default: false

env:
  ECR_REPOSITORY: kh2u/yhiac-turnkey
  BUILD_PLATFORMS: linux/arm64

jobs:
  # =============================================================================
  # 阶段1: 镜像构建和推送
  # =============================================================================
  build-image:
    name: 🏗️ Build Turnkey Docker Image
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'prod' || 'dev') }}
    permissions:
      contents: read
      packages: write
      id-token: write
      
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tags: ${{ steps.meta.outputs.tags }}
      registry-url: ${{ steps.login-ecr.outputs.registry }}
      test-tag: ${{ steps.extract-test-tag.outputs.tag }}
      environment: ${{ steps.set-env.outputs.environment }}
        
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        
    - name: 🏷️ Set environment
      id: set-env
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
        elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
          echo "environment=prod" >> $GITHUB_OUTPUT
        else
          echo "environment=dev" >> $GITHUB_OUTPUT
        fi
        
    - name: 🐳 Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      with:
        platforms: ${{ env.BUILD_PLATFORMS }}
        driver-opts: |
          image=moby/buildkit:buildx-stable-1
          network=host
        
    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ vars.AWS_REGION }}
        
    - name: 🔑 Log in to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
        
    - name: 📝 Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}
        tags: |
          type=ref,event=branch,suffix=-{{sha}}
          type=ref,event=branch
          type=ref,event=pr,prefix=pr-
          type=ref,event=tag
          type=semver,pattern={{version}},prefix=turnkey-v
          type=semver,pattern={{major}}.{{minor}},prefix=turnkey-v
          type=semver,pattern={{major}},prefix=turnkey-v
          type=raw,value=latest,enable={{is_default_branch}}
          type=raw,value={{branch}}-latest,enable=${{ github.event_name != 'pull_request' }}
          type=raw,value=${{ github.event.inputs.tag }},enable=${{ github.event.inputs.tag != '' }}
          type=raw,value=${{ steps.set-env.outputs.environment }}-{{sha}},enable=true
        labels: |
          org.opencontainers.image.title=Taiwan E-Invoice Turnkey System
          org.opencontainers.image.description=Taiwan Electronic Invoice Turnkey System for enterprise integration
          org.opencontainers.image.vendor=YuanHui Team
          org.opencontainers.image.url=https://github.com/${{ github.repository }}
          org.opencontainers.image.source=https://github.com/${{ github.repository }}.git
          turnkey.version=3.2.0
          turnkey.environment=${{ steps.set-env.outputs.environment }}
          turnkey.java.version=17
        annotations: |
          org.opencontainers.image.title=Taiwan E-Invoice Turnkey System
          org.opencontainers.image.description=Taiwan Electronic Invoice Turnkey System for enterprise integration
          
    - name: 🧮 Setup cache configuration
      id: cache-config
      run: |
        if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
          echo "cache-from=" >> $GITHUB_OUTPUT
          echo "Using no cache (force rebuild)"
        else
          cache_from="type=gha,scope=turnkey-${{ steps.set-env.outputs.environment }}"
          cache_from="${cache_from};type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:cache-${{ steps.set-env.outputs.environment }}"
          echo "cache-from=${cache_from}" >> $GITHUB_OUTPUT
          echo "Using cache: ${cache_from}"
        fi
        
        cache_to="type=gha,mode=max,scope=turnkey-${{ steps.set-env.outputs.environment }}"
        cache_to="${cache_to};type=registry,ref=${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}:cache-${{ steps.set-env.outputs.environment }},mode=max"
        echo "cache-to=${cache_to}" >> $GITHUB_OUTPUT
        
    - name: 🔨 Build and push image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: apps/einvoice-turnkey/docker
        file: apps/einvoice-turnkey/docker/Dockerfile
        platforms: ${{ env.BUILD_PLATFORMS }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        annotations: ${{ steps.meta.outputs.annotations }}
        cache-from: ${{ steps.cache-config.outputs.cache-from }}
        cache-to: ${{ steps.cache-config.outputs.cache-to }}
        build-args: |
          TURNKEY_VERSION=3.2.0
          BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
          VCS_REF=${{ github.sha }}
          ENVIRONMENT=${{ steps.set-env.outputs.environment }}
          
    - name: 🏷️ Extract test tag
      id: extract-test-tag
      run: |
        # 提取环境特定的标签用于测试
        echo '${{ steps.meta.outputs.tags }}' | grep '${{ steps.set-env.outputs.environment }}-' | head -1 | sed 's/.*://' > test_tag.txt
        test_tag=$(cat test_tag.txt)
        if [ -z "$test_tag" ]; then
          # 如果没有环境特定标签，使用第一个标签
          test_tag=$(echo '${{ steps.meta.outputs.tags }}' | head -1 | sed 's/.*://')
        fi
        echo "tag=${test_tag}" >> $GITHUB_OUTPUT
        echo "Extracted test tag: ${test_tag}"
          
    - name: 📊 Build summary
      run: |
        echo "=== Turnkey Docker Build Summary ===" >> $GITHUB_STEP_SUMMARY
        echo "**Environment**: ${{ steps.set-env.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
        echo "**Platform**: ${{ env.BUILD_PLATFORMS }}" >> $GITHUB_STEP_SUMMARY
        echo "**Image**: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}" >> $GITHUB_STEP_SUMMARY
        echo "**Digest**: ${{ steps.build.outputs.digest }}" >> $GITHUB_STEP_SUMMARY
        echo "**Test Tag**: ${{ steps.extract-test-tag.outputs.tag }}" >> $GITHUB_STEP_SUMMARY
        
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### 🏷️ Image Tags" >> $GITHUB_STEP_SUMMARY
        echo '${{ steps.meta.outputs.tags }}' | while read tag; do
          echo "- \`$tag\`" >> $GITHUB_STEP_SUMMARY
        done


  # =============================================================================
  # 阶段2: 安全扫描
  # =============================================================================
  security-scan:
    name: 🔒 Security Scan
    needs: build-image
    runs-on: ubuntu-latest
    environment: ${{ needs.build-image.outputs.environment }}
    
    steps:
    - name: 🔧 Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ vars.AWS_REGION }}
        
    - name: 🔑 Log in to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2
        
    - name: 🔍 Run Trivy vulnerability scanner
      env:
        IMAGE_URL: ${{ needs.build-image.outputs.registry-url }}/${{ env.ECR_REPOSITORY }}
        IMAGE_TAG: ${{ needs.build-image.outputs.test-tag }}
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.IMAGE_URL }}:${{ env.IMAGE_TAG }}
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: 📊 Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      continue-on-error: true
      with:
        sarif_file: 'trivy-results.sarif'
        
    - name: 📋 Display security scan summary
      if: always()
      run: |
        echo "=== Security Scan Results ===" >> $GITHUB_STEP_SUMMARY
        if [ -f "trivy-results.sarif" ]; then
          echo "✅ Security scan completed successfully" >> $GITHUB_STEP_SUMMARY
          if command -v jq >/dev/null 2>&1; then
            vulnerabilities=$(jq -r '.runs[0].results | length' trivy-results.sarif 2>/dev/null || echo "unknown")
            echo "**Vulnerabilities found**: $vulnerabilities" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "❌ Security scan results not found" >> $GITHUB_STEP_SUMMARY
        fi
        
    - name: 📦 Upload security scan results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: turnkey-security-scan-${{ needs.build-image.outputs.environment }}
        path: trivy-results.sarif
        retention-days: 30

  # =============================================================================
  # 阶段3: 部署通知
  # =============================================================================
  notify-deployment:
    name: 📬 Deployment Notification
    needs: [build-image, security-scan]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: 📬 Notify success
      if: needs.build-image.result == 'success' && needs.security-scan.result == 'success'
      run: |
        echo "✅ Turnkey image built and scanned successfully!" >> $GITHUB_STEP_SUMMARY
        echo "**Environment**: ${{ needs.build-image.outputs.environment }}" >> $GITHUB_STEP_SUMMARY
        echo "**Image**: ${{ needs.build-image.outputs.registry-url }}/${{ env.ECR_REPOSITORY }}:${{ needs.build-image.outputs.test-tag }}" >> $GITHUB_STEP_SUMMARY
        echo "**Digest**: ${{ needs.build-image.outputs.image-digest }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "🚀 Ready for deployment to ${{ needs.build-image.outputs.environment }} environment" >> $GITHUB_STEP_SUMMARY
        
    - name: 📬 Notify failure
      if: needs.build-image.result == 'failure' || needs.security-scan.result == 'failure'
      run: |
        echo "❌ Turnkey build or security scan failed!" >> $GITHUB_STEP_SUMMARY
        echo "**Build**: ${{ needs.build-image.result }}" >> $GITHUB_STEP_SUMMARY
        echo "**Security**: ${{ needs.security-scan.result }}" >> $GITHUB_STEP_SUMMARY
        exit 1

  # =============================================================================
  # 阶段4: CDK 部署更新（仅生产环境）
  # =============================================================================
  update-cdk-config:
    name: 🔧 Update CDK Configuration
    needs: [build-image, security-scan]
    runs-on: ubuntu-latest
    if: needs.build-image.outputs.environment == 'prod' && github.ref == 'refs/heads/main' && success()
    environment: prod
    
    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 🔧 Update Turnkey configuration
      env:
        IMAGE_URL: ${{ needs.build-image.outputs.registry-url }}/${{ env.ECR_REPOSITORY }}
        IMAGE_TAG: latest
      run: |
        # 更新生产环境配置文件
        sed -i 's|useCustomImage: true.*|useCustomImage: true,|' lib/config/environments/prod/turnkey.ts
        sed -i "s|// url:.*|url: '${IMAGE_URL}:${IMAGE_TAG}',|" lib/config/environments/prod/turnkey.ts || \
        sed -i "/useCustomImage: true,/a\\    url: '${IMAGE_URL}:${IMAGE_TAG}'," lib/config/environments/prod/turnkey.ts
        
        echo "Updated production configuration with image: ${IMAGE_URL}:${IMAGE_TAG}"
        
    - name: 📝 Commit configuration update
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add lib/config/environments/prod/turnkey.ts
        git diff --staged --quiet || git commit -m "chore: update turnkey production image to latest build

        Image: ${{ needs.build-image.outputs.registry-url }}/${{ env.ECR_REPOSITORY }}:latest
        Digest: ${{ needs.build-image.outputs.image-digest }}
        Build: ${{ github.sha }}
        
        🤖 Generated with [GitHub Actions](https://github.com/${{ github.repository }}/actions)"
        git push origin main