#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import { getConfig, EnvironmentConfig } from '../lib/config';
import { NetworkStack } from '../lib/stacks/network-stack';
// import { DatabaseStack } from '../lib/stacks/database-stack'; // 已替换为Aurora
import { AuroraDatabaseStack } from '../lib/stacks/aurora-database-stack';
import { EcsStack } from '../lib/stacks/ecs-stack';
import { ServiceConnectStack } from '../lib/stacks/service-connect-stack';
import { LoadBalancerStack } from '../lib/stacks/load-balancer-stack';
import { RabbitMQStack } from '../lib/stacks/rabbitmq-stack';
import { AirflowStack } from '../lib/stacks/airflow-stack';
// import { DnsStack } from '../lib/stacks/dns-stack'; // DNS由第三方管理
// import { ApplicationStack } from '../lib/stacks/application-stack'; // 已移除，后续重新设计
import { ClaudeRelayStack } from '../lib/stacks/claude-relay-stack';
import { WikiJSStack } from '../lib/stacks/wikijs-stack';
import { CloudFrontStack } from '../lib/stacks/cloudfront-stack';
import { OpenZitiStack } from '../lib/stacks/openziti-stack';
import { MonitoringStack } from '../lib/stacks/monitoring-stack';
import { SecurityStack } from '../lib/stacks/security-stack';
import { SwarmStack } from '../lib/stacks/swarm-stack';
import { TurnkeyStack } from '../lib/stacks/turnkey-stack';

const app = new cdk.App();
const config = getConfig();

// 环境配置
const env = {
  account: config.account,
  region: config.region,
};

// 网络基础设施栈
const networkStack = new NetworkStack(app, `YuanhuiNetwork-${config.environment}`, {
  env,
  config,
});

// ECS集群栈
const ecsStack = new EcsStack(app, `YuanhuiEcs-${config.environment}`, {
  env,
  config,
  vpc: networkStack.vpc,
  ecsSecurityGroup: networkStack.ecsSecurityGroup
});

// Service Connect命名空间栈
const serviceConnectStack = new ServiceConnectStack(app, `YuanhuiServiceConnect-${config.environment}`, {
  env,
  config,
});

// 负载均衡器栈现在在应用栈之前创建，只依赖于网络栈
// 负载均衡器栈（现在在应用栈之前创建，只依赖网络栈）
const loadBalancerStack = new LoadBalancerStack(app, `YuanhuiLoadBalancer-${config.environment}`, {
  env,
  config,
  vpc: networkStack.vpc,
  webAcl: networkStack.webAcl,
  albSecurityGroup: networkStack.albSecurityGroup,
});

// Aurora数据库栈（替换原有的容器化PostgreSQL）
const auroraDatabaseStack = new AuroraDatabaseStack(app, `YuanhuiAuroraDatabase-${config.environment}`, {
  env,
  config,
  vpc: networkStack.vpc,
});

// 数据库栈只依赖网络栈
auroraDatabaseStack.addDependency(networkStack);


// RabbitMQ栈（现在支持主ALB集成和EFS存储）
const rabbitmqStack = new RabbitMQStack(app, `YuanhuiRabbitMQ-${config.environment}`, {
  env,
  config,
  vpc: networkStack.vpc,
  cluster: ecsStack.cluster,
  serviceConnectNamespace: serviceConnectStack.namespace,
  loadBalancerStack: config.rabbitmq.enabled ? loadBalancerStack : undefined, // 添加LoadBalancerStack依赖
  sharedEfsFileSystem: ecsStack.sharedEfsFileSystem,  // 使用ECS栈的共享EFS文件系统
});

// Airflow栈（现在依赖LoadBalancerStack和ECS共享EFS）
const airflowStack = new AirflowStack(app, `YuanhuiAirflow-${config.environment}`, {
  env,
  config,
  vpc: networkStack.vpc,
  cluster: ecsStack.cluster,
  serviceConnectNamespace: serviceConnectStack.namespace,
  databaseEndpoint: auroraDatabaseStack.clusterEndpoint,
  airflowDatabaseSecret: auroraDatabaseStack.airflowDatabaseSecret,  // 使用Airflow专用数据库凭证
  rabbitmqEndpoint: rabbitmqStack.rabbitmqEndpoint,
  rabbitmqSecret: rabbitmqStack.rabbitmqSecret,
  loadBalancerStack: config.airflow.enabled ? loadBalancerStack : undefined, // 添加LoadBalancerStack依赖
  sharedEfsFileSystem: ecsStack.sharedEfsFileSystem,  // 使用ECS栈的共享EFS文件系统
});

// 确保Airflow栈在数据库栈和ECS栈之后创建
airflowStack.addDependency(auroraDatabaseStack);
airflowStack.addDependency(ecsStack);  // 添加对ECS栈的依赖，因为需要使用共享EFS

// 注释掉原有数据库栈，已替换为Aurora
// const databaseStack = new DatabaseStack(app, `YuanhuiDatabase-${config.environment}`, {
//   env,
//   config,
//   vpc: networkStack.vpc,
//   cluster: ecsStack.cluster,
// });

// DNS和SSL证书栈 - 由第三方管理，不需要创建
// const dnsStack = new DnsStack(app, `YuanhuiDns-${config.environment}`, {
//   env,
//   config,
// });

// ApplicationStack已移除，后续会重新设计统一的应用服务管理

// Claude Relay 中转服务栈（仅在启用时创建，现在使用内置Redis和ECS共享EFS）
let claudeRelayStack: ClaudeRelayStack | undefined;
if (config.claudeRelay.enabled) {
  claudeRelayStack = new ClaudeRelayStack(app, `YuanhuiClaudeRelay-${config.environment}`, {
    env,
    config,
    vpc: networkStack.vpc,
    cluster: ecsStack.cluster,
    loadBalancerStack: loadBalancerStack, // 添加LoadBalancerStack依赖
    ecsStack: ecsStack, // 使用ECS栈获取共享EFS
    serviceConnectNamespace: serviceConnectStack.namespace, // 添加Service Connect支持
    ecsSecurityGroup: networkStack.ecsSecurityGroup, // 使用network-stack的ECS安全组
  });
}

// WikiJS栈（如果启用）
let wikijsStack: WikiJSStack | undefined;
if (config.wikijs?.enabled) {
  wikijsStack = new WikiJSStack(app, `YuanhuiWikiJS-${config.environment}`, {
    env,
    config,
    vpc: networkStack.vpc,
    cluster: ecsStack.cluster,
    loadBalancerStack: loadBalancerStack,
    serviceConnectNamespace: serviceConnectStack.namespace,
    ecsSecurityGroup: networkStack.ecsSecurityGroup,
  });
}

// CloudFront CDN栈（仅在启用时创建）
let cloudFrontStack: CloudFrontStack | undefined;
if (config.cloudfront.enabled) {
  cloudFrontStack = new CloudFrontStack(app, `YuanhuiCloudFront-${config.environment}`, {
    env,
    config,
    loadBalancer: loadBalancerStack.publicLoadBalancer,
    // certificate: dnsStack.khmallCertificate, // 证书由第三方管理
    // hostedZone: dnsStack.hostedZone, // DNS由第三方管理
    webAcl: networkStack.webAcl,
  });
}

// OpenZiti零信任网络栈（仅在启用时创建）
let openZitiStack: OpenZitiStack | undefined;
if (config.network.openziti.enabled) {
  openZitiStack = new OpenZitiStack(app, `YuanhuiOpenZiti-${config.environment}`, {
    env,
    config,
    vpc: networkStack.vpc,
    cluster: ecsStack.cluster,
    // hostedZone: dnsStack.hostedZone, // DNS由第三方管理
    databaseEndpoint: auroraDatabaseStack.clusterEndpoint,
    databaseSecret: auroraDatabaseStack.databaseSecret,
  });
}

// 安全栈 - 管理GitHub Actions CI/CD权限和安全资产存储
const securityStack = new SecurityStack(app, `YuanhuiSecurity-${config.environment}`, {
  env,
  config,
});

// Turnkey电子发票系统栈（仅在启用时创建）
let turnkeyStack: TurnkeyStack | undefined;
if (config.turnkey.enabled) {
  turnkeyStack = new TurnkeyStack(app, `YuanhuiTurnkey-${config.environment}`, {
    env,
    config,
    vpc: networkStack.vpc,
    cluster: ecsStack.cluster,
    serviceConnectNamespace: serviceConnectStack.namespace,
    sharedEfsFileSystem: ecsStack.sharedEfsFileSystem,
    secureAssetsBucket: securityStack.secureAssetsBucket,
  });
}

// Docker Swarm 集群栈（仅在启用时创建）
let swarmStack: SwarmStack | undefined;
if (config.swarm.enabled) {
  swarmStack = new SwarmStack(app, `YuanhuiSwarm-${config.environment}`, {
    env,
    config,
    vpc: networkStack.vpc,
  });
}

// 监控栈
const monitoringStack = new MonitoringStack(app, `YuanhuiMonitoring-${config.environment}`, {
  env,
  config,
  // applicationStack, // 已移除
  loadBalancerStack,
  databaseStack: auroraDatabaseStack, // 使用新的Aurora数据库栈
  rabbitmqStack,
  airflowStack,
});

// 添加栈依赖关系
ecsStack.addDependency(networkStack);
// serviceConnectStack.addDependency(ecsStack);
auroraDatabaseStack.addDependency(networkStack);
rabbitmqStack.addDependency(serviceConnectStack);
rabbitmqStack.addDependency(ecsStack); // 添加对ECS栈的依赖，因为需要使用共享EFS
if (config.rabbitmq.enabled) {
  rabbitmqStack.addDependency(loadBalancerStack); // 添加LoadBalancerStack依赖
}
airflowStack.addDependency(auroraDatabaseStack);
airflowStack.addDependency(rabbitmqStack);
if (config.airflow.enabled) {
  airflowStack.addDependency(loadBalancerStack); // 添加LoadBalancerStack依赖
}
// applicationStack 依赖关系已移除

// Claude Relay栈依赖关系（现在使用内置Redis，不依赖独立Redis栈）
if (claudeRelayStack) {
  claudeRelayStack.addDependency(ecsStack); // 依赖ECS栈获取共享EFS
  claudeRelayStack.addDependency(serviceConnectStack); // 添加Service Connect依赖
  claudeRelayStack.addDependency(loadBalancerStack); // 添加LoadBalancerStack依赖
}

// WikiJS栈依赖关系
if (wikijsStack) {
  wikijsStack.addDependency(networkStack);
  wikijsStack.addDependency(ecsStack);
  wikijsStack.addDependency(serviceConnectStack);
  wikijsStack.addDependency(loadBalancerStack);
}

// LoadBalancer栈现在只依赖网络栈
loadBalancerStack.addDependency(networkStack); // 需要VPC和WebACL
// applicationStack.addDependency(dnsStack); // DNS由第三方管理

// CloudFront依赖关系
if (cloudFrontStack) {
  // cloudFrontStack.addDependency(applicationStack); // ApplicationStack已移除
  // cloudFrontStack.addDependency(dnsStack); // DNS由第三方管理
}

// OpenZiti依赖关系
if (openZitiStack) {
  openZitiStack.addDependency(ecsStack);
  openZitiStack.addDependency(auroraDatabaseStack);
  // openZitiStack.addDependency(dnsStack); // DNS由第三方管理
}

// Turnkey栈依赖关系（移除数据库依赖，通过配置管理数据库连接）
if (turnkeyStack) {
  turnkeyStack.addDependency(networkStack);
  turnkeyStack.addDependency(ecsStack);
  turnkeyStack.addDependency(serviceConnectStack);
  if (config.turnkey.routing.enabled) {
    turnkeyStack.addDependency(loadBalancerStack);
  }
}

// Swarm栈依赖关系
if (swarmStack) {
  swarmStack.addDependency(networkStack);
}

// 监控栈依赖关系
// monitoringStack.addDependency(applicationStack); // ApplicationStack已移除
monitoringStack.addDependency(loadBalancerStack); // 添加LoadBalancerStack依赖
monitoringStack.addDependency(rabbitmqStack);
monitoringStack.addDependency(airflowStack);
monitoringStack.addDependency(securityStack);
if (claudeRelayStack) {
  monitoringStack.addDependency(claudeRelayStack);
}
if (wikijsStack) {
  monitoringStack.addDependency(wikijsStack);
}
if (cloudFrontStack) {
  monitoringStack.addDependency(cloudFrontStack);
}
if (openZitiStack) {
  monitoringStack.addDependency(openZitiStack);
}
if (turnkeyStack) {
  monitoringStack.addDependency(turnkeyStack);
}
if (swarmStack) {
  monitoringStack.addDependency(swarmStack);
}

// DNS记录将在Application栈中创建，避免循环依赖