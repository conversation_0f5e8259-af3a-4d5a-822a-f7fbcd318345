/**
 * 默认配置值
 * 提取公共默认值，避免重复
 */

export const defaultResourceLimits = {
  dev: {
    small: { cpu: 64, memory: 128 },
    medium: { cpu: 128, memory: 256 },
    large: { cpu: 256, memory: 512 },
  },
  prod: {
    small: { cpu: 256, memory: 512 },
    medium: { cpu: 512, memory: 1024 },
    large: { cpu: 1024, memory: 2048 },
    xlarge: { cpu: 2048, memory: 4096 },
  },
};

export const defaultPorts = {
  http: 80,
  https: 443,
  airflow: 8080,
  rabbitmq: 5672,
  rabbitmqManagement: 15672,
  redis: 6379,
  postgres: 5432,
  claudeRelay: 3000,
  wikijs: 3000,
  ssh: 22,
  swarmManagement: 2377,
  swarmCommunication: 7946,
  swarmOverlay: 4789,
};

export const defaultRetentionDays = {
  dev: {
    logs: 1,
    monitoring: 1,
    backup: 3,
    efsBackup: 3,
  },
  prod: {
    logs: 7,
    monitoring: 30,
    backup: 14,
    efsBackup: 14,
  },
};

export const defaultStorageSizes = {
  dev: {
    small: 5,
    medium: 10,
    large: 20,
  },
  prod: {
    small: 20,
    medium: 50,
    large: 100,
  },
};

export const defaultDatabaseSettings = {
  dev: {
    engine: 'rds-postgresql' as const,
    rdsInstanceConfig: {
      instanceClass: 'db.t4g.micro',
      allocatedStorage: 20,
      multiAz: false,
    },
    backupRetention: 3,
    deletionProtection: false,
    enablePerformanceInsights: false,
  },
  prod: {
    engine: 'aurora-postgresql' as const,
    serverlessV2Scaling: {
      minCapacity: 0,
      maxCapacity: 8,
    },
    backupRetention: 14,
    deletionProtection: true,
    enablePerformanceInsights: true,
  },
};

export const defaultVpcSettings = {
  dev: {
    cidr: '10.0.0.0/16',
    maxAzs: 2,
    natGateways: 1,
  },
  prod: {
    cidr: '172.32.0.0/16',
    maxAzs: 3,
    natGateways: 1,
  },
};

export const defaultSecuritySettings = {
  dev: {
    enableWaf: false,
    enableOpenziti: false,
    enableDetailedMonitoring: false,
  },
  prod: {
    enableWaf: false, // 当前生产环境也关闭
    enableOpenziti: true,
    enableDetailedMonitoring: true,
  },
};

export const defaultEfsBackupSettings = {
  dev: {
    enabled: true,
    dailyBackupRetention: 3, // 天数
    weeklyBackupRetention: 0, // 开发环境不保留周备份
    moveToColdStorageAfterDays: 1, // 1天后转冷存储（低成本）
    enableContinuousBackup: false, // 关闭连续备份降低成本
    backupSchedule: {
      daily: { hour: 2, minute: 0 }, // 凌晨2点
      weekly: null, // 开发环境不执行周备份
    },
  },
  prod: {
    enabled: true,
    dailyBackupRetention: 14, // 天数
    weeklyBackupRetention: 90, // 周备份保留3个月
    moveToColdStorageAfterDays: 30, // 30天后转冷存储
    enableContinuousBackup: false, // 关闭连续备份降低成本
    backupSchedule: {
      daily: { hour: 2, minute: 0 }, // 凌晨2点
      weekly: { weekDay: 1, hour: 3, minute: 0 }, // 每周一凌晨3点
    },
  },
};