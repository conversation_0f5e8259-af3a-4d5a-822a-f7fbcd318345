/**
 * 环境配置类型定义
 */

import { BaseConfig } from '../base';
import {
  NetworkConfig,
  DatabaseConfig,
  EcsConfig,
  RabbitMQConfig,
  AirflowConfig,
  CloudFrontConfig,
  MonitoringConfig,
  SwarmConfig,
  LoadBalancerConfig,
  ClaudeRelayConfig,
  WikiJSConfig,
  BackupConfig,
  TurnkeyConfig,
} from '../stacks';

export interface EnvironmentConfig extends BaseConfig {
  network: NetworkConfig;
  database: DatabaseConfig;
  ecs: EcsConfig;
  rabbitmq: RabbitMQConfig;
  airflow: AirflowConfig;
  cloudfront: CloudFrontConfig;
  monitoring: MonitoringConfig;
  swarm: SwarmConfig;
  loadBalancer: LoadBalancerConfig;
  claudeRelay: ClaudeRelayConfig;
  wikijs: WikiJSConfig;
  backup: BackupConfig;
  turnkey: TurnkeyConfig;
}

export type Environment = 'dev' | 'prod';

export interface ResourceLimits {
  cpu: number;
  memory: number;
}

export interface StorageConfig {
  size: number;
  type?: 'gp2' | 'gp3';
  encrypted?: boolean;
}

export interface DomainConfig {
  zoneName: string;
  hostedZoneId?: string;
}

export interface ApplicationDomains {
  yherp: {
    internal: string;
    public: string;
  };
  khmall: {
    domain: string;
  };
}

export interface HealthCheckConfig {
  healthyHttpCodes: string;
  interval: number;
  timeout: number;
  healthyThresholdCount: number;
  unhealthyThresholdCount: number;
}

export interface LoggingConfig {
  retentionDays: number;
  logGroupName: string;
}

export interface MonitoringConfigBase {
  enableDetailedMonitoring: boolean;
  enableXRayTracing?: boolean;
}