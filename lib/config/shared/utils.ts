/**
 * 配置工具函数
 */

import { Environment, ResourceLimits } from './types';
import { 
  defaultResourceLimits, 
  defaultRetentionDays, 
  defaultStorageSizes,
  defaultDatabaseSettings,
  defaultVpcSettings,
  defaultSecuritySettings
} from './defaults';

/**
 * 根据环境获取资源配置
 */
export function getResourceByEnv(env: Environment, size: 'small' | 'medium' | 'large' | 'xlarge'): ResourceLimits {
  const envConfig = defaultResourceLimits[env];
  
  if (env === 'prod') {
    const prodConfig = envConfig as typeof defaultResourceLimits.prod;
    if (size === 'xlarge') {
      return prodConfig.xlarge;
    }
    return prodConfig[size as keyof typeof prodConfig] || prodConfig.medium;
  } else {
    const devConfig = envConfig as typeof defaultResourceLimits.dev;
    const validSize = size === 'xlarge' ? 'large' : size; // dev环境没有xlarge，用large替代
    return devConfig[validSize as keyof typeof devConfig] || devConfig.medium;
  }
}

/**
 * 根据环境获取日志保留天数
 */
export function getRetentionDaysByEnv(env: Environment, type: 'logs' | 'monitoring' | 'backup'): number {
  return defaultRetentionDays[env][type];
}

/**
 * 根据环境获取存储大小
 */
export function getStorageSizeByEnv(env: Environment, size: 'small' | 'medium' | 'large'): number {
  return defaultStorageSizes[env][size];
}

/**
 * 生成域名
 */
export function generateDomain(service: string, env: Environment, baseDomain: string): string {
  if (env === 'prod') {
    return `${service}.${baseDomain}`;
  }
  return `${service}-${env}.${baseDomain}`;
}

/**
 * 生成应用域名配置
 */
export function generateApplicationDomains(env: Environment) {
  const baseDomain = 'kh2u.com';
  const ecommerceDomain = env === 'prod' ? 'j2mall.com' : 'j2mall.tw';
  
  return {
    yherp: {
      internal: env === 'prod' ? `yh.${baseDomain}` : `yh-${env}.${baseDomain}`,
      public: env === 'prod' ? `dp.${baseDomain}` : `dp-${env}.${baseDomain}`,
    },
    khmall: {
      domain: ecommerceDomain,
    },
  };
}

/**
 * 根据环境获取数据库配置
 */
export function getDatabaseConfigByEnv(env: Environment) {
  return defaultDatabaseSettings[env];
}

/**
 * 检查是否为开发环境数据库配置
 */
export function isDevDatabaseConfig(config: any): config is typeof defaultDatabaseSettings.dev {
  return config.engine === 'rds-postgresql';
}

/**
 * 检查是否为生产环境数据库配置
 */
export function isProdDatabaseConfig(config: any): config is typeof defaultDatabaseSettings.prod {
  return config.engine === 'aurora-postgresql';
}

/**
 * 根据环境获取VPC配置
 */
export function getVpcConfigByEnv(env: Environment) {
  return defaultVpcSettings[env];
}

/**
 * 根据环境获取安全配置
 */
export function getSecuritySettingsByEnv(env: Environment) {
  return defaultSecuritySettings[env];
}

/**
 * 合并配置对象
 */
export function mergeConfigs<T extends Record<string, any>>(base: T, override: Partial<T>): T {
  return { ...base, ...override };
}

/**
 * 生成资源名称
 */
export function generateResourceName(prefix: string, env: Environment, suffix?: string): string {
  const parts = [prefix, env];
  if (suffix) {
    parts.push(suffix);
  }
  return parts.join('-');
}

/**
 * 检查环境是否为生产环境
 */
export function isProduction(env: Environment): boolean {
  return env === 'prod';
}

/**
 * 检查环境是否为开发环境
 */
export function isDevelopment(env: Environment): boolean {
  return env === 'dev';
}