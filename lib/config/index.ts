/**
 * 配置文件主入口
 * 统一导出所有配置类型和环境配置
 */

export * from './base';
export * from './stacks';
export * from './shared';

// 重新导出环境配置以保持向后兼容
export * from './environments/dev';
export * from './environments/prod';

// 环境配置联合类型
export type { EnvironmentConfig } from './shared/types';

// 根据环境变量获取配置
export function getConfig() {
  const env = process.env.NODE_ENV || 'dev';
  
  switch (env) {
    case 'prod':
    case 'production':
      return require('./environments/prod').prodConfig;
    case 'dev':
    case 'development':
    default:
      return require('./environments/dev').devConfig;
  }
}