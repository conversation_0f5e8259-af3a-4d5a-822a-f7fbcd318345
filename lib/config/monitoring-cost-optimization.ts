/**
 * CloudWatch监控成本优化配置
 * 
 * 此文件包含了针对不同环境的监控成本优化策略
 */

export interface MonitoringCostConfig {
  // Container Insights配置
  containerInsights: {
    enabled: boolean;
    mode: 'DISABLED' | 'ENABLED' | 'ENHANCED';
    // 成本估算（月度USD）
    estimatedMonthlyCost: number;
  };
  
  // 自定义指标配置
  customMetrics: {
    enabled: boolean;
    // 每个指标的月度成本约$0.30
    maxMetricsCount: number;
    estimatedMonthlyCost: number;
  };
  
  // 日志保留策略
  logRetention: {
    // 应用日志保留天数
    applicationLogs: number;
    // 系统日志保留天数
    systemLogs: number;
    // 访问日志保留天数
    accessLogs: number;
  };
  
  // 告警配置
  alarms: {
    // 基础告警（免费）
    basicAlarms: boolean;
    // 复合告警（收费）
    compositeAlarms: boolean;
    // 告警评估频率（影响成本）
    evaluationFrequency: 'low' | 'medium' | 'high';
  };
}

// 开发环境 - 成本优化配置
export const devMonitoringConfig: MonitoringCostConfig = {
  containerInsights: {
    enabled: false, // 关闭以节省成本
    mode: 'DISABLED',
    estimatedMonthlyCost: 0,
  },
  
  customMetrics: {
    enabled: false, // 开发环境不需要自定义指标
    maxMetricsCount: 0,
    estimatedMonthlyCost: 0,
  },
  
  logRetention: {
    applicationLogs: 3, // 3天足够开发调试
    systemLogs: 1,     // 1天
    accessLogs: 1,     // 1天
  },
  
  alarms: {
    basicAlarms: true,     // 保留基础告警
    compositeAlarms: false, // 关闭复合告警
    evaluationFrequency: 'low',
  },
};

// 生产环境 - 平衡配置
export const prodMonitoringConfig: MonitoringCostConfig = {
  containerInsights: {
    enabled: true,
    mode: 'ENABLED', // 使用标准模式而非Enhanced
    estimatedMonthlyCost: 15, // 约$15/月
  },
  
  customMetrics: {
    enabled: true,
    maxMetricsCount: 10, // 限制自定义指标数量
    estimatedMonthlyCost: 3, // 约$3/月
  },
  
  logRetention: {
    applicationLogs: 30, // 30天用于问题追踪
    systemLogs: 14,     // 14天
    accessLogs: 7,      // 7天
  },
  
  alarms: {
    basicAlarms: true,
    compositeAlarms: true, // 生产环境启用复合告警
    evaluationFrequency: 'medium',
  },
};

// 成本优化建议
export const costOptimizationTips = {
  immediate: [
    '关闭开发环境的Container Insights Enhanced模式',
    '减少日志保留天数',
    '限制自定义指标数量',
    '使用基础告警而非复合告警',
  ],
  
  longTerm: [
    '实施指标采样策略',
    '使用CloudWatch Logs Insights而非持续查询',
    '定期清理未使用的告警',
    '考虑使用第三方监控工具（如Grafana）',
  ],
  
  monitoring: [
    '设置成本告警监控CloudWatch费用',
    '每月审查指标使用情况',
    '定期评估监控需求',
  ],
};

/**
 * 获取环境对应的监控配置
 */
export function getMonitoringCostConfig(environment: string): MonitoringCostConfig {
  switch (environment) {
    case 'prod':
    case 'production':
      return prodMonitoringConfig;
    case 'dev':
    case 'development':
    default:
      return devMonitoringConfig;
  }
}

/**
 * 计算预估月度监控成本
 */
export function calculateEstimatedMonthlyCost(config: MonitoringCostConfig): number {
  return config.containerInsights.estimatedMonthlyCost + 
         config.customMetrics.estimatedMonthlyCost;
}
