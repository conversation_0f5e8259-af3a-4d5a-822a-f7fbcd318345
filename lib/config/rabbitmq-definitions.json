{"rabbit_version": "4.1.2", "rabbitmq_version": "4.1.2", "product_name": "RabbitMQ", "product_version": "4.1.2", "users": [{"name": "guest", "password_hash": "", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator", "limits": {}}], "vhosts": [{"name": "/", "description": "Default virtual host", "tags": [], "default_queue_type": "classic", "metadata": {"description": "Default virtual host", "tags": []}}], "permissions": [{"user": "guest", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "internal_cluster_id", "value": "rabbitmq-cluster-id"}], "policies": [{"vhost": "/", "name": "ha-policy", "pattern": ".*", "apply-to": "all", "definition": {"ha-mode": "all", "ha-sync-mode": "automatic"}, "priority": 0}], "queues": [], "exchanges": [{"name": "", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "amq.direct", "vhost": "/", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "amq.fanout", "vhost": "/", "type": "fanout", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "amq.headers", "vhost": "/", "type": "headers", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "amq.match", "vhost": "/", "type": "headers", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "amq.rabbitmq.trace", "vhost": "/", "type": "topic", "durable": true, "auto_delete": false, "internal": true, "arguments": {}}, {"name": "amq.topic", "vhost": "/", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "bindings": []}