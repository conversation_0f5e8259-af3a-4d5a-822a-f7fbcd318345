/**
 * 数据库堆栈配置
 */

import { StackConfig } from '../base';

export interface DatabaseConfig extends StackConfig {
  // 数据库引擎配置
  engine: 'aurora-postgresql' | 'rds-postgresql';
  engineVersion: string;

  // Aurora Serverless v2 配置（仅生产环境）
  serverlessV2Scaling: {
    minCapacity: number;
    maxCapacity: number;
  };

  // RDS 实例配置（仅开发环境免费套餐）
  rdsInstanceConfig?: {
    instanceClass: 'db.t3.micro' | 'db.t4g.micro';
    allocatedStorage: number; // GB，免费套餐最大20GB
    maxAllocatedStorage: number; // GB，设置为与allocatedStorage相同以避免自动扩展费用
    storageType: 'gp2' | 'gp3';
    multiAz: boolean; // 免费套餐建议设为false
  };

  // 通用配置
  backupRetention: number;
  deletionProtection: boolean;

  // 读写分离配置（仅Aurora）
  readerInstances: number;

  // 监控和性能配置
  enablePerformanceInsights: boolean;
  performanceInsightsRetention: number;

  // 网络配置
  enableClusterEndpoint: boolean;
  enableReaderEndpoint: boolean;
}