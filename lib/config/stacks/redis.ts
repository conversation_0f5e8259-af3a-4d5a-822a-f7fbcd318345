/**
 * Redis堆栈配置
 */

import { StackConfig } from '../base';

export interface RedisConfig extends StackConfig {
  // ECS容器配置
  cpu: number;        // CPU单位（最小128）
  memory: number;     // 内存MB（最小与CPU匹配）
  numCacheNodes: number; // 保留用于向后兼容，单实例模式固定为1
  // 保留nodeType用于向后兼容（如果需要回退到ElastiCache）
  nodeType?: string;
}

/**
 * 内置Redis服务配置接口
 * 用于Claude-Relay等服务的内置Redis配置
 */
export interface EmbeddedRedisConfig {
  // 服务基本配置
  service: {
    name: string;
    port: number;
    memory: number; // 容器内存限制 (MiB)
    cpu: number;    // CPU预留值
  };

  // 容器配置
  container: {
    image: string;
    tag: string;
  };

  // 持久化存储配置
  persistence: {
    enabled: boolean;
    dataPath: string;         // 容器内数据路径
    efsPath: string;          // EFS中的存储路径 (相对于/ecs根目录)
    enableAof: boolean;       // 启用AOF持久化
    enableRdb: boolean;       // 启用RDB快照
    aofSyncPolicy: 'always' | 'everysec' | 'no'; // AOF同步策略
  };

  // Redis服务器配置
  serverConfig: {
    maxMemory: string;                           // Redis最大内存使用
    maxMemoryPolicy: string;                     // 内存满时策略
    databases: number;                           // 数据库数量
    tcpKeepAlive: number;                        // TCP keepalive间隔(秒)
    timeout: number;                             // 客户端连接超时(0=禁用)
    tcpBacklog: number;                          // TCP监听队列长度
    logLevel: 'debug' | 'verbose' | 'notice' | 'warning'; // 日志级别
  };

  // 健康检查配置
  healthCheck: {
    interval: number;     // 检查间隔(秒)
    timeout: number;      // 超时时间(秒)  
    retries: number;      // 重试次数
    startPeriod: number;  // 启动宽限期(秒)
  };

  // 日志配置
  logging: {
    logGroupName: string;
    retentionDays: number;
  };

  // 监控配置
  monitoring: {
    enableMetrics: boolean;
    enableSlowLog: boolean;
    slowLogMaxLen: number; // 慢查询日志最大长度
  };
}