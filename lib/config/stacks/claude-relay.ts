/**
 * <PERSON>lay 服务栈配置接口
 * 中转服务配置管理
 */

import { EmbeddedRedisConfig } from './redis';

export interface ClaudeRelayConfig {
  enabled: boolean;
  
  // 服务基本配置
  service: {
    name: string;
    port: number;
    healthCheckPath: string;
  };
  
  // 容器配置
  container: {
    image: string;
    tag: string;
    cpu: number;
    memory: number;
  };
  
  // ECS 服务配置
  ecs: {
    desiredCount: number;
    minCapacity: number;
    maxCapacity: number;
    enableAutoScaling: boolean;
  };
  
  // 域名和路由配置
  routing: {
    domain?: string;
    pathPatterns: string[];
    priority: number; // ALB 路由优先级
  };
  
  // 环境变量配置
  environment: {
    [key: string]: string;
  };
  
  // 密钥配置 (存储在 Secrets Manager)
  secrets: {
    jwtSecretName: string;
    encryptionKeyName: string;
  };
  
  // 健康检查配置
  healthCheck: {
    healthyHttpCodes: string;
    interval: number; // seconds
    timeout: number; // seconds
    healthyThresholdCount: number;
    unhealthyThresholdCount: number;
  };
  
  // 日志配置
  logging: {
    retentionDays: number;
    logGroupName: string;
  };
  
  // 监控配置
  monitoring: {
    enableDetailedMonitoring: boolean;
    enableXRayTracing: boolean;
  };

  // 内置Redis配置
  redis: EmbeddedRedisConfig;
}