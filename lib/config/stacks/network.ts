/**
 * 网络堆栈配置
 */

import { StackConfig } from '../base';

export interface NetworkConfig extends StackConfig {
  // VPC配置
  vpc: {
    cidr: string;
    maxAzs: number;
    natGateways: number;
    // 是否为NAT网关使用固定EIP（用于第三方白名单）
    useFixedNatGatewayEIPs?: boolean;
  };

  // 域名和DNS配置
  domains: {
    // 主域名配置
    primary: {
      zoneName: string;
      hostedZoneId?: string; // 如果已存在托管区域
    };
    // 应用域名配置
    applications: {
      yherp: {
        internal: string; // yh.kh2u.com - 内部访问
        public: string;   // dp.kh2u.com - 公网访问
      };
      khmall: {
        domain: string;   // jmall.tw - 电商平台
      };
    };
  };

  // SSL证书配置
  ssl: {
    // 是否启用自动证书管理
    enableAutoManagement: boolean;
    // 证书验证方法
    validationMethod: 'DNS' | 'EMAIL';
    // 证书域名列表
    certificateDomains: string[];
  };

  // WAF配置
  waf: {
    // 是否启用WAF
    enabled: boolean;
    // WAF规则配置
    rules: {
      // 启用AWS托管规则
      enableAWSManagedRules: boolean;
      // 启用IP白名单
      enableIPWhitelist: boolean;
      // 白名单IP地址
      whitelistIPs: string[];
      // 启用地理位置阻止
      enableGeoBlocking: boolean;
      // 阻止的国家代码
      blockedCountries: string[];
      // 启用速率限制
      enableRateLimit: boolean;
      // 速率限制配置
      rateLimit: {
        limit: number;
        windowSize: number; // 秒
      };
    };
  };

  // OpenZiti配置
  openziti: {
    // 是否启用OpenZiti
    enabled: boolean;
    // 控制器配置
    controller: {
      endpoint: string;
      port: number;
    };
    // 网络配置
    network: {
      name: string;
      cidr: string;
    };
  };
}