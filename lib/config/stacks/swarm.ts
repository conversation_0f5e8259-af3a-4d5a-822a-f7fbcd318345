/**
 * Docker Swarm 堆栈配置
 */

import { StackConfig } from '../base';

export interface SwarmNodeConfig {
  instanceType: string;
  count: number;
  subnetType: 'public' | 'private';
  role: 'manager' | 'worker';
}

export interface SwarmConfig extends StackConfig {
  // 集群基础配置
  clusterName: string;
  keyPairName: string;
  
  // EIP配置
  useFixedEIP?: boolean; // 是否为Swarm Manager分配固定EIP
  
  // 存储配置
  storage: {
    volumeType: string;
    volumeSize: number;
    encrypted: boolean;
  };
  
  // 节点配置
  nodes: {
    manager: SwarmNodeConfig;
    workers: SwarmNodeConfig;
  };
  
  // 网络端口配置
  ports: {
    ssh: number;
    swarmManagement: number;
    swarmCommunication: number;
    swarmOverlay: number;
    customPorts?: number[];
  };
  
  // IAM 角色配置
  iamRole: {
    roleName: string;
    policies: string[];
  };
  
  // EFS 文件系统配置
  efs?: {
    enabled: boolean;
    fileSystems: Array<{
      name: string;
      description: string;
      encrypted?: boolean;          // 是否加密，默认false
    }>;
  };
  
  // 监控配置
  monitoring: {
    enableCloudWatchAgent: boolean;
    logRetentionDays: number;
  };
}