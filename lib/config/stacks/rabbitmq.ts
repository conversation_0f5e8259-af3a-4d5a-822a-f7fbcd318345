/**
 * RabbitMQ堆栈配置
 */

import { StackConfig } from '../base';

export interface RabbitMQConfig extends StackConfig {
  // 容器配置
  cpu: number;
  memory: number;
  // 存储配置
  storageSize: number; // GB
  // 管理界面配置
  enableManagementUI: boolean;
  managementPort: number;
  // 集群配置
  enableClustering: boolean;
  replicas: number;
  // 域名和路由配置
  routing: {
    domain?: string;
    priority: number; // ALB 路由优先级
  };
}