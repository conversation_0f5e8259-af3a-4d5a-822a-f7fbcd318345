/**
 * LoadBalancer 栈配置接口
 * 管理共享的应用负载均衡器
 */

export interface LoadBalancerConfig {
  enabled: boolean;
  
  // 公网负载均衡器配置
  public: {
    enabled: boolean;
    name?: string;
    enableWafAssociation: boolean;
  };
  
  // SSL/TLS 配置
  ssl: {
    enableHttpsRedirect: boolean; // HTTP 到 HTTPS 重定向
    certificateArns?: string[];   // 外部管理的证书 ARN
  };
  
  // 监听器配置
  listeners: {
    http: {
      port: number;
      enabled: boolean;
    };
    https: {
      port: number;
      enabled: boolean;
    };
  };
  
  // 访问日志配置
  accessLogs: {
    enabled: boolean;
    bucketName?: string;
    bucketPrefix?: string;
  };
}