/**
 * 备份配置接口
 * 采用分层配置模式，备份策略独立于资源配置
 */

import { StackConfig } from '../base';

/**
 * 备份策略配置接口
 */
export interface BackupPolicyConfig {
  daily: {
    retention: number; // 保留天数
    schedule: { hour: number; minute: number }; // 备份时间
    moveToColdStorageAfter: number; // 转冷存储天数
  };
  weekly?: {
    retention: number; // 保留天数
    schedule: { weekDay: number; hour: number; minute: number }; // 备份时间
    moveToColdStorageAfter: number; // 转冷存储天数
  };
  enableContinuousBackup: boolean; // 是否启用连续备份
}

/**
 * EFS 备份特定设置
 */
export interface EfsBackupSettings {
  fileSystemId?: string; // 文件系统ID
  accessPoints?: string[]; // 访问点列表
  performanceMode?: 'generalPurpose' | 'maxIO'; // 性能模式
}

/**
 * 数据库备份特定设置
 */
export interface DatabaseBackupSettings {
  clusterId?: string; // 集群ID
  preferredBackupWindow?: string; // 首选备份窗口
  skipFinalSnapshot?: boolean; // 是否跳过最终快照
}

/**
 * 资源备份配置
 */
export interface ResourceBackupConfig<T = any> {
  enabled: boolean; // 是否启用备份
  policy: 'lowCost' | 'standard' | 'enterprise'; // 使用的备份策略
  customSettings?: Partial<T>; // 自定义设置
}

/**
 * 主备份配置接口
 */
export interface BackupConfig extends StackConfig {
  enabled: boolean; // 全局备份开关
  
  // 预定义的备份策略
  policies: {
    lowCost: BackupPolicyConfig; // 低成本策略
    standard: BackupPolicyConfig; // 标准策略
    enterprise: BackupPolicyConfig; // 企业级策略
  };
  
  // 各种资源的备份配置
  resources: {
    efs: ResourceBackupConfig<EfsBackupSettings>; // EFS 备份配置
    database: ResourceBackupConfig<DatabaseBackupSettings>; // 数据库备份配置
  };
  
  // 全局设置
  defaultSettings: {
    backupVaultName: string; // 备份保险库名称
    enableEncryption: boolean; // 是否启用加密
    deletionProtection: boolean; // 是否启用删除保护
  };
  
  // 监控和告警配置
  monitoring: {
    enableAlerts: boolean; // 是否启用告警
    snsTopicArn?: string; // SNS 主题 ARN
    alertOnFailure: boolean; // 备份失败时告警
    alertOnSuccess: boolean; // 备份成功时告警
  };
}

/**
 * 备份策略类型
 */
export type BackupPolicyType = 'lowCost' | 'standard' | 'enterprise';

/**
 * 备份资源类型
 */
export type BackupResourceType = 'efs' | 'database';

/**
 * 备份调度配置
 */
export interface BackupScheduleConfig {
  hour: number; // 小时 (0-23)
  minute: number; // 分钟 (0-59)
  weekDay?: number; // 星期几 (1-7, 仅周备份使用)
}

/**
 * 备份生命周期配置
 */
export interface BackupLifecycleConfig {
  deleteAfterDays: number; // 删除前保留天数
  moveToColdStorageAfterDays: number; // 转冷存储天数
  moveToArchiveAfterDays?: number; // 转归档存储天数（可选）
}

/**
 * 备份配置验证接口
 */
export interface BackupConfigValidation {
  isValid: boolean; // 配置是否有效
  errors: string[]; // 错误列表
  warnings: string[]; // 警告列表
}