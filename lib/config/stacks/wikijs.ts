/**
 * Wiki.js 服务栈配置接口
 * 企业级 Wiki 知识管理系统配置
 */

export interface WikiJSConfig {
  enabled: boolean;
  
  // 服务基本配置
  service: {
    name: string;
    port: number;
    healthCheckPath: string;
  };
  
  // 容器配置
  container: {
    image: string;
    tag: string;
    cpu: number;
    memory: number;
  };
  
  // ECS 服务配置
  ecs: {
    desiredCount: number;
    minCapacity: number;
    maxCapacity: number;
    enableAutoScaling: boolean;
  };
  
  // 域名和路由配置
  routing: {
    domain?: string;
    pathPatterns: string[];
    priority: number; // ALB 路由优先级
  };
  
  // 数据库配置
  database: {
    // AWS Secrets Manager ARN containing database connection info
    // Secret应包含字段: host, port, username, password (不包含dbname)
    secretArn: string; 
    type: 'postgres' | 'mysql' | 'mariadb' | 'mssql';
    dbName: string; // 数据库名称，从配置获取而不是从ASM
    ssl: boolean;
    pool: {
      min: number;
      max: number;
    };
  };
  
  // 环境变量配置
  environment: {
    [key: string]: string;
  };
  
  // Wiki.js不需要额外的secrets配置
  // 根据官方文档，Wiki.js会自动处理会话管理
  
  // 健康检查配置
  healthCheck: {
    healthyHttpCodes: string;
    interval: number; // seconds
    timeout: number; // seconds
    healthyThresholdCount: number;
    unhealthyThresholdCount: number;
  };
  
  // 日志配置
  logging: {
    retentionDays: number;
    logGroupName: string;
  };
  
  // 注：移除了不必要的监控、备份、缓存配置
  // 这些功能在栈实现中未使用
}