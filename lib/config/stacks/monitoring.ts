/**
 * 监控堆栈配置
 */

import { StackConfig } from '../base';

export interface BudgetAlert {
  name: string;
  amount: number;
  threshold: number;
  subscribers: string[];
}

export interface EcsOptimizationConfig {
  trackInstanceUtilization: boolean;
  alertOnLowUtilization: boolean;
  lowUtilizationThreshold: number;
}

export interface CostMonitoringConfig {
  enabled: boolean;
  budgetAlerts: BudgetAlert[];
  ecsOptimization: EcsOptimizationConfig;
}

export interface MonitoringConfig extends StackConfig {
  enableDetailedMonitoring: boolean;
  logRetentionDays: number;
  costMonitoring?: CostMonitoringConfig;
}