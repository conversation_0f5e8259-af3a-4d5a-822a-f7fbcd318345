/**
 * 电子发票Turnkey系统配置接口
 */

import { StackConfig } from '../base';

/**
 * Turnkey Docker镜像配置
 */
export interface TurnkeyImageConfig {
  /** 是否使用自定义构建的镜像 */
  useCustomImage: boolean;
  /** 第三方镜像URL（当不使用自定义镜像时） */
  url?: string;
}

/**
 * Turnkey资源配置
 */
export interface TurnkeyResourceConfig {
  /** CPU单位数 (1024 = 1 vCPU) */
  cpu: number;
  /** 内存大小 (MB) */
  memory: number;
}

/**
 * Turnkey路由配置
 */
export interface TurnkeyRoutingConfig {
  /** 是否启用外部路由访问 */
  enabled: boolean;
  /** 外部访问域名 */
  domain?: string;
  /** 负载均衡器路由优先级 */
  priority?: number;
  /** 健康检查路径 */
  healthCheckPath?: string;
}

/**
 * Turnkey数据库配置
 */
export interface TurnkeyDatabaseConfig {
  /** 数据库主机名 */
  host: string;
  /** 数据库端口 */
  port: number;
  /** 数据库名称 */
  databaseName: string;
  /** 数据库用户名 */
  userName: string;
  /** 数据库密码的AWS Secrets Manager密钥名称 */
  passwordSecretName: string;
  /** 数据库连接池大小 */
  connectionPoolSize: number;
}

/**
 * Turnkey监控配置
 */
export interface TurnkeyMonitoringConfig {
  /** 是否启用详细监控 */
  enableDetailedMonitoring: boolean;
  /** 健康检查间隔（秒） */
  healthCheckInterval: number;
  /** 健康检查超时时间（秒） */
  healthCheckTimeout: number;
  /** 健康检查重试次数 */
  healthCheckRetries: number;
  /** 应用启动等待时间（秒） */
  startupGracePeriod: number;
}

/**
 * Turnkey证书管理配置
 */
export interface TurnkeyCertificateConfig {
  /** 是否启用证书管理 */
  enabled: boolean;
  /** 证书文件名列表 */
  fileList: string[];
}

/**
 * Turnkey系统完整配置
 */
export interface TurnkeyConfig extends StackConfig {
  /** Docker镜像配置 */
  image: TurnkeyImageConfig;
  
  /** 资源配置 */
  resources: TurnkeyResourceConfig;
  
  /** 服务副本数量 */
  replicas: number;
  
  /** 路由配置 */
  routing: TurnkeyRoutingConfig;
  
  /** 数据库配置 */
  database: TurnkeyDatabaseConfig;
  
  /** 监控配置 */
  monitoring: TurnkeyMonitoringConfig;
  
  /** 证书管理配置 */
  certificates: TurnkeyCertificateConfig;
  
  /** 启动密码（可选，通过Secrets Manager管理） */
  startupPasswordSecret?: string;
  
  /** 额外的环境变量 */
  environmentVariables?: { [key: string]: string };
  
  /** EFS工作目录配置 */
  workDirectory: {
    /** EFS挂载路径 */
    mountPath: string;
    /** 是否只读 */
    readOnly: boolean;
    /** 文件系统权限 */
    posixUser: {
      uid: string;
      gid: string;
    };
  };
}