/**
 * ECS堆栈配置
 */

import { StackConfig } from '../base';

/**
 * 容量提供商配置
 */
export interface CapacityProviderConfig {
  name: string;
  instanceType: string;
  architecture: 'x86_64' | 'arm64';
  weight: number;
  base: number;
  spotEnabled?: boolean;
  minCapacity: number;
  maxCapacity: number;
  desiredCapacity: number;
  targetCapacityPercent?: number;
}

export interface EcsConfig extends StackConfig {
  // 容量提供商配置
  capacityProviders: CapacityProviderConfig[];
  
  // 默认容量提供商策略（可选）
  defaultCapacityProviderStrategy?: {
    capacityProvider: string;
    weight: number;
    base?: number;
  }[];
  
  // EFS共享文件系统配置
  efs: {
    enabled: boolean;
    fileSystemName: string;
    performanceMode: 'generalPurpose' | 'maxIO';
    throughputMode: 'bursting' | 'provisioned';
    encryptInTransit: boolean;
    encryptAtRest: boolean;
  };
}