/**
 * Apache Airflow堆栈配置
 */

import { StackConfig } from '../base';

export type ExecutorType = 'LocalExecutor' | 'CeleryExecutor' | 'KubernetesExecutor';

export interface DagBundleConfig {
  // DAG Bundle名称
  name: string;
  // 类路径
  classpath: string;
  // 配置参数
  kwargs: Record<string, any>;
}

export interface GitDagBundleConfig extends DagBundleConfig {
  classpath: 'airflow.providers.git.bundles.git.GitDagBundle';
  kwargs: {
    tracking_ref: string;
    git_conn_id: string;
    subdir?: string;
    refresh_interval?: number;
  };
}

export interface LocalDagBundleConfig extends DagBundleConfig {
  classpath: 'airflow.dag_processing.bundles.local.LocalDagBundle';
  kwargs: {
    local_folder: string;
  };
}

// S3 DAG Bundle配置（Amazon provider官方实现）
export interface S3DagBundleConfig extends DagBundleConfig {
  classpath: 'airflow.providers.amazon.aws.bundles.s3.S3DagBundle';
  kwargs: {
    s3_bucket: string;
    s3_key_prefix: string;
    aws_conn_id?: string;
  };
}

export interface AirflowConfig extends StackConfig {
  // 版本配置
  version: string;
  
  // 自定义镜像配置
  image: {
    useCustomImage: boolean;
    url?: string; // 第三方镜像URL，优先级高于官方镜像
    customImageTag?: string;
    customImageRepo?: string;
    // 是否包含最新的Amazon provider
    includeLatestAmazonProvider: boolean;
  };

  // DAG Bundle配置
  dagBundles: (GitDagBundleConfig | LocalDagBundleConfig | S3DagBundleConfig)[];

  // 组件配置
  webserver: {
    cpu: number;
    memory: number;
    replicas: number;
    port: number;
  };
  scheduler: {
    cpu: number;
    memory: number;
    replicas: number;
  };
  worker: {
    cpu: number;
    memory: number;
    replicas: number;
  };
  
  // 存储配置
  logsStorageSize: number; // GB
  dagsStorageSize: number; // GB
  
  // 执行器配置
  executor: ExecutorType;

  // 域名和路由配置
  routing: {
    domain?: string;
    priority: number; // ALB 路由优先级
  };

  // 环境变量配置
  environmentVariables?: Record<string, string>;

  // 连接配置
  connections?: {
    aws?: {
      connectionId: string;
      connectionType: string;
      extra?: Record<string, any>;
    };
    postgres?: {
      connectionId: string;
      host: string;
      schema: string;
      login?: string;
      password?: string;
    };
  };

  // 插件配置
  plugins?: {
    enabled: boolean;
    s3Location?: string;
  };
}