# RabbitMQ配置文件
# 此配置文件替代已弃用的环境变量

## 内存管理
# 内存高水位线设置为60%（替代RABBITMQ_VM_MEMORY_HIGH_WATERMARK）
vm_memory_high_watermark.relative = 0.6

# 内存高水位线分页阈值
vm_memory_high_watermark_paging_ratio = 0.5

## 磁盘管理
# 磁盘空闲限制（替代RABBITMQ_DISK_FREE_LIMIT）
disk_free_limit.absolute = 2GB

## 日志配置
# 主日志文件
log.file = /var/log/rabbitmq/rabbit.log
log.file.level = info
log.file.rotation.date = $D0
log.file.rotation.size = 10485760

# SASL日志
log.file.sasl = /var/log/rabbitmq/rabbit-sasl.log

## 网络配置
# AMQP端口
listeners.tcp.default = 5672

# 管理插件端口
management.tcp.port = 15672

# 启用管理插件
management.load_definitions = /etc/rabbitmq/definitions.json

## 集群配置
# 使用长节点名
cluster_formation.peer_discovery_backend = classic_config
cluster_formation.classic_config.nodes.1 = rabbit@localhost

## 性能优化
# 通道最大数量
channel_max = 2047

# 帧最大大小
frame_max = 131072

# 心跳间隔
heartbeat = 60

# 队列主节点定位策略
queue_master_locator = min-masters

## 默认用户和虚拟主机
# 默认虚拟主机
default_vhost = /

# 默认权限
default_permissions.configure = .*
default_permissions.read = .*
default_permissions.write = .*

## 插件配置
# 启用的插件
plugins.directories.1 = /opt/rabbitmq/plugins
plugins.expand_criteria.1 = rabbitmq_management
plugins.expand_criteria.2 = rabbitmq_prometheus

## 安全配置
# SSL配置（如果需要）
# ssl_options.cacertfile = /etc/rabbitmq/ca_certificate.pem
# ssl_options.certfile = /etc/rabbitmq/server_certificate.pem
# ssl_options.keyfile = /etc/rabbitmq/server_key.pem
# ssl_options.verify = verify_peer
# ssl_options.fail_if_no_peer_cert = true

## 监控配置
# 启用Prometheus指标
prometheus.tcp.port = 15692

## 队列配置
# 默认队列类型
default_queue_type = classic

# 队列TTL
# queue_ttl = 1800000

## 消息配置
# 消息TTL
# message_ttl = 3600000

# 最大消息大小
max_message_size = 134217728
