/**
 * 生产环境 - ECS栈配置
 */

import { EcsConfig } from '../../stacks';
import { getResourceByEnv } from '../../shared';

const env = 'prod';

export const ecsConfig: EcsConfig = {
  enabled: true,
  
  // 容量提供商配置 - 生产环境ARM架构优先，x86兜底
  capacityProviders: [
    {
      name: 'arm64-capacity-provider',
      instanceType: 'm6g.large',
      architecture: 'arm64',
      weight: 100,
      base: 2, // 增加ARM基础容量确保稳定性
      spotEnabled: false,
      minCapacity: 1, // 确保至少一个ARM实例运行
      maxCapacity: 4, // 增加最大容量以支持更高负载
      desiredCapacity: 1, // 增加期望容量
      targetCapacityPercent: 80,
    }
  ],
  
  // 默认容量提供商策略 - ARM架构优先，x86作为兜底方案
  defaultCapacityProviderStrategy: [
    {
      capacityProvider: 'arm64-capacity-provider',
      weight: 85,
      base: 2,
    }
  ],
  
  // EFS共享存储配置
  efs: {
    enabled: true,
    fileSystemName: `yuanhui-ecs-shared-${env}`,
    performanceMode: 'generalPurpose',
    throughputMode: 'bursting',
    encryptInTransit: true,
    encryptAtRest: true, // 生产环境启用加密
  },
};