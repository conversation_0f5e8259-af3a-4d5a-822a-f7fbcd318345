/**
 * 生产环境 - 监控栈配置
 */

import { MonitoringConfig } from '../../stacks';
import { getRetentionDaysByEnv, getSecuritySettingsByEnv } from '../../shared';

const env = 'prod';
const logRetention = getRetentionDaysByEnv(env, 'logs');
const securitySettings = getSecuritySettingsByEnv(env);

export const monitoringConfig: MonitoringConfig = {
  enabled: true,
  enableDetailedMonitoring: securitySettings.enableDetailedMonitoring,
  logRetentionDays: logRetention,
};