/**
 * 生产环境 - Wiki.js 栈配置
 */

import { WikiJSConfig } from '../../stacks';
import { 
  getResourceByEnv, 
  getRetentionDaysByEnv, 
  generateDomain, 
  defaultPorts 
} from '../../shared';

const env = 'prod';
const resourceLimits = getResourceByEnv(env, 'small');
const logRetention = getRetentionDaysByEnv(env, 'logs');

export const wikijsConfig: WikiJSConfig = {
  enabled: true, // 生产环境启用
  
  service: {
    name: 'wikijs',
    port: defaultPorts.wikijs,
    healthCheckPath: '/healthz',
  },
  
  container: {
    image: 'requarks/wiki',
    tag: '2.5',
    cpu: resourceLimits.cpu,
    memory: resourceLimits.memory,
  },
  
  ecs: {
    desiredCount: 1, // 生产环境部署1个副本
    minCapacity: 1,
    maxCapacity: 2,
    enableAutoScaling: false,
  },
  
  routing: {
    domain: generateDomain('wiki', env, 'kh2u.com'),
    pathPatterns: [],
    priority: 110,
  },
  
  database: {
    // 必须提供包含数据库连接信息的 Secrets Manager ARN
    // 密钥应包含字段: host, port, username, password (不包含dbname)
    // 示例格式: arn:aws:secretsmanager:region:account:secret:name-randomstring
    secretArn: process.env.WIKIJS_DATABASE_SECRET_ARN || 'arn:aws:secretsmanager:ap-east-2:************:secret:prod/wikijs/rds-N30BQF', 
    type: 'postgres',
    dbName: 'wiki', // 数据库名称
    ssl: false, // 生产环境强制SSL
    pool: {
      min: 5,
      max: 20,
    },
  },
  
  environment: {
    NODE_ENV: 'production',
    LOG_LEVEL: 'warn',
    // 生产环境相关的优化配置
    NODE_OPTIONS: '--max-old-space-size=1024',
    UV_THREADPOOL_SIZE: '4',
  },
  
  healthCheck: {
    healthyHttpCodes: '200',
    interval: 30,
    timeout: 5,
    healthyThresholdCount: 3,
    unhealthyThresholdCount: 2,
  },
  
  logging: {
    retentionDays: logRetention,
    logGroupName: `/aws/ecs/wikijs-${env}`,
  },
};