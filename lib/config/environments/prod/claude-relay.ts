/**
 * 生产环境 - <PERSON>栈配置
 */

import { ClaudeRelayConfig } from '../../stacks';
import { 
  getResourceByEnv, 
  getRetentionDaysByEnv, 
  generateDomain, 
  defaultPorts 
} from '../../shared';

const env = 'prod';
const resourceLimits = getResourceByEnv(env, 'small');
const logRetention = getRetentionDaysByEnv(env, 'logs');

export const claudeRelayConfig: ClaudeRelayConfig = {
  enabled: true,
  service: {
    name: 'claude-relay',
    port: defaultPorts.claudeRelay,
    healthCheckPath: '/health',
  },
  container: {
    image: 'weishaw/claude-relay-service',
    tag: 'v1.1.132',
    cpu: resourceLimits.cpu,
    memory: resourceLimits.memory,
  },
  ecs: {
    desiredCount: 1,
    minCapacity: 1,
    maxCapacity: 1,
    enableAutoScaling: true, // 生产环境启用自动扩容
  },
  routing: {
    domain: generateDoma<PERSON>('claude-relay', env, 'kh2u.com'),
    pathPatterns: [], // 不再使用路径模式，改用域名路由
    priority: 100,
  },
  environment: {
    NODE_ENV: 'production',
    LOG_LEVEL: 'warn',
  },
  secrets: {
    jwtSecretName: `claude-relay-jwt-secret-${env}`,
    encryptionKeyName: `claude-relay-encryption-key-${env}`,
  },
  healthCheck: {
    healthyHttpCodes: '200,404',
    interval: 30,
    timeout: 5,
    healthyThresholdCount: 2,
    unhealthyThresholdCount: 3,
  },
  logging: {
    retentionDays: logRetention,
    logGroupName: `/aws/ecs/claude-relay-${env}`,
  },
  monitoring: {
    enableDetailedMonitoring: true,
    enableXRayTracing: false,
  },

  // 内置Redis配置
  redis: {
    service: {
      name: 'redis',
      port: 6379,
      memory: 256, // 256MB内存限制（生产环境）
      cpu: 128,    // 128 CPU单位
    },
    container: {
      image: 'redis',
      tag: '7.2-alpine',
    },
    persistence: {
      enabled: true,
      dataPath: '/data',
      efsPath: 'claude-relay/redis', // 相对于/ecs的路径
      enableAof: true,
      enableRdb: true,
      aofSyncPolicy: 'everysec',
    },
    serverConfig: {
      maxMemory: '200mb',
      maxMemoryPolicy: 'allkeys-lru',
      databases: 16,
      tcpKeepAlive: 300,
      timeout: 0,
      tcpBacklog: 511,
      logLevel: 'warning', // 内部使用，仅记录警告和错误
    },
    healthCheck: {
      interval: 30,
      timeout: 5,
      retries: 3,
      startPeriod: 15,
    },
    logging: {
      logGroupName: `/aws/ecs/claude-relay-redis-${env}`, // Redis基本日志记录
      retentionDays: 3, // 生产环境保留3天
    },
    monitoring: {
      enableMetrics: false,        // 禁用指标监控
      enableSlowLog: false,        // 禁用慢查询日志
      slowLogMaxLen: 0,           // 设置为0禁用
    },
  },
};