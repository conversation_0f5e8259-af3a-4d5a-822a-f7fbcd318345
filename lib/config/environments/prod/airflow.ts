/**
 * 生产环境 - Airflow栈配置
 */

import { AirflowConfig } from '../../stacks';
import { getResourceByEnv, getStorageSizeByEnv, defaultPorts, generateDomain } from '../../shared';

const env = 'prod';
const webserverResources = getResourceByEnv(env, 'small');
const schedulerResources = getResourceByEnv(env, 'small');
const workerResources = getResourceByEnv(env, 'small');
const logsStorage = getStorageSizeByEnv(env, 'small');
const dagsStorage = getStorageSizeByEnv(env, 'small');

export const airflowConfig: AirflowConfig = {
  enabled: true,
  version: '3.0.3',
  image: {
    useCustomImage: false,
    // url: 'my-registry.com/production-airflow:3.0.3-stable', // 使用第三方镜像URL（可选）
    // url: 'quay.io/apache/airflow:3.0.3-python3.11', // 使用不同仓库的官方镜像（可选）
    url: '138264596682.dkr.ecr.ap-east-2.amazonaws.com/kh2u/afps:main-5a45a07',
    customImageTag: 'prod-latest',
    customImageRepo: 'yherp-airflow',
    includeLatestAmazonProvider: true,
  },
  dagBundles: [
    // {
    //   name: 's3_dags',
    //   classpath: 'airflow.providers.amazon.aws.bundles.s3.S3DagBundle',
    //   kwargs: {
    //     s3_bucket: 'yuanhui-airflow-dags-prod',
    //     s3_key_prefix: 'dags/',
    //     aws_conn_id: 'aws_default',
    //   },
    // },
    {
      name: 'yuan_dags',
      classpath: 'airflow.providers.git.bundles.git.GitDagBundle',
      kwargs: {
        tracking_ref: 'od15',
        subdir: 'airflow/dags',
        git_conn_id: 'git_yuan',
        refresh_interval: 3600
      },
    },
  ],
  webserver: {
    cpu: webserverResources.cpu,
    memory: webserverResources.memory,
    replicas: 1,
    port: defaultPorts.airflow,
  },
  scheduler: {
    cpu: schedulerResources.cpu,
    memory: schedulerResources.memory,
    replicas: 1,
  },
  worker: {
    cpu: workerResources.cpu,
    memory: workerResources.memory,
    replicas: 1,
  },
  logsStorageSize: logsStorage,
  dagsStorageSize: dagsStorage,
  executor: 'LocalExecutor',
  routing: {
    domain: generateDomain('airflow', env, 'kh2u.com'),
    priority: 300, // ALB 路由优先级
  },
  environmentVariables: {
    // 基本配置
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'false',
    AIRFLOW__CORE__LOAD_EXAMPLES: 'false',
    
    // Simple Auth Manager 认证配置（Airflow 3.0新特性）
    // AIRFLOW__CORE__AUTH_MANAGER: 'airflow.auth.managers.simple.simple_auth_manager.SimpleAuthManager',
    AIRFLOW__CORE__SIMPLE_AUTH_MANAGER_USERS: 'yuanhui_admin:admin,yuanhui_viewer:viewer',
    AIRFLOW__CORE__SIMPLE_AUTH_MANAGER_ALL_ADMINS: 'TRUE',
    
    // 生产环境安全配置
    AIRFLOW__API__EXPOSE_CONFIG: 'True',
    AIRFLOW__CORE__TEST_CONNECTION: 'Disabled',
    AIRFLOW__API__SESSION_LIFETIME_MINUTES: '30',
  },
  connections: {
    aws: {
      connectionId: 'aws_default',
      connectionType: 'aws',
      extra: {
        region_name: 'ap-east-2',
      },
    },
    postgres: {
      connectionId: 'postgres_default',
      host: 'aurora-cluster.cluster-xyz.ap-east-2.rds.amazonaws.com',
      schema: 'postgres',
    },
  },
  plugins: {
    enabled: true,
    s3Location: 's3://yuanhui-airflow-plugins-prod/plugins/',
  },
};