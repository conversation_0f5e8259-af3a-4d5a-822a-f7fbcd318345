/**
 * 生产环境 - RabbitMQ栈配置
 */

import { RabbitMQConfig } from '../../stacks';
import { getResourceByEnv, getStorageSizeByEnv, defaultPorts, generateDomain } from '../../shared';

const env = 'prod';
const resourceLimits = getResourceByEnv(env, 'small'); // 改为最低资源配置
const storageSize = getStorageSizeByEnv(env, 'small');

export const rabbitmqConfig: RabbitMQConfig = {
  enabled: true,
  cpu: resourceLimits.cpu,
  memory: resourceLimits.memory,
  storageSize,
  enableManagementUI: true, // 生产环境启用管理UI，通过域名访问
  managementPort: defaultPorts.rabbitmqManagement,
  enableClustering: false,
  replicas: 1,
  routing: {
    domain: generateDomain('mq', env, 'kh2u.com'),
    priority: 200, // ALB 路由优先级（高于airflow的300）
  },
};