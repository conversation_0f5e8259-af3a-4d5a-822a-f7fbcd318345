/**
 * 生产环境 - 备份栈配置
 * 采用标准策略，平衡成本和数据安全性
 */

import { BackupConfig } from '../../stacks';
import { defaultEfsBackupSettings } from '../../shared';

const env = 'prod';
const efsDefaults = defaultEfsBackupSettings[env];

export const backupConfig: BackupConfig = {
  enabled: true,
  
  // 预定义备份策略
  policies: {
    // 低成本策略 - 基础备份需求
    lowCost: {
      daily: {
        retention: 7, // 1周保留
        schedule: { hour: 2, minute: 0 },
        moveToColdStorageAfter: 3,
      },
      enableContinuousBackup: false,
    },
    
    // 标准策略 - 生产环境默认使用
    standard: {
      daily: {
        retention: efsDefaults.dailyBackupRetention,
        schedule: efsDefaults.backupSchedule.daily,
        moveToColdStorageAfter: efsDefaults.moveToColdStorageAfterDays,
      },
      weekly: efsDefaults.backupSchedule.weekly ? {
        retention: efsDefaults.weeklyBackupRetention,
        schedule: efsDefaults.backupSchedule.weekly,
        moveToColdStorageAfter: 7, // 1周后转冷存储
      } : undefined,
      enableContinuousBackup: efsDefaults.enableContinuousBackup,
    },
    
    // 企业级策略 - 高可用性要求
    enterprise: {
      daily: {
        retention: 30, // 1个月保留
        schedule: { hour: 1, minute: 0 },
        moveToColdStorageAfter: 7,
      },
      weekly: {
        retention: 365, // 1年保留
        schedule: { weekDay: 7, hour: 2, minute: 0 }, // 每周日凌晨2点
        moveToColdStorageAfter: 30,
      },
      enableContinuousBackup: true, // 企业级启用连续备份
    },
  },
  
  // 资源备份配置
  resources: {
    // EFS 备份配置
    efs: {
      enabled: efsDefaults.enabled,
      policy: 'standard', // 生产环境使用标准策略
      customSettings: {
        performanceMode: 'generalPurpose',
      },
    },
    
    // 数据库备份配置
    database: {
      enabled: true,
      policy: 'standard', // 生产环境使用标准策略
      customSettings: {
        skipFinalSnapshot: false, // 生产环境保留最终快照
        preferredBackupWindow: '03:00-04:00', // 凌晨3-4点备份窗口
      },
    },
  },
  
  // 全局设置
  defaultSettings: {
    backupVaultName: `yuanhui-backup-vault-${env}`,
    enableEncryption: true, // 生产环境启用加密
    deletionProtection: true, // 生产环境启用删除保护
  },
  
  // 监控和告警配置
  monitoring: {
    enableAlerts: true,
    alertOnFailure: true, // 备份失败时告警
    alertOnSuccess: false, // 减少噪音，成功不告警
  },
};