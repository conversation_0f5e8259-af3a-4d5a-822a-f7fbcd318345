/**
 * 生产环境 - 负载均衡器栈配置
 */

import { LoadBalancerConfig } from '../../stacks';
import { generateResourceName, defaultPorts } from '../../shared';

const env = 'prod';

export const loadBalancerConfig: LoadBalancerConfig = {
  enabled: true,
  public: {
    enabled: true,
    name: generateResourceName('yuanhui-public-alb', env),
    enableWafAssociation: false, // WAF暂时关闭
  },
  ssl: {
    enableHttpsRedirect: true,
    certificateArns: ["arn:aws:acm:ap-east-2:138264596682:certificate/9c2cb582-b33f-462a-a6c3-656768784e20"], // 生产环境证书ARN，待用户提供
  },
  listeners: {
    http: {
      port: defaultPorts.http,
      enabled: true,
    },
    https: {
      port: defaultPorts.https,
      enabled: true, // 等证书ARN配置后启用
    },
  },
  accessLogs: {
    enabled: false, // 暂时关闭访问日志
  },
};