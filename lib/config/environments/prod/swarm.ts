/**
 * 生产环境 - Swarm栈配置
 */

import { SwarmConfig } from '../../stacks';
import { 
  getRetentionDaysByEnv, 
  generateResourceName, 
  defaultPorts 
} from '../../shared';

const env = 'prod';
const logRetention = getRetentionDaysByEnv(env, 'monitoring');

export const swarmConfig: SwarmConfig = {
  enabled: true,
  clusterName: generateResourceName('yuanhui-swarm', env),
  keyPairName: 'ec2',
  useFixedEIP: true,
  
  storage: {
    volumeType: 'gp3',
    volumeSize: 40,
    encrypted: true,
  },
  
  nodes: {
    manager: {
      instanceType: 't3.medium',
      count: 1,
      subnetType: 'public', // 改回公开子网，使用独立EIP
      role: 'manager',
    },
    workers: {
      instanceType: 't3.large',
      count: 3,
      subnetType: 'private',
      role: 'worker',
    },
  },
  
  ports: {
    ssh: defaultPorts.ssh,
    swarmManagement: defaultPorts.swarmManagement,
    swarmCommunication: defaultPorts.swarmCommunication,
    swarmOverlay: defaultPorts.swarmOverlay,
    customPorts: [defaultPorts.http, defaultPorts.https, 39132, 23869, 24719],
  },
  
  iamRole: {
    roleName: 'swarmnode',
    policies: [
      'AmazonEC2ReadOnlyAccess',
      'AmazonSSMManagedInstanceCore',
      'CloudWatchAgentServerPolicy',
    ],
  },
  
  monitoring: {
    enableCloudWatchAgent: true, // 生产环境启用监控
    logRetentionDays: logRetention,
  },

  // EFS 文件系统配置
  efs: {
    enabled: true,
    fileSystems: [
      {
        name: 'odoo-data',
        description: 'Production application persistent data',
        encrypted: false, // 根据原配置保持不加密
      }
    ]
  },
};