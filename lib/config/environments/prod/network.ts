/**
 * 生产环境 - 网络栈配置
 */

import { NetworkConfig } from '../../stacks';
import { 
  getVpcConfigByEnv, 
  getSecuritySettingsByEnv, 
  generateApplicationDomains 
} from '../../shared';

const env = 'prod';
const vpcConfig = getVpcConfigByEnv(env);
const securitySettings = getSecuritySettingsByEnv(env);
const applicationDomains = generateApplicationDomains(env);

export const networkConfig: NetworkConfig = {
  enabled: true,
  vpc: {
    cidr: vpcConfig.cidr,
    maxAzs: vpcConfig.maxAzs,
    natGateways: vpcConfig.natGateways, // 优化：从3个NAT网关减少到1个
    useFixedNatGatewayEIPs: true,
  },
  domains: {
    primary: {
      zoneName: 'kh2u.com',
    },
    applications: applicationDomains,
  },
  ssl: {
    enableAutoManagement: true,
    validationMethod: 'DNS',
    certificateDomains: [
      '*.kh2u.com',
      'j2mall.com',
      '*.j2mall.com',
    ],
  },
  waf: {
    enabled: securitySettings.enableWaf,
    rules: {
      enableAWSManagedRules: true,
      enableIPWhitelist: true,
      whitelistIPs: [
        '***********/24',
        '************/24',
      ],
      enableGeoBlocking: true,
      blockedCountries: ['RU', 'KP'],
      enableRateLimit: true,
      rateLimit: {
        limit: 2000,
        windowSize: 300,
      },
    },
  },
  openziti: {
    enabled: securitySettings.enableOpenziti,
    controller: {
      endpoint: 'ziti-controller.kh2u.com',
      port: 443,
    },
    network: {
      name: 'yuanhui-prod-network',
      cidr: '**********/16',
    },
  },
};