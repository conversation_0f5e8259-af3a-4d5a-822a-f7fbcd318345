/**
 * 电子发票Turnkey系统生产环境配置
 */

import { TurnkeyConfig } from '../../stacks/turnkey';
import { getResourceByEnv, getRetentionDaysByEnv } from '../../shared';

const env = 'prod';
const resourceLimits = getResourceByEnv(env, 'small');
const logRetention = getRetentionDaysByEnv(env, 'logs');

export const turnkey: TurnkeyConfig = {
  enabled: true,
  
  image: {
    useCustomImage: false,  // 生产环境使用GitHub Actions构建的ECR镜像确保安全性
    url: '{{ECR_REGISTRY}}/kh2u/yhiac-turnkey:prod-7f1210f',  // 生产环境使用latest标签
  },
  
  resources: {
    cpu: resourceLimits.cpu,     // 1 vCPU，生产环境资源充足
    memory: resourceLimits.memory,  // 2GB内存，确保性能
  },
  
  replicas: 1,  // 生产环境部署1个副本确保高可用性
  
  routing: {
    enabled: false,  // 生产环境启用外部路由
    domain: 'turnkey.kh2u.com',  // 生产环境域名
    priority: 130,  // 较高优先级
    healthCheckPath: '/health',
  },
  
  database: {
    host: 'yuanhuiauroradatabase-pro-aurorapostgresqlclusterw-sespunnofj29.c3c8ccka8h7t.ap-east-2.rds.amazonaws.com',  // 部署时需要替换为实际Aurora集群端点
    port: 5432,
    databaseName: 'eturn',
    userName: 'eturn',
    passwordSecretName: 'prod/eturn/rdspwd',  // 数据库密码存储在Secrets Manager
    connectionPoolSize: 10,  // 生产环境更大的连接池
  },
  
  monitoring: {
    enableDetailedMonitoring: true,  // 生产环境启用详细监控
    healthCheckInterval: 20,         // 20秒检查一次，更频繁
    healthCheckTimeout: 8,           // 8秒超时，更严格
    healthCheckRetries: 5,           // 重试5次，更容错
    startupGracePeriod: 180,         // 启动等待3分钟，允许更长初始化时间
  },
  
  certificates: {
    enabled: true,                                     // 生产环境启用证书管理
    fileList: [                                        // 证书文件名列表（存储在S3 turnkey/certs/ 目录下）
      'jj01_JJ_server.pfx',
      'yuan_server.pfx',
    ],
  },
  
  // 生产环境必须配置启动密码
  // startupPasswordSecret: 'turnkey/prod/startup-password',
  
  environmentVariables: {
    // 生产环境特有的环境变量
    TURNKEY_ENV: 'production',
    TURNKEY_DEBUG: 'false',
    LOG_LEVEL: 'WARN',  // 生产环境减少日志量
    // 性能优化配置
    JAVA_OPTS: '-Xmx1536m -Xms512m -XX:+UseG1GC -XX:MaxGCPauseMillis=200',
  },
  
  workDirectory: {
    mountPath: '/var/EINVTurnkey',
    readOnly: false,
    posixUser: {
      uid: '101',  // turnkey用户ID
      gid: '101',     // turnkey组ID
    },
  },
};