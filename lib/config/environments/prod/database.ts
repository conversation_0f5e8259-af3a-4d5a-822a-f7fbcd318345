/**
 * 生产环境 - 数据库栈配置
 */

import { DatabaseConfig } from '../../stacks';
import { getDatabaseConfigByEnv, isProdDatabaseConfig } from '../../shared';

const env = 'prod';
const dbDefaults = getDatabaseConfigByEnv(env);

export const databaseConfig: DatabaseConfig = {
  enabled: true,
  engine: dbDefaults.engine,  // 生产环境使用Aurora Serverless v2
  engineVersion: '16.6',

  // Aurora Serverless v2配置
  serverlessV2Scaling: isProdDatabaseConfig(dbDefaults) ? dbDefaults.serverlessV2Scaling : {
    minCapacity: 0,
    maxCapacity: 8,
  },

  // RDS配置（生产环境不使用）
  rdsInstanceConfig: {
    instanceClass: 'db.t4g.micro',
    allocatedStorage: 20,
    maxAllocatedStorage: 20,
    storageType: 'gp2',
    multiAz: false,
  },

  backupRetention: dbDefaults.backupRetention,
  deletionProtection: dbDefaults.deletionProtection,
  readerInstances: 0,
  enablePerformanceInsights: dbDefaults.enablePerformanceInsights,
  performanceInsightsRetention: 7,
  enableClusterEndpoint: true,
  enableReaderEndpoint: true,
};