/**
 * 电子发票Turnkey系统开发环境配置
 */

import { TurnkeyConfig } from '../../stacks/turnkey';

export const turnkey: TurnkeyConfig = {
  enabled: true,
  
  image: {
    useCustomImage: true,  // 开发环境使用GitHub Actions构建的ECR镜像
    url: '{{ECR_REGISTRY}}/kh2u/yhiac-turnkey:dev-latest',  // 开发环境使用dev-latest标签
  },
  
  resources: {
    cpu: 512,      // 0.5 vCPU，开发环境资源较少
    memory: 1024,  // 1GB内存
  },
  
  replicas: 1,  // 开发环境只需要1个副本
  
  routing: {
    enabled: false,  // 开发环境暂不启用外部路由
    // 如果需要外部访问，可以配置：
    // domain: 'turnkey-dev.kh2u.com',
    // priority: 150,
    // healthCheckPath: '/health'
  },
  
  database: {
    host: '{{DATABASE_CLUSTER_ENDPOINT}}',  // 部署时需要替换为实际Aurora集群端点
    port: 5432,
    databaseName: 'eturn',
    userName: 'eturn',
    passwordSecretName: 'turnkey/dev/db-password',  // 数据库密码存储在Secrets Manager
    connectionPoolSize: 5,
  },
  
  monitoring: {
    enableDetailedMonitoring: false,  // 开发环境不启用详细监控以节省成本
    healthCheckInterval: 30,          // 30秒检查一次
    healthCheckTimeout: 10,           // 10秒超时
    healthCheckRetries: 3,            // 重试3次
    startupGracePeriod: 120,          // 启动等待2分钟
  },
  
  certificates: {
    enabled: true,                                    // 开发环境启用证书管理
    fileList: [                                       // 证书文件名列表（存储在S3 turnkey/certs/ 目录下）
      'cert1_invoice.pfx',
      'cert2_receipt.pfx',
      'cert3_allowance.pfx',
    ],
  },
  
  // 开发环境可以配置启动密码（通过Secrets Manager）
  // startupPasswordSecret: 'turnkey/dev/startup-password',
  
  environmentVariables: {
    // 开发环境特有的环境变量
    TURNKEY_ENV: 'development',
    TURNKEY_DEBUG: 'true',
    LOG_LEVEL: 'INFO',
  },
  
  workDirectory: {
    mountPath: '/var/EINVTurnkey',
    readOnly: false,
    posixUser: {
      uid: '1001',  // turnkey用户ID
      gid: '0',     // root组
    },
  },
};