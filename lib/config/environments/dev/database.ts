/**
 * 开发环境 - 数据库栈配置
 */

import { DatabaseConfig } from '../../stacks';
import { getDatabaseConfigByEnv, isDevDatabaseConfig } from '../../shared';

const env = 'dev';
const dbDefaults = getDatabaseConfigByEnv(env);

export const databaseConfig: DatabaseConfig = {
  enabled: true,
  engine: dbDefaults.engine,
  engineVersion: '16.6',

  // Aurora配置（开发环境不使用，但保留以兼容接口）
  serverlessV2Scaling: {
    minCapacity: 0.5,
    maxCapacity: 1,
  },

  // RDS免费套餐配置
  rdsInstanceConfig: isDevDatabaseConfig(dbDefaults) ? {
    instanceClass: dbDefaults.rdsInstanceConfig.instanceClass as 'db.t4g.micro',
    allocatedStorage: dbDefaults.rdsInstanceConfig.allocatedStorage,
    maxAllocatedStorage: dbDefaults.rdsInstanceConfig.allocatedStorage, // 禁用自动扩展以避免费用
    storageType: 'gp2',
    multiAz: dbDefaults.rdsInstanceConfig.multiAz,
  } : {
    instanceClass: 'db.t4g.micro',
    allocatedStorage: 20,
    maxAllocatedStorage: 20,
    storageType: 'gp2',
    multiAz: false,
  },

  backupRetention: dbDefaults.backupRetention,
  deletionProtection: dbDefaults.deletionProtection,
  readerInstances: 0, // RDS单实例不支持读副本
  enablePerformanceInsights: dbDefaults.enablePerformanceInsights,
  performanceInsightsRetention: 7,
  enableClusterEndpoint: true,
  enableReaderEndpoint: false,
};