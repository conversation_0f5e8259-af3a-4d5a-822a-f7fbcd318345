/**
 * 开发环境 - RabbitMQ栈配置
 */

import { RabbitMQConfig } from '../../stacks';
import { getResourceByEnv, getStorageSizeByEnv, defaultPorts, generateDomain } from '../../shared';

const env = 'dev';
const resourceLimits = getResourceByEnv(env, 'medium');
const storageSize = getStorageSizeByEnv(env, 'small');

export const rabbitmqConfig: RabbitMQConfig = {
  enabled: true,
  cpu: resourceLimits.cpu,
  memory: resourceLimits.memory,
  storageSize,
  enableManagementUI: true,
  managementPort: defaultPorts.rabbitmqManagement,
  enableClustering: false,
  replicas: 1,
  routing: {
    domain: generateDomain('mq', env, 'kh2u.com'),
    priority: 200, // ALB 路由优先级（高于airflow的300）
  },
};