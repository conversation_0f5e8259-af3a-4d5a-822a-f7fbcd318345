/**
 * 开发环境 - Swarm栈配置
 */

import { SwarmConfig } from '../../stacks';
import { 
  getRetentionDaysByEnv, 
  generateResourceName, 
  defaultPorts 
} from '../../shared';

const env = 'dev';
const logRetention = getRetentionDaysByEnv(env, 'logs');

export const swarmConfig: SwarmConfig = {
  enabled: false, // 开发环境默认关闭Swarm以节省资源
  clusterName: generateResourceName('yuanhui-swarm', env),
  keyPairName: 'ec2',
  useFixedEIP: true,
  
  storage: {
    volumeType: 'gp2',
    volumeSize: 40,
    encrypted: true,
  },
  
  nodes: {
    manager: {
      instanceType: 't3.medium',
      count: 1,
      subnetType: 'public',
      role: 'manager',
    },
    workers: {
      instanceType: 't3.large',
      count: 2,
      subnetType: 'private',
      role: 'worker',
    },
  },
  
  ports: {
    ssh: defaultPorts.ssh,
    swarmManagement: defaultPorts.swarmManagement,
    swarmCommunication: defaultPorts.swarmCommunication,
    swarmOverlay: defaultPorts.swarmOverlay,
    customPorts: [defaultPorts.http, defaultPorts.https, defaultPorts.airflow, defaultPorts.claudeRelay],
  },
  
  iamRole: {
    roleName: 'swarmnode',
    policies: [
      'AmazonEC2ReadOnlyAccess',
      'AmazonSSMManagedInstanceCore',
      'CloudWatchAgentServerPolicy',
    ],
  },
  
  monitoring: {
    enableCloudWatchAgent: false, // 开发环境关闭以节省费用
    logRetentionDays: logRetention,
  },

  // EFS 文件系统配置
  efs: {
    enabled: true,
    fileSystems: [
      {
        name: 'app-data',
        description: 'Application persistent data storage',
        encrypted: false, // 开发环境不加密以节省费用
      },
      {
        name: 'shared-logs',
        description: 'Centralized log storage for all services',
        encrypted: false,
      }
    ]
  },
};