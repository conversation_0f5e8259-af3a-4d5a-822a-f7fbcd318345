/**
 * 开发环境 - 负载均衡器栈配置
 */

import { LoadBalancerConfig } from '../../stacks';
import { generateResourceName, defaultPorts } from '../../shared';

const env = 'dev';

export const loadBalancerConfig: LoadBalancerConfig = {
  enabled: true,
  public: {
    enabled: true,
    name: generateResourceName('yuanhui-public-alb', env),
    enableWafAssociation: false, // 开发环境关闭WAF
  },
  ssl: {
    enableHttpsRedirect: false, // 开发环境关闭HTTPS重定向，因为HTTPS监听器未启用
    certificateArns: [], // 开发环境证书ARN，待用户提供
  },
  listeners: {
    http: {
      port: defaultPorts.http,
      enabled: true,
    },
    https: {
      port: defaultPorts.https,
      enabled: false, // 开发环境暂时关闭HTTPS，等证书ARN配置后启用
    },
  },
  accessLogs: {
    enabled: false, // 开发环境关闭访问日志以节省成本
  },
};