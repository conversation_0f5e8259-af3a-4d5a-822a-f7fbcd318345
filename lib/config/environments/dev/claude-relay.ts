/**
 * 开发环境 - <PERSON>栈配置
 */

import { ClaudeRelayConfig } from '../../stacks';
import { 
  getResourceByEnv, 
  getRetentionDaysByEnv, 
  generateDomain, 
  defaultPorts 
} from '../../shared';

const env = 'dev';
const resourceLimits = getResourceByEnv(env, 'medium');
const logRetention = getRetentionDaysByEnv(env, 'logs');

export const claudeRelayConfig: ClaudeRelayConfig = {
  enabled: true, // 启用以测试内置Redis功能
  service: {
    name: 'claude-relay',
    port: defaultPorts.claudeRelay,
    healthCheckPath: '/health',
  },
  container: {
    image: 'weishaw/claude-relay-service',
    tag: 'latest',
    cpu: resourceLimits.cpu,
    memory: resourceLimits.memory,
  },
  ecs: {
    desiredCount: 1,
    minCapacity: 1,
    maxCapacity: 2,
    enableAutoScaling: false, // 开发环境关闭自动扩容
  },
  routing: {
    domain: generateDomain('claude-relay', env, 'kh2u.com'),
    pathPatterns: [], // 不再使用路径模式，改用域名路由
    priority: 100,
  },
  environment: {
    NODE_ENV: 'production',
    LOG_LEVEL: 'info',
    ADMIN_USER: 'admin',
    ADMIN_PASSWORD: 'admin123',
  },
  secrets: {
    jwtSecretName: `claude-relay-jwt-secret-${env}`,
    encryptionKeyName: `claude-relay-encryption-key-${env}`,
  },
  healthCheck: {
    healthyHttpCodes: '200,404',
    interval: 30,
    timeout: 10,
    healthyThresholdCount: 2,
    unhealthyThresholdCount: 5,
  },
  logging: {
    retentionDays: logRetention,
    logGroupName: `/aws/ecs/claude-relay-${env}`,
  },
  monitoring: {
    enableDetailedMonitoring: false,
    enableXRayTracing: false,
  },

  // 内置Redis配置
  redis: {
    service: {
      name: 'redis',
      port: 6379,
      memory: 128, // 128MB内存限制
      cpu: 64,     // 64 CPU单位
    },
    container: {
      image: 'redis',
      tag: '7.2-alpine',
    },
    persistence: {
      enabled: true,
      dataPath: '/data',
      efsPath: 'claude-relay/redis', // 相对于/ecs的路径
      enableAof: true,
      enableRdb: true,
      aofSyncPolicy: 'everysec',
    },
    serverConfig: {
      maxMemory: '100mb',
      maxMemoryPolicy: 'allkeys-lru',
      databases: 16,
      tcpKeepAlive: 300,
      timeout: 0,
      tcpBacklog: 511,
      logLevel: 'warning', // 内部使用，仅记录警告和错误
    },
    healthCheck: {
      interval: 30,
      timeout: 5,
      retries: 3,
      startPeriod: 15,
    },
    logging: {
      logGroupName: `/aws/ecs/claude-relay-redis-${env}`, // Redis基本日志记录
      retentionDays: 1, // 开发环境保留1天
    },
    monitoring: {
      enableMetrics: false,        // 禁用指标监控
      enableSlowLog: false,        // 禁用慢查询日志
      slowLogMaxLen: 0,           // 设置为0禁用
    },
  },
};