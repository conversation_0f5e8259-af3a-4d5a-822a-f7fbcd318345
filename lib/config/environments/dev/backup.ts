/**
 * 开发环境 - 备份栈配置
 * 采用低成本策略，优化开发环境成本
 */

import { BackupConfig } from '../../stacks';
import { defaultEfsBackupSettings } from '../../shared';

const env = 'dev';
const efsDefaults = defaultEfsBackupSettings[env];

export const backupConfig: BackupConfig = {
  enabled: true,
  
  // 预定义备份策略
  policies: {
    // 低成本策略 - 开发环境默认使用
    lowCost: {
      daily: {
        retention: efsDefaults.dailyBackupRetention,
        schedule: efsDefaults.backupSchedule.daily,
        moveToColdStorageAfter: efsDefaults.moveToColdStorageAfterDays,
      },
      enableContinuousBackup: efsDefaults.enableContinuousBackup,
    },
    
    // 标准策略 - 备用
    standard: {
      daily: {
        retention: 7, // 1周保留
        schedule: { hour: 2, minute: 0 },
        moveToColdStorageAfter: 3,
      },
      weekly: {
        retention: 30, // 1个月保留
        schedule: { weekDay: 1, hour: 3, minute: 0 }, // 每周一凌晨3点
        moveToColdStorageAfter: 7,
      },
      enableContinuousBackup: false,
    },
    
    // 企业级策略 - 开发环境不使用，但保留接口完整性
    enterprise: {
      daily: {
        retention: 30,
        schedule: { hour: 1, minute: 0 },
        moveToColdStorageAfter: 7,
      },
      weekly: {
        retention: 90,
        schedule: { weekDay: 7, hour: 2, minute: 0 }, // 每周日凌晨2点
        moveToColdStorageAfter: 14,
      },
      enableContinuousBackup: false,
    },
  },
  
  // 资源备份配置
  resources: {
    // EFS 备份配置
    efs: {
      enabled: efsDefaults.enabled,
      policy: 'lowCost', // 开发环境使用低成本策略
      customSettings: {
        performanceMode: 'generalPurpose',
      },
    },
    
    // 数据库备份配置
    database: {
      enabled: true,
      policy: 'lowCost', // 开发环境使用低成本策略
      customSettings: {
        skipFinalSnapshot: true, // 开发环境跳过最终快照
      },
    },
  },
  
  // 全局设置
  defaultSettings: {
    backupVaultName: `yuanhui-backup-vault-${env}`,
    enableEncryption: false, // 开发环境不加密以降低成本
    deletionProtection: false, // 开发环境不启用删除保护
  },
  
  // 监控和告警配置
  monitoring: {
    enableAlerts: true,
    alertOnFailure: true, // 备份失败时告警
    alertOnSuccess: false, // 开发环境不需要成功告警
  },
};