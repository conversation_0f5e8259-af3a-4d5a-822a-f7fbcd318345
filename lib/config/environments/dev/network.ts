/**
 * 开发环境 - 网络栈配置
 */

import { NetworkConfig } from '../../stacks';
import { 
  getVpcConfigByEnv, 
  getSecuritySettingsByEnv, 
  generateApplicationDomains 
} from '../../shared';

const env = 'dev';
const vpcConfig = getVpcConfigByEnv(env);
const securitySettings = getSecuritySettingsByEnv(env);
const applicationDomains = generateApplicationDomains(env);

export const networkConfig: NetworkConfig = {
  enabled: true,
  vpc: {
    cidr: vpcConfig.cidr,
    maxAzs: vpcConfig.maxAzs,
    natGateways: vpcConfig.natGateways,
    useFixedNatGatewayEIPs: true,
  },
  domains: {
    primary: {
      zoneName: 'kh2u.com',
    },
    applications: applicationDomains,
  },
  ssl: {
    enableAutoManagement: true,
    validationMethod: 'DNS',
    certificateDomains: [
      '*.kh2u.com',
      'j2mall.tw',
    ],
  },
  waf: {
    enabled: securitySettings.enableWaf,
    rules: {
      enableAWSManagedRules: true,
      enableIPWhitelist: false,
      whitelistIPs: [],
      enableGeoBlocking: false,
      blockedCountries: [],
      enableRateLimit: true,
      rateLimit: {
        limit: 1000,
        windowSize: 300,
      },
    },
  },
  openziti: {
    enabled: securitySettings.enableOpenziti,
    controller: {
      endpoint: 'ziti-controller-dev.kh2u.com',
      port: 443,
    },
    network: {
      name: 'yuanhui-dev-network',
      cidr: '**********/16',
    },
  },
};