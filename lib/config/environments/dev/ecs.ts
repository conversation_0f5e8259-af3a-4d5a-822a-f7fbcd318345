/**
 * 开发环境 - ECS栈配置
 */

import { EcsConfig } from '../../stacks';
import { getResourceByEnv } from '../../shared';

const env = 'dev';
export const ecsConfig: EcsConfig = {
  enabled: true,
  
  // 容量提供商配置 - 开发环境完全使用ARM架构
  capacityProviders: [
    {
      name: 'arm64-capacity-provider',
      instanceType: 'm6g.large',
      architecture: 'arm64',
      weight: 100,
      base: 1,
      spotEnabled: false,
      minCapacity: 0,
      maxCapacity: 2,
      desiredCapacity: 1,
      targetCapacityPercent: 95,
    }
  ],
  
  // 默认容量提供商策略
  defaultCapacityProviderStrategy: [
    {
      capacityProvider: 'arm64-capacity-provider',
      weight: 100,
      base: 1,
    }
  ],
  
  // EFS共享存储配置
  efs: {
    enabled: true,
    fileSystemName: `yuanhui-ecs-shared-${env}`,
    performanceMode: 'generalPurpose',
    throughputMode: 'bursting',
    encryptInTransit: true,
    encryptAtRest: false, // 开发环境不加密以降低成本
  },
};