/**
 * 开发环境 - Airflow栈配置
 */

import { AirflowConfig } from '../../stacks';
import { getResourceByEnv, getStorageSizeByEnv, defaultPorts, generateDomain } from '../../shared';

const env = 'dev';
const webserverResources = getResourceByEnv(env, 'medium');
const schedulerResources = getResourceByEnv(env, 'small');
const workerResources = getResourceByEnv(env, 'medium');
const logsStorage = getStorageSizeByEnv(env, 'small');
const dagsStorage = getStorageSizeByEnv(env, 'small');

export const airflowConfig: AirflowConfig = {
  enabled: true,
  version: 'latest',
  image: {
    useCustomImage: false,
    // url: 'my-registry.com/custom-airflow:2.8.0-slim', // 使用第三方镜像URL（可选）
    // url: 'quay.io/apache/airflow:2.8.0-python3.11', // 使用不同仓库的官方镜像（可选）
    customImageTag: 'dev-latest',
    customImageRepo: 'yherp-airflow',
    includeLatestAmazonProvider: true,
  },
  dagBundles: [
    {
      name: 'local_dags',
      classpath: 'airflow.dag_processing.bundles.local.LocalDagBundle',
      kwargs: {
        local_folder: '/opt/airflow/dags',
      },
    },
    {
      name: 'git_dags',
      classpath: 'airflow.providers.git.bundles.git.GitDagBundle',
      kwargs: {
        tracking_ref: 'develop',
        git_conn_id: 'git_default',
      },
    },
  ],
  webserver: {
    cpu: webserverResources.cpu,
    memory: webserverResources.memory,
    replicas: 1,
    port: defaultPorts.airflow,
  },
  scheduler: {
    cpu: schedulerResources.cpu,
    memory: schedulerResources.memory,
    replicas: 1,
  },
  worker: {
    cpu: workerResources.cpu,
    memory: workerResources.memory,
    replicas: 1,
  },
  logsStorageSize: logsStorage,
  dagsStorageSize: dagsStorage,
  executor: 'LocalExecutor',
  routing: {
    domain: generateDomain('airflow', env, 'kh2u.com'),
    priority: 300, // ALB 路由优先级
  },
  environmentVariables: {
    // 基本配置
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true',
    AIRFLOW__CORE__LOAD_EXAMPLES: 'false',
    
    // Simple Auth Manager 认证配置（开发环境使用简单设置）
    AIRFLOW__CORE__AUTH_MANAGER: 'airflow.auth.managers.simple.simple_auth_manager.SimpleAuthManager',
    AIRFLOW__CORE__SIMPLE_AUTH_MANAGER_USERS: 'dev_admin:admin,dev_user:user',
    
    // 开发环境可以暴露配置页面用于调试
    AIRFLOW__API__EXPOSE_CONFIG: 'true',
  },
  connections: {
    aws: {
      connectionId: 'aws_default',
      connectionType: 'aws',
    },
  },
};