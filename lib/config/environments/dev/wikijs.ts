/**
 * 开发环境 - Wiki.js 栈配置
 */

import { WikiJSConfig } from '../../stacks';
import { 
  getResourceByEnv, 
  getRetentionDaysByEnv, 
  generateDomain, 
  defaultPorts 
} from '../../shared';

const env = 'dev';
const resourceLimits = getResourceByEnv(env, 'medium');
const logRetention = getRetentionDaysByEnv(env, 'logs');

export const wikijsConfig: WikiJSConfig = {
  enabled: false, // 默认关闭，需要手动启用
  
  service: {
    name: 'wikijs',
    port: defaultPorts.wikijs,
    healthCheckPath: '/healthz',
  },
  
  container: {
    image: 'requarks/wiki',
    tag: '2.5',
    cpu: resourceLimits.cpu,
    memory: resourceLimits.memory,
  },
  
  ecs: {
    desiredCount: 1,
    minCapacity: 1,
    maxCapacity: 2,
    enableAutoScaling: false, // 开发环境关闭自动扩容
  },
  
  routing: {
    domain: generateDomain('wiki', env, 'kh2u.com'),
    pathPatterns: [],
    priority: 110,
  },
  
  database: {
    // 必须提供包含数据库连接信息的 Secrets Manager ARN
    // 密钥应包含字段: host, port, username, password (不包含dbname)
    // 示例格式: arn:aws:secretsmanager:region:account:secret:name-randomstring
    secretArn: process.env.WIKIJS_DATABASE_SECRET_ARN || '', 
    type: 'postgres',
    dbName: 'wiki', // 数据库名称
    ssl: false, // 开发环境不强制SSL
    pool: {
      min: 2,
      max: 10,
    },
  },
  
  environment: {
    NODE_ENV: 'development',
  },
  
  // Wiki.js不需要额外的secrets配置
  
  healthCheck: {
    healthyHttpCodes: '200',
    interval: 30,
    timeout: 5,
    healthyThresholdCount: 2,
    unhealthyThresholdCount: 3,
  },
  
  logging: {
    retentionDays: logRetention,
    logGroupName: `/aws/ecs/wikijs-${env}`,
  },
  
  // 简化配置，使用默认值
};