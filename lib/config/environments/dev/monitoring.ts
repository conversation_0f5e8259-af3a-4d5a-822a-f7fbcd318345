/**
 * 开发环境 - 监控栈配置
 */

import { MonitoringConfig } from '../../stacks';
import { getRetentionDaysByEnv, getSecuritySettingsByEnv } from '../../shared';

const env = 'dev';
const logRetention = getRetentionDaysByEnv(env, 'logs');
const securitySettings = getSecuritySettingsByEnv(env);

export const monitoringConfig: MonitoringConfig = {
  enabled: true,
  enableDetailedMonitoring: securitySettings.enableDetailedMonitoring,
  logRetentionDays: logRetention,
  // 成本监控配置
  costMonitoring: {
    enabled: true,
    budgetAlerts: [
      {
        name: 'dev-monthly-budget',
        amount: 50, // 开发环境月预算50美元
        threshold: 80, // 80%时告警
        subscribers: [], // 添加邮箱地址
      },
      {
        name: 'dev-weekly-budget',
        amount: 15, // 周预算15美元
        threshold: 90, // 90%时告警
        subscribers: [],
      }
    ],
    // ECS成本优化监控
    ecsOptimization: {
      trackInstanceUtilization: true,
      alertOnLowUtilization: true, // 低利用率告警
      lowUtilizationThreshold: 20, // 低于20%利用率告警
    }
  }
};