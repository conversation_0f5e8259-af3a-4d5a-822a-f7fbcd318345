import * as cdk from 'aws-cdk-lib';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import { LoadBalancerStack } from './load-balancer-stack';

export interface RabbitMQStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  cluster: ecs.Cluster;
  serviceConnectNamespace: servicediscovery.HttpNamespace;
  loadBalancerStack?: LoadBalancerStack;
  sharedEfsFileSystem: efs.FileSystem;
}

/**
 * RabbitMQ消息队列栈
 * 包含RabbitMQ容器服务、持久化存储和管理界面
 */
export class RabbitMQStack extends cdk.Stack {
  public readonly rabbitmqService: ecs.Ec2Service;
  public readonly rabbitmqSecret: secretsmanager.Secret;
  public rabbitmqTargetGroup?: elbv2.ApplicationTargetGroup;
  public readonly rabbitmqEndpoint: string;
  
  private dataAccessPoint: efs.AccessPoint;

  constructor(scope: Construct, id: string, props: RabbitMQStackProps) {
    super(scope, id, props);

    const { config, vpc, cluster, serviceConnectNamespace, loadBalancerStack, sharedEfsFileSystem } = props;

    // 创建RabbitMQ凭证
    this.rabbitmqSecret = new secretsmanager.Secret(this, 'RabbitMQSecret', {
      description: 'RabbitMQ credentials',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({
          username: 'admin',
          vhost: '/',
        }),
        generateStringKey: 'password',
        includeSpace: false,
        excludeUppercase: false,
        excludeLowercase: false,
        excludeNumbers: false,
        excludePunctuation: true,
        passwordLength: 32,
      },
    });

    // 创建RabbitMQ数据专用的EFS访问点
    this.createRabbitMQDataAccessPoint(sharedEfsFileSystem);

    // 创建RabbitMQ任务定义
    const taskDefinition = this.createRabbitMQTaskDefinition(config, sharedEfsFileSystem);

    // 获取ECS安全组（从网络栈导入）
    const ecsSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'ImportedEcsSecurityGroup',
      cdk.Fn.importValue(`${config.environment}-ecs-security-group-id`)
    ) as ec2.SecurityGroup;

    // 创建RabbitMQ服务（使用AWSVPC网络模式）
    this.rabbitmqService = new ecs.Ec2Service(this, 'RabbitMQService', {
      cluster,
      taskDefinition,
      desiredCount: config.rabbitmq.replicas,
      serviceName: `rabbitmq-${config.environment}`,
      enableExecuteCommand: true,
      // AWSVPC网络模式配置
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
      securityGroups: [ecsSecurityGroup],
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        services: [
          {
            portMappingName: 'rabbitmq-amqp',
            dnsName: 'rabbitmq',
            port: 5672,
          },
          {
            portMappingName: 'rabbitmq-management',
            dnsName: 'rabbitmq-management',
            port: config.rabbitmq.managementPort,
          },
        ],
      },
    });

    // 设置RabbitMQ端点
    this.rabbitmqEndpoint = `rabbitmq.${serviceConnectNamespace.namespaceName}:5672`;

    // 如果启用管理界面且配置了域名，集成到主ALB
    if (config.rabbitmq.enableManagementUI && config.rabbitmq.routing.domain && loadBalancerStack) {
      this.integrateWithMainALB(config, vpc, loadBalancerStack);
    }

    // 输出重要信息
    new cdk.CfnOutput(this, 'RabbitMQEndpoint', {
      value: this.rabbitmqEndpoint,
      description: 'RabbitMQ AMQP endpoint',
      exportName: `${config.environment}-rabbitmq-endpoint`,
    });

    new cdk.CfnOutput(this, 'RabbitMQSecretArn', {
      value: this.rabbitmqSecret.secretArn,
      description: 'RabbitMQ credentials secret ARN',
      exportName: `${config.environment}-rabbitmq-secret-arn`,
    });

    if (config.rabbitmq.routing.domain) {
      // 根据环境和SSL配置确定协议
      const protocol = loadBalancerStack?.publicHttpsListener ? 'https' : 'http';
      new cdk.CfnOutput(this, 'RabbitMQManagementURL', {
        value: `${protocol}://${config.rabbitmq.routing.domain}`,
        description: 'RabbitMQ Management UI URL',
        exportName: `${config.environment}-rabbitmq-management-url`,
      });
    }
  }

  private createRabbitMQTaskDefinition(config: EnvironmentConfig, sharedEfsFileSystem: efs.FileSystem): ecs.Ec2TaskDefinition {
    const taskDefinition = new ecs.Ec2TaskDefinition(this, 'RabbitMQTaskDefinition', {
      family: `rabbitmq-${config.environment}`,
      networkMode: ecs.NetworkMode.AWS_VPC, // 使用AWSVPC网络模式
    });

    // 添加EFS数据卷（使用Access Point）
    taskDefinition.addVolume({
      name: 'rabbitmq-data',
      efsVolumeConfiguration: {
        fileSystemId: sharedEfsFileSystem.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.dataAccessPoint.accessPointId,
        },
      },
    });

    // 配置文件已通过环境变量配置，无需EFS配置卷

    // 为任务角色添加EFS访问权限
    taskDefinition.taskRole.addManagedPolicy(
      iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonElasticFileSystemClientFullAccess')
    );

    // 添加EFS根目录访问权限（必需的，允许容器访问Access Point指定的目录）
    sharedEfsFileSystem.grantRootAccess(taskDefinition.taskRole);

    // RabbitMQ容器配置 - 完全使用环境变量配置
    const container = taskDefinition.addContainer('RabbitMQContainer', {
      image: ecs.ContainerImage.fromRegistry('rabbitmq:4.1.2-management-alpine'),
      cpu: config.rabbitmq.cpu,
      memoryReservationMiB: config.rabbitmq.memory,
      environment: {
        // 基本配置（官方支持的核心环境变量）
        RABBITMQ_DEFAULT_VHOST: '/',
        RABBITMQ_USE_LONGNAME: 'true',
        
        // 启用管理插件（使用官方推荐方式）
        RABBITMQ_PLUGINS: 'rabbitmq_management',
        
        // 日志配置
        RABBITMQ_LOG_LEVEL: 'info',
      },
      secrets: {
        RABBITMQ_DEFAULT_USER: ecs.Secret.fromSecretsManager(this.rabbitmqSecret, 'username'),
        RABBITMQ_DEFAULT_PASS: ecs.Secret.fromSecretsManager(this.rabbitmqSecret, 'password'),
      },
      portMappings: [
        {
          name: 'rabbitmq-management',
          containerPort: config.rabbitmq.managementPort,
          protocol: ecs.Protocol.TCP,
        },
        {
          name: 'rabbitmq-amqp',
          containerPort: 5672,
          protocol: ecs.Protocol.TCP,
        },
      ],
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'rabbitmq',
        logGroup: new logs.LogGroup(this, 'RabbitMQLogGroup', {
          logGroupName: `/aws/ecs/rabbitmq-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        // 使用RabbitMQ 2024年推荐的Stage 2健康检查：status命令包含ping检查且提供更多系统信息
        command: ['CMD-SHELL', 'rabbitmq-diagnostics -q status'],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(10), // 增加超时时间，因为status命令比ping命令需要更多时间
        retries: 3,
        startPeriod: cdk.Duration.seconds(90), // 增加启动期，给RabbitMQ更多初始化时间
      },
    });

    // 添加数据卷挂载
    container.addMountPoints({
      sourceVolume: 'rabbitmq-data',
      containerPath: '/var/lib/rabbitmq',
      readOnly: false,
    });

    // 配置文件通过环境变量配置，无需挂载配置卷

    return taskDefinition;
  }


  private integrateWithMainALB(config: EnvironmentConfig, vpc: ec2.Vpc, loadBalancerStack: LoadBalancerStack): void {
    // 创建RabbitMQ管理界面的目标组 - AWSVPC模式
    this.rabbitmqTargetGroup = new elbv2.ApplicationTargetGroup(this, 'RabbitMQTargetGroup', {
      vpc,
      port: config.rabbitmq.managementPort,  // 使用配置的管理端口
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.IP, // AWSVPC模式使用IP目标类型
      targetGroupName: `rabbitmq-tg-${config.environment}`,
      healthCheck: {
        enabled: true,
        // 使用RabbitMQ Management API的健康检查端点
        path: '/api/health/checks/alarms', // 更精确的健康检查，检查节点告警状态
        port: 'traffic-port',
        protocol: elbv2.Protocol.HTTP,
        healthyThresholdCount: 2,
        unhealthyThresholdCount: 5,
        timeout: cdk.Duration.seconds(10),
        interval: cdk.Duration.seconds(30),
        healthyHttpCodes: '200,401', // 401是因为没有认证，但服务是健康的
      },
      deregistrationDelay: cdk.Duration.seconds(30),
    });

    // 获取RabbitMQ域名
    const rabbitmqDomain = config.rabbitmq.routing.domain;

    // 创建监听器规则 - 使用域名路由（仅当配置了域名时）
    if (rabbitmqDomain) {
      const hostHeaders = [rabbitmqDomain];

      // HTTP 监听器路由
      // 注意：只有在未启用 HTTPS 重定向时才添加，因为启用重定向时，
      // LoadBalancerStack 会将所有 HTTP 请求全局重定向到 HTTPS
      if (loadBalancerStack.publicHttpListener && !config.loadBalancer.ssl.enableHttpsRedirect) {
        new elbv2.ApplicationListenerRule(this, 'RabbitMQHttpRule', {
          listener: loadBalancerStack.publicHttpListener,
          priority: config.rabbitmq.routing.priority,
          conditions: [elbv2.ListenerCondition.hostHeaders(hostHeaders)],
          action: elbv2.ListenerAction.forward([this.rabbitmqTargetGroup]),
        });
      }

      // HTTPS 监听器路由（生产环境的主要路由）
      // 当启用 HTTPS 重定向时，所有流量最终都会到达这里
      if (loadBalancerStack.publicHttpsListener) {
        new elbv2.ApplicationListenerRule(this, 'RabbitMQHttpsRule', {
          listener: loadBalancerStack.publicHttpsListener,
          priority: config.rabbitmq.routing.priority,
          conditions: [elbv2.ListenerCondition.hostHeaders(hostHeaders)],
          action: elbv2.ListenerAction.forward([this.rabbitmqTargetGroup]),
        });
      }
    }

    // 将服务附加到目标组 - 明确指定容器名称和端口
    this.rabbitmqTargetGroup.addTarget(this.rabbitmqService.loadBalancerTarget({
      containerName: 'RabbitMQContainer',
      containerPort: config.rabbitmq.managementPort,
    }));

    // 输出目标组ARN
    new cdk.CfnOutput(this, 'RabbitMQTargetGroupArn', {
      value: this.rabbitmqTargetGroup.targetGroupArn,
      description: 'RabbitMQ Management Target Group ARN',
      exportName: `${config.environment}-rabbitmq-target-group-arn`,
    });
  }

  /**
   * 为RabbitMQ创建专用的EFS数据访问点，使用共享EFS的子目录
   * 目录结构: /ecs/rabbitmq/data/
   */
  private createRabbitMQDataAccessPoint(sharedEfsFileSystem: efs.FileSystem): void {
    // 创建RabbitMQ数据访问点 - 使用共享EFS的rabbitmq/data子目录
    this.dataAccessPoint = sharedEfsFileSystem.addAccessPoint('RabbitMQDataAccessPoint', {
      path: `/ecs/rabbitmq/data`,
      posixUser: {
        uid: '100', // rabbitmq 用户 ID（Alpine版本）
        gid: '101', // rabbitmq 组 ID（Alpine版本）
      },
      createAcl: {
        ownerUid: '100', // rabbitmq 用户 ID（Alpine版本）
        ownerGid: '101', // rabbitmq 组 ID（Alpine版本）
        permissions: '755', // 更安全的权限设置
      },
    });
  }
}
