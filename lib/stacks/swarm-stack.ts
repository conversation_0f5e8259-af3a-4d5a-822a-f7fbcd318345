import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';

export interface SwarmStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
}

/**
 * Docker Swarm 集群栈
 * 创建3台EC2实例，Swarm集群配置由Ansible脚本完成：1台Manager（公网）+ 2台Worker（私网）
 */
export class SwarmStack extends cdk.Stack {
  public readonly swarmSecurityGroup: ec2.SecurityGroup;
  public readonly swarmRole: iam.Role;
  public readonly managerInstance: ec2.Instance;
  public readonly managerEIP?: ec2.CfnEIP;
  public readonly workerInstances: ec2.Instance[];
  public efsSecurityGroup?: ec2.SecurityGroup;
  public readonly efsFileSystems: efs.FileSystem[] = [];

  constructor(scope: Construct, id: string, props: SwarmStackProps) {
    super(scope, id, props);

    const { config, vpc } = props;
    const swarmConfig = config.swarm;

    if (!swarmConfig.enabled) {
      return;
    }

    // 注释：Swarm join tokens 将由 Ansible 脚本管理，不在此处创建

    // 创建 Swarm 专用安全组
    this.swarmSecurityGroup = new ec2.SecurityGroup(this, 'SwarmSecurityGroup', {
      vpc,
      description: 'Security group for Docker Swarm cluster',
      allowAllOutbound: true,
    });

    // SSH 访问
    this.swarmSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(swarmConfig.ports.ssh),
      'SSH access'
    );

    // Swarm 管理端口 (Manager 节点)
    this.swarmSecurityGroup.addIngressRule(
      this.swarmSecurityGroup,
      ec2.Port.tcp(swarmConfig.ports.swarmManagement),
      'Docker Swarm cluster management'
    );

    // Swarm 节点通信端口 (TCP/UDP)
    this.swarmSecurityGroup.addIngressRule(
      this.swarmSecurityGroup,
      ec2.Port.tcp(swarmConfig.ports.swarmCommunication),
      'Docker Swarm container network discovery (TCP)'
    );

    this.swarmSecurityGroup.addIngressRule(
      this.swarmSecurityGroup,
      ec2.Port.udp(swarmConfig.ports.swarmCommunication),
      'Docker Swarm container network discovery (UDP)'
    );

    // Swarm 覆盖网络端口 (UDP)
    this.swarmSecurityGroup.addIngressRule(
      this.swarmSecurityGroup,
      ec2.Port.udp(swarmConfig.ports.swarmOverlay),
      'Docker Swarm overlay network traffic'
    );

    // 自定义应用端口
    if (swarmConfig.ports.customPorts) {
      swarmConfig.ports.customPorts.forEach((port) => {
        this.swarmSecurityGroup.addIngressRule(
          ec2.Peer.anyIpv4(),
          ec2.Port.tcp(port),
          `Custom application port ${port}`
        );
      });
    }

    // 创建 IAM 角色
    this.swarmRole = new iam.Role(this, 'SwarmNodeRole', {
      roleName: `${swarmConfig.iamRole.roleName}-${config.environment}`,
      assumedBy: new iam.ServicePrincipal('ec2.amazonaws.com'),
      description: `IAM role for Docker Swarm nodes in ${config.environment}`,
    });

    // 添加托管策略
    swarmConfig.iamRole.policies.forEach(policyName => {
      this.swarmRole.addManagedPolicy(
        iam.ManagedPolicy.fromAwsManagedPolicyName(policyName)
      );
    });

    // 添加必要的Docker和ECR权限
    this.swarmRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'ecr:GetAuthorizationToken',
        'ecr:BatchCheckLayerAvailability',
        'ecr:GetDownloadUrlForLayer',
        'ecr:BatchGetImage',
        'ecr:DescribeRepositories',
        'ecr:ListImages',
      ],
      resources: ['*'],
    }));

    // 添加Docker Hub和其他容器注册表访问权限
    this.swarmRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
        'logs:DescribeLogGroups',
        'logs:DescribeLogStreams',
      ],
      resources: ['*'],
    }));

    // 注释：Secrets Manager 权限由 Ansible 脚本管理，如需要可单独配置

    // 添加 EC2 描述权限用于服务发现
    this.swarmRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'ec2:DescribeInstances',
        'ec2:DescribeTags',
      ],
      resources: ['*'],
    }));

    // 如果启用EFS，添加EFS访问权限
    if (swarmConfig.efs?.enabled) {
      this.swarmRole.addToPolicy(new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          // EFS文件系统和挂载点管理权限
          'elasticfilesystem:DescribeFileSystems',
          'elasticfilesystem:DescribeAccessPoints',
          'elasticfilesystem:DescribeMountTargets',
          'elasticfilesystem:CreateAccessPoint',
          'elasticfilesystem:DeleteAccessPoint',
          'elasticfilesystem:ModifyAccessPoint',
          'elasticfilesystem:DescribeBackupPolicy',
          'elasticfilesystem:DescribeFileSystemPolicy',
          
          // EFS客户端读写权限（通过Access Point）
          'elasticfilesystem:ClientRootAccess',
          'elasticfilesystem:ClientMount',
          'elasticfilesystem:ClientWrite',
          'elasticfilesystem:ClientRead',
        ],
        resources: ['*'],
      }));

      // 创建EFS文件系统
      this.createEfsFileSystems(config, vpc);
    }

    // 创建实例配置文件
    new iam.CfnInstanceProfile(this, 'SwarmInstanceProfile', {
      roles: [this.swarmRole.roleName],
      instanceProfileName: `${swarmConfig.iamRole.roleName}-profile-${config.environment}`,
    });

    // 获取最新的 Amazon Linux 2023 AMI
    const machineImage = ec2.MachineImage.latestAmazonLinux2023({
      cachedInContext: true,
    });

    // 创建基础用户数据（仅安装Docker，不初始化Swarm）
    const managerUserData = this.createBasicUserData(config);
    const keyPair = ec2.KeyPair.fromKeyPairName(this, "KeyPair", swarmConfig.keyPairName);

    // 创建 Manager 节点
    this.managerInstance = new ec2.Instance(this, 'SwarmManager', {
      vpc,
      instanceType: new ec2.InstanceType(swarmConfig.nodes.manager.instanceType),
      machineImage,
      vpcSubnets: {
        subnetType: swarmConfig.nodes.manager.subnetType === 'public' 
          ? ec2.SubnetType.PUBLIC 
          : ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
      securityGroup: this.swarmSecurityGroup,
      keyPair: keyPair,
      role: this.swarmRole,
      userData: managerUserData,
      blockDevices: [{
        deviceName: '/dev/xvda',
        volume: ec2.BlockDeviceVolume.ebs(swarmConfig.storage.volumeSize, {
          volumeType: ec2.EbsDeviceVolumeType.GP2,
          encrypted: swarmConfig.storage.encrypted,
        }),
      }],
    });

    // 创建Swarm Manager的独立EIP (如果启用)
    if (swarmConfig.useFixedEIP) {
      this.managerEIP = new ec2.CfnEIP(this, 'SwarmManagerEIP', {
        domain: 'vpc',
        tags: [
          { key: 'Name', value: `Swarm-Manager-${config.environment}` },
          { key: 'Environment', value: config.environment },
          { key: 'Project', value: 'YuanhuiOdoo' },
          { key: 'Purpose', value: 'Swarm Manager Fixed IP' }
        ]
      });

      // 关联独立EIP到Manager实例
      new ec2.CfnEIPAssociation(this, 'ManagerEIPAssociation', {
        instanceId: this.managerInstance.instanceId,
        allocationId: this.managerEIP.attrAllocationId,
      });
    }

    // 添加标签到 Manager 节点
    cdk.Tags.of(this.managerInstance).add('SwarmRole', 'manager');
    cdk.Tags.of(this.managerInstance).add('SwarmCluster', swarmConfig.clusterName);

    // Worker节点使用相同的基础用户数据
    const workerUserData = this.createBasicUserData(config);

    // 创建 Worker 节点
    this.workerInstances = [];
    for (let i = 0; i < swarmConfig.nodes.workers.count; i++) {
      const workerInstance = new ec2.Instance(this, `SwarmWorker${i + 1}`, {
        vpc,
        instanceType: new ec2.InstanceType(swarmConfig.nodes.workers.instanceType),
        machineImage,
        vpcSubnets: {
          subnetType: swarmConfig.nodes.workers.subnetType === 'public' 
            ? ec2.SubnetType.PUBLIC 
            : ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        securityGroup: this.swarmSecurityGroup,
        keyPair: keyPair,
        role: this.swarmRole,
        userData: workerUserData,
        blockDevices: [{
          deviceName: '/dev/xvda',
          volume: ec2.BlockDeviceVolume.ebs(swarmConfig.storage.volumeSize, {
            volumeType: ec2.EbsDeviceVolumeType.GP2,
            encrypted: swarmConfig.storage.encrypted,
          }),
        }],
      });

      // 添加标签到 Worker 节点
      cdk.Tags.of(workerInstance).add('SwarmRole', 'worker');
      cdk.Tags.of(workerInstance).add('SwarmCluster', swarmConfig.clusterName);
      this.workerInstances.push(workerInstance);
    }

    // 创建 CloudWatch 日志组
    if (swarmConfig.monitoring.enableCloudWatchAgent) {
      new logs.LogGroup(this, 'SwarmLogGroup', {
        logGroupName: `/aws/ec2/swarm/${config.environment}`,
        retention: logs.RetentionDays.ONE_MONTH,
        removalPolicy: cdk.RemovalPolicy.DESTROY,
      });
    }

    // 输出重要信息
    new cdk.CfnOutput(this, 'SwarmManagerInstanceId', {
      value: this.managerInstance.instanceId,
      description: 'Swarm Manager Instance ID',
      exportName: `${config.environment}-swarm-manager-instance-id`,
    });

    // 输出Manager EIP信息 (如果启用)
    if (swarmConfig.useFixedEIP && this.managerEIP) {
      new cdk.CfnOutput(this, 'SwarmManagerFixedEIP', {
        value: this.managerEIP.ref,
        description: 'Swarm Manager Fixed Public IP',
        exportName: `${config.environment}-swarm-manager-eip`,
      });

      new cdk.CfnOutput(this, 'SwarmManagerEIPAllocationIdOutput', {
        value: this.managerEIP.attrAllocationId,
        description: 'Swarm Manager EIP Allocation ID',
        exportName: `${config.environment}-swarm-manager-eip-allocation-id`,
      });
    }

    new cdk.CfnOutput(this, 'SwarmManagerPrivateIp', {
      value: this.managerInstance.instancePrivateIp,
      description: 'Swarm Manager Private IP',
      exportName: `${config.environment}-swarm-manager-private-ip`,
    });

    new cdk.CfnOutput(this, 'SwarmWorkerInstanceIds', {
      value: this.workerInstances.map(instance => instance.instanceId).join(','),
      description: 'Swarm Worker Instance IDs',
      exportName: `${config.environment}-swarm-worker-instance-ids`,
    });

    new cdk.CfnOutput(this, 'SwarmSecurityGroupId', {
      value: this.swarmSecurityGroup.securityGroupId,
      description: 'Swarm Security Group ID',
      exportName: `${config.environment}-swarm-security-group-id`,
    });

    // 输出EFS信息
    if (swarmConfig.efs?.enabled && this.efsFileSystems.length > 0) {
      new cdk.CfnOutput(this, 'EfsFileSystemIds', {
        value: this.efsFileSystems.map(fs => fs.fileSystemId).join(','),
        description: 'EFS File System IDs',
        exportName: `${config.environment}-swarm-efs-ids`,
      });

      new cdk.CfnOutput(this, 'EfsFileSystemDnsNames', {
        value: this.efsFileSystems.map(fs => `${fs.fileSystemId}.efs.${config.region}.amazonaws.com`).join(','),
        description: 'EFS File System DNS Names',
        exportName: `${config.environment}-swarm-efs-dns-names`,
      });

      if (this.efsSecurityGroup) {
        new cdk.CfnOutput(this, 'EfsSecurityGroupId', {
          value: this.efsSecurityGroup.securityGroupId,
          description: 'EFS Security Group ID',
          exportName: `${config.environment}-swarm-efs-security-group-id`,
        });
      }
    }

    // 注释：Join tokens 输出将由 Ansible 脚本管理

    // 添加栈标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'Swarm');
  }

  /**
   * 创建EFS文件系统
   */
  private createEfsFileSystems(config: EnvironmentConfig, vpc: ec2.Vpc): void {
    const swarmConfig = config.swarm;
    
    if (!swarmConfig.efs?.enabled || !swarmConfig.efs.fileSystems.length) {
      return;
    }

    // 创建EFS安全组
    this.efsSecurityGroup = new ec2.SecurityGroup(this, 'EfsSecurityGroup', {
      vpc,
      description: 'Security group for Swarm EFS file systems',
      allowAllOutbound: false,
    });

    // 允许来自Swarm安全组的NFS访问
    this.efsSecurityGroup.addIngressRule(
      this.swarmSecurityGroup,
      ec2.Port.tcp(2049),
      'Allow NFS access from Swarm cluster'
    );

    // 允许出站流量到VPC
    this.efsSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.allTraffic(),
      'Allow outbound traffic to VPC'
    );

    // 为每个配置的文件系统创建EFS
    swarmConfig.efs.fileSystems.forEach((fsConfig, index) => {
      const encrypted = fsConfig.encrypted ?? false;
      
      // 生命周期策略：开发环境7天，生产环境30天
      const lifecyclePolicy = config.environment === 'prod'
        ? efs.LifecyclePolicy.AFTER_30_DAYS
        : efs.LifecyclePolicy.AFTER_7_DAYS;

      // 根据multiAz配置选择子网
      const vpcSubnets = 
        { subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS };

      const fileSystem = new efs.FileSystem(this, `SwarmEfs${index + 1}`, {
        vpc,
        vpcSubnets,
        securityGroup: this.efsSecurityGroup,
        encrypted,
        performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
        throughputMode: efs.ThroughputMode.BURSTING,
        lifecyclePolicy,
        removalPolicy: config.environment === 'prod' 
          ? cdk.RemovalPolicy.RETAIN 
          : cdk.RemovalPolicy.DESTROY,
      });

      // 添加标签
      cdk.Tags.of(fileSystem).add('Name', `${swarmConfig.clusterName}-${fsConfig.name}`);
      cdk.Tags.of(fileSystem).add('Description', fsConfig.description);
      cdk.Tags.of(fileSystem).add('Environment', config.environment);
      cdk.Tags.of(fileSystem).add('Encrypted', encrypted.toString());

      this.efsFileSystems.push(fileSystem);
    });
  }

  /**
   * 创建基础用户数据脚本（仅安装Docker和基础工具，不初始化Swarm）
   * Swarm集群配置将由Ansible脚本完成
   */
  private createBasicUserData(config: EnvironmentConfig): ec2.UserData {
    const userData = ec2.UserData.forLinux();
    const swarmConfig = config.swarm;

    userData.addCommands(
      // 更新系统
      'yum update -y',
      
      // 安装 Docker
      'yum install -y docker',
      'systemctl start docker',
      'systemctl enable docker',
      'usermod -a -G docker ec2-user',
      
      // 安装 AWS CLI v2
      'curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"',
      'unzip awscliv2.zip',
      './aws/install',
      'rm -rf awscliv2.zip aws/',
      
      // 安装必要的工具
      'yum install -y jq git htop',
      
      // 安装EFS工具（如果启用了EFS）
      ...(swarmConfig.efs?.enabled ? [
        'yum install -y amazon-efs-utils',
        'systemctl enable amazon-efs-mount-watchdog',
        'systemctl start amazon-efs-mount-watchdog',
      ] : []),
      
      // 安装 CloudWatch Agent（如果启用）
      ...(swarmConfig.monitoring.enableCloudWatchAgent ? [
        'yum install -y amazon-cloudwatch-agent',
        'systemctl start amazon-cloudwatch-agent',
        'systemctl enable amazon-cloudwatch-agent',
      ] : []),
      
      // 创建部署完成标记
      'echo "Basic Docker node setup completed at $(date)" > /var/log/swarm-setup.log',
      'echo "Swarm cluster configuration will be handled by Ansible" >> /var/log/swarm-setup.log',
    );

    return userData;
  }
}