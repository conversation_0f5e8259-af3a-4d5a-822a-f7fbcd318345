import * as cdk from 'aws-cdk-lib';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';

export interface ServiceConnectStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
}

/**
 * ECS Service Connect命名空间栈
 * 为所有服务提供统一的服务发现和通信
 */
export class ServiceConnectStack extends cdk.Stack {
  public readonly namespace: servicediscovery.HttpNamespace;

  constructor(scope: Construct, id: string, props: ServiceConnectStackProps) {
    super(scope, id, props);

    const { config } = props;

    // 创建Service Connect命名空间
    this.namespace = new servicediscovery.HttpNamespace(this, 'ServiceConnectNamespace', {
      name: `${config.environment}-services`,
      description: `Service Connect namespace for ${config.environment} environment`,
    });

    // 输出命名空间信息
    new cdk.CfnOutput(this, 'ServiceConnectNamespaceName', {
      value: this.namespace.namespaceName,
      description: 'Service Connect namespace name',
      exportName: `${config.environment}-service-connect-namespace`,
    });

    new cdk.CfnOutput(this, 'ServiceConnectNamespaceId', {
      value: this.namespace.namespaceId,
      description: 'Service Connect namespace ID',
      exportName: `${config.environment}-service-connect-namespace-id`,
    });
  }
}
