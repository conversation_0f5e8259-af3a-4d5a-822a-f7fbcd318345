import * as cdk from 'aws-cdk-lib';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as cloudwatchActions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as snsSubscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as logs from 'aws-cdk-lib/aws-logs';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
// import { ApplicationStack } from './application-stack'; // 已移除
import { AuroraDatabaseStack } from './aurora-database-stack';
import { RabbitMQStack } from './rabbitmq-stack';
import { AirflowStack } from './airflow-stack';

import { LoadBalancerStack } from './load-balancer-stack';

export interface MonitoringStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  // applicationStack: ApplicationStack; // 已移除
  loadBalancerStack: LoadBalancerStack;
  databaseStack: AuroraDatabaseStack;
  rabbitmqStack?: RabbitMQStack;
  airflowStack?: AirflowStack;
}

/**
 * 监控和告警栈
 * 包含CloudWatch监控、告警和SNS通知
 */
export class MonitoringStack extends cdk.Stack {
  public readonly alarmTopic: sns.Topic;
  public readonly dashboard: cloudwatch.Dashboard;

  constructor(scope: Construct, id: string, props: MonitoringStackProps) {
    super(scope, id, props);

    const { config, /* applicationStack, */ loadBalancerStack, databaseStack, rabbitmqStack, airflowStack } = props;

    // 创建SNS主题用于告警通知
    this.alarmTopic = new sns.Topic(this, 'AlarmTopic', {
      topicName: `yuanhui-alarms-${config.environment}`,
      displayName: '元晖Odoo系统告警',
    });

    // 添加邮件订阅（需要手动确认）
    this.alarmTopic.addSubscription(
      new snsSubscriptions.EmailSubscription('<EMAIL>')
    );

    // 创建CloudWatch仪表板
    this.dashboard = new cloudwatch.Dashboard(this, 'YuanhuiDashboard', {
      dashboardName: `yuanhui-odoo-${config.environment}`,
    });

    // 设置应用监控
    // this.setupApplicationMonitoring(applicationStack, config); // ApplicationStack已移除

    // 设置数据库监控（支持Aurora和RDS）
    this.setupAuroraMonitoring(databaseStack, config);

    // 添加数据库监控到仪表板
    this.addDatabaseWidgetsToDashboard(databaseStack, config);

    // 设置负载均衡器监控
    this.setupLoadBalancerMonitoring(loadBalancerStack, config);

    // 设置自定义指标
    this.setupCustomMetrics(config);

    // 输出监控信息
    new cdk.CfnOutput(this, 'DashboardUrl', {
      value: `https://${config.region}.console.aws.amazon.com/cloudwatch/home?region=${config.region}#dashboards:name=${this.dashboard.dashboardName}`,
      description: 'CloudWatch Dashboard URL',
      exportName: `${config.environment}-dashboard-url`,
    });

    new cdk.CfnOutput(this, 'AlarmTopicArn', {
      value: this.alarmTopic.topicArn,
      description: 'SNS Alarm Topic ARN',
      exportName: `${config.environment}-alarm-topic-arn`,
    });

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'Monitoring');
  }

  /**
   * 设置应用监控
   */
  /* // ApplicationStack监控已移除
  private setupApplicationMonitoring(applicationStack: ApplicationStack, config: EnvironmentConfig): void {
    // ECS服务CPU使用率告警
    const yherpCpuAlarm = new cloudwatch.Alarm(this, 'YherpCpuAlarm', {
      metric: applicationStack.yherpService.metricCpuUtilization(),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'Yherp服务CPU使用率过高',
    });

    yherpCpuAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    const khmallCpuAlarm = new cloudwatch.Alarm(this, 'KhmallCpuAlarm', {
      metric: applicationStack.khmallService.metricCpuUtilization(),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'Khmall服务CPU使用率过高',
    });

    khmallCpuAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // ECS服务内存使用率告警
    const yherpMemoryAlarm = new cloudwatch.Alarm(this, 'YherpMemoryAlarm', {
      metric: applicationStack.yherpService.metricMemoryUtilization(),
      threshold: 85,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'Yherp服务内存使用率过高',
    });

    yherpMemoryAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    const khmallMemoryAlarm = new cloudwatch.Alarm(this, 'KhmallMemoryAlarm', {
      metric: applicationStack.khmallService.metricMemoryUtilization(),
      threshold: 85,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'Khmall服务内存使用率过高',
    });

    khmallMemoryAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // 添加到仪表板
    this.dashboard.addWidgets(
      new cloudwatch.GraphWidget({
        title: 'ECS服务CPU使用率',
        left: [
          applicationStack.yherpService.metricCpuUtilization(),
          applicationStack.khmallService.metricCpuUtilization(),
          applicationStack.cronService.metricCpuUtilization(),
        ],
        width: 12,
        height: 6,
      }),
      new cloudwatch.GraphWidget({
        title: 'ECS服务内存使用率',
        left: [
          applicationStack.yherpService.metricMemoryUtilization(),
          applicationStack.khmallService.metricMemoryUtilization(),
          applicationStack.cronService.metricMemoryUtilization(),
        ],
        width: 12,
        height: 6,
      })
    );
  }

  /**
   * 设置数据库监控（已废弃，使用Aurora监控）
   */
  private setupDatabaseMonitoring(databaseStack: any, config: EnvironmentConfig): void {
    // PostgreSQL主服务CPU使用率告警
    const dbMasterCpuAlarm = new cloudwatch.Alarm(this, 'DatabaseMasterCpuAlarm', {
      metric: databaseStack.postgresqlMasterService.metricCpuUtilization(),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'PostgreSQL主服务CPU使用率过高',
    });

    dbMasterCpuAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // PostgreSQL从服务CPU使用率告警
    const dbReplicaCpuAlarm = new cloudwatch.Alarm(this, 'DatabaseReplicaCpuAlarm', {
      metric: databaseStack.postgresqlReplicaService.metricCpuUtilization(),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'PostgreSQL从服务CPU使用率过高',
    });

    dbReplicaCpuAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // PostgreSQL主服务内存使用率告警
    const dbMasterMemoryAlarm = new cloudwatch.Alarm(this, 'DatabaseMasterMemoryAlarm', {
      metric: databaseStack.postgresqlMasterService.metricMemoryUtilization(),
      threshold: 85,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'PostgreSQL主服务内存使用率过高',
    });

    dbMasterMemoryAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // PgPool-II服务CPU使用率告警
    const pgpoolCpuAlarm = new cloudwatch.Alarm(this, 'PgPoolCpuAlarm', {
      metric: databaseStack.pgpoolService.metricCpuUtilization(),
      threshold: 70,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'PgPool-II服务CPU使用率过高',
    });

    pgpoolCpuAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // PgPool-II服务内存使用率告警
    const pgpoolMemoryAlarm = new cloudwatch.Alarm(this, 'PgPoolMemoryAlarm', {
      metric: databaseStack.pgpoolService.metricMemoryUtilization(),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'PgPool-II服务内存使用率过高',
    });

    pgpoolMemoryAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // 添加到仪表板
    this.dashboard.addWidgets(
      new cloudwatch.GraphWidget({
        title: 'PostgreSQL服务性能指标',
        left: [
          databaseStack.postgresqlMasterService.metricCpuUtilization(),
          databaseStack.postgresqlReplicaService.metricCpuUtilization(),
          databaseStack.pgpoolService.metricCpuUtilization(),
        ],
        right: [
          databaseStack.postgresqlMasterService.metricMemoryUtilization(),
          databaseStack.postgresqlReplicaService.metricMemoryUtilization(),
          databaseStack.pgpoolService.metricMemoryUtilization(),
        ],
        width: 12,
        height: 6,
      })
    );
  }

  /**
   * 设置负载均衡器监控
   */
  private setupLoadBalancerMonitoring(loadBalancerStack: LoadBalancerStack, config: EnvironmentConfig): void {
    // 负载均衡器目标健康状态告警
    const targetHealthAlarm = new cloudwatch.Alarm(this, 'TargetHealthAlarm', {
      metric: loadBalancerStack.publicLoadBalancer.metrics.targetResponseTime(),
      threshold: 5000, // 5秒
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.BREACHING,
      alarmDescription: '负载均衡器响应时间过长',
    });

    targetHealthAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // HTTP 5xx错误告警
    const http5xxAlarm = new cloudwatch.Alarm(this, 'Http5xxAlarm', {
      metric: new cloudwatch.Metric({
        namespace: 'AWS/ApplicationELB',
        metricName: 'HTTPCode_Target_5XX_Count',
        dimensionsMap: {
          LoadBalancer: loadBalancerStack.publicLoadBalancer.loadBalancerFullName,
        },
        statistic: 'Sum',
      }),
      threshold: 10,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'HTTP 5xx错误过多',
    });

    http5xxAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // 添加到仪表板
    this.dashboard.addWidgets(
      new cloudwatch.GraphWidget({
        title: '负载均衡器指标',
        left: [
          loadBalancerStack.publicLoadBalancer.metrics.requestCount(),
          loadBalancerStack.publicLoadBalancer.metrics.targetResponseTime(),
        ],
        right: [
          new cloudwatch.Metric({
            namespace: 'AWS/ApplicationELB',
            metricName: 'HTTPCode_Target_2XX_Count',
            dimensionsMap: {
              LoadBalancer: loadBalancerStack.publicLoadBalancer.loadBalancerFullName,
            },
            statistic: 'Sum',
          }),
          new cloudwatch.Metric({
            namespace: 'AWS/ApplicationELB',
            metricName: 'HTTPCode_Target_5XX_Count',
            dimensionsMap: {
              LoadBalancer: loadBalancerStack.publicLoadBalancer.loadBalancerFullName,
            },
            statistic: 'Sum',
          }),
        ],
        width: 12,
        height: 6,
      })
    );
  }

  /**
   * 设置自定义指标
   */
  private setupCustomMetrics(config: EnvironmentConfig): void {
    // 创建自定义指标命名空间
    const customNamespace = 'YuanhuiOdoo/Application';

    // 磁盘使用率告警（需要在应用中发送自定义指标）
    const diskUsageAlarm = new cloudwatch.Alarm(this, 'DiskUsageAlarm', {
      metric: new cloudwatch.Metric({
        namespace: customNamespace,
        metricName: 'DiskUsagePercent',
        statistic: 'Average',
      }),
      threshold: 85,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: '磁盘使用率过高',
    });

    diskUsageAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // 应用错误率告警
    const errorRateAlarm = new cloudwatch.Alarm(this, 'ErrorRateAlarm', {
      metric: new cloudwatch.Metric({
        namespace: customNamespace,
        metricName: 'ErrorRate',
        statistic: 'Average',
      }),
      threshold: 5, // 5%
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: '应用错误率过高',
    });

    errorRateAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // 添加自定义指标到仪表板
    this.dashboard.addWidgets(
      new cloudwatch.GraphWidget({
        title: '自定义应用指标',
        left: [
          new cloudwatch.Metric({
            namespace: customNamespace,
            metricName: 'DiskUsagePercent',
            statistic: 'Average',
          }),
          new cloudwatch.Metric({
            namespace: customNamespace,
            metricName: 'ErrorRate',
            statistic: 'Average',
          }),
        ],
        width: 12,
        height: 6,
      })
    );
  }

  /**
   * 设置数据库监控（支持Aurora和RDS）
   */
  private setupAuroraMonitoring(databaseStack: AuroraDatabaseStack, config: EnvironmentConfig): void {
    // 根据环境和数据库类型设置不同的监控
    if (config.environment === 'dev' && databaseStack.rdsInstance) {
      // 开发环境：RDS实例监控
      this.setupRdsInstanceMonitoring(databaseStack, config);
    } else if (databaseStack.auroraCluster) {
      // 生产环境：Aurora集群监控
      this.setupAuroraClusterMonitoring(databaseStack, config);
    }
  }

  /**
   * 设置RDS实例监控（开发环境）
   */
  private setupRdsInstanceMonitoring(databaseStack: AuroraDatabaseStack, config: EnvironmentConfig): void {
    if (!databaseStack.rdsInstance) return;

    // RDS实例CPU使用率告警
    const rdsCpuAlarm = new cloudwatch.Alarm(this, 'RdsCpuAlarm', {
      metric: new cloudwatch.Metric({
        namespace: 'AWS/RDS',
        metricName: 'CPUUtilization',
        dimensionsMap: {
          DBInstanceIdentifier: databaseStack.rdsInstance.instanceIdentifier,
        },
        statistic: 'Average',
        period: cdk.Duration.minutes(5),
      }),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'RDS instance CPU utilization is high',
    });

    rdsCpuAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // RDS连接数告警
    const rdsConnectionsAlarm = new cloudwatch.Alarm(this, 'RdsConnectionsAlarm', {
      metric: new cloudwatch.Metric({
        namespace: 'AWS/RDS',
        metricName: 'DatabaseConnections',
        dimensionsMap: {
          DBInstanceIdentifier: databaseStack.rdsInstance.instanceIdentifier,
        },
        statistic: 'Average',
        period: cdk.Duration.minutes(5),
      }),
      threshold: 20, // RDS免费套餐连接数较低
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'RDS instance connection count is high',
    });

    rdsConnectionsAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));
  }

  /**
   * 设置Aurora集群监控（生产环境）
   */
  private setupAuroraClusterMonitoring(databaseStack: AuroraDatabaseStack, config: EnvironmentConfig): void {
    if (!databaseStack.auroraCluster) return;

    // Aurora集群CPU使用率告警
    const auroraCpuAlarm = new cloudwatch.Alarm(this, 'AuroraCpuAlarm', {
      metric: new cloudwatch.Metric({
        namespace: 'AWS/RDS',
        metricName: 'CPUUtilization',
        dimensionsMap: {
          DBClusterIdentifier: databaseStack.auroraCluster.clusterIdentifier,
        },
        statistic: 'Average',
        period: cdk.Duration.minutes(5),
      }),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'Aurora cluster CPU utilization is high',
    });

    auroraCpuAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // Aurora连接数告警
    const auroraConnectionsAlarm = new cloudwatch.Alarm(this, 'AuroraConnectionsAlarm', {
      metric: new cloudwatch.Metric({
        namespace: 'AWS/RDS',
        metricName: 'DatabaseConnections',
        dimensionsMap: {
          DBClusterIdentifier: databaseStack.auroraCluster.clusterIdentifier,
        },
        statistic: 'Average',
        period: cdk.Duration.minutes(5),
      }),
      threshold: 80,
      evaluationPeriods: 2,
      treatMissingData: cloudwatch.TreatMissingData.NOT_BREACHING,
      alarmDescription: 'Aurora cluster connection count is high',
    });

    auroraConnectionsAlarm.addAlarmAction(new cloudwatchActions.SnsAction(this.alarmTopic));

    // 添加Aurora监控到仪表板
    this.dashboard.addWidgets(
      new cloudwatch.GraphWidget({
        title: 'Aurora Database Metrics',
        left: [auroraCpuAlarm.metric],
        right: [auroraConnectionsAlarm.metric],
        width: 12,
        height: 6,
      })
    );
  }

  /**
   * 添加数据库监控到仪表板
   */
  private addDatabaseWidgetsToDashboard(databaseStack: AuroraDatabaseStack, config: EnvironmentConfig): void {
    if (config.environment === 'dev' && databaseStack.rdsInstance) {
      // RDS实例监控仪表板
      this.dashboard.addWidgets(
        new cloudwatch.GraphWidget({
          title: 'RDS Database Metrics (Free Tier)',
          left: [
            new cloudwatch.Metric({
              namespace: 'AWS/RDS',
              metricName: 'CPUUtilization',
              dimensionsMap: {
                DBInstanceIdentifier: databaseStack.rdsInstance.instanceIdentifier,
              },
              statistic: 'Average',
              period: cdk.Duration.minutes(5),
            })
          ],
          right: [
            new cloudwatch.Metric({
              namespace: 'AWS/RDS',
              metricName: 'DatabaseConnections',
              dimensionsMap: {
                DBInstanceIdentifier: databaseStack.rdsInstance.instanceIdentifier,
              },
              statistic: 'Average',
              period: cdk.Duration.minutes(5),
            })
          ],
          width: 12,
          height: 6,
        })
      );
    }
  }
}
