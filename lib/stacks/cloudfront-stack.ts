import * as cdk from 'aws-cdk-lib';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53targets from 'aws-cdk-lib/aws-route53-targets';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';

export interface CloudFrontStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  loadBalancer: elbv2.ApplicationLoadBalancer;
  // certificate?: acm.Certificate; // 证书由第三方管理
  // hostedZone?: route53.IHostedZone; // DNS由第三方管理
  webAcl?: wafv2.CfnWebACL;
}

/**
 * CloudFront CDN配置栈
 * 为jmall.tw提供全球内容分发和静态资源加速
 */
export class CloudFrontStack extends cdk.Stack {
  public readonly distribution: cloudfront.Distribution;
  public readonly webAcl?: wafv2.CfnWebACL;

  constructor(scope: Construct, id: string, props: CloudFrontStackProps) {
    super(scope, id, props);

    const { config, loadBalancer } = props;

    // 如果CloudFront未启用，跳过创建
    if (!config.cloudfront.enabled) {
      return;
    }

    // 创建CloudFront专用的WAF（CLOUDFRONT scope）
    if (config.network.waf.enabled) {
      this.webAcl = this.createCloudFrontWebAcl(config);
    }

    // 创建缓存策略
    const cachePolicy = new cloudfront.CachePolicy(this, 'KhmallCachePolicy', {
      cachePolicyName: `khmall-cache-policy-${config.environment}`,
      comment: 'Cache policy for Khmall e-commerce platform',
      defaultTtl: cdk.Duration.seconds(config.cloudfront.cacheBehaviors.defaultTTL),
      maxTtl: cdk.Duration.seconds(config.cloudfront.cacheBehaviors.maxTTL),
      minTtl: cdk.Duration.seconds(config.cloudfront.cacheBehaviors.minTTL),
      headerBehavior: cloudfront.CacheHeaderBehavior.allowList(
        'Host',
        'CloudFront-Forwarded-Proto',
        'CloudFront-Is-Desktop-Viewer',
        'CloudFront-Is-Mobile-Viewer',
        'CloudFront-Is-Tablet-Viewer'
      ),
      queryStringBehavior: cloudfront.CacheQueryStringBehavior.allowList(
        'search',
        'category',
        'page',
        'sort',
        'filter'
      ),
      cookieBehavior: cloudfront.CacheCookieBehavior.allowList(
        'session_id',
        'frontend_lang'
      ),
    });

    // 创建Origin Request策略
    const originRequestPolicy = new cloudfront.OriginRequestPolicy(this, 'KhmallOriginRequestPolicy', {
      originRequestPolicyName: `khmall-origin-request-policy-${config.environment}`,
      comment: 'Origin request policy for Khmall',
      headerBehavior: cloudfront.OriginRequestHeaderBehavior.allowList(
        'Host',
        'User-Agent',
        'Referer',
        'Accept-Language',
        'X-Forwarded-For',
        'X-Forwarded-Proto',
        'X-Forwarded-Host'
      ),
      queryStringBehavior: cloudfront.OriginRequestQueryStringBehavior.all(),
      cookieBehavior: cloudfront.OriginRequestCookieBehavior.all(),
    });

    // 创建Response Headers策略
    const responseHeadersPolicy = new cloudfront.ResponseHeadersPolicy(this, 'KhmallResponseHeadersPolicy', {
      responseHeadersPolicyName: `khmall-response-headers-policy-${config.environment}`,
      comment: 'Response headers policy for Khmall security',
      securityHeadersBehavior: {
        contentTypeOptions: { override: true },
        frameOptions: { frameOption: cloudfront.HeadersFrameOption.DENY, override: true },
        referrerPolicy: { referrerPolicy: cloudfront.HeadersReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN, override: true },
        strictTransportSecurity: {
          accessControlMaxAge: cdk.Duration.seconds(31536000),
          includeSubdomains: true,
          preload: true,
          override: true,
        },
        xssProtection: { protection: true, modeBlock: true, override: true },
      },
      customHeadersBehavior: {
        customHeaders: [
          { header: 'X-Powered-By', value: 'Yuanhui-Odoo', override: true },
          { header: 'Cache-Control', value: 'public, max-age=31536000', override: false },
        ],
      },
    });

    // 创建CloudFront分发
    this.distribution = new cloudfront.Distribution(this, 'KhmallDistribution', {
      comment: `CloudFront distribution for ${config.network.domains.applications.khmall.domain} - ${config.environment}`,
      defaultRootObject: '/',
      
      // 主要源配置
      defaultBehavior: {
        origin: new origins.LoadBalancerV2Origin(loadBalancer, {
          protocolPolicy: cloudfront.OriginProtocolPolicy.HTTP_ONLY,
          httpPort: 80,
        }),
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        cachePolicy: cachePolicy,
        originRequestPolicy: originRequestPolicy,
        responseHeadersPolicy: responseHeadersPolicy,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        compress: true,
      },

      // 静态资源缓存行为
      additionalBehaviors: {
        '/web/static/*': {
          origin: new origins.LoadBalancerV2Origin(loadBalancer, {
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTP_ONLY,
            httpPort: 80,
            customHeaders: {
              'Host': config.network.domains.applications.khmall.domain,
            },
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
          compress: true,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        },
        '/web/image/*': {
          origin: new origins.LoadBalancerV2Origin(loadBalancer, {
            protocolPolicy: cloudfront.OriginProtocolPolicy.HTTP_ONLY,
            httpPort: 80,
            customHeaders: {
              'Host': config.network.domains.applications.khmall.domain,
            },
          }),
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
          compress: true,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD,
        },
      },

      // 域名和证书配置由第三方管理
      // domainNames: certificate ? [config.network.domains.applications.khmall.domain] : undefined,
      // certificate: certificate,

      // 地理位置限制
      geoRestriction: config.cloudfront.geoRestriction.restrictionType !== 'none' 
        ? cloudfront.GeoRestriction.allowlist(...config.cloudfront.geoRestriction.locations)
        : cloudfront.GeoRestriction.allowlist(),

      // 价格等级
      priceClass: cloudfront.PriceClass.PRICE_CLASS_ALL,

      // 启用IPv6
      enableIpv6: true,

      // 启用日志
      enableLogging: true,
      logBucket: undefined, // 可以配置S3存储桶用于访问日志

      // 关联WAF
      webAclId: this.webAcl?.attrArn,

      // 错误页面配置
      errorResponses: [
        {
          httpStatus: 404,
          responseHttpStatus: 404,
          responsePagePath: '/web/404',
          ttl: cdk.Duration.minutes(5),
        },
        {
          httpStatus: 500,
          responseHttpStatus: 500,
          responsePagePath: '/web/500',
          ttl: cdk.Duration.minutes(1),
        },
      ],
    });

    // DNS记录由第三方管理
    // 需要手动配置域名 j2mall.tw 指向CloudFront分发域名
    // CloudFront域名可通过控制台或CDK输出获取

    // 输出CloudFront信息
    new cdk.CfnOutput(this, 'DistributionId', {
      value: this.distribution.distributionId,
      description: 'CloudFront Distribution ID',
      exportName: `${config.environment}-cloudfront-distribution-id`,
    });

    new cdk.CfnOutput(this, 'DistributionDomainName', {
      value: this.distribution.distributionDomainName,
      description: 'CloudFront Distribution Domain Name',
      exportName: `${config.environment}-cloudfront-domain-name`,
    });

    if (this.webAcl) {
      new cdk.CfnOutput(this, 'CloudFrontWebAclArn', {
        value: this.webAcl.attrArn,
        description: 'CloudFront WAF Web ACL ARN',
        exportName: `${config.environment}-cloudfront-web-acl-arn`,
      });
    }

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'CloudFront');
  }

  /**
   * 创建CloudFront专用的WAF Web ACL
   */
  private createCloudFrontWebAcl(config: EnvironmentConfig): wafv2.CfnWebACL {
    const rules: wafv2.CfnWebACL.RuleProperty[] = [];

    // AWS托管规则集
    if (config.network.waf.rules.enableAWSManagedRules) {
      rules.push({
        name: 'AWSManagedRulesCommonRuleSet',
        priority: 1,
        overrideAction: { none: {} },
        statement: {
          managedRuleGroupStatement: {
            vendorName: 'AWS',
            name: 'AWSManagedRulesCommonRuleSet',
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'CloudFrontCommonRuleSetMetric',
        },
      });
    }

    // 速率限制规则
    if (config.network.waf.rules.enableRateLimit) {
      rules.push({
        name: 'CloudFrontRateLimitRule',
        priority: 30,
        action: { block: {} },
        statement: {
          rateBasedStatement: {
            limit: config.network.waf.rules.rateLimit.limit * 2, // CloudFront通常需要更高的限制
            aggregateKeyType: 'IP',
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'CloudFrontRateLimitMetric',
        },
      });
    }

    return new wafv2.CfnWebACL(this, 'CloudFrontWebAcl', {
      scope: 'CLOUDFRONT',
      defaultAction: { allow: {} },
      rules,
      visibilityConfig: {
        sampledRequestsEnabled: true,
        cloudWatchMetricsEnabled: true,
        metricName: `yuanhui-cloudfront-waf-${config.environment}`,
      },
      name: `yuanhui-cloudfront-waf-${config.environment}`,
      description: `CloudFront WAF for Yuanhui Khmall ${config.environment} environment`,
    });
  }
}
