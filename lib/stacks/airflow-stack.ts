import * as cdk from 'aws-cdk-lib';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as iam from 'aws-cdk-lib/aws-iam';
import { DockerImageAsset, Platform } from 'aws-cdk-lib/aws-ecr-assets';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import { LoadBalancerStack } from './load-balancer-stack';
import * as path from 'path';

export interface AirflowStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  cluster: ecs.Cluster;
  serviceConnectNamespace: servicediscovery.HttpNamespace;
  databaseEndpoint: string;
  airflowDatabaseSecret: secretsmanager.Secret;  // 使用Airflow专用数据库凭证
  rabbitmqEndpoint: string;
  rabbitmqSecret: secretsmanager.Secret;
  loadBalancerStack?: LoadBalancerStack;
  sharedEfsFileSystem: efs.FileSystem;  // 使用ECS共享EFS文件系统
}

/**
 * Apache Airflow工作流引擎栈
 * 包含Api server、Scheduler和Worker组件
 * 现在自管理负载均衡器路由配置
 */
export class AirflowStack extends cdk.Stack {
  // 独立服务架构属性
  public readonly apiServerService?: ecs.Ec2Service;
  public readonly schedulerService?: ecs.Ec2Service;
  public readonly dagProcessorService?: ecs.Ec2Service;
  public readonly triggererService?: ecs.Ec2Service;
  public readonly workerService?: ecs.Ec2Service;
  
  public readonly airflowSecret: secretsmanager.ISecret;
  public airflowTargetGroup?: elbv2.ApplicationTargetGroup;

  public readonly sharedEfsFileSystem: efs.FileSystem;  // 使用共享EFS文件系统
  private dagsAccessPoint: efs.AccessPoint;
  private logsAccessPoint: efs.AccessPoint;

  // 共享资源
  private sharedExecutionRole: iam.Role;
  private sharedEfsPolicy: iam.Policy;
  private sharedEcrPolicy: iam.Policy;

  constructor(scope: Construct, id: string, props: AirflowStackProps) {
    super(scope, id, props);

    const { config, vpc, cluster, serviceConnectNamespace, airflowDatabaseSecret, rabbitmqEndpoint, rabbitmqSecret, loadBalancerStack, sharedEfsFileSystem } = props;

    // 使用ECS共享EFS文件系统
    this.sharedEfsFileSystem = sharedEfsFileSystem;

    // 数据库连接凭证将在 setupAirflowConnections 方法中创建

    // 根据配置决定是否创建自定义Airflow Docker镜像资产
    const airflowImageAsset = config.airflow.image.useCustomImage
      ? new DockerImageAsset(this, 'AirflowDockerImageAsset', {
          directory: path.join(__dirname, '../../docker/airflow'),
          platform: Platform.LINUX_AMD64,
        })
      : undefined;

    // 创建Airflow凭证
    this.airflowSecret = new secretsmanager.Secret(this, 'AirflowSecret', {
      description: 'Apache Airflow configuration secrets',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({
          admin_username: 'admin',
          admin_email: '<EMAIL>',
          // 根据Airflow 3.0.2要求，使用临时的Fernet密钥和Web服务器密钥
          // 在生产环境中应该使用专门生成的密钥
          fernet_key: 'ZmDfcTF7_60GrrY167zsiPd67pEvs0aGOv2oasOM1Pg=',
          api_secret_key: 'temporary_secret_key_change_in_production',
          jwt_secret: 'sg3wertewrgergtwetfwdfgsdfgedfgewrftertDJWJSHHDWEW'
        }),
        generateStringKey: 'admin_password',
        includeSpace: false,
        excludeUppercase: false,
        excludeLowercase: false,
        excludeNumbers: false,
        excludePunctuation: true,
        passwordLength: 32,
      },
    });

    // 创建Airflow数据库连接和消息队列连接的密钥
    this.setupAirflowConnections(config, rabbitmqEndpoint, airflowDatabaseSecret, rabbitmqSecret);

    // 注意：在生产环境中，应该使用以下方式生成安全的密钥：
    // - Fernet密钥：python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"
    // - Web服务器密钥：openssl rand -hex 16

    // 创建Airflow专用的EFS访问点，使用共享EFS的子目录
    this.createAirflowEfsAccessPoints(config);

    // 创建所有Airflow服务共享的资源
    this.createSharedResources(config);

    // bridge网络模式下不需要容器级安全组，使用主机网络安全策略

    // 注意：现在使用官方Airflow镜像模式，由官方镜像内置脚本处理数据库初始化
    // 无需复杂的init container和自定义脚本

    // 创建独立的Airflow服务，每个组件分离部署
    // Api server服务
    this.apiServerService = this.createApiServerService(
      config, cluster, serviceConnectNamespace, airflowImageAsset
    );

    // Scheduler服务
    this.schedulerService = this.createSchedulerService(
      config, cluster, serviceConnectNamespace, airflowImageAsset
    );

    // DAG Processor服务
    this.dagProcessorService = this.createDagProcessorService(
      config, cluster, serviceConnectNamespace, airflowImageAsset
    );

    // Triggerer服务
    this.triggererService = this.createTriggererService(
      config, cluster, serviceConnectNamespace, airflowImageAsset
    );

    // Worker服务（仅在使用CeleryExecutor时创建）
    if (config.airflow.executor === 'CeleryExecutor') {
      this.workerService = this.createWorkerService(
        config, cluster, serviceConnectNamespace, airflowImageAsset
      );
    }

    // 注意：Airflow Api server 现在通过 LoadBalancerStack 的统一 ALB 提供外部访问
    
    // 配置负载均衡器路由（如果LoadBalancerStack可用）
    if (loadBalancerStack) {
      this.configureLoadBalancer(config, vpc, loadBalancerStack);
    }

    // 输出重要信息
    this.createOutputs(config);
  }



  /**
   * 创建所有Airflow服务共享的资源
   * 包括执行角色、EFS策略和ECR策略
   */
  private createSharedResources(config: EnvironmentConfig): void {
    // 创建共享的执行角色
    this.sharedExecutionRole = new iam.Role(this, 'AirflowSharedExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
      ],
      roleName: `airflow-execution-role-${config.environment}`,
    });

    // 创建共享的EFS策略
    this.sharedEfsPolicy = new iam.Policy(this, 'AirflowSharedEfsPolicy', {
      policyName: `airflow-efs-policy-${config.environment}`,
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'elasticfilesystem:ClientMount',
            'elasticfilesystem:ClientWrite',
            'elasticfilesystem:ClientRootAccess',
            'elasticfilesystem:DescribeFileSystems',
            'elasticfilesystem:DescribeAccessPoints',
            'elasticfilesystem:DescribeMountTargets',
          ],
          resources: [
            this.sharedEfsFileSystem.fileSystemArn,
            `${this.sharedEfsFileSystem.fileSystemArn}/*`,
          ],
        }),
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ec2:DescribeAvailabilityZones',
            'ec2:DescribeNetworkInterfaces',
            'ec2:DescribeSubnets',
            'ec2:DescribeVpcs',
            'ec2:DescribeSecurityGroups',
          ],
          resources: ['*'],
        }),
      ],
    });

    // 创建共享的ECR策略
    this.sharedEcrPolicy = new iam.Policy(this, 'AirflowSharedEcrPolicy', {
      policyName: `airflow-ecr-policy-${config.environment}`,
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: ['ecr:GetAuthorizationToken'],
          resources: ['*'],
        }),
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecr:BatchCheckLayerAvailability',
            'ecr:GetDownloadUrlForLayer',
            'ecr:BatchGetImage',
            'ecr:DescribeRepositories',
            'ecr:DescribeImages',
          ],
          resources: [`arn:aws:ecr:${this.region}:${this.account}:repository/*`],
        }),
      ],
    });

    // 将ECR策略附加到执行角色
    this.sharedEcrPolicy.attachToRole(this.sharedExecutionRole);
  }

  /**
   * 为Airflow创建专用的EFS访问点，使用共享EFS的子目录
   * 目录结构: /ecs/airflow/dags/ 和 /ecs/airflow/logs/
   */
  private createAirflowEfsAccessPoints(config: EnvironmentConfig): void {
    // 创建Airflow DAGs访问点 - 使用共享EFS的airflow/dags子目录
    this.dagsAccessPoint = this.sharedEfsFileSystem.addAccessPoint('AirflowDagsAccessPoint', {
      path: '/ecs/airflow/dags',
      posixUser: {
        uid: '50000', // airflow 用户 ID
        gid: '0',     // root 组，允许容器内的权限管理
      },
      createAcl: {
        ownerUid: '50000',
        ownerGid: '0',
        permissions: '755', // 所有者可读写执行，组和其他用户可读执行
      },
    });

    // 创建Airflow Logs访问点 - 使用共享EFS的airflow/logs子目录
    this.logsAccessPoint = this.sharedEfsFileSystem.addAccessPoint('AirflowLogsAccessPoint', {
      path: '/ecs/airflow/logs',
      posixUser: {
        uid: '50000', // airflow 用户 ID
        gid: '0',     // root 组，允许容器内的权限管理
      },
      createAcl: {
        ownerUid: '50000',
        ownerGid: '0',
        permissions: '755', // 所有者可读写执行，组和其他用户可读执行
      },
    });
  }

  /**
   * 生成Airflow 3.0 DAG Bundle配置的环境变量
   * 将配置文件中的dagBundles数组转换为Airflow DAG_PROCESSOR__DAG_BUNDLE_CONFIG_LIST格式
   */
  private generateDagBundleConfig(config: EnvironmentConfig): string {
    return JSON.stringify(config.airflow.dagBundles);
  }

  /**
   * 设置Airflow连接到AWS Secrets Manager
   *
   * 创建两种类型的secrets：
   * 1. SQLAlchemy连接字符串格式 - 用于Airflow核心数据库连接
   * 2. Airflow Connection格式 - 用于DAGs中的数据库操作
   */
  private setupAirflowConnections(
    config: EnvironmentConfig,
    rabbitmqEndpoint: string,
    databaseSecret: secretsmanager.Secret,
    rabbitmqSecret: secretsmanager.Secret
  ): void {
    // 创建安全的数据库连接字符串Secret（用于AIRFLOW__DATABASE__SQL_ALCHEMY_CONN）
    // 使用简单的固定值方式，在部署时静态解析
    new secretsmanager.Secret(this, 'AirflowDbConnectionString', {
      secretName: `airflow/config/${config.environment}/sql_alchemy_conn`,
      description: `Airflow database connection string for ${config.environment} environment`,
      secretStringValue: cdk.SecretValue.unsafePlainText(
        cdk.Fn.sub('postgresql://${username}:${password}@${host}:${port}/${database}', {
          username: databaseSecret.secretValueFromJson('username').unsafeUnwrap(),
          password: databaseSecret.secretValueFromJson('password').unsafeUnwrap(),
          host: databaseSecret.secretValueFromJson('host').unsafeUnwrap(),
          port: databaseSecret.secretValueFromJson('port').unsafeUnwrap(),
          database: databaseSecret.secretValueFromJson('database').unsafeUnwrap()
        })
      )
    });

    // 创建Airflow Connection格式的数据库连接密钥（用于DAGs中的连接）
    // 使用Airflow专用数据库凭证中的信息
    new secretsmanager.Secret(this, 'AirflowDbConnection', {
      secretName: `airflow/connections/${config.environment}/airflow_db`,
      description: `Airflow database connection for DAGs in ${config.environment} environment`,
      secretObjectValue: {
        conn_type: cdk.SecretValue.unsafePlainText('postgres'),
        host: databaseSecret.secretValueFromJson('host'),
        port: databaseSecret.secretValueFromJson('port'),
        schema: databaseSecret.secretValueFromJson('database'),
        login: databaseSecret.secretValueFromJson('username'),
        password: databaseSecret.secretValueFromJson('password'),
      },
    });

    // 如果使用CeleryExecutor，创建RabbitMQ连接密钥
    if (config.airflow.executor === 'CeleryExecutor') {
      const rabbitmqHost = rabbitmqEndpoint.split(':')[0];
      const rabbitmqPort = '5672';
      const rabbitmqUser = 'admin';

      // Airflow Connection格式的RabbitMQ连接密钥（用于DAGs中的连接）
      new secretsmanager.Secret(this, 'AirflowRabbitMQConnection', {
        secretName: `airflow/connections/${config.environment}/rabbitmq_broker`,
        description: `Airflow RabbitMQ broker connection for DAGs in ${config.environment} environment`,
        secretObjectValue: {
          conn_type: cdk.SecretValue.unsafePlainText('amqp'),
          host: cdk.SecretValue.unsafePlainText(rabbitmqHost),
          port: cdk.SecretValue.unsafePlainText(rabbitmqPort),
          login: cdk.SecretValue.unsafePlainText(rabbitmqUser),
          password: rabbitmqSecret.secretValueFromJson('password'),
        },
      });
    }
    // LocalExecutor不需要消息队列连接
  }


  /**
   * 通用辅助方法：获取容器镜像
   * 参考 docker-compose 的镜像选择逻辑
   */
  private getContainerImage(config: EnvironmentConfig, airflowImageAsset?: DockerImageAsset): ecs.ContainerImage {
    if (config.airflow.image.useCustomImage && airflowImageAsset) {
      return ecs.ContainerImage.fromDockerImageAsset(airflowImageAsset);
    } else if (config.airflow.image.url) {
      return ecs.ContainerImage.fromRegistry(config.airflow.image.url);
    } else {
      return ecs.ContainerImage.fromRegistry(`apache/airflow:${config.airflow.version}-python3.11`);
    }
  }

  /**
   * 通用辅助方法：获取公共环境变量
   * 参考 docker-compose 的 airflow-common-env 设计
   */
  private getCommonEnvironment(config: EnvironmentConfig): Record<string, string> {
    return {
      // AWS region configuration for boto3
      AWS_DEFAULT_REGION: this.region,
      AWS_REGION: this.region,

      // AWS Secrets Manager backend configuration
      AIRFLOW__SECRETS__BACKEND: 'airflow.providers.amazon.aws.secrets.secrets_manager.SecretsManagerBackend',
      AIRFLOW__SECRETS__BACKEND_KWARGS: JSON.stringify({
        connections_prefix: `airflow/connections/${config.environment}`,
        variables_prefix: `airflow/variables/${config.environment}`,
        config_prefix: `airflow/config/${config.environment}`,
        profile_name: null,
        region_name: this.region
      }),

      // JWT认证配置（Airflow 3.0新增）
      AIRFLOW__API_AUTH__JWT_EXPIRATION_TIME: '86400',
      AIRFLOW__API_AUTH__JWT_ISSUER: `airflow-${config.environment}`,
      AIRFLOW__API_AUTH__JWT_LEEWAY: '300',

      // 执行API配置
      AIRFLOW__EXECUTION_API__JWT_EXPIRATION_TIME: '3600',
      AIRFLOW__EXECUTION_API__JWT_LEEWAY: '300',
      AIRFLOW__CORE__EXECUTION_API_SERVER_URL: 'http://airflow-apiserver:8080/execution/',

      // 基本配置
      AIRFLOW__CORE__EXECUTOR: config.airflow.executor,
      AIRFLOW__CORE__LOAD_EXAMPLES: 'False',
      AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'True',
      AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG: '1',
      AIRFLOW__CORE__PARALLELISM: '32',
      AIRFLOW__CORE__DAG_CONCURRENCY: '16',
      AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG: '16',

      // 路径配置
      AIRFLOW__CORE__DAGS_FOLDER: '/opt/airflow/dags',
      AIRFLOW__LOGGING__BASE_LOG_FOLDER: '/opt/airflow/logs',

      // 日志服务配置（修复 ECS 环境下的日志获取问题）
      // LocalExecutor: 由 Scheduler 在 8793 端口提供日志服务
      // CeleryExecutor: 由 Worker 在 8793 端口提供日志服务
      // AIRFLOW__LOGGING__WORKER_LOG_SERVER_PORT: '8793',
      // ECS 环境下的主机名解析
      // AIRFLOW__CORE__HOSTNAME_CALLABLE: 'socket.gethostname',

      // DAG Bundle配置（Airflow 3.0新特性）
      AIRFLOW__DAG_PROCESSOR__DAG_BUNDLE_CONFIG_LIST: this.generateDagBundleConfig(config),
      AIRFLOW__DAG_PROCESSOR__REFRESH_INTERVAL: '200',

      // Celery配置（如果使用CeleryExecutor）
      ...(config.airflow.executor === 'CeleryExecutor' ? {
        AIRFLOW__CELERY__BROKER_URL: `rabbitmq_broker`,
        AIRFLOW__CELERY__RESULT_BACKEND_SECRET: `sql_alchemy_conn`,
        AIRFLOW__CELERY__WORKER_CONCURRENCY: '16',
      } : {}),

      // LocalExecutor配置
      ...(config.airflow.executor === 'LocalExecutor' ? {
        AIRFLOW__CORE__SQL_ALCHEMY_POOL_SIZE: '5',
        AIRFLOW__CORE__SQL_ALCHEMY_MAX_OVERFLOW: '10',
        AIRFLOW__CORE__SQL_ALCHEMY_POOL_PRE_PING: 'True',
      } : {}),

      // 日志配置
      AIRFLOW__LOGGING__LOGGING_LEVEL: 'INFO',
      AIRFLOW__LOGGING__FAB_LOGGING_LEVEL: 'WARN',
      AIRFLOW__LOGGING__COLORED_CONSOLE_LOG: 'False',

      // 安全配置
      AIRFLOW__CORE__ENABLE_XCOM_PICKLING: 'False',
      AIRFLOW__CORE__KILLED_TASK_CLEANUP_TIME: '60',

      // Scheduler配置
      AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK: 'true',

      // 用户自定义环境变量
      ...config.airflow.environmentVariables
    };
  }

  /**
   * 通用辅助方法：获取公共Secrets配置
   * 参考 docker-compose 的 secrets 管理模式
   */
  private getCommonSecrets(config: EnvironmentConfig, component: string): Record<string, ecs.Secret> {
    return {
      AIRFLOW__CORE__FERNET_KEY: ecs.Secret.fromSecretsManager(this.airflowSecret, 'fernet_key'),
      AIRFLOW__API__SECRET_KEY: ecs.Secret.fromSecretsManager(this.airflowSecret, 'api_secret_key'),
      AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: ecs.Secret.fromSecretsManager(
        secretsmanager.Secret.fromSecretNameV2(this, `ImportedDbConnectionString${component}`, `airflow/config/${config.environment}/sql_alchemy_conn`)
      ),
      AIRFLOW__API_AUTH__JWT_SECRET: ecs.Secret.fromSecretsManager(this.airflowSecret, 'jwt_secret'),
    };
  }

  /**
   * 通用辅助方法：获取EFS挂载点
   * 参考 docker-compose 的 volumes 配置
   */
  private getCommonMountPoints() {
    return [
      {
        sourceVolume: 'airflow-dags',
        containerPath: '/opt/airflow/dags',
        readOnly: false,
      },
      {
        sourceVolume: 'airflow-logs',
        containerPath: '/opt/airflow/logs',
        readOnly: false,
      }
    ];
  }

  /**
   * 通用辅助方法：创建通用任务定义基础结构
   * 使用共享的执行角色、EFS策略和ECR策略
   */
  private createBaseTaskDefinition(
    component: string,
    config: EnvironmentConfig
  ): ecs.Ec2TaskDefinition {
    const taskDefinition = new ecs.Ec2TaskDefinition(this, `Airflow${component}TaskDefinition`, {
      family: `airflow-${component.toLowerCase()}-${config.environment}`,
      networkMode: ecs.NetworkMode.BRIDGE,
      executionRole: this.sharedExecutionRole, // 使用共享执行角色
    });

    // 为任务定义添加EFS访问权限
    this.sharedEfsFileSystem.grantRootAccess(taskDefinition.taskRole);

    // 添加AWS Secrets Manager访问权限
    taskDefinition.taskRole.addManagedPolicy(
      iam.ManagedPolicy.fromAwsManagedPolicyName('SecretsManagerReadWrite')
    );

    // 将共享的EFS策略附加到任务角色
    this.sharedEfsPolicy.attachToRole(taskDefinition.taskRole);

    // 添加EFS卷
    taskDefinition.addVolume({
      name: 'airflow-dags',
      efsVolumeConfiguration: {
        fileSystemId: this.sharedEfsFileSystem.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.dagsAccessPoint.accessPointId,
        },
      },
    });

    taskDefinition.addVolume({
      name: 'airflow-logs',
      efsVolumeConfiguration: {
        fileSystemId: this.sharedEfsFileSystem.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.logsAccessPoint.accessPointId,
        },
      },
    });

    return taskDefinition;
  }

  /**
   * 创建独立的API Server任务定义
   */
  private createApiServerTaskDefinition(
    config: EnvironmentConfig,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2TaskDefinition {
    const taskDefinition = this.createBaseTaskDefinition('ApiServer', config);

    // API Server容器
    const apiServerContainer = taskDefinition.addContainer('AirflowApiServerContainer', {
      image: this.getContainerImage(config, airflowImageAsset),
      cpu: config.airflow.webserver.cpu,
      memoryReservationMiB: config.airflow.webserver.memory,
      user: '50000:0',
      command: ['airflow', 'api-server'],
      environment: {
        ...this.getCommonEnvironment(config),
        // API Server特定配置
        AIRFLOW__WEBSERVER__EXPOSE_CONFIG: 'True',
        AIRFLOW__WEBSERVER__RBAC: 'True',
        AIRFLOW__WEBSERVER__WEB_SERVER_PORT: config.airflow.webserver.port.toString(),
        AIRFLOW__WEBSERVER__WORKERS: '4',
        AIRFLOW__WEBSERVER__WORKER_TIMEOUT: '120',
        AIRFLOW__WEBSERVER__WORKER_REFRESH_BATCH_SIZE: '1',
        AIRFLOW__WEBSERVER__WORKER_REFRESH_INTERVAL: '6000',
      },
      secrets: this.getCommonSecrets(config, 'ApiServer'),
      portMappings: [
        {
          name: 'airflow-apiserver',
          containerPort: config.airflow.webserver.port,
          hostPort: 0, // 动态端口映射
          protocol: ecs.Protocol.TCP,
        },
      ],
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'airflow-apiserver',
        logGroup: new logs.LogGroup(this, 'AirflowApiServerLogGroup', {
          logGroupName: `/aws/ecs/airflow-apiserver-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        command: ['CMD', 'curl', '--fail', `http://localhost:${config.airflow.webserver.port}/api/v2/version`],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(10),
        retries: 5,
        startPeriod: cdk.Duration.seconds(30),
      },
    });

    // 添加EFS挂载点
    apiServerContainer.addMountPoints(...this.getCommonMountPoints());

    return taskDefinition;
  }

  /**
   * 创建独立的API Server服务
   */
  private createApiServerService(
    config: EnvironmentConfig,
    cluster: ecs.Cluster,
    serviceConnectNamespace: servicediscovery.HttpNamespace,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2Service {
    const taskDefinition = this.createApiServerTaskDefinition(
      config,
      airflowImageAsset
    );

    return new ecs.Ec2Service(this, 'AirflowApiServerService', {
      cluster,
      taskDefinition,
      desiredCount: config.airflow.webserver.replicas,
      serviceName: `airflow-apiserver-${config.environment}`,
      enableExecuteCommand: true,
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        services: [{
          portMappingName: 'airflow-apiserver',
          dnsName: 'airflow-apiserver',
          port: config.airflow.webserver.port,
        }],
      },
    });
  }

  /**
   * 创建独立的Scheduler任务定义
   */
  private createSchedulerTaskDefinition(
    config: EnvironmentConfig,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2TaskDefinition {
    const taskDefinition = this.createBaseTaskDefinition('Scheduler', config);

    // Scheduler容器
    const schedulerContainer = taskDefinition.addContainer('AirflowSchedulerContainer', {
      image: this.getContainerImage(config, airflowImageAsset),
      cpu: config.airflow.scheduler.cpu,
      memoryReservationMiB: config.airflow.scheduler.memory,
      user: '50000:0',
      command: ['airflow', 'scheduler'],
      environment: {
        ...this.getCommonEnvironment(config),
        // Scheduler特定配置
        AIRFLOW__SCHEDULER__DAG_DIR_LIST_INTERVAL: '300',
        AIRFLOW__SCHEDULER__CATCHUP_BY_DEFAULT: 'False',
        AIRFLOW__SCHEDULER__MAX_TIS_PER_QUERY: '512',
        AIRFLOW__SCHEDULER__PROCESSOR_POLL_INTERVAL: '1',
      },
      secrets: this.getCommonSecrets(config, 'Scheduler'),
      portMappings: [
        {
          name: 'scheduler-health',
          containerPort: 8974, // Scheduler健康检查端口
          hostPort: 0, // 动态端口映射
          protocol: ecs.Protocol.TCP,
        },
        // LocalExecutor 模式下，Scheduler 需要提供日志服务
        {
          name: 'scheduler-logs',
          containerPort: 8793, // 日志服务端口
          hostPort: 0, // 动态端口映射
          protocol: ecs.Protocol.TCP,
        },
      ],
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'airflow-scheduler',
        logGroup: new logs.LogGroup(this, 'AirflowSchedulerLogGroup', {
          logGroupName: `/aws/ecs/airflow-scheduler-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        command: ['CMD', 'curl', '--fail', 'http://localhost:8974/health'],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(10),
        retries: 5,
        startPeriod: cdk.Duration.seconds(30),
      },
    });

    // 添加EFS挂载点
    schedulerContainer.addMountPoints(...this.getCommonMountPoints());

    return taskDefinition;
  }

  /**
   * 创建独立的Scheduler服务
   */
  private createSchedulerService(
    config: EnvironmentConfig,
    cluster: ecs.Cluster,
    serviceConnectNamespace: servicediscovery.HttpNamespace,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2Service {
    const taskDefinition = this.createSchedulerTaskDefinition(
      config,
      airflowImageAsset
    );

    return new ecs.Ec2Service(this, 'AirflowSchedulerService', {
      cluster,
      taskDefinition,
      desiredCount: config.airflow.scheduler.replicas,
      serviceName: `airflow-scheduler-${config.environment}`,
      enableExecuteCommand: true,
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        services: [
          {
            portMappingName: 'scheduler-health',
            dnsName: 'airflow-scheduler',
            port: 8974,
          },
          // LocalExecutor 模式下，Scheduler 也提供日志服务
          {
            portMappingName: 'scheduler-logs',
            dnsName: 'airflow-scheduler-logs',
            port: 8793,
          },
        ],
      },
    });
  }

  /**
   * 创建独立的DAG Processor任务定义
   */
  private createDagProcessorTaskDefinition(
    config: EnvironmentConfig,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2TaskDefinition {
    const taskDefinition = this.createBaseTaskDefinition('DagProcessor', config);

    // DAG Processor容器
    const dagProcessorContainer = taskDefinition.addContainer('AirflowDagProcessorContainer', {
      image: this.getContainerImage(config, airflowImageAsset),
      cpu: 256, // DAG Processor通常不需要太多资源
      memoryReservationMiB: 512,
      user: '50000:0',
      command: ['airflow', 'dag-processor'],
      environment: this.getCommonEnvironment(config),
      secrets: this.getCommonSecrets(config, 'DagProcessor'),
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'airflow-dag-processor',
        logGroup: new logs.LogGroup(this, 'AirflowDagProcessorLogGroup', {
          logGroupName: `/aws/ecs/airflow-dag-processor-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        command: ['CMD-SHELL', 'airflow jobs check --job-type DagProcessorJob --hostname "${HOSTNAME}"'],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(10),
        retries: 5,
        startPeriod: cdk.Duration.seconds(30),
      },
    });

    // 添加EFS挂载点
    dagProcessorContainer.addMountPoints(...this.getCommonMountPoints());

    return taskDefinition;
  }

  /**
   * 创建独立的DAG Processor服务
   */
  private createDagProcessorService(
    config: EnvironmentConfig,
    cluster: ecs.Cluster,
    serviceConnectNamespace: servicediscovery.HttpNamespace,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2Service {
    const taskDefinition = this.createDagProcessorTaskDefinition(
      config,
      airflowImageAsset
    );

    return new ecs.Ec2Service(this, 'AirflowDagProcessorService', {
      cluster,
      taskDefinition,
      desiredCount: 1, // DAG Processor通常只需要1个实例
      serviceName: `airflow-dag-processor-${config.environment}`,
      enableExecuteCommand: true,
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        // DAG Processor无需对外暴露服务端口，仅作为客户端
      },
    });
  }

  /**
   * 创建独立的Triggerer任务定义
   */
  private createTriggererTaskDefinition(
    config: EnvironmentConfig,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2TaskDefinition {
    const taskDefinition = this.createBaseTaskDefinition('Triggerer', config);

    // Triggerer容器
    const triggererContainer = taskDefinition.addContainer('AirflowTriggererContainer', {
      image: this.getContainerImage(config, airflowImageAsset),
      cpu: 256, // Triggerer通常不需要太多资源
      memoryReservationMiB: 512,
      user: '50000:0',
      command: ['airflow', 'triggerer'],
      environment: this.getCommonEnvironment(config),
      secrets: this.getCommonSecrets(config, 'Triggerer'),
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'airflow-triggerer',
        logGroup: new logs.LogGroup(this, 'AirflowTriggererLogGroup', {
          logGroupName: `/aws/ecs/airflow-triggerer-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        command: ['CMD-SHELL', 'airflow jobs check --job-type TriggererJob --hostname "${HOSTNAME}"'],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(10),
        retries: 5,
        startPeriod: cdk.Duration.seconds(30),
      },
    });

    // 添加EFS挂载点
    triggererContainer.addMountPoints(...this.getCommonMountPoints());

    return taskDefinition;
  }

  /**
   * 创建独立的Triggerer服务
   */
  private createTriggererService(
    config: EnvironmentConfig,
    cluster: ecs.Cluster,
    serviceConnectNamespace: servicediscovery.HttpNamespace,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2Service {
    const taskDefinition = this.createTriggererTaskDefinition(
      config,
      airflowImageAsset
    );

    return new ecs.Ec2Service(this, 'AirflowTriggererService', {
      cluster,
      taskDefinition,
      desiredCount: 1, // Triggerer通常只需要1个实例
      serviceName: `airflow-triggerer-${config.environment}`,
      enableExecuteCommand: true,
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        // Triggerer无需对外暴露服务端口，仅作为客户端
      },
    });
  }

  /**
   * 创建独立的Worker任务定义（仅CeleryExecutor）
   */
  private createWorkerTaskDefinition(
    config: EnvironmentConfig,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2TaskDefinition {
    const taskDefinition = this.createBaseTaskDefinition('Worker', config);

    // Worker容器
    const workerContainer = taskDefinition.addContainer('AirflowWorkerContainer', {
      image: this.getContainerImage(config, airflowImageAsset),
      cpu: config.airflow.worker.cpu,
      memoryReservationMiB: config.airflow.worker.memory,
      user: '50000:0',
      command: ['airflow', 'celery', 'worker'],
      environment: {
        ...this.getCommonEnvironment(config),
        // Worker特定配置（处理SIGTERM信号）
        DUMB_INIT_SETSID: '0',
        // Celery特定配置
        ...(config.airflow.executor === 'CeleryExecutor' ? {
          AIRFLOW__CELERY__BROKER_URL: `rabbitmq_broker`,
          AIRFLOW__CELERY__RESULT_BACKEND_SECRET: `sql_alchemy_conn`,
          AIRFLOW__CELERY__WORKER_CONCURRENCY: '16',
        } : {}),
      },
      secrets: this.getCommonSecrets(config, 'Worker'),
      portMappings: [
        {
          name: 'worker-logs',
          containerPort: 8793,
          hostPort: 0, // 动态端口映射
          protocol: ecs.Protocol.TCP,
        },
      ],
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'airflow-worker',
        logGroup: new logs.LogGroup(this, 'AirflowWorkerLogGroup', {
          logGroupName: `/aws/ecs/airflow-worker-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        command: ['CMD-SHELL', 'celery --app airflow.providers.celery.executors.celery_executor.app inspect ping -d "celery@${HOSTNAME}" || celery --app airflow.executors.celery_executor.app inspect ping -d "celery@${HOSTNAME}"'],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(10),
        retries: 5,
        startPeriod: cdk.Duration.seconds(30),
      },
    });

    // 添加EFS挂载点
    workerContainer.addMountPoints(...this.getCommonMountPoints());

    return taskDefinition;
  }

  /**
   * 创建独立的Worker服务（仅CeleryExecutor）
   */
  private createWorkerService(
    config: EnvironmentConfig,
    cluster: ecs.Cluster,
    serviceConnectNamespace: servicediscovery.HttpNamespace,
    airflowImageAsset?: DockerImageAsset
  ): ecs.Ec2Service {
    const taskDefinition = this.createWorkerTaskDefinition(
      config,
      airflowImageAsset
    );

    return new ecs.Ec2Service(this, 'AirflowWorkerService', {
      cluster,
      taskDefinition,
      desiredCount: config.airflow.worker.replicas,
      serviceName: `airflow-worker-${config.environment}`,
      enableExecuteCommand: true,
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        services: [{
          portMappingName: 'worker-logs',
          dnsName: 'airflow-worker-logs',
          port: 8793,
        }],
      },
    });
  }


  /**
   * 配置负载均衡器路由
   */
  private configureLoadBalancer(
    config: EnvironmentConfig,
    vpc: ec2.Vpc,
    loadBalancerStack: LoadBalancerStack
  ): void {
    // 创建Airflow Api server目标组 - 支持动态端口
    this.airflowTargetGroup = new elbv2.ApplicationTargetGroup(this, 'AirflowApiServerTargetGroup', {
      vpc,
      port: 80,  // ALB监听端口，实际转发到动态端口
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.INSTANCE, // 使用INSTANCE类型支持动态端口
      targetGroupName: `airflow-target-group-${config.environment}`,
      healthCheck: {
        enabled: true,
        healthyHttpCodes: '200,302',
        path: '/api/v2/monitor/health',
        port: 'traffic-port',  // 使用流量端口进行健康检查
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(10),
        healthyThresholdCount: 2,
        unhealthyThresholdCount: 5,
        protocol: elbv2.Protocol.HTTP,
      },
    });

    // 获取Airflow域名
    const airflowDomain = config.airflow.routing.domain;

    // 创建监听器规则 - 使用域名路由（仅当配置了域名时）
    if (airflowDomain) {
      const hostHeaders = [airflowDomain];

      // HTTP 监听器路由
      // 注意：只有在未启用 HTTPS 重定向时才添加，因为启用重定向时，
      // LoadBalancerStack 会将所有 HTTP 请求全局重定向到 HTTPS
      if (loadBalancerStack.publicHttpListener && !config.loadBalancer.ssl.enableHttpsRedirect) {
        new elbv2.ApplicationListenerRule(this, 'AirflowHttpRule', {
          listener: loadBalancerStack.publicHttpListener,
          priority: config.airflow.routing.priority,
          conditions: [elbv2.ListenerCondition.hostHeaders(hostHeaders)],
          action: elbv2.ListenerAction.forward([this.airflowTargetGroup]),
        });
      }

      // HTTPS 监听器路由（生产环境的主要路由）
      // 当启用 HTTPS 重定向时，所有流量最终都会到达这里
      if (loadBalancerStack.publicHttpsListener) {
        new elbv2.ApplicationListenerRule(this, 'AirflowHttpsRule', {
          listener: loadBalancerStack.publicHttpsListener,
          priority: config.airflow.routing.priority,
          conditions: [elbv2.ListenerCondition.hostHeaders(hostHeaders)],
          action: elbv2.ListenerAction.forward([this.airflowTargetGroup]),
        });
      }
    }

    if (!this.apiServerService) {
      throw new Error('Airflow Api server service is not defined');
    }
    // 在独立服务架构中使用apiServerService
    const containerName = 'AirflowApiServerContainer';
    
    this.airflowTargetGroup.addTarget(this.apiServerService.loadBalancerTarget({
      containerName: containerName, // 固定容器名称
      containerPort: config.airflow.webserver.port,
    }));

    // 输出Airflow访问URL
    if (loadBalancerStack.publicLoadBalancer && airflowDomain) {
      const protocol = config.loadBalancer.ssl.enableHttpsRedirect ? 'https' : 'http';
      const url = `${protocol}://${airflowDomain}`;
      
      new cdk.CfnOutput(this, 'AirflowApiServerURL', {
        value: url,
        description: 'Airflow Api server URL (通过域名访问)',
        exportName: `${config.environment}-airflow-api-server-url`,
      });
    }
  }

  private createOutputs(config: EnvironmentConfig): void {
    // 注意：Airflow Webserver URL 现在由 LoadBalancerStack 管理

    new cdk.CfnOutput(this, 'AirflowSecretArn', {
      value: this.airflowSecret.secretArn,
      description: 'Airflow Secret ARN',
      exportName: `${config.environment}-airflow-secret-arn`,
    });

    new cdk.CfnOutput(this, 'AirflowSharedEFSId', {
      value: this.sharedEfsFileSystem.fileSystemId,
      description: 'Airflow Shared EFS file system ID (from ECS stack)',
      exportName: `${config.environment}-airflow-shared-efs-id`,
    });

    // 注意：数据库初始化现在通过官方Airflow镜像内置脚本自动处理
    // 连接配置通过AWS Secrets Manager管理
  }


}
