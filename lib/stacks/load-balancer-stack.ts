import * as cdk from 'aws-cdk-lib';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
// 移除应用栈导入，LoadBalancerStack现在只依赖于Network

/**
 * LoadBalancer 栈属性接口
 */
export interface LoadBalancerStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  webAcl?: wafv2.CfnWebACL;
  albSecurityGroup: ec2.SecurityGroup;
}

/**
 * 负载均衡器栈
 * 负责创建负载均衡器主体和基础监听器，提供接口供应用栈添加路由规则
 */
export class LoadBalancerStack extends cdk.Stack {
  public readonly publicLoadBalancer: elbv2.ApplicationLoadBalancer;
  public readonly publicSecurityGroup: ec2.SecurityGroup;
  public readonly publicHttpListener?: elbv2.ApplicationListener;
  public readonly publicHttpsListener?: elbv2.ApplicationListener;
  private readonly vpc: ec2.Vpc;

  constructor(scope: Construct, id: string, props: LoadBalancerStackProps) {
    super(scope, id, props);

    const { config, vpc, webAcl, albSecurityGroup } = props;
    this.vpc = vpc;

    // 使用从network-stack传入的安全组，而不是创建新的安全组
    this.publicSecurityGroup = albSecurityGroup;

    // 创建公网应用负载均衡器
    if (config.loadBalancer.public.enabled) {
      this.publicLoadBalancer = new elbv2.ApplicationLoadBalancer(this, 'PublicApplicationLoadBalancer', {
        vpc,
        internetFacing: true,
        securityGroup: this.publicSecurityGroup,
        vpcSubnets: {
          subnetType: ec2.SubnetType.PUBLIC,
        },
        loadBalancerName: config.loadBalancer.public.name || `yuanhui-public-alb-${config.environment}`,
      });

      // 如果启用WAF，关联到公网ALB
      if (webAcl && config.loadBalancer.public.enableWafAssociation) {
        new wafv2.CfnWebACLAssociation(this, 'PublicAlbWafAssociation', {
          resourceArn: this.publicLoadBalancer.loadBalancerArn,
          webAclArn: webAcl.attrArn,
        });
      }

      // 输出公网ALB信息
      new cdk.CfnOutput(this, 'PublicLoadBalancerDnsName', {
        value: this.publicLoadBalancer.loadBalancerDnsName,
        description: 'Public Application Load Balancer DNS Name',
        exportName: `${config.environment}-public-alb-dns-name`,
      });

      new cdk.CfnOutput(this, 'PublicLoadBalancerArn', {
        value: this.publicLoadBalancer.loadBalancerArn,
        description: 'Public Application Load Balancer ARN',
        exportName: `${config.environment}-public-alb-arn`,
      });
    }


    // 创建基础监听器（默认404响应）
    this.createDefaultListeners(config);

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'LoadBalancer');
  }

  /**
   * 创建默认监听器
   */
  private createDefaultListeners(config: EnvironmentConfig): void {
    // 创建公网HTTP监听器
    if (this.publicLoadBalancer) {
      (this as any).publicHttpListener = new elbv2.ApplicationListener(this, 'PublicHttpListener', {
        loadBalancer: this.publicLoadBalancer,
        port: config.loadBalancer.listeners.http.port,
        protocol: elbv2.ApplicationProtocol.HTTP,
        defaultAction: config.loadBalancer.ssl.enableHttpsRedirect 
          ? elbv2.ListenerAction.redirect({
              protocol: 'HTTPS',
              port: config.loadBalancer.listeners.https.port.toString(),
              permanent: true,
            })
          : elbv2.ListenerAction.fixedResponse(404, {
              contentType: 'text/html',
              messageBody: '<h1>404 - Service Not Found</h1><p>No service is configured for this request.</p>',
            }),
      });

      // 创建HTTPS监听器（如果启用且有证书）
      if (config.loadBalancer.listeners.https.enabled && 
          config.loadBalancer.ssl.certificateArns?.length) {
        
        const certificates = config.loadBalancer.ssl.certificateArns.map(arn => 
          elbv2.ListenerCertificate.fromArn(arn)
        );
        
        (this as any).publicHttpsListener = new elbv2.ApplicationListener(this, 'PublicHttpsListener', {
          loadBalancer: this.publicLoadBalancer,
          port: config.loadBalancer.listeners.https.port,
          protocol: elbv2.ApplicationProtocol.HTTPS,
          certificates,
          defaultAction: elbv2.ListenerAction.fixedResponse(404, {
            contentType: 'text/html',
            messageBody: '<h1>404 - Service Not Found</h1><p>No HTTPS service is configured for this request.</p>',
          }),
        });
      }
    }

  }

  /**
   * 获取VPC引用
   * 供应用栈创建目标组使用
   */
  public getVpc(): ec2.Vpc {
    return this.vpc;
  }

}