import * as cdk from 'aws-cdk-lib';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import { LoadBalancerStack } from './load-balancer-stack';

/**
 * Wiki.js 栈属性接口
 */
export interface WikiJSStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  cluster: ecs.Cluster;
  loadBalancerStack?: LoadBalancerStack;
  serviceConnectNamespace: servicediscovery.HttpNamespace;
  ecsSecurityGroup: ec2.SecurityGroup;
}

/**
 * Wiki.js 企业级知识管理系统栈
 * 基于 PostgreSQL 的高性能 Wiki 平台，支持高可用部署
 * 使用 Bridge 网络模式进行负载均衡器集成
 */
export class WikiJSStack extends cdk.Stack {
  public readonly service: ecs.Ec2Service;
  public readonly appSecurityGroup: ec2.SecurityGroup;
  public wikijsTargetGroup?: elbv2.ApplicationTargetGroup;
  private readonly config: EnvironmentConfig;

  constructor(scope: Construct, id: string, props: WikiJSStackProps) {
    super(scope, id, props);

    const { config, vpc, cluster, loadBalancerStack, serviceConnectNamespace, ecsSecurityGroup } = props;
    this.config = config;

    // 只有在启用 Wiki.js 时才创建资源
    if (!config.wikijs.enabled) {
      return;
    }

    // Wiki.js不需要额外的session secret或JWT secret
    // 根据官方文档，Wiki.js会自动处理会话管理

    // 获取数据库连接信息
    // 确保配置了有效的数据库密钥ARN
    if (!config.wikijs.database.secretArn) {
      throw new Error('Database secret ARN must be configured for WikiJS');
    }

    // 使用 fromSecretPartialArn 方法引用外部 Secret
    // 这是推荐的方式来引用现有的 Secrets Manager 密钥
    const databaseSecret = secretsmanager.Secret.fromSecretPartialArn(
      this, 
      'DatabaseSecret', 
      config.wikijs.database.secretArn
    );

    // 创建日志组
    const logGroup = new logs.LogGroup(this, 'LogGroup', {
      logGroupName: config.wikijs.logging.logGroupName,
      retention: config.wikijs.logging.retentionDays === 1 
        ? logs.RetentionDays.ONE_DAY 
        : logs.RetentionDays.THREE_DAYS,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // 创建执行角色
    const executionRole = new iam.Role(this, 'WikiJSExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
      ],
    });
    // 给任务定义的执行角色添加 Secrets Manager 访问权限
    // 这样容器才能在启动时从 Secrets Manager 获取数据库连接信息
    // 在容器添加后，执行角色已经被创建
    // databaseSecret.grantRead(executionRole);
    executionRole.addManagedPolicy(
      iam.ManagedPolicy.fromAwsManagedPolicyName('SecretsManagerReadWrite')
    );

    // 创建任务定义 - 使用 BRIDGE 网络模式以提高部署密度和简化网络配置
    const taskDefinition = new ecs.Ec2TaskDefinition(this, 'TaskDefinition', {
      networkMode: ecs.NetworkMode.BRIDGE,
      family: `${config.wikijs.service.name}-${config.environment}`,
      executionRole,
    });

    // 添加 Wiki.js 容器
    const container = taskDefinition.addContainer('WikiJSContainer', {
      image: ecs.ContainerImage.fromRegistry(
        `${config.wikijs.container.image}:${config.wikijs.container.tag}`
      ),
      memoryReservationMiB: config.wikijs.container.memory,
      cpu: config.wikijs.container.cpu,
      environment: {
        ...config.wikijs.environment,
        // 必要的数据库环境变量（根据官方文档）
        DB_TYPE: config.wikijs.database.type,
        DB_NAME: config.wikijs.database.dbName, // 从配置获取而不是从ASM
        DB_SSL: config.wikijs.database.ssl ? 'true' : 'false',
      },
      secrets: {
        // 数据库连接密钥（根据官方文档的必需环境变量）
        DB_HOST: ecs.Secret.fromSecretsManager(databaseSecret, 'host'),
        DB_PORT: ecs.Secret.fromSecretsManager(databaseSecret, 'port'),
        DB_USER: ecs.Secret.fromSecretsManager(databaseSecret, 'username'),
        DB_PASS: ecs.Secret.fromSecretsManager(databaseSecret, 'password'),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: config.wikijs.service.name,
        logGroup: logGroup,
      }),
      healthCheck: {
        command: [
          'CMD-SHELL',
          `curl -f http://localhost:${config.wikijs.service.port}/healthz || exit 1`
        ],
        interval: cdk.Duration.seconds(30),
        timeout: cdk.Duration.seconds(5),
        retries: 3,
        startPeriod: cdk.Duration.seconds(60),
      },
    });

    // 添加端口映射 - BRIDGE 模式下使用动态端口映射
    container.addPortMappings({
      name: config.wikijs.service.name,
      containerPort: config.wikijs.service.port,
      hostPort: 0, // 使用动态端口分配
      protocol: ecs.Protocol.TCP,
    });


    // BRIDGE 网络模式下使用 ECS 实例的安全组
    this.appSecurityGroup = ecsSecurityGroup;

    // 创建 ECS 服务（使用 BRIDGE 网络模式）
    this.service = new ecs.Ec2Service(this, 'Service', {
      cluster,
      taskDefinition,
      desiredCount: config.wikijs.ecs.desiredCount,
      serviceName: `${config.wikijs.service.name}-${config.environment}`,
      // BRIDGE 模式不需要指定 VPC 子网和安全组，使用 ECS 实例的网络配置
      serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        services: [{
          portMappingName: config.wikijs.service.name,
          dnsName: 'wikijs',
          port: config.wikijs.service.port,
        }],
      },
    });

    // 配置自动扩容（如果启用）
    if (config.wikijs.ecs.enableAutoScaling) {
      this.setupAutoScaling();
    }

    // 配置负载均衡器路由（如果 LoadBalancerStack 可用）
    if (loadBalancerStack) {
      this.configureLoadBalancer(config, vpc, loadBalancerStack);
    }

    // 输出服务信息
    new cdk.CfnOutput(this, 'ServiceArn', {
      value: this.service.serviceArn,
      description: 'Wiki.js ECS Service ARN',
      exportName: `${config.environment}-wikijs-service-arn`,
    });

    // 输出 Service Connect 信息
    new cdk.CfnOutput(this, 'ServiceConnectInfo', {
      value: `Wiki.js Service Connect DNS: wikijs:${config.wikijs.service.port}`,
      description: 'Wiki.js Service Connect endpoint',
      exportName: `${config.environment}-wikijs-service-connect`,
    });

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'WikiJS');
    cdk.Tags.of(this).add('Service', 'wikijs');
  }

  /**
   * 配置负载均衡器路由
   */
  private configureLoadBalancer(
    config: EnvironmentConfig,
    vpc: ec2.Vpc,
    loadBalancerStack: LoadBalancerStack
  ): void {
    // 创建 Wiki.js 目标组 - 使用 INSTANCE 目标类型以匹配 BRIDGE 网络模式
    this.wikijsTargetGroup = new elbv2.ApplicationTargetGroup(this, 'WikiJSTargetGroup', {
      vpc,
      port: config.wikijs.service.port,
      protocol: elbv2.ApplicationProtocol.HTTP,
      targetType: elbv2.TargetType.INSTANCE, // BRIDGE 网络模式使用 INSTANCE 目标类型
      healthCheck: {
        enabled: true,
        healthyHttpCodes: config.wikijs.healthCheck.healthyHttpCodes,
        path: config.wikijs.service.healthCheckPath,
        interval: cdk.Duration.seconds(config.wikijs.healthCheck.interval),
        timeout: cdk.Duration.seconds(config.wikijs.healthCheck.timeout),
        healthyThresholdCount: config.wikijs.healthCheck.healthyThresholdCount,
        unhealthyThresholdCount: config.wikijs.healthCheck.unhealthyThresholdCount,
        protocol: elbv2.Protocol.HTTP,
      },
      // 配置会话粘性以支持 Wiki.js 的会话管理
      stickinessCookieDuration: cdk.Duration.hours(1),
      stickinessCookieName: 'WIKIJS_SESSION',
    });

    // 创建监听器规则 - 使用域名路由（仅当配置了域名时）
    if (config.wikijs.routing.domain) {
      const hostHeaders = [config.wikijs.routing.domain];

      // HTTP 监听器路由
      // 注意：只有在未启用 HTTPS 重定向时才添加，因为启用重定向时，
      // LoadBalancerStack 会将所有 HTTP 请求全局重定向到 HTTPS
      if (loadBalancerStack.publicHttpListener && !config.loadBalancer.ssl.enableHttpsRedirect) {
        new elbv2.ApplicationListenerRule(this, 'WikiJSHttpRule', {
          listener: loadBalancerStack.publicHttpListener,
          priority: config.wikijs.routing.priority,
          conditions: [elbv2.ListenerCondition.hostHeaders(hostHeaders)],
          action: elbv2.ListenerAction.forward([this.wikijsTargetGroup]),
        });
      }

      // HTTPS 监听器路由（生产环境的主要路由）
      // 当启用 HTTPS 重定向时，所有流量最终都会到达这里
      if (loadBalancerStack.publicHttpsListener) {
        new elbv2.ApplicationListenerRule(this, 'WikiJSHttpsRule', {
          listener: loadBalancerStack.publicHttpsListener,
          priority: config.wikijs.routing.priority,
          conditions: [elbv2.ListenerCondition.hostHeaders(hostHeaders)],
          action: elbv2.ListenerAction.forward([this.wikijsTargetGroup]),
        });
      }
    }

    // 将服务附加到目标组 - 明确指定容器名称和端口
    this.wikijsTargetGroup.addTarget(this.service.loadBalancerTarget({
      containerName: 'WikiJSContainer',
      containerPort: config.wikijs.service.port,
    }));

    // 输出 Wiki.js 访问 URL
    if (loadBalancerStack.publicLoadBalancer && config.wikijs.routing.domain) {
      const protocol = config.loadBalancer.ssl.enableHttpsRedirect ? 'https' : 'http';
      const url = `${protocol}://${config.wikijs.routing.domain}`;
      
      new cdk.CfnOutput(this, 'WikiJSDNS', {
        value: url,
        description: 'Wiki.js Service URL',
        exportName: `${config.environment}-wikijs-url`,
      });
    }
  }

  /**
   * 设置自动扩容
   */
  private setupAutoScaling(): void {
    const scaling = this.service.autoScaleTaskCount({
      minCapacity: this.config.wikijs.ecs.minCapacity,
      maxCapacity: this.config.wikijs.ecs.maxCapacity,
    });

    // CPU 使用率扩容策略
    scaling.scaleOnCpuUtilization('CpuScaling', {
      targetUtilizationPercent: 70,
      scaleInCooldown: cdk.Duration.minutes(5),
      scaleOutCooldown: cdk.Duration.minutes(2),
    });

    // 内存使用率扩容策略
    scaling.scaleOnMemoryUtilization('MemoryScaling', {
      targetUtilizationPercent: 80,
      scaleInCooldown: cdk.Duration.minutes(5),
      scaleOutCooldown: cdk.Duration.minutes(2),
    });
  }
}