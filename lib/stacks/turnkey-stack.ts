import * as cdk from 'aws-cdk-lib';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as servicediscovery from 'aws-cdk-lib/aws-servicediscovery';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { DockerImageAsset, Platform } from 'aws-cdk-lib/aws-ecr-assets';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import * as path from 'path';

export interface TurnkeyStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  cluster: ecs.Cluster;
  serviceConnectNamespace: servicediscovery.HttpNamespace;
  sharedEfsFileSystem: efs.FileSystem;
  secureAssetsBucket: s3.Bucket;
}

/**
 * 电子发票Turnkey系统栈
 * 提供完整的电子发票交换服务，包括配置管理和健康检查
 */
export class TurnkeyStack extends cdk.Stack {
  public readonly turnkeyService: ecs.Ec2Service;
  public readonly turnkeySecret: secretsmanager.Secret;
  public readonly sharedEfsFileSystem: efs.FileSystem;
  private dataAccessPoint: efs.AccessPoint;

  constructor(scope: Construct, id: string, props: TurnkeyStackProps) {
    super(scope, id, props);

    const { config, vpc, cluster, serviceConnectNamespace, sharedEfsFileSystem, secureAssetsBucket } = props;

    // 使用ECS共享EFS文件系统
    this.sharedEfsFileSystem = sharedEfsFileSystem;

    // 创建Turnkey Docker镜像资产
    const turnkeyImageAsset = config.turnkey.image.useCustomImage
      ? new DockerImageAsset(this, 'TurnkeyDockerImageAsset', {
          directory: path.join(__dirname, '../../apps/einvoice-turnkey/docker'),
          platform: Platform.LINUX_AMD64,
        })
      : undefined;

    // 创建Turnkey密钥（用于存储启动密码等敏感信息）
    this.turnkeySecret = new secretsmanager.Secret(this, 'TurnkeySecret', {
      description: 'Taiwan E-Invoice Turnkey System secrets',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({
          system_key: 'turnkey_system_key_change_in_production',
        }),
        generateStringKey: 'startup_password',
        includeSpace: false,
        excludeUppercase: false,
        excludeLowercase: false,
        excludeNumbers: false,
        excludePunctuation: true,
        passwordLength: 16,
      },
    });

    // SSM Parameter Store 将由运维团队单独创建和管理证书
    // 不需要在 CDK 中创建 SSM 参数，只需要授权访问

    // 创建Turnkey专用的EFS访问点
    this.createTurnkeyEfsAccessPoints(config);

    // 创建Turnkey服务
    this.turnkeyService = this.createTurnkeyService(
      config,
      cluster,
      serviceConnectNamespace,
      secureAssetsBucket,
      turnkeyImageAsset
    );


    // 输出重要信息
    this.createOutputs(config);
  }

  /**
   * 为Turnkey创建专用的EFS访问点，使用共享EFS的子目录
   * 目录结构: /ecs/turnkey/work/
   */
  private createTurnkeyEfsAccessPoints(config: EnvironmentConfig): void {
    // 创建Turnkey工作目录访问点 - 使用共享EFS的turnkey/work子目录
    this.dataAccessPoint = this.sharedEfsFileSystem.addAccessPoint('TurnkeyWorkAccessPoint', {
      path: '/ecs/turnkey/work',
      posixUser: {
        uid: config.turnkey.workDirectory.posixUser.uid,
        gid: config.turnkey.workDirectory.posixUser.gid,
      },
      createAcl: {
        ownerUid: config.turnkey.workDirectory.posixUser.uid,
        ownerGid: config.turnkey.workDirectory.posixUser.gid,
        permissions: '755',
      },
    });
  }

  /**
   * 构建证书文件S3 URL列表
   */
  private buildCertFilesUrls(config: EnvironmentConfig, secureAssetsBucket: s3.Bucket): string {
    if (!config.turnkey.certificates.enabled || !config.turnkey.certificates.fileList.length) {
      return '';
    }

    const certUrls = config.turnkey.certificates.fileList.map(filename => 
      `s3://${secureAssetsBucket.bucketName}/turnkey/certs/${filename}`
    );

    return certUrls.join(',');
  }

  /**
   * 创建Turnkey任务定义
   */
  private createTurnkeyTaskDefinition(
    config: EnvironmentConfig,
    serviceConnectNamespace: servicediscovery.HttpNamespace,
    secureAssetsBucket: s3.Bucket,
    turnkeyImageAsset?: DockerImageAsset
  ): ecs.Ec2TaskDefinition {
    // 创建执行角色
    const executionRole = new iam.Role(this, 'TurnkeyExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
      ],
    });

    const taskDefinition = new ecs.Ec2TaskDefinition(this, 'TurnkeyTaskDefinition', {
      family: `turnkey-${config.environment}`,
      networkMode: ecs.NetworkMode.BRIDGE,  // 使用BRIDGE模式节省ENI
      executionRole: executionRole,
    });

    // 获取数据库密码Secret引用
    const dbPasswordSecret = secretsmanager.Secret.fromSecretNameV2(
      this, 
      'TurnkeyDbPasswordSecret', 
      config.turnkey.database.passwordSecretName
    );

    // 为任务定义添加EFS访问权限
    this.sharedEfsFileSystem.grantRootAccess(taskDefinition.taskRole);

    // 添加S3证书访问权限
    secureAssetsBucket.grantRead(taskDefinition.taskRole, 'turnkey/certs/*');

    // 添加Secrets Manager访问权限（包含数据库密码密钥）
    const secretsPolicy = new iam.Policy(this, 'TurnkeySecretsPolicy', {
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'secretsmanager:GetSecretValue',
          ],
          resources: [
            this.turnkeySecret.secretArn,
            dbPasswordSecret.secretArn,
          ],
        }),
      ],
    });
    
    secretsPolicy.attachToRole(taskDefinition.taskRole);


    // 添加EFS权限
    const efsPolicy = new iam.Policy(this, 'TurnkeyEfsPolicy', {
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'elasticfilesystem:ClientMount',
            'elasticfilesystem:ClientWrite',
            'elasticfilesystem:ClientRootAccess',
            'elasticfilesystem:DescribeFileSystems',
            'elasticfilesystem:DescribeAccessPoints',
            'elasticfilesystem:DescribeMountTargets',
          ],
          resources: [
            this.sharedEfsFileSystem.fileSystemArn,
            `${this.sharedEfsFileSystem.fileSystemArn}/*`,
          ],
        }),
      ],
    });

    efsPolicy.attachToRole(taskDefinition.taskRole);

    // 添加ECR权限到执行角色
    const ecrPolicy = new iam.Policy(this, 'TurnkeyEcrPolicy', {
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecr:GetAuthorizationToken',
          ],
          resources: ['*'],
        }),
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecr:BatchCheckLayerAvailability',
            'ecr:GetDownloadUrlForLayer',
            'ecr:BatchGetImage',
            'ecr:DescribeRepositories',
            'ecr:DescribeImages',
          ],
          resources: [
            `arn:aws:ecr:${this.region}:${this.account}:repository/*`,
          ],
        }),
      ],
    });

    ecrPolicy.attachToRole(executionRole);

    // 添加S3权限到任务角色（用于证书下载）
    const s3Policy = new iam.Policy(this, 'TurnkeyS3Policy', {
      statements: [
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            's3:GetObject',
          ],
          resources: [
            `${secureAssetsBucket.bucketArn}/turnkey/certs/*`,
          ],
        }),
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            's3:ListBucket',
          ],
          resources: [
            secureAssetsBucket.bucketArn,
          ],
          conditions: {
            StringLike: {
              's3:prefix': ['turnkey/certs/*'],
            },
          },
        }),
      ],
    });

    s3Policy.attachToRole(taskDefinition.taskRole);

    // 添加EFS工作目录卷
    taskDefinition.addVolume({
      name: 'turnkey-work',
      efsVolumeConfiguration: {
        fileSystemId: this.sharedEfsFileSystem.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.dataAccessPoint.accessPointId,
        },
      },
    });

    // 获取镜像
    const getContainerImage = (): ecs.ContainerImage => {
      if (config.turnkey.image.useCustomImage && turnkeyImageAsset) {
        return ecs.ContainerImage.fromDockerImageAsset(turnkeyImageAsset);
      } else if (config.turnkey.image.url) {
        // 替换ECR registry占位符
        const imageUrl = config.turnkey.image.url.replace(
          '{{ECR_REGISTRY}}',
          `${this.account}.dkr.ecr.${this.region}.amazonaws.com`
        );
        return ecs.ContainerImage.fromRegistry(imageUrl);
      } else {
        throw new Error('Turnkey image configuration is invalid');
      }
    };

    // Turnkey主容器配置
    const container = taskDefinition.addContainer('TurnkeyContainer', {
      image: getContainerImage(),
      cpu: config.turnkey.resources.cpu,
      memoryReservationMiB: config.turnkey.resources.memory,
      command: ['start'],
      environment: {
        // AWS区域配置（确保MinIO Client使用正确区域）
        AWS_DEFAULT_REGION: this.region,
        AWS_REGION: this.region,
        
        // 数据库连接配置（从配置文件获取基本信息）
        DB_HOST: config.turnkey.database.host,
        DB_PORT: config.turnkey.database.port.toString(),
        DB_NAME: config.turnkey.database.databaseName,
        DB_USER: config.turnkey.database.userName,
        
        // S3证书管理配置
        CERT_FILES: this.buildCertFilesUrls(config, secureAssetsBucket),
        
        // 应用环境配置
        ...config.turnkey.environmentVariables,
      },
      secrets: {
        // 数据库密码（从AWS Secrets Manager获取）
        DB_PASSWORD: ecs.Secret.fromSecretsManager(dbPasswordSecret, "password"),
        
        // 启动密码（如果配置了）
        ...(config.turnkey.startupPasswordSecret ? {
          TURNKEY_PASSWORD: ecs.Secret.fromSecretsManager(
            secretsmanager.Secret.fromSecretNameV2(this, 'ImportedStartupSecret', config.turnkey.startupPasswordSecret)
          ),
        } : {}),
      },
      // Turnkey系统作为内部服务，不暴露HTTP端口
      // 主要通过文件系统和数据库交互，无需外部网络访问
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'turnkey',
        logGroup: new logs.LogGroup(this, 'TurnkeyLogGroup', {
          logGroupName: `/aws/ecs/turnkey-${config.environment}`,
          retention: logs.RetentionDays.ONE_MONTH,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      healthCheck: {
        command: ['CMD-SHELL', '/app/health-check.sh || exit 1'],
        interval: cdk.Duration.seconds(config.turnkey.monitoring.healthCheckInterval),
        timeout: cdk.Duration.seconds(config.turnkey.monitoring.healthCheckTimeout),
        retries: config.turnkey.monitoring.healthCheckRetries,
        startPeriod: cdk.Duration.seconds(config.turnkey.monitoring.startupGracePeriod),
      },
    });

    // 添加EFS工作目录挂载点
    container.addMountPoints({
      sourceVolume: 'turnkey-work',
      containerPath: config.turnkey.workDirectory.mountPath,
      readOnly: config.turnkey.workDirectory.readOnly,
    });

    return taskDefinition;
  }

  /**
   * 创建Turnkey服务
   */
  private createTurnkeyService(
    config: EnvironmentConfig,
    cluster: ecs.Cluster,
    serviceConnectNamespace: servicediscovery.HttpNamespace,
    secureAssetsBucket: s3.Bucket,
    turnkeyImageAsset?: DockerImageAsset
  ): ecs.Ec2Service {
    const taskDefinition = this.createTurnkeyTaskDefinition(
      config,
      serviceConnectNamespace,
      secureAssetsBucket,
      turnkeyImageAsset
    );

    return new ecs.Ec2Service(this, 'TurnkeyService', {
      cluster,
      taskDefinition,
      desiredCount: config.turnkey.replicas,
      serviceName: `turnkey-${config.environment}`,
      enableExecuteCommand: true,
        serviceConnectConfiguration: {
        namespace: serviceConnectNamespace.namespaceName,
        // Turnkey作为内部客户端服务，只需要Service Connect的客户端功能来访问数据库
        // 不提供任何服务端口，不需要services配置
      },
    });
  }


  /**
   * 创建输出信息
   */
  private createOutputs(config: EnvironmentConfig): void {
    new cdk.CfnOutput(this, 'TurnkeySecretArn', {
      value: this.turnkeySecret.secretArn,
      description: 'Turnkey Secret ARN',
      exportName: `${config.environment}-turnkey-secret-arn`,
    });

    new cdk.CfnOutput(this, 'TurnkeyCertificateInfo', {
      value: JSON.stringify({
        enabled: config.turnkey.certificates.enabled,
        fileCount: config.turnkey.certificates.fileList.length,
        files: config.turnkey.certificates.fileList,
      }),
      description: 'Turnkey Certificate configuration information',
      exportName: `${config.environment}-turnkey-certificate-info`,
    });

    new cdk.CfnOutput(this, 'TurnkeyServiceArn', {
      value: this.turnkeyService.serviceArn,
      description: 'Turnkey ECS Service ARN',
      exportName: `${config.environment}-turnkey-service-arn`,
    });

    new cdk.CfnOutput(this, 'TurnkeySharedEFSId', {
      value: this.sharedEfsFileSystem.fileSystemId,
      description: 'Turnkey Shared EFS file system ID (from ECS stack)',
      exportName: `${config.environment}-turnkey-shared-efs-id`,
    });

    new cdk.CfnOutput(this, 'TurnkeyDatabaseInfo', {
      value: JSON.stringify({
        databaseName: config.turnkey.database.databaseName,
        userName: config.turnkey.database.userName,
        environment: config.environment,
      }),
      description: 'Turnkey Database Configuration Info',
      exportName: `${config.environment}-turnkey-database-info`,
    });
  }
}