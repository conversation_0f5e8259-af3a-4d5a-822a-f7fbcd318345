import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import * as logs from 'aws-cdk-lib/aws-logs';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';

export interface NetworkStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
}

/**
 * 网络基础设施栈
 * 包含VPC、子网、安全组、NAT网关等网络组件
 */
export class NetworkStack extends cdk.Stack {
  public readonly vpc: ec2.Vpc;
  public readonly databaseSecurityGroup: ec2.SecurityGroup;
  public readonly ecsSecurityGroup: ec2.SecurityGroup;
  public readonly redisSecurityGroup: ec2.SecurityGroup;
  public readonly albSecurityGroup: ec2.SecurityGroup;
  public readonly zitiSecurityGroup: ec2.SecurityGroup;
  public readonly webAcl?: wafv2.CfnWebACL;
  public readonly natGatewayEIPs: ec2.CfnEIP[];

  constructor(scope: Construct, id: string, props: NetworkStackProps) {
    super(scope, id, props);

    const { config } = props;

    // 创建固定的NAT网关EIP (仅在启用时)
    this.natGatewayEIPs = [];
    if (config.network.vpc.useFixedNatGatewayEIPs) {
      for (let i = 0; i < config.network.vpc.natGateways; i++) {
        const eip = new ec2.CfnEIP(this, `NatGateway${i + 1}EIP`, {
          domain: 'vpc',
          tags: [
            { key: 'Name', value: `NAT-Gateway-${i + 1}-${config.environment}` },
            { key: 'Environment', value: config.environment },
            { key: 'Project', value: 'YuanhuiOdoo' },
            { key: 'Purpose', value: 'NAT Gateway Fixed IP' }
          ]
        });
        this.natGatewayEIPs.push(eip);
      }
    }

    // 创建VPC (恢复原有子网架构，仅优化NAT网关数量)
    const vpcOptions: any = {
      ipAddresses: ec2.IpAddresses.cidr(config.network.vpc.cidr),
      maxAzs: config.network.vpc.maxAzs,
      natGateways: config.network.vpc.natGateways, // 生产环境从3改为1，所有私有子网共享
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'Public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'Private', 
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 28,
          name: 'Database',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
      ],
      enableDnsHostnames: true,
      enableDnsSupport: true,
    };

    // 配置NAT网关提供程序（如果使用固定EIP）
    if (config.network.vpc.useFixedNatGatewayEIPs && this.natGatewayEIPs.length > 0) {
      vpcOptions.natGatewayProvider = ec2.NatProvider.gateway({
        eipAllocationIds: this.natGatewayEIPs.map(eip => eip.attrAllocationId)
      });
    }

    this.vpc = new ec2.Vpc(this, 'YuanhuiVpc', vpcOptions);

    // 公网应用负载均衡器安全组
    this.albSecurityGroup = new ec2.SecurityGroup(this, 'PublicAlbSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for Public Application Load Balancer',
      allowAllOutbound: true,
    });

    this.albSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(80),
      'Allow HTTP traffic from internet'
    );

    this.albSecurityGroup.addIngressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(443),
      'Allow HTTPS traffic from internet'
    );


    // ECS服务安全组
    this.ecsSecurityGroup = new ec2.SecurityGroup(this, 'EcsSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for ECS services',
      allowAllOutbound: true,
    });

    // 允许来自公网ALB的流量到动态端口范围
    this.ecsSecurityGroup.addIngressRule(
      this.albSecurityGroup,
      ec2.Port.tcpRange(32768, 65535),
      'Allow ALB access to ECS dynamic port range (32768-65535)'
    );


    // 允许服务间通信
    this.ecsSecurityGroup.addIngressRule(
      this.ecsSecurityGroup,
      ec2.Port.allTraffic(),
      'Allow inter-service communication'
    );

    // 允许访问 EFS（NFS 端口 2049）
    this.ecsSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(this.vpc.vpcCidrBlock),
      ec2.Port.tcp(2049),
      'Allow EFS access from ECS'
    );

    // 注意：由于上面已允许ALB到ECS的所有流量，健康检查和应用流量都已被覆盖
    // 删除重复的端口特定规则以避免冗余

    // 数据库安全组
    this.databaseSecurityGroup = new ec2.SecurityGroup(this, 'DatabaseSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for PostgreSQL database',
      allowAllOutbound: false,
    });

    this.databaseSecurityGroup.addIngressRule(
      this.ecsSecurityGroup,
      ec2.Port.tcp(5432),
      'Allow PostgreSQL access from ECS'
    );

    this.databaseSecurityGroup.addIngressRule(
      this.ecsSecurityGroup,
      ec2.Port.tcp(5434),
      'Allow PgPool-II access from ECS'
    );

    // 为OpenZiti组件创建专用安全组（将由OpenZiti栈使用）
    this.zitiSecurityGroup = new ec2.SecurityGroup(this, 'ZitiSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for OpenZiti components',
      allowAllOutbound: true,
    });

    // 允许OpenZiti访问数据库
    this.databaseSecurityGroup.addIngressRule(
      this.zitiSecurityGroup,
      ec2.Port.tcp(5432),
      'Allow PostgreSQL access from OpenZiti components'
    );

    // Redis安全组
    this.redisSecurityGroup = new ec2.SecurityGroup(this, 'RedisSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for Redis cache',
      allowAllOutbound: false,
    });

    this.redisSecurityGroup.addIngressRule(
      this.ecsSecurityGroup,
      ec2.Port.tcp(6379),
      'Allow Redis access from ECS'
    );

    // 创建WAF Web ACL（如果启用）
    if (config.network.waf.enabled) {
      this.webAcl = this.createWebAcl(config);
    }

    // 输出重要的网络信息
    new cdk.CfnOutput(this, 'VpcId', {
      value: this.vpc.vpcId,
      description: 'VPC ID',
      exportName: `${config.environment}-vpc-id`,
    });

    new cdk.CfnOutput(this, 'VpcCidr', {
      value: this.vpc.vpcCidrBlock,
      description: 'VPC CIDR Block',
      exportName: `${config.environment}-vpc-cidr`,
    });

    new cdk.CfnOutput(this, 'PrivateSubnetIds', {
      value: this.vpc.privateSubnets.map(subnet => subnet.subnetId).join(','),
      description: 'Private Subnet IDs',
      exportName: `${config.environment}-private-subnet-ids`,
    });

    new cdk.CfnOutput(this, 'PublicSubnetIds', {
      value: this.vpc.publicSubnets.map(subnet => subnet.subnetId).join(','),
      description: 'Public Subnet IDs',
      exportName: `${config.environment}-public-subnet-ids`,
    });

    // 输出安全组信息
    new cdk.CfnOutput(this, 'DatabaseSecurityGroupId', {
      value: this.databaseSecurityGroup.securityGroupId,
      description: 'Database Security Group ID',
      exportName: `${config.environment}-database-security-group-id`,
    });

    new cdk.CfnOutput(this, 'RedisSecurityGroupId', {
      value: this.redisSecurityGroup.securityGroupId,
      description: 'Redis Security Group ID',
      exportName: `${config.environment}-redis-security-group-id`,
    });

    new cdk.CfnOutput(this, 'EcsSecurityGroupId', {
      value: this.ecsSecurityGroup.securityGroupId,
      description: 'ECS Security Group ID',
      exportName: `${config.environment}-ecs-security-group-id`,
    });

    new cdk.CfnOutput(this, 'ZitiSecurityGroupId', {
      value: this.zitiSecurityGroup.securityGroupId,
      description: 'OpenZiti Security Group ID',
      exportName: `${config.environment}-ziti-security-group-id`,
    });

    // 输出WAF信息
    if (this.webAcl) {
      new cdk.CfnOutput(this, 'WebAclArn', {
        value: this.webAcl.attrArn,
        description: 'WAF Web ACL ARN',
        exportName: `${config.environment}-web-acl-arn`,
      });
    }

    // 输出NAT网关EIP信息
    this.natGatewayEIPs.forEach((eip, index) => {
      new cdk.CfnOutput(this, `NatGateway${index + 1}PublicIP`, {
        value: eip.ref,
        description: `NAT Gateway ${index + 1} Public IP (Fixed)`,
        exportName: `${config.environment}-nat-gateway-${index + 1}-public-ip`,
      });

      new cdk.CfnOutput(this, `NatGateway${index + 1}EIPAllocationId`, {
        value: eip.attrAllocationId,
        description: `NAT Gateway ${index + 1} EIP Allocation ID`,
        exportName: `${config.environment}-nat-gateway-${index + 1}-eip-allocation-id`,
      });
    });


    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'Network');
  }

  /**
   * 创建WAF Web ACL
   */
  private createWebAcl(config: EnvironmentConfig): wafv2.CfnWebACL {
    const rules: wafv2.CfnWebACL.RuleProperty[] = [];

    // AWS托管规则集
    if (config.network.waf.rules.enableAWSManagedRules) {
      // 核心规则集
      rules.push({
        name: 'AWSManagedRulesCommonRuleSet',
        priority: 1,
        overrideAction: { none: {} },
        statement: {
          managedRuleGroupStatement: {
            vendorName: 'AWS',
            name: 'AWSManagedRulesCommonRuleSet',
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'CommonRuleSetMetric',
        },
      });

      // 已知坏输入规则集
      rules.push({
        name: 'AWSManagedRulesKnownBadInputsRuleSet',
        priority: 2,
        overrideAction: { none: {} },
        statement: {
          managedRuleGroupStatement: {
            vendorName: 'AWS',
            name: 'AWSManagedRulesKnownBadInputsRuleSet',
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'KnownBadInputsRuleSetMetric',
        },
      });

      // SQL注入规则集
      rules.push({
        name: 'AWSManagedRulesSQLiRuleSet',
        priority: 3,
        overrideAction: { none: {} },
        statement: {
          managedRuleGroupStatement: {
            vendorName: 'AWS',
            name: 'AWSManagedRulesSQLiRuleSet',
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'SQLiRuleSetMetric',
        },
      });
    }

    // IP白名单规则
    if (config.network.waf.rules.enableIPWhitelist && config.network.waf.rules.whitelistIPs.length > 0) {
      rules.push({
        name: 'IPWhitelistRule',
        priority: 10,
        action: { allow: {} },
        statement: {
          ipSetReferenceStatement: {
            arn: this.createIPSet(config.network.waf.rules.whitelistIPs, 'Whitelist', config).attrArn,
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'IPWhitelistMetric',
        },
      });
    }

    // 地理位置阻止规则
    if (config.network.waf.rules.enableGeoBlocking && config.network.waf.rules.blockedCountries.length > 0) {
      rules.push({
        name: 'GeoBlockingRule',
        priority: 20,
        action: { block: {} },
        statement: {
          geoMatchStatement: {
            countryCodes: config.network.waf.rules.blockedCountries,
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'GeoBlockingMetric',
        },
      });
    }

    // 速率限制规则
    if (config.network.waf.rules.enableRateLimit) {
      rules.push({
        name: 'RateLimitRule',
        priority: 30,
        action: { block: {} },
        statement: {
          rateBasedStatement: {
            limit: config.network.waf.rules.rateLimit.limit,
            aggregateKeyType: 'IP',
          },
        },
        visibilityConfig: {
          sampledRequestsEnabled: true,
          cloudWatchMetricsEnabled: true,
          metricName: 'RateLimitMetric',
        },
      });
    }

    // 创建WAF日志组
    const wafLogGroup = new logs.LogGroup(this, 'WafLogGroup', {
      logGroupName: `/aws/wafv2/yuanhui-${config.environment}`,
      retention: logs.RetentionDays.ONE_MONTH,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // 创建Web ACL
    const webAcl = new wafv2.CfnWebACL(this, 'WebAcl', {
      scope: 'REGIONAL',
      defaultAction: { allow: {} },
      rules,
      visibilityConfig: {
        sampledRequestsEnabled: true,
        cloudWatchMetricsEnabled: true,
        metricName: `yuanhui-waf-${config.environment}`,
      },
      name: `yuanhui-waf-${config.environment}`,
      description: `WAF for Yuanhui Odoo ${config.environment} environment`,
    });

    // 配置WAF日志
    new wafv2.CfnLoggingConfiguration(this, 'WafLoggingConfig', {
      resourceArn: webAcl.attrArn,
      logDestinationConfigs: [wafLogGroup.logGroupArn],
    });

    return webAcl;
  }

  /**
   * 创建IP集合
   */
  private createIPSet(ips: string[], name: string, config: EnvironmentConfig): wafv2.CfnIPSet {
    return new wafv2.CfnIPSet(this, `${name}IPSet`, {
      scope: 'REGIONAL',
      ipAddressVersion: 'IPV4',
      addresses: ips,
      name: `yuanhui-${name.toLowerCase()}-${config.environment}`,
      description: `${name} IP set for ${config.environment}`,
    });
  }
}
