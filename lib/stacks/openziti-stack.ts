import * as cdk from 'aws-cdk-lib';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as efs from 'aws-cdk-lib/aws-efs';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';

export interface OpenZitiStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  cluster: ecs.Cluster;
  // hostedZone?: route53.IHostedZone; // DNS由第三方管理
  databaseEndpoint?: string;
  databaseSecret?: secretsmanager.ISecret;
}

/**
 * OpenZiti零信任网络栈
 * 为yh.kh2u.com提供安全的内部访问控制
 */
export class OpenZitiStack extends cdk.Stack {
  public readonly controllerService?: ecs.Ec2Service;
  public readonly routerService?: ecs.Ec2Service;
  public readonly controllerEndpoint?: string;
  private zitiTaskRole?: iam.Role;
  private zitiExecutionRole?: iam.Role;
  private enrollTokenSecret?: secretsmanager.Secret;
  private fileSystem?: efs.FileSystem;
  private accessPoint?: efs.AccessPoint;

  constructor(scope: Construct, id: string, props: OpenZitiStackProps) {
    super(scope, id, props);

    const { config, vpc, cluster, databaseEndpoint, databaseSecret } = props;

    // 如果OpenZiti未启用，跳过创建
    if (!config.network.openziti.enabled) {
      return;
    }

    // 创建EFS安全组
    const efsSecurityGroup = new ec2.SecurityGroup(this, 'ZitiEfsSecurityGroup', {
      vpc,
      description: 'Security group for OpenZiti EFS',
      allowAllOutbound: false,
    });

    // 允许来自ECS任务的NFS访问（端口2049）
    efsSecurityGroup.addIngressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.tcp(2049),
      'Allow NFS access from VPC'
    );

    // 允许出站流量到VPC（用于响应）
    efsSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.allTraffic(),
      'Allow outbound traffic to VPC'
    );

    // 创建EFS文件系统用于持久化存储
    this.fileSystem = new efs.FileSystem(this, 'ZitiFileSystem', {
      vpc,
      fileSystemName: `ziti-persistent-${config.environment}`,
      encrypted: true,
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      throughputMode: efs.ThroughputMode.BURSTING,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
      securityGroup: efsSecurityGroup,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
    });

    // 创建EFS访问点
    this.accessPoint = new efs.AccessPoint(this, 'ZitiAccessPoint', {
      fileSystem: this.fileSystem,
      path: '/persistent',
      posixUser: {
        uid: '0',  // root用户
        gid: '0',  // root组
      },
      createAcl: {
        ownerUid: '0',
        ownerGid: '0',
        permissions: '755',
      },
    });

    // 使用网络栈提供的OpenZiti安全组
    const zitiSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'ImportedZitiSecurityGroup',
      cdk.Fn.importValue(`${config.environment}-ziti-security-group-id`)
    );

    // 为从网络栈导入的安全组添加OpenZiti特定的入站规则
    zitiSecurityGroup.addIngressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.tcp(443),
      'Allow Ziti controller access from VPC'
    );

    // 允许路由器端口访问
    zitiSecurityGroup.addIngressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.tcp(3022),
      'Allow Ziti router access from VPC'
    );

    // 允许管理API访问
    zitiSecurityGroup.addIngressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.tcp(1280),
      'Allow Ziti management API access'
    );

    // 创建OpenZiti配置密钥
    const zitiSecret = new secretsmanager.Secret(this, 'ZitiSecret', {
      secretName: `yuanhui-ziti-config-${config.environment}`,
      description: 'OpenZiti configuration and credentials',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({
          admin_username: 'admin',
          network_name: config.network.openziti.network.name,
          controller_endpoint: config.network.openziti.controller.endpoint,
          router_name: `router-${config.environment}`,
          tunnel_identity_name: `database-tunnel-${config.environment}`,
        }),
        generateStringKey: 'admin_password',
        includeSpace: false,
        excludeUppercase: false,
        excludeLowercase: false,
        excludeNumbers: false,
        excludePunctuation: true,
        passwordLength: 32,
      },
    });

    // 创建注册令牌密钥
    this.enrollTokenSecret = new secretsmanager.Secret(this, 'ZitiEnrollTokenSecret', {
      secretName: `yuanhui-ziti-enroll-tokens-${config.environment}`,
      description: 'OpenZiti enrollment tokens for router and tunnel services',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({
          network_name: config.network.openziti.network.name,
          controller_endpoint: config.network.openziti.controller.endpoint,
        }),
        generateStringKey: 'placeholder_token',
        includeSpace: false,
        excludeUppercase: false,
        excludeLowercase: false,
        excludeNumbers: false,
        excludePunctuation: true,
        passwordLength: 64,
      },
    });

    // 创建OpenZiti控制器任务定义
    const controllerTaskDefinition = new ecs.Ec2TaskDefinition(this, 'ZitiControllerTask', {
      family: `ziti-controller-${config.environment}`,
      networkMode: ecs.NetworkMode.BRIDGE,
      taskRole: this.getZitiTaskRole(),
      executionRole: this.getZitiExecutionRole(),
    });

    // 添加EFS卷
    controllerTaskDefinition.addVolume({
      name: 'ziti-persistent',
      efsVolumeConfiguration: {
        fileSystemId: this.fileSystem!.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.accessPoint!.accessPointId,
        },
      },
    });

    // 添加控制器容器
    const controllerContainer = controllerTaskDefinition.addContainer('ZitiControllerContainer', {
      image: ecs.ContainerImage.fromRegistry('openziti/quickstart:latest'),
      memoryReservationMiB: 512,
      command: ['/var/openziti/scripts/run-controller.sh'],
      environment: {
        // 使用官方推荐的环境变量配置
        ZITI_CTRL_ADVERTISED_ADDRESS: config.network.openziti.controller.endpoint,
        ZITI_CTRL_ADVERTISED_PORT: '443',
        ZITI_EDGE_CTRL_ADVERTISED_ADDRESS: config.network.openziti.controller.endpoint,
        ZITI_EDGE_CTRL_ADVERTISED_PORT: '443',
        ZITI_ROUTER_ADVERTISED_ADDRESS: `ziti-edge-router-${config.environment}`,
        ZITI_ROUTER_PORT: '3022',
        // 网络配置
        ZITI_NETWORK_NAME: config.network.openziti.network.name,
        ZITI_CONTROLLER_HOSTNAME: config.network.openziti.controller.endpoint,
        // 启用详细日志
        VERBOSE: '1',
        DEBUG: '1',
      },
      secrets: {
        ZITI_PWD: ecs.Secret.fromSecretsManager(zitiSecret, 'admin_password'),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'ziti-controller',
        logGroup: new logs.LogGroup(this, 'ZitiControllerLogGroup', {
          logGroupName: `/aws/ecs/ziti-controller-${config.environment}`,
          retention: logs.RetentionDays.ONE_WEEK,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      portMappings: [
        {
          containerPort: 443,
          hostPort: 443,
          protocol: ecs.Protocol.TCP,
        },
        {
          containerPort: 1280,
          hostPort: 1280,
          protocol: ecs.Protocol.TCP,
        },
      ],
    });

    // 添加挂载点
    controllerContainer.addMountPoints({
      sourceVolume: 'ziti-persistent',
      containerPath: '/persistent',
      readOnly: false,
    });

    // 创建控制器服务
    this.controllerService = new ecs.Ec2Service(this, 'ZitiControllerService', {
      cluster,
      taskDefinition: controllerTaskDefinition,
      desiredCount: 1,
      serviceName: `ziti-controller-${config.environment}`,
    });

    // 创建初始化任务定义（用于设置访问控制策略）
    const initTaskDefinition = new ecs.Ec2TaskDefinition(this, 'ZitiInitTask', {
      family: `ziti-init-${config.environment}`,
      networkMode: ecs.NetworkMode.BRIDGE,
      taskRole: this.getZitiTaskRole(),
      executionRole: this.getZitiExecutionRole(),
    });

    // 添加EFS卷到初始化任务
    initTaskDefinition.addVolume({
      name: 'ziti-persistent',
      efsVolumeConfiguration: {
        fileSystemId: this.fileSystem!.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.accessPoint!.accessPointId,
        },
      },
    });

    // 添加初始化容器
    const initContainer = initTaskDefinition.addContainer('ZitiInitContainer', {
      image: ecs.ContainerImage.fromRegistry('openziti/quickstart:latest'),
      memoryReservationMiB: 256,
      command: ['/var/openziti/scripts/run-with-ziti-cli.sh', '/var/openziti/scripts/access-control.sh'],
      environment: {
        ZITI_CTRL_ADVERTISED_ADDRESS: config.network.openziti.controller.endpoint,
        ZITI_CTRL_ADVERTISED_PORT: '443',
        ZITI_EDGE_CTRL_ADVERTISED_ADDRESS: config.network.openziti.controller.endpoint,
        ZITI_EDGE_CTRL_ADVERTISED_PORT: '443',
      },
      secrets: {
        ZITI_PWD: ecs.Secret.fromSecretsManager(zitiSecret, 'admin_password'),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'ziti-init',
        logGroup: new logs.LogGroup(this, 'ZitiInitLogGroup', {
          logGroupName: `/aws/ecs/ziti-init-${config.environment}`,
          retention: logs.RetentionDays.ONE_WEEK,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      essential: true,
    });

    // 添加挂载点
    initContainer.addMountPoints({
      sourceVolume: 'ziti-persistent',
      containerPath: '/persistent',
      readOnly: false,
    });

    // 创建OpenZiti路由器任务定义
    const routerTaskDefinition = new ecs.Ec2TaskDefinition(this, 'ZitiRouterTask', {
      family: `ziti-router-${config.environment}`,
      networkMode: ecs.NetworkMode.BRIDGE,
      taskRole: this.getZitiTaskRole(),
      executionRole: this.getZitiExecutionRole(),
    });

    // 添加EFS卷到路由器任务
    routerTaskDefinition.addVolume({
      name: 'ziti-persistent',
      efsVolumeConfiguration: {
        fileSystemId: this.fileSystem!.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.accessPoint!.accessPointId,
        },
      },
    });

    // 添加路由器容器
    const routerContainer = routerTaskDefinition.addContainer('ZitiRouterContainer', {
      image: ecs.ContainerImage.fromRegistry('openziti/quickstart:latest'),
      memoryReservationMiB: 256,
      command: ['/var/openziti/scripts/run-router.sh', 'edge'],
      environment: {
        ZITI_CTRL_ADVERTISED_ADDRESS: config.network.openziti.controller.endpoint,
        ZITI_CTRL_ADVERTISED_PORT: '443',
        ZITI_ROUTER_ADVERTISED_ADDRESS: `ziti-edge-router-${config.environment}`,
        ZITI_ROUTER_PORT: '3022',
        ZITI_ROUTER_NAME: `ziti-edge-router-${config.environment}`,
        // 启用详细日志
        VERBOSE: '1',
        DEBUG: '1',
      },
      secrets: {
        ZITI_PWD: ecs.Secret.fromSecretsManager(zitiSecret, 'admin_password'),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'ziti-router',
        logGroup: new logs.LogGroup(this, 'ZitiRouterLogGroup', {
          logGroupName: `/aws/ecs/ziti-router-${config.environment}`,
          retention: logs.RetentionDays.ONE_WEEK,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      portMappings: [
        {
          containerPort: 3022,
          hostPort: 3022,
          protocol: ecs.Protocol.TCP,
        },
      ],
    });

    // 添加挂载点
    routerContainer.addMountPoints({
      sourceVolume: 'ziti-persistent',
      containerPath: '/persistent',
      readOnly: false,
    });

    // 创建路由器服务（依赖控制器）
    this.routerService = new ecs.Ec2Service(this, 'ZitiRouterService', {
      cluster,
      taskDefinition: routerTaskDefinition,
      desiredCount: 1, // 现在可以自动启动
      serviceName: `ziti-router-${config.environment}`,
    });
    
    // 路由器服务依赖控制器服务
    this.routerService.node.addDependency(this.controllerService);

    // 如果有数据库端点，创建数据库隧道服务
    if (databaseEndpoint && databaseSecret) {
      this.createDatabaseTunnelService(
        cluster,
        databaseEndpoint,
        databaseSecret,
        config
      );
    }

    // 设置控制器端点
    this.controllerEndpoint = config.network.openziti.controller.endpoint;

    // DNS记录由第三方管理
    // 需要手动配置域名 ziti-controller-dev.kh2u.com 指向内部负载均衡器

    // 输出OpenZiti信息
    new cdk.CfnOutput(this, 'ZitiControllerEndpoint', {
      value: this.controllerEndpoint,
      description: 'OpenZiti Controller Endpoint',
      exportName: `${config.environment}-ziti-controller-endpoint`,
    });

    new cdk.CfnOutput(this, 'ZitiNetworkName', {
      value: config.network.openziti.network.name,
      description: 'OpenZiti Network Name',
      exportName: `${config.environment}-ziti-network-name`,
    });

    new cdk.CfnOutput(this, 'ZitiSecretArn', {
      value: zitiSecret.secretArn,
      description: 'OpenZiti Configuration Secret ARN',
      exportName: `${config.environment}-ziti-secret-arn`,
    });

    new cdk.CfnOutput(this, 'ZitiEnrollTokenSecretArn', {
      value: this.enrollTokenSecret!.secretArn,
      description: 'OpenZiti Enrollment Token Secret ARN',
      exportName: `${config.environment}-ziti-enroll-token-secret-arn`,
    });

    new cdk.CfnOutput(this, 'ZitiFileSystemId', {
      value: this.fileSystem!.fileSystemId,
      description: 'OpenZiti EFS File System ID',
      exportName: `${config.environment}-ziti-filesystem-id`,
    });

    new cdk.CfnOutput(this, 'ZitiAccessPointId', {
      value: this.accessPoint!.accessPointId,
      description: 'OpenZiti EFS Access Point ID',
      exportName: `${config.environment}-ziti-access-point-id`,
    });

    new cdk.CfnOutput(this, 'ZitiSetupInstructions', {
      value: this.getZitiSetupInstructions(config.environment),
      description: 'OpenZiti setup instructions',
    });

    // 如果配置了数据库服务，输出连接信息
    if (databaseEndpoint) {
      new cdk.CfnOutput(this, 'DatabaseTunnelInstructions', {
        value: 'Database accessible via OpenZiti tunnel at localhost:5432',
        description: 'Database connection instructions via OpenZiti',
      });

      new cdk.CfnOutput(this, 'MacOSClientSetupScript', {
        value: this.getDatabaseClientSetupScript(),
        description: 'macOS ARM client setup script for database access',
      });
    }

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'OpenZiti');
  }

  /**
   * 获取或创建OpenZiti任务角色（单例）
   */
  private getZitiTaskRole(): iam.Role {
    if (!this.zitiTaskRole) {
      this.zitiTaskRole = new iam.Role(this, 'ZitiTaskRole', {
        assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
        description: 'Task role for OpenZiti services',
      });

      // 添加必要的权限
      this.zitiTaskRole.addToPolicy(new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'ec2:DescribeNetworkInterfaces',
          'ec2:DescribeInstances',
          'ecs:DescribeServices',
          'ecs:DescribeTasks',
          // EFS权限
          'elasticfilesystem:ClientMount',
          'elasticfilesystem:ClientWrite',
          'elasticfilesystem:ClientRootAccess',
        ],
        resources: ['*'],
      }));
    }

    return this.zitiTaskRole;
  }

  /**
   * 获取或创建OpenZiti执行角色（单例）
   */
  private getZitiExecutionRole(): iam.Role {
    if (!this.zitiExecutionRole) {
      this.zitiExecutionRole = new iam.Role(this, 'ZitiExecutionRole', {
        assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
        description: 'Execution role for OpenZiti services',
        managedPolicies: [
          iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
        ],
      });

      // 添加访问Secrets Manager的权限
      this.zitiExecutionRole.addToPolicy(new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'secretsmanager:GetSecretValue',
        ],
        resources: ['*'],
      }));
    }

    return this.zitiExecutionRole;
  }

  /**
   * 创建数据库隧道服务
   */
  private createDatabaseTunnelService(
    cluster: ecs.Cluster,
    databaseEndpoint: string,
    _databaseSecret: secretsmanager.ISecret,
    config: EnvironmentConfig
  ): ecs.Ec2Service {
    // 获取或创建Ziti配置密钥
    const zitiSecret = secretsmanager.Secret.fromSecretNameV2(
      this,
      'ZitiDatabaseTunnelSecret',
      `yuanhui-ziti-config-${config.environment}`
    );
    // 创建数据库隧道任务定义
    const tunnelTaskDefinition = new ecs.Ec2TaskDefinition(this, 'ZitiDatabaseTunnelTask', {
      family: `ziti-database-tunnel-${config.environment}`,
      networkMode: ecs.NetworkMode.BRIDGE,
      taskRole: this.createZitiDatabaseTunnelTaskRole(),
      executionRole: this.createZitiDatabaseTunnelExecutionRole(),
    });

    // 解析数据库主机和端口
    const [dbHost, dbPort] = databaseEndpoint.split(':');

    // 添加EFS卷到隧道任务
    tunnelTaskDefinition.addVolume({
      name: 'ziti-persistent',
      efsVolumeConfiguration: {
        fileSystemId: this.fileSystem!.fileSystemId,
        transitEncryption: 'ENABLED',
        authorizationConfig: {
          iam: 'ENABLED',
          accessPointId: this.accessPoint!.accessPointId,
        },
      },
    });

    // 添加数据库隧道容器
    const tunnelContainer = tunnelTaskDefinition.addContainer('ZitiDatabaseTunnelContainer', {
      image: ecs.ContainerImage.fromRegistry('openziti/ziti-edge-tunnel:latest'),
      memoryReservationMiB: 128,
      environment: {
        ZITI_CTRL_ADVERTISED_ADDRESS: config.network.openziti.controller.endpoint,
        ZITI_CTRL_ADVERTISED_PORT: '443',
        DATABASE_HOST: dbHost,
        DATABASE_PORT: dbPort || '5432',
        TUNNEL_SERVICE_NAME: 'database-service',
        ZITI_IDENTITY_BASENAME: `database-tunnel-${config.environment}`,
      },
      secrets: {
        ZITI_PWD: ecs.Secret.fromSecretsManager(zitiSecret, 'admin_password'),
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'ziti-database-tunnel',
        logGroup: new logs.LogGroup(this, 'ZitiDatabaseTunnelLogGroup', {
          logGroupName: `/aws/ecs/ziti-database-tunnel-${config.environment}`,
          retention: logs.RetentionDays.ONE_WEEK,
          removalPolicy: cdk.RemovalPolicy.DESTROY,
        }),
      }),
      portMappings: [
        {
          containerPort: 5432,
          hostPort: 5432,
          protocol: ecs.Protocol.TCP,
        },
      ],
    });

    // 添加挂载点
    tunnelContainer.addMountPoints({
      sourceVolume: 'ziti-persistent',
      containerPath: '/persistent',
      readOnly: false,
    });

    // 创建数据库隧道服务（依赖控制器和路由器）
    const tunnelService = new ecs.Ec2Service(this, 'ZitiDatabaseTunnelService', {
      cluster,
      taskDefinition: tunnelTaskDefinition,
      desiredCount: 0, // 初始设为0，需要手动配置隧道服务后启动
      serviceName: `ziti-database-tunnel-${config.environment}`,
    });
    
    // 隧道服务依赖控制器和路由器服务
    tunnelService.node.addDependency(this.controllerService!);
    if (this.routerService) {
      tunnelService.node.addDependency(this.routerService);
    }
    
    return tunnelService;
  }

  /**
   * 创建数据库隧道任务角色
   */
  private createZitiDatabaseTunnelTaskRole(): iam.Role {
    const role = new iam.Role(this, 'ZitiDatabaseTunnelTaskRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      description: 'Task role for OpenZiti database tunnel service',
    });

    // 添加必要的权限
    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'ec2:DescribeNetworkInterfaces',
        'ec2:DescribeInstances',
        'ecs:DescribeServices',
        'ecs:DescribeTasks',
        // EFS权限
        'elasticfilesystem:ClientMount',
        'elasticfilesystem:ClientWrite',
        'elasticfilesystem:ClientRootAccess',
      ],
      resources: ['*'],
    }));

    return role;
  }

  /**
   * 创建数据库隧道执行角色
   */
  private createZitiDatabaseTunnelExecutionRole(): iam.Role {
    const role = new iam.Role(this, 'ZitiDatabaseTunnelExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      description: 'Execution role for OpenZiti database tunnel service',
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
      ],
    });

    // 添加访问Secrets Manager的权限
    role.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'secretsmanager:GetSecretValue',
      ],
      resources: ['*'],
    }));

    return role;
  }

  /**
   * 获取OpenZiti客户端配置脚本
   */
  public getClientSetupScript(): string {
    return `#!/bin/bash
# OpenZiti客户端设置脚本
# 使用方法: ./setup-ziti-client.sh

echo "设置OpenZiti客户端..."

# 下载并安装Ziti客户端
curl -sSLf https://get.openziti.io/install.sh | bash

# 配置环境变量
export ZITI_CTRL_ADVERTISED_ADDRESS="${this.controllerEndpoint}"
export ZITI_CTRL_ADVERTISED_PORT="443"

echo "OpenZiti客户端安装完成"
echo "控制器端点: ${this.controllerEndpoint}"
echo ""
echo "下一步:"
echo "1. 联系管理员获取身份配置文件"
echo "2. 使用 'ziti edge enroll' 命令注册身份"
echo "3. 使用 'ziti-edge-tunnel' 连接到网络"
echo "4. 数据库连接地址: localhost:5432"
`;
  }

  /**
   * 获取OpenZiti设置说明
   */
  private getZitiSetupInstructions(environment: string): string {
    return `OpenZiti setup (simplified with EFS persistence):

1. Wait for controller to be healthy:
   aws ecs describe-services --cluster yuanhui-odoo-${environment} --services ziti-controller-${environment}

2. Router service will start automatically after controller is healthy

3. For database tunnel (optional):
   - Access controller web console: https://${this.controllerEndpoint}:1280
   - Create identity for database tunnel: database-tunnel-${environment}
   - Download identity JSON file and configure tunnel service

4. Enable tunnel service (after creating identity):
   aws ecs update-service --cluster yuanhui-odoo-${environment} --service ziti-database-tunnel-${environment} --desired-count 1

5. Verify services are running:
   aws ecs describe-services --cluster yuanhui-odoo-${environment} --services ziti-controller-${environment} ziti-router-${environment}

Note: All configuration is now persisted in EFS, no manual token management required.
Controller web console: https://${this.controllerEndpoint}:1280`;
  }

  /**
   * 获取数据库客户端配置脚本（macOS ARM）
   */
  public getDatabaseClientSetupScript(): string {
    return `#!/bin/bash
# macOS ARM OpenZiti数据库客户端设置脚本
# 使用方法: ./setup-ziti-database-client.sh

echo "设置OpenZiti数据库客户端 (macOS ARM)..."

# 检查是否为Apple Silicon Mac
if [[ $(uname -m) != "arm64" ]]; then
  echo "警告: 此脚本专为Apple Silicon Mac设计"
fi

# 下载并安装Ziti客户端
echo "安装OpenZiti客户端..."
curl -sSLf https://get.openziti.io/install.sh | bash

# 安装PostgreSQL客户端工具
echo "安装PostgreSQL客户端工具..."
if command -v brew &> /dev/null; then
  brew install postgresql
else
  echo "请先安装Homebrew: https://brew.sh/"
  exit 1
fi

echo ""
echo "OpenZiti数据库客户端安装完成!"
echo ""
echo "配置步骤:"
echo "1. 获取身份配置文件 (.json) 并保存到 ~/.ziti/"
echo "2. 启动Ziti隧道: ziti-edge-tunnel run ~/.ziti/your-identity.json"
echo "3. 在新终端中连接数据库:"
echo "   psql -h localhost -p 5432 -U odoo_admin -d postgres"
echo ""
echo "控制器端点: ${this.controllerEndpoint}"
echo "本地数据库端口: 5432"
`;
  }
}
