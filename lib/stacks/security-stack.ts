import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';

export interface SecurityStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
}

/**
 * 安全栈 - 管理外部应用和基础设施的IAM权限
 * 专门用于GitHub Actions CI/CD流程的权限管理
 */
export class SecurityStack extends cdk.Stack {
  public readonly githubActionsUser: iam.User;
  public readonly githubActionsAccessKey: iam.AccessKey;
  public readonly secureAssetsBucket: s3.Bucket;

  constructor(scope: Construct, id: string, props: SecurityStackProps) {
    super(scope, id, props);

    const { config } = props;

    // 创建GitHub Actions专用IAM用户
    this.githubActionsUser = this.createGitHubActionsUser(config);

    // 创建访问密钥
    this.githubActionsAccessKey = this.createAccessKey(config);

    // 创建ECR权限策略
    this.createEcrPolicy(config);

    // 创建ECS权限策略
    this.createEcsPolicy(config);

    // 创建安全资产存储桶
    this.secureAssetsBucket = this.createSecureAssetsBucket(config);

    // 输出重要信息
    this.createOutputs(config);

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'Security');
  }

  /**
   * 创建GitHub Actions专用IAM用户
   */
  private createGitHubActionsUser(config: EnvironmentConfig): iam.User {
    return new iam.User(this, 'GitHubActionsUser', {
      userName: `yuanhui-github-actions-${config.environment}`,
      path: '/ci-cd/',
    });
  }

  /**
   * 创建访问密钥
   */
  private createAccessKey(config: EnvironmentConfig): iam.AccessKey {
    return new iam.AccessKey(this, 'GitHubActionsAccessKey', {
      user: this.githubActionsUser,
    });
  }

  /**
   * 创建ECR权限策略
   */
  private createEcrPolicy(config: EnvironmentConfig): void {
    // 允许访问所有ECR仓库的ARN模式
    const allEcrRepositoryArns = `arn:aws:ecr:${config.region}:${config.account}:repository/*`;

    const ecrPolicy = new iam.Policy(this, 'GitHubActionsEcrPolicy', {
      policyName: `yuanhui-github-actions-ecr-${config.environment}`,
      statements: [
        // ECR认证权限
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecr:GetAuthorizationToken',
          ],
          resources: ['*'],
        }),
        // ECR仓库管理权限 - 创建和描述仓库
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecr:CreateRepository',
            'ecr:DescribeRepositories',
          ],
          resources: ['*'],
        }),
        // ECR仓库操作权限 - 允许访问所有仓库
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecr:BatchCheckLayerAvailability',
            'ecr:GetDownloadUrlForLayer',
            'ecr:BatchGetImage',
            'ecr:PutImage',
            'ecr:InitiateLayerUpload',
            'ecr:UploadLayerPart',
            'ecr:CompleteLayerUpload',
            'ecr:DescribeImages',
            'ecr:ListImages',
            'ecr:StartImageScan',
            'ecr:DescribeImageScanFindings',
          ],
          resources: [allEcrRepositoryArns],
        }),
      ],
    });

    ecrPolicy.attachToUser(this.githubActionsUser);
  }

  /**
   * 创建ECS权限策略
   */
  private createEcsPolicy(config: EnvironmentConfig): void {
    // ECS集群ARN
    const clusterArn = `arn:aws:ecs:${config.region}:${config.account}:cluster/yuanhui-odoo-${config.environment}`;
    
    // ECS服务ARN模式
    const serviceArnPattern = `arn:aws:ecs:${config.region}:${config.account}:service/yuanhui-odoo-${config.environment}/*`;

    const ecsPolicy = new iam.Policy(this, 'GitHubActionsEcsPolicy', {
      policyName: `yuanhui-github-actions-ecs-${config.environment}`,
      statements: [
        // ECS集群和服务查询权限
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecs:DescribeClusters',
            'ecs:DescribeServices',
            'ecs:DescribeTasks',
            'ecs:DescribeTaskDefinition',
            'ecs:ListServices',
            'ecs:ListTasks',
          ],
          resources: [
            clusterArn,
            serviceArnPattern,
            `arn:aws:ecs:${config.region}:${config.account}:task-definition/*`,
            `arn:aws:ecs:${config.region}:${config.account}:task/yuanhui-odoo-${config.environment}/*`,
          ],
        }),
        // ECS服务更新权限
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            'ecs:UpdateService',
          ],
          resources: [serviceArnPattern],
          conditions: {
            StringEquals: {
              'ecs:cluster': clusterArn,
            },
          },
        }),
      ],
    });

    ecsPolicy.attachToUser(this.githubActionsUser);
  }

  /**
   * 创建安全资产存储桶
   */
  private createSecureAssetsBucket(config: EnvironmentConfig): s3.Bucket {
    return new s3.Bucket(this, 'SecureAssetsBucket', {
      bucketName: `yuanhui-secure-assets-${config.environment}`,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      versioned: true,
      lifecycleRules: [
        {
          // 非当前版本30天后删除，节省存储成本
          noncurrentVersionExpiration: cdk.Duration.days(30),
        },
      ],
      removalPolicy: config.environment === 'prod' ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
    });
  }

  /**
   * 创建输出
   */
  private createOutputs(config: EnvironmentConfig): void {
    // 输出IAM用户信息
    new cdk.CfnOutput(this, 'GitHubActionsUserName', {
      value: this.githubActionsUser.userName,
      description: 'GitHub Actions IAM用户名',
    });

    new cdk.CfnOutput(this, 'GitHubActionsUserArn', {
      value: this.githubActionsUser.userArn,
      description: 'GitHub Actions IAM用户ARN',
    });

    // 输出访问密钥信息
    new cdk.CfnOutput(this, 'GitHubActionsAccessKeyId', {
      value: this.githubActionsAccessKey.accessKeyId,
      description: 'GitHub Actions AWS_ACCESS_KEY_ID',
    });

    new cdk.CfnOutput(this, 'GitHubActionsSecretAccessKey', {
      value: this.githubActionsAccessKey.secretAccessKey.unsafeUnwrap(),
      description: 'GitHub Actions AWS_SECRET_ACCESS_KEY (请妥善保管)',
    });

    // 输出权限配置信息
    new cdk.CfnOutput(this, 'EcrRepositories', {
      value: '所有ECR仓库（*）',
      description: '有权限访问的ECR仓库范围',
    });

    new cdk.CfnOutput(this, 'EcsClusterName', {
      value: `yuanhui-odoo-${config.environment}`,
      description: '有权限管理的ECS集群名称',
    });

    // 输出使用说明
    new cdk.CfnOutput(this, 'GitHubSecretsSetup', {
      value: 'AWS_ACCESS_KEY_ID 和 AWS_SECRET_ACCESS_KEY',
      description: '在GitHub仓库设置中添加这两个密钥作为Secrets',
    });

    // 输出S3安全资产桶信息
    new cdk.CfnOutput(this, 'SecureAssetsBucketName', {
      value: this.secureAssetsBucket.bucketName,
      description: '安全资产存储桶名称',
      exportName: `${config.environment}-secure-assets-bucket-name`,
    });

    new cdk.CfnOutput(this, 'SecureAssetsBucketArn', {
      value: this.secureAssetsBucket.bucketArn,
      description: '安全资产存储桶ARN',
      exportName: `${config.environment}-secure-assets-bucket-arn`,
    });
  }
}
