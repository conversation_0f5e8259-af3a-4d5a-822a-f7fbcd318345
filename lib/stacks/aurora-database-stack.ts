import * as cdk from 'aws-cdk-lib';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as logs from 'aws-cdk-lib/aws-logs';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import { SqlExecutorConstruct } from '../constructs/sql-executor-construct';
import { BackupConstruct } from '../constructs/backup-construct';

export interface AuroraDatabaseStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  zitiSecurityGroup?: ec2.ISecurityGroup;
}

/**
 * RDS PostgreSQL 数据库栈
 * Dev环境使用免费套餐，Prod环境使用Aurora Serverless v2
 */
export class AuroraDatabaseStack extends cdk.Stack {
  public readonly auroraCluster?: rds.DatabaseCluster;
  public readonly rdsInstance?: rds.DatabaseInstance;
  public readonly databaseSecret: secretsmanager.Secret;
  public readonly clusterEndpoint: string;
  public readonly readerEndpoint: string;

  // Airflow专用数据库相关属性
  public readonly airflowDatabaseSecret: secretsmanager.Secret;

  // 备份构造
  public readonly databaseBackupConstruct: BackupConstruct;

  constructor(scope: Construct, id: string, props: AuroraDatabaseStackProps) {
    super(scope, id, props);

    const { config, vpc, zitiSecurityGroup } = props;

    // 创建数据库凭证
    this.databaseSecret = new secretsmanager.Secret(this, 'AuroraDatabaseSecret', {
      description: 'Aurora PostgreSQL database credentials',
      generateSecretString: {
        secretStringTemplate: JSON.stringify({
          username: 'odoo_admin',
          database: 'postgres',
          port: '5432'
        }),
        generateStringKey: 'password',
        includeSpace: false,
        excludeUppercase: false,
        excludeLowercase: false,
        excludeNumbers: false,
        excludePunctuation: true,
        passwordLength: 32,
      },
    });

    // 为数据库密钥添加标签
    cdk.Tags.of(this.databaseSecret).add('Environment', config.environment);
    cdk.Tags.of(this.databaseSecret).add('Project', 'Yuanhui');
    cdk.Tags.of(this.databaseSecret).add('Component', 'Database');

    // 获取数据库安全组（从网络栈导入）
    const databaseSecurityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      'ImportedDatabaseSecurityGroup',
      cdk.Fn.importValue(`${config.environment}-database-security-group-id`)
    );

    // 如果OpenZiti启用，允许Ziti路由器访问数据库
    if (zitiSecurityGroup && config.network.openziti.enabled) {
      databaseSecurityGroup.addIngressRule(
        zitiSecurityGroup,
        ec2.Port.tcp(5432),
        'Allow PostgreSQL access from OpenZiti router'
      );
    }

    // 允许来自日本区域 ap-server 的访问（兼容旧系统）
    const allowedCidrs = [
      '***********/20',
      '***********/24', 
      '***********/24'
    ];

    allowedCidrs.forEach((cidr, index) => {
      databaseSecurityGroup.addIngressRule(
        ec2.Peer.ipv4(cidr),
        ec2.Port.tcp(5432),
        `Allow PostgreSQL access from JP region ap-server ${index + 1} (${cidr})`
      );
    });

    // 允许来自当前VPC的Swarm节点访问（为未来的Swarm Stack准备）
    databaseSecurityGroup.addIngressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.tcp(5432),
      'Allow PostgreSQL access from VPC (including Swarm nodes)'
    );

    // 创建数据库子网组
    const dbSubnetGroup = new rds.SubnetGroup(this, 'DatabaseSubnetGroup', {
      description: `Subnet group for ${config.environment} PostgreSQL database`,
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
      },
    });

    // 根据环境选择数据库类型
    if (config.environment === 'dev') {
      // 开发环境：使用RDS免费套餐
      this.rdsInstance = new rds.DatabaseInstance(this, 'PostgreSQLInstance', {
        engine: rds.DatabaseInstanceEngine.postgres({
          version: rds.PostgresEngineVersion.VER_16_6,
        }),
        instanceType: ec2.InstanceType.of(ec2.InstanceClass.T4G, ec2.InstanceSize.MICRO), // 免费套餐
        credentials: rds.Credentials.fromSecret(this.databaseSecret),
        databaseName: 'postgres',
        vpc,
        vpcSubnets: {
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        securityGroups: [databaseSecurityGroup],
        subnetGroup: dbSubnetGroup,

        // 免费套餐存储配置
        allocatedStorage: 20, // 免费套餐最大20GB
        maxAllocatedStorage: 20, // 禁用自动扩展以避免费用
        storageType: rds.StorageType.GP2, // 通用SSD
        storageEncrypted: true,

        // 备份配置
        backupRetention: cdk.Duration.days(config.database.backupRetention),
        preferredBackupWindow: '03:00-04:00',

        // 维护配置
        preferredMaintenanceWindow: 'sun:04:00-sun:05:00',

        // 删除保护
        deletionProtection: config.database.deletionProtection,

        // 监控配置 - 开发环境关闭以节省费用
        monitoringInterval: cdk.Duration.seconds(0), // 关闭增强监控
        enablePerformanceInsights: false, // 关闭性能洞察

        // CloudWatch日志导出
        cloudwatchLogsExports: ['postgresql'],
        cloudwatchLogsRetention: logs.RetentionDays.THREE_DAYS,

        // 多可用区部署 - 开发环境关闭
        multiAz: false,

        // 自动小版本升级
        autoMinorVersionUpgrade: true,
      });

      // 设置端点
      this.clusterEndpoint = this.rdsInstance.instanceEndpoint.socketAddress;
      this.readerEndpoint = this.rdsInstance.instanceEndpoint.socketAddress; // 单实例，读写都是同一个端点

      // 为RDS实例添加标签
      cdk.Tags.of(this.rdsInstance).add('Environment', config.environment);
      cdk.Tags.of(this.rdsInstance).add('Project', 'Yuanhui');
      cdk.Tags.of(this.rdsInstance).add('Component', 'Database');

    } else {
      // 生产环境：使用Aurora Serverless v2
      this.auroraCluster = new rds.DatabaseCluster(this, 'AuroraPostgreSQLCluster', {
        engine: rds.DatabaseClusterEngine.auroraPostgres({
          version: rds.AuroraPostgresEngineVersion.VER_16_6,
        }),
        credentials: rds.Credentials.fromSecret(this.databaseSecret),
        defaultDatabaseName: 'postgres',
        vpc,
        vpcSubnets: {
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        securityGroups: [databaseSecurityGroup],
        subnetGroup: dbSubnetGroup,

        // Writer实例配置（Serverless v2）
        writer: rds.ClusterInstance.serverlessV2('writer', {
          scaleWithWriter: true,
        }),

        // Reader实例配置（如果需要）
        readers: config.database.readerInstances > 0 ? [
          ...Array.from({ length: config.database.readerInstances }, (_, i) =>
            rds.ClusterInstance.serverlessV2(`reader${i + 1}`, {
              scaleWithWriter: false,
            })
          )
        ] : [],

        // Serverless v2 配置
        serverlessV2MinCapacity: config.database.serverlessV2Scaling.minCapacity,
        serverlessV2MaxCapacity: config.database.serverlessV2Scaling.maxCapacity,

        // 备份配置
        backup: {
          retention: cdk.Duration.days(config.database.backupRetention),
          preferredWindow: '03:00-04:00',
        },

        // 维护配置
        preferredMaintenanceWindow: 'sun:04:00-sun:05:00',

        // 删除保护
        deletionProtection: config.database.deletionProtection,

        // 存储加密
        storageEncrypted: true,

        // 监控配置 - 根据环境和配置启用
        monitoringInterval: config.database.enablePerformanceInsights ? cdk.Duration.seconds(60) : undefined,
        enablePerformanceInsights: config.database.enablePerformanceInsights,
        performanceInsightRetention: config.database.enablePerformanceInsights
          ? (config.database.performanceInsightsRetention <= 7
             ? rds.PerformanceInsightRetention.DEFAULT
             : rds.PerformanceInsightRetention.MONTHS_1)
          : undefined,

        // CloudWatch日志导出 - 根据环境调整保留期
        cloudwatchLogsExports: ['postgresql'],
        cloudwatchLogsRetention: config.environment === 'prod'
          ? logs.RetentionDays.TWO_WEEKS  // 生产环境2周
          : logs.RetentionDays.THREE_DAYS, // 开发环境3天
      });

      // 设置端点
      this.clusterEndpoint = this.auroraCluster.clusterEndpoint.socketAddress;
      this.readerEndpoint = this.auroraCluster.clusterReadEndpoint.socketAddress;

      // 为Aurora集群添加标签
      cdk.Tags.of(this.auroraCluster).add('Environment', config.environment);
      cdk.Tags.of(this.auroraCluster).add('Project', 'Yuanhui');
      cdk.Tags.of(this.auroraCluster).add('Component', 'Database');
    }

    // 注释掉密码轮换功能，因为ap-east-2区域不支持Serverless Application Repository
    // 对于Aurora集群可以启用密码轮换
    // if (this.auroraCluster) {
    //   this.auroraCluster.addRotationSingleUser({
    //     automaticallyAfter: cdk.Duration.days(30),
    //   });
    // }

    // 创建Airflow专用数据库凭证
    const airflowDatabaseName = config.environment === 'dev' ? 'airflow-dev' : 'airflow';
    this.airflowDatabaseSecret = new secretsmanager.Secret(this, 'AirflowDatabaseSecret', {
      description: `Airflow PostgreSQL database credentials for ${config.environment} environment`,
      generateSecretString: {
        secretStringTemplate: JSON.stringify({
          username: 'airflow_user',
          database: airflowDatabaseName,
          port: '5432',
          host: this.clusterEndpoint.split(':')[0]
        }),
        generateStringKey: 'password',
        includeSpace: false,
        excludeUppercase: false,
        excludeLowercase: false,
        excludeNumbers: false,
        excludePunctuation: true,
        passwordLength: 32,
      },
    });

    // 为Airflow数据库密钥添加标签
    cdk.Tags.of(this.airflowDatabaseSecret).add('Environment', config.environment);
    cdk.Tags.of(this.airflowDatabaseSecret).add('Project', 'Yuanhui');
    cdk.Tags.of(this.airflowDatabaseSecret).add('Component', 'Airflow');

    // 使用SqlExecutorConstruct进行Airflow数据库初始化
    const airflowDatabaseInit = new SqlExecutorConstruct(this, 'AirflowDatabaseSetup', {
      config,
      vpc,
      databaseCluster: this.auroraCluster,
      databaseInstance: this.rdsInstance,
      databaseSecret: this.databaseSecret,
      databaseSecurityGroup: databaseSecurityGroup,
      resourceName: 'airflow-database-init',
      description: 'Initialize Airflow database and user',
      additionalSecrets: {
        airflow: this.airflowDatabaseSecret,
      },
      sqlCommands: [
        // 创建Airflow用户（使用DO块检查用户是否存在）
        `DO $$
        BEGIN
          IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'airflow_user') THEN
            CREATE USER airflow_user WITH PASSWORD '__AIRFLOW_PASSWORD__';
          ELSE
            ALTER USER airflow_user WITH PASSWORD '__AIRFLOW_PASSWORD__';
          END IF;
        END
        $$;`,

        // 创建Airflow数据库（使用DO块检查数据库是否存在）
        `DO $$
        BEGIN
          IF NOT EXISTS (SELECT FROM pg_database WHERE datname = '${airflowDatabaseName}') THEN
            CREATE DATABASE "${airflowDatabaseName}";
          END IF;
        END
        $$;`,

        // 授予权限
        `GRANT ALL PRIVILEGES ON DATABASE "${airflowDatabaseName}" TO airflow_user;`,
        `ALTER DATABASE "${airflowDatabaseName}" OWNER TO airflow_user;`,
      ],
    });

    // 输出重要信息
    new cdk.CfnOutput(this, 'DatabaseEndpoint', {
      value: this.clusterEndpoint,
      description: `${config.environment === 'dev' ? 'RDS PostgreSQL' : 'Aurora PostgreSQL'} database endpoint (read/write)`,
      exportName: `${config.environment}-database-endpoint`,
    });

    new cdk.CfnOutput(this, 'DatabaseReaderEndpoint', {
      value: this.readerEndpoint,
      description: `${config.environment === 'dev' ? 'RDS PostgreSQL' : 'Aurora PostgreSQL'} reader endpoint (read-only)`,
      exportName: `${config.environment}-database-reader-endpoint`,
    });

    new cdk.CfnOutput(this, 'DatabaseSecretArn', {
      value: this.databaseSecret.secretArn,
      description: 'Database credentials secret ARN',
      exportName: `${config.environment}-database-secret-arn`,
    });

    new cdk.CfnOutput(this, 'DatabaseType', {
      value: config.environment === 'dev' ? 'RDS PostgreSQL (Free Tier)' : 'Aurora Serverless v2',
      description: 'Database type used in this environment',
      exportName: `${config.environment}-database-type`,
    });

    // Airflow数据库相关输出
    new cdk.CfnOutput(this, 'AirflowDatabaseSecretArn', {
      value: this.airflowDatabaseSecret.secretArn,
      description: 'Airflow database credentials secret ARN',
      exportName: `${config.environment}-airflow-database-secret-arn`,
    });

    new cdk.CfnOutput(this, 'AirflowDatabaseName', {
      value: config.environment === 'dev' ? 'airflow-dev' : 'airflow',
      description: 'Airflow database name',
      exportName: `${config.environment}-airflow-database-name`,
    });

    // 创建数据库备份构造
    this.databaseBackupConstruct = new BackupConstruct(this, 'DatabaseBackupConstruct', {
      config,
      database: this.auroraCluster,
      databaseInstance: this.rdsInstance,
      backupNameSuffix: 'database',
    });

    // 输出数据库备份相关信息
    new cdk.CfnOutput(this, 'DatabaseBackupVaultArn', {
      value: this.databaseBackupConstruct.backupVault.backupVaultArn,
      description: 'Database Backup Vault ARN',
      exportName: `YuanhuiDatabase-${config.environment}-BackupVaultArn`,
    });

    new cdk.CfnOutput(this, 'DatabaseBackupPlanArn', {
      value: this.databaseBackupConstruct.backupPlan.backupPlanArn,
      description: 'Database Backup Plan ARN',
      exportName: `YuanhuiDatabase-${config.environment}-BackupPlanArn`,
    });
  }
}
