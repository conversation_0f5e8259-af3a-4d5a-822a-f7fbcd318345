import * as cdk from 'aws-cdk-lib';
import * as ecs from 'aws-cdk-lib/aws-ecs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as efs from 'aws-cdk-lib/aws-efs';
import * as autoscaling from 'aws-cdk-lib/aws-autoscaling';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';
import { BackupConstruct } from '../constructs/backup-construct';

export interface EcsStackProps extends cdk.StackProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  ecsSecurityGroup: ec2.SecurityGroup
}

/**
 * ECS集群栈
 * 包含ECS集群、自动扩容组和相关IAM角色
 */
export class EcsStack extends cdk.Stack {
  public readonly cluster: ecs.Cluster;
  public readonly taskExecutionRole: iam.Role;
  public readonly taskRole: iam.Role;
  public readonly sharedEfsFileSystem: efs.FileSystem;
  public readonly efsBackupConstruct: BackupConstruct;

  constructor(scope: Construct, id: string, props: EcsStackProps) {
    super(scope, id, props);

    const { config, vpc, ecsSecurityGroup } = props;

    // 创建ECS集群
    // 注意：ENHANCED模式会产生大量自定义指标，费用较高（约$50+/月）
    // 开发环境建议使用DISABLED，生产环境可使用ENHANCED（高级模式）
    this.cluster = new ecs.Cluster(this, 'YuanhuiEcsCluster', {
      vpc,
      clusterName: `yuanhui-odoo-${config.environment}`,
      containerInsightsV2: config.monitoring.enableDetailedMonitoring
        ? ecs.ContainerInsights.ENHANCED
        : ecs.ContainerInsights.DISABLED,
      // 启用Fargate容量提供者以支持Docker-in-Docker构建任务
      enableFargateCapacityProviders: true,
    });

    // 创建任务执行角色
    this.taskExecutionRole = new iam.Role(this, 'TaskExecutionRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      description: 'ECS Task Execution Role',
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonECSTaskExecutionRolePolicy'),
      ],
    });

    // 添加访问Secrets Manager的权限
    this.taskExecutionRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'secretsmanager:GetSecretValue',
        'secretsmanager:DescribeSecret',
      ],
      resources: ['*'],
    }));

    // 添加ECR私有仓库访问权限到任务执行角色
    this.taskExecutionRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'ecr:BatchCheckLayerAvailability',
        'ecr:GetDownloadUrlForLayer',
        'ecr:BatchGetImage',
        'ecr:GetAuthorizationToken',
      ],
      resources: ['*'],
    }));

    // 添加EFS访问权限到任务执行角色
    this.taskExecutionRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'elasticfilesystem:ClientMount',
        'elasticfilesystem:ClientWrite',
        'elasticfilesystem:DescribeMountTargets',
        'elasticfilesystem:DescribeFileSystems',
      ],
      resources: ['*'],
    }));

    // 创建任务角色
    this.taskRole = new iam.Role(this, 'TaskRole', {
      assumedBy: new iam.ServicePrincipal('ecs-tasks.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName("AmazonEC2ContainerRegistryPullOnly")
      ],
      description: 'ECS Task Role',
    });

    // 添加CloudWatch日志权限
    this.taskRole.addToPolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
        'logs:DescribeLogStreams',
      ],
      resources: ['*'],
    }));

    // 创建ECS实例角色，添加EFS访问权限
    const ecsInstanceRole = new iam.Role(this, 'EcsInstanceRole', {
      assumedBy: new iam.ServicePrincipal('ec2.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AmazonEC2ContainerServiceforEC2Role'),
        // 添加EFS客户端权限
        iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonElasticFileSystemClientFullAccess'),
        // 添加SSM权限以支持会话管理连接
        iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonSSMManagedInstanceCore'),
      ],
    });

    // 创建用户数据脚本，安装EFS依赖
    const userData = ec2.UserData.forLinux();
    userData.addCommands(
      // 更新系统
      'yum update -y',

      // 安装EFS工具
      'yum install -y amazon-efs-utils',

      // 安装Python和pip（如果还没有）
      'yum install -y python3 python3-pip',

      // 安装botocore
      'pip3 install botocore',

      // 确保EFS挂载助手可用
      'which mount.efs || echo "EFS utils installation failed"',

      // // 配置ECS代理使用containerd运行时以提升性能和降低资源消耗
      // 'echo ECS_CLUSTER=' + `yuanhui-odoo-${config.environment}` + ' >> /etc/ecs/ecs.config',
      // 'echo ECS_ENABLE_CONTAINER_METADATA=true >> /etc/ecs/ecs.config',
      // 'echo ECS_ENABLE_TASK_IAM_ROLE=true >> /etc/ecs/ecs.config',
      // 'echo ECS_ENABLE_TASK_IAM_ROLE_NETWORK_HOST=true >> /etc/ecs/ecs.config',
      // // 启用containerd运行时，相比Docker运行时更轻量、启动更快
      // 'echo ECS_CONTAINER_RUNTIME=containerd >> /etc/ecs/ecs.config',
      // // 启用容器运行时统计信息收集
      // 'echo ECS_ENABLE_CONTAINER_RUNTIME_STATS=true >> /etc/ecs/ecs.config',
      // // 优化日志驱动配置
      // 'echo ECS_AVAILABLE_LOGGING_DRIVERS=\'["json-file","awslogs"]\' >> /etc/ecs/ecs.config',
      // 'systemctl enable ecs',
      // 'systemctl start ecs'
    );

    // 创建多个容量提供商支持不同架构
    const capacityProviders: ecs.AsgCapacityProvider[] = [];
    const capacityProviderMap: { [key: string]: ecs.AsgCapacityProvider } = {};
    
    config.ecs.capacityProviders.forEach((cpConfig, index) => {
      // 根据架构选择合适的镜像
      const machineImage = cpConfig.architecture === 'arm64'
        ? ecs.EcsOptimizedImage.amazonLinux2023(ecs.AmiHardwareType.ARM)
        : ecs.EcsOptimizedImage.amazonLinux2023();

      // 创建具有有意义名称的自动扩容组
      const asgName = `yuanhui-ecs-asg-${cpConfig.architecture}-${cpConfig.instanceType.replace('.', '')}-${config.environment}`;
      const autoScalingGroup = new autoscaling.AutoScalingGroup(this, `EcsAutoScalingGroup${cpConfig.architecture}${index + 1}`, {
        vpc,
        instanceType: new ec2.InstanceType(cpConfig.instanceType),
        machineImage: machineImage,
        minCapacity: cpConfig.minCapacity,
        maxCapacity: cpConfig.maxCapacity,
        desiredCapacity: cpConfig.desiredCapacity,
        autoScalingGroupName: asgName,
        vpcSubnets: {
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        role: ecsInstanceRole,
        userData: userData,
        securityGroup: ecsSecurityGroup,
        // 如果启用Spot实例
        ...(cpConfig.spotEnabled && {
          spotPrice: '0.10', // 设置合理的Spot价格
          spotInstanceDraining: cdk.Duration.minutes(5),
        }),
      });

      // 添加详细标签以识别架构和配置信息
      cdk.Tags.of(autoScalingGroup).add('Name', `yuanhui-ecs-${cpConfig.architecture}-${config.environment}`);
      cdk.Tags.of(autoScalingGroup).add('Architecture', cpConfig.architecture);
      cdk.Tags.of(autoScalingGroup).add('CapacityProvider', cpConfig.name);
      cdk.Tags.of(autoScalingGroup).add('InstanceType', cpConfig.instanceType);
      cdk.Tags.of(autoScalingGroup).add('Environment', config.environment);
      cdk.Tags.of(autoScalingGroup).add('Project', 'YuanhuiOdoo');
      cdk.Tags.of(autoScalingGroup).add('Purpose', 'ECS-Compute');
      cdk.Tags.of(autoScalingGroup).add('SpotEnabled', cpConfig.spotEnabled ? 'true' : 'false');

      // 创建具有有意义名称的容量提供商
      const capacityProviderName = `${cpConfig.architecture}-optimized-${config.environment}`;
      const capacityProvider = new ecs.AsgCapacityProvider(this, `CapacityProvider${cpConfig.architecture}${index + 1}`, {
        autoScalingGroup,
        capacityProviderName: capacityProviderName,
        enableManagedScaling: true,
        enableManagedTerminationProtection: false,
        enableManagedDraining: true,
        targetCapacityPercent: cpConfig.targetCapacityPercent || (config.environment === 'prod' ? 80 : 95),
        machineImageType: ecs.MachineImageType.AMAZON_LINUX_2,
      });

      capacityProviders.push(capacityProvider);
      capacityProviderMap[cpConfig.name] = capacityProvider;
      this.cluster.addAsgCapacityProvider(capacityProvider);
    });

    // 设置默认容量提供商策略（如果配置了）
    // 必须在所有容量提供商添加到集群后才能设置策略
    if (config.ecs.defaultCapacityProviderStrategy) {
      this.cluster.addDefaultCapacityProviderStrategy(
        config.ecs.defaultCapacityProviderStrategy.map(strategy => ({
          capacityProvider: capacityProviderMap[strategy.capacityProvider].capacityProviderName,
          weight: strategy.weight,
          base: strategy.base,
        }))
      );
    }

    // 创建ECS共享EFS文件系统并获取根访问点
    const efsResult = this.createSharedEfsFileSystem(config, vpc);
    this.sharedEfsFileSystem = efsResult.fileSystem;

    // 创建CloudWatch日志组
    // 根据环境调整日志保留期以控制成本
    const retentionDays = config.environment === 'prod'
      ? logs.RetentionDays.ONE_MONTH
      : logs.RetentionDays.THREE_DAYS; // 开发环境3天足够

    const logGroup = new logs.LogGroup(this, 'EcsLogGroup', {
      logGroupName: `/aws/ecs/yuanhui-odoo-${config.environment}`,
      retention: retentionDays,
      removalPolicy: cdk.RemovalPolicy.DESTROY,
    });

    // 输出ECS信息 - 使用固定的导出名称以避免更新冲突
    new cdk.CfnOutput(this, 'ClusterName', {
      value: this.cluster.clusterName,
      description: 'ECS Cluster Name',
      exportName: `YuanhuiEcs-${config.environment}-ClusterName`,
    });

    new cdk.CfnOutput(this, 'ClusterArn', {
      value: this.cluster.clusterArn,
      description: 'ECS Cluster ARN',
      exportName: `YuanhuiEcs-${config.environment}-ClusterArn`,
    });

    // 输出容量提供商信息
    const capacityProviderNames = config.ecs.capacityProviders.map(cp => cp.name);
    new cdk.CfnOutput(this, 'CapacityProviders', {
      value: capacityProviderNames.join(','),
      description: 'ECS Capacity Providers',
      exportName: `YuanhuiEcs-${config.environment}-CapacityProviders`,
    });

    // 输出架构信息
    const architectures = config.ecs.capacityProviders.map(cp => `${cp.name}:${cp.architecture}`);
    new cdk.CfnOutput(this, 'Architectures', {
      value: architectures.join(','),
      description: 'ECS Capacity Provider Architectures',
      exportName: `YuanhuiEcs-${config.environment}-Architectures`,
    });

    new cdk.CfnOutput(this, 'TaskExecutionRoleArn', {
      value: this.taskExecutionRole.roleArn,
      description: 'ECS Task Execution Role ARN',
      exportName: `YuanhuiEcs-${config.environment}-TaskExecutionRoleArn`,
    });

    new cdk.CfnOutput(this, 'TaskRoleArn', {
      value: this.taskRole.roleArn,
      description: 'ECS Task Role ARN',
      exportName: `YuanhuiEcs-${config.environment}-TaskRoleArn`,
    });

    new cdk.CfnOutput(this, 'LogGroupName', {
      value: logGroup.logGroupName,
      description: 'CloudWatch Log Group Name',
      exportName: `YuanhuiEcs-${config.environment}-LogGroupName`,
    });

    new cdk.CfnOutput(this, 'SharedEfsFileSystemId', {
      value: this.sharedEfsFileSystem.fileSystemId,
      description: 'ECS Shared EFS File System ID',
      exportName: `YuanhuiEcs-${config.environment}-SharedEfsFileSystemId`,
    });

    new cdk.CfnOutput(this, 'SharedEfsFileSystemDns', {
      value: `${this.sharedEfsFileSystem.fileSystemId}.efs.${config.region}.amazonaws.com`,
      description: 'ECS Shared EFS File System DNS',
      exportName: `YuanhuiEcs-${config.environment}-SharedEfsFileSystemDns`,
    });

    // 创建EFS备份构造
    this.efsBackupConstruct = new BackupConstruct(this, 'EfsBackupConstruct', {
      config,
      efsFileSystem: this.sharedEfsFileSystem,
      backupNameSuffix: 'efs',
    });

    new cdk.CfnOutput(this, 'EfsBackupVaultArn', {
      value: this.efsBackupConstruct.backupVault.backupVaultArn,
      description: 'EFS Backup Vault ARN',
      exportName: `YuanhuiEcs-${config.environment}-EfsBackupVaultArn`,
    });

    new cdk.CfnOutput(this, 'EfsBackupPlanArn', {
      value: this.efsBackupConstruct.backupPlan.backupPlanArn,
      description: 'EFS Backup Plan ARN',
      exportName: `YuanhuiEcs-${config.environment}-EfsBackupPlanArn`,
    });

    // 添加标签
    cdk.Tags.of(this).add('Environment', config.environment);
    cdk.Tags.of(this).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(this).add('Stack', 'ECS');
  }

  /**
   * 创建ECS共享EFS文件系统
   * 为各个ECS服务提供持久化存储，根目录结构为 /ecs/<stackname>/<service>/
   */
  private createSharedEfsFileSystem(config: EnvironmentConfig, vpc: ec2.Vpc): { fileSystem: efs.FileSystem } {
    // 创建EFS安全组
    const efsSecurityGroup = new ec2.SecurityGroup(this, 'SharedEfsSecurityGroup', {
      vpc,
      description: 'Security group for ECS shared EFS file system',
      allowAllOutbound: false,
    });

    // 允许VPC内的NFS访问（2049端口）
    efsSecurityGroup.addIngressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.tcp(2049),
      'Allow NFS access from VPC for ECS services'
    );

    // 允许出站流量到VPC（用于响应）
    efsSecurityGroup.addEgressRule(
      ec2.Peer.ipv4(vpc.vpcCidrBlock),
      ec2.Port.allTraffic(),
      'Allow outbound traffic to VPC'
    );

    // 创建EFS文件系统
    const fileSystem = new efs.FileSystem(this, 'SharedEfsFileSystem', {
      vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
      securityGroup: efsSecurityGroup,
      fileSystemName: `yuanhui-ecs-shared-${config.environment}`,
      encrypted: config.environment === 'prod', // 生产环境启用加密
      performanceMode: efs.PerformanceMode.GENERAL_PURPOSE,
      throughputMode: efs.ThroughputMode.BURSTING,
      removalPolicy: config.environment === 'prod' 
        ? cdk.RemovalPolicy.RETAIN 
        : cdk.RemovalPolicy.DESTROY,
    });

    // 添加标签
    cdk.Tags.of(fileSystem).add('Name', `yuanhui-ecs-shared-${config.environment}`);
    cdk.Tags.of(fileSystem).add('Environment', config.environment);
    cdk.Tags.of(fileSystem).add('Project', 'YuanhuiOdoo');
    cdk.Tags.of(fileSystem).add('Usage', 'ECS-Shared-Storage');

    return { fileSystem };
  }
}
