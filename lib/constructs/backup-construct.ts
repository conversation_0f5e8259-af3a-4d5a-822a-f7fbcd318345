import * as cdk from 'aws-cdk-lib';
import * as backup from 'aws-cdk-lib/aws-backup';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as efs from 'aws-cdk-lib/aws-efs';
import { Construct } from 'constructs';
import { EnvironmentConfig, BackupPolicyConfig, BackupPolicyType, BackupResourceType } from '../config';

export interface BackupConstructProps {
  config: EnvironmentConfig;
  database?: rds.DatabaseCluster;
  databaseInstance?: rds.DatabaseInstance;
  efsFileSystem?: efs.FileSystem;
  backupNameSuffix?: string;
}

/**
 * 备份和灾难恢复构造
 * 包含AWS Backup服务配置和自动化备份策略
 */
export class BackupConstruct extends Construct {
  public readonly backupVault: backup.BackupVault;
  public readonly backupPlan: backup.BackupPlan;

  constructor(scope: Construct, id: string, props: BackupConstructProps) {
    super(scope, id);

    const { config, database, databaseInstance, efsFileSystem, backupNameSuffix } = props;

    // 创建备份保险库
    const vaultName = backupNameSuffix 
      ? `${config.backup.defaultSettings.backupVaultName}-${backupNameSuffix}`
      : config.backup.defaultSettings.backupVaultName;
    
    this.backupVault = new backup.BackupVault(this, 'BackupVault', {
      backupVaultName: vaultName,
      encryptionKey: undefined, // 使用默认AWS管理的密钥
      // 只在生产环境启用删除保护策略
      accessPolicy: config.backup.defaultSettings.deletionProtection 
        ? new iam.PolicyDocument({
            statements: [
              new iam.PolicyStatement({
                effect: iam.Effect.DENY,
                principals: [new iam.AnyPrincipal()],
                actions: ['backup:DeleteBackupVault', 'backup:DeleteRecoveryPoint'],
                resources: ['*'],
                conditions: {
                  StringNotEquals: {
                    'aws:userid': [
                      // 添加允许删除的用户ID
                      'AIDACKCEVSQ6C2EXAMPLE',
                    ],
                  },
                },
              }),
            ],
          })
        : undefined, // 开发环境不设置访问策略
    });

    // 创建备份计划
    const planName = backupNameSuffix
      ? `${config.backup.defaultSettings.backupVaultName.replace('vault', 'plan')}-${backupNameSuffix}`
      : `${config.backup.defaultSettings.backupVaultName.replace('vault', 'plan')}`;
    
    this.backupPlan = new backup.BackupPlan(this, 'BackupPlan', {
      backupPlanName: planName,
      backupVault: this.backupVault,
    });

    // 添加备份规则
    this.addBackupRules(config);

    // 创建备份选择
    this.createBackupSelection(config, database, databaseInstance, efsFileSystem);

    // 创建灾难恢复Lambda函数
    this.createDisasterRecoveryFunction(config);

    // 设置备份监控
    this.setupBackupMonitoring(config);
  }

  /**
   * 添加备份规则 - 配置驱动
   */
  private addBackupRules(config: EnvironmentConfig): void {
    const backupConfig = config.backup;
    
    if (!backupConfig.enabled) {
      return;
    }

    // 根据启用的资源添加相应的备份规则
    if (backupConfig.resources.efs.enabled) {
      this.addResourceBackupRules(config, 'efs', backupConfig.resources.efs.policy);
    }

    if (backupConfig.resources.database.enabled) {
      this.addResourceBackupRules(config, 'database', backupConfig.resources.database.policy);
    }
  }

  /**
   * 为特定资源类型添加备份规则
   */
  private addResourceBackupRules(config: EnvironmentConfig, resourceType: BackupResourceType, policyType: BackupPolicyType): void {
    const backupConfig = config.backup;
    const policy = backupConfig.policies[policyType];
    
    // 添加每日备份规则
    // 只有当删除时间和冷存储时间间隔>=90天时才添加冷存储设置
    const coldStorageGap = policy.daily.retention - policy.daily.moveToColdStorageAfter;
    const dailyRuleProps: backup.BackupPlanRuleProps = {
      ruleName: `yuanhui-${resourceType}-daily-backup-${config.environment}`,
      deleteAfter: cdk.Duration.days(policy.daily.retention),
      scheduleExpression: events.Schedule.cron({ 
        hour: policy.daily.schedule.hour.toString(), 
        minute: policy.daily.schedule.minute.toString() 
      }),
      enableContinuousBackup: policy.enableContinuousBackup,
      backupVault: this.backupVault,
      // 只有当删除时间和冷存储时间间隔>=90天时才添加冷存储设置
      ...(coldStorageGap >= 90 && { moveToColdStorageAfter: cdk.Duration.days(policy.daily.moveToColdStorageAfter) }),
    };

    this.backupPlan.addRule(new backup.BackupPlanRule(dailyRuleProps));

    // 添加每周备份规则（如果配置了）
    if (policy.weekly) {
      // 只有当删除时间和冷存储时间间隔>=90天时才添加冷存储设置
      const weeklyColdStorageGap = policy.weekly.retention - policy.weekly.moveToColdStorageAfter;
      const weeklyRuleProps: backup.BackupPlanRuleProps = {
        ruleName: `yuanhui-${resourceType}-weekly-backup-${config.environment}`,
        deleteAfter: cdk.Duration.days(policy.weekly.retention),
        scheduleExpression: events.Schedule.cron({ 
          weekDay: policy.weekly.schedule.weekDay.toString(),
          hour: policy.weekly.schedule.hour.toString(), 
          minute: policy.weekly.schedule.minute.toString() 
        }),
        enableContinuousBackup: policy.enableContinuousBackup,
        backupVault: this.backupVault,
        // 只有当删除时间和冷存储时间间隔>=90天时才添加冷存储设置
        ...(weeklyColdStorageGap >= 90 && { moveToColdStorageAfter: cdk.Duration.days(policy.weekly.moveToColdStorageAfter) }),
      };

      this.backupPlan.addRule(new backup.BackupPlanRule(weeklyRuleProps));
    }
  }

  /**
   * 创建备份选择
   */
  private createBackupSelection(config: EnvironmentConfig, database?: rds.DatabaseCluster, databaseInstance?: rds.DatabaseInstance, efsFileSystem?: efs.FileSystem): void {
    // 创建备份角色
    const backupRole = new iam.Role(this, 'BackupRole', {
      assumedBy: new iam.ServicePrincipal('backup.amazonaws.com'),
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSBackupServiceRolePolicyForBackup'),
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSBackupServiceRolePolicyForRestores'),
      ],
    });

    // 收集要备份的资源
    const backupResources: backup.BackupResource[] = [];

    // 添加Aurora集群到备份选择（如果提供）
    if (database) {
      backupResources.push(
        backup.BackupResource.fromArn(`arn:aws:rds:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:cluster:${database.clusterIdentifier}`)
      );
    }

    // 添加RDS实例到备份选择（如果提供）
    if (databaseInstance) {
      backupResources.push(
        backup.BackupResource.fromArn(`arn:aws:rds:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:db:${databaseInstance.instanceIdentifier}`)
      );
    }

    // 添加EFS文件系统到备份选择（如果提供）
    if (efsFileSystem) {
      backupResources.push(
        backup.BackupResource.fromArn(`arn:aws:elasticfilesystem:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:file-system/${efsFileSystem.fileSystemId}`)
      );
    }

    // 只有在有资源时才创建备份选择
    if (backupResources.length > 0) {
      this.backupPlan.addSelection('ResourceSelection', {
        role: backupRole,
        resources: backupResources,
        allowRestores: true,
      });
    }
  }

  /**
   * 创建灾难恢复Lambda函数
   */
  private createDisasterRecoveryFunction(config: EnvironmentConfig): void {
    // 创建灾难恢复Lambda函数
    const drFunction = new lambda.Function(this, 'DisasterRecoveryFunction', {
      runtime: lambda.Runtime.PYTHON_3_9,
      handler: 'index.handler',
      code: lambda.Code.fromInline(`
import json
import boto3
import logging

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def handler(event, context):
    """
    灾难恢复处理函数 - 支持EFS和RDS备份
    """
    try:
        # 解析事件
        detail = event.get('detail', {})
        state = detail.get('state', '')
        resource_arn = detail.get('resourceArn', '')
        backup_job_id = detail.get('backupJobId', '')
        
        # 确定资源类型
        resource_type = get_resource_type(resource_arn)
        
        if state == 'FAILED':
            # 备份失败处理
            logger.error(f"{resource_type} backup failed: {detail}")
            send_alert(detail, resource_type)
        elif state == 'COMPLETED':
            # 备份成功处理
            logger.info(f"{resource_type} backup completed: {backup_job_id}")
            log_backup_success(detail, resource_type)
        elif state == 'ABORTED':
            # 备份中止处理
            logger.warning(f"{resource_type} backup aborted: {detail}")
            send_alert(detail, resource_type, 'ABORTED')
            
        return {
            'statusCode': 200,
            'body': json.dumps('Disaster recovery function executed successfully')
        }
    except Exception as e:
        logger.error(f"Error in disaster recovery function: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps(f'Error: {str(e)}')
        }

def get_resource_type(resource_arn):
    """
    根据ARN确定资源类型
    """
    if 'elasticfilesystem' in resource_arn:
        return 'EFS'
    elif 'rds' in resource_arn:
        return 'RDS'
    else:
        return 'Unknown'

def log_backup_success(detail, resource_type):
    """
    记录备份成功信息
    """
    backup_job_id = detail.get('backupJobId', '')
    resource_arn = detail.get('resourceArn', '')
    logger.info(f"{resource_type} backup success - Job: {backup_job_id}, Resource: {resource_arn}")

def send_alert(detail, resource_type='Unknown', alert_type='FAILED'):
    """
    发送告警通知 - 支持不同资源类型和告警类型
    """
    sns = boto3.client('sns')
    # 这里应该使用实际的SNS主题ARN
    topic_arn = 'arn:aws:sns:region:account:topic-name'
    
    backup_job_id = detail.get('backupJobId', 'N/A')
    resource_arn = detail.get('resourceArn', 'N/A')
    error_message = detail.get('statusMessage', 'No error message provided')
    
    if alert_type == 'FAILED':
        subject = f'元晖Odoo系统{resource_type}备份失败告警'
        message = f"""
{resource_type}备份失败告警详情:

备份任务ID: {backup_job_id}
资源ARN: {resource_arn}
失败原因: {error_message}
发生时间: {detail.get('creationDate', 'N/A')}

请及时检查并处理此问题。
        """
    elif alert_type == 'ABORTED':
        subject = f'元晖Odoo系统{resource_type}备份中止告警'
        message = f"""
{resource_type}备份中止告警详情:

备份任务ID: {backup_job_id}
资源ARN: {resource_arn}
中止原因: {error_message}
发生时间: {detail.get('creationDate', 'N/A')}

请检查备份配置和资源状态。
        """
    
    try:
        sns.publish(
            TopicArn=topic_arn,
            Message=message,
            Subject=subject
        )
        logger.info(f"Alert sent successfully for {resource_type} backup {alert_type}")
    except Exception as e:
        logger.error(f"Failed to send {resource_type} backup alert: {str(e)}")
      `),
      environment: {
        ENVIRONMENT: config.environment,
        BACKUP_ENABLED: config.backup.enabled.toString(),
        ALERT_ON_FAILURE: config.backup.monitoring.alertOnFailure.toString(),
        ALERT_ON_SUCCESS: config.backup.monitoring.alertOnSuccess.toString(),
      },
      timeout: cdk.Duration.minutes(5),
    });

    // 添加必要的权限
    drFunction.addToRolePolicy(new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      actions: [
        'backup:DescribeBackupJob',
        'backup:DescribeRestoreJob',
        'sns:Publish',
        'logs:CreateLogGroup',
        'logs:CreateLogStream',
        'logs:PutLogEvents',
      ],
      resources: ['*'],
    }));

    // 创建EventBridge规则来触发Lambda
    const backupEventRule = new events.Rule(this, 'BackupEventRule', {
      eventPattern: {
        source: ['aws.backup'],
        detailType: ['Backup Job State Change'],
        detail: {
          state: ['COMPLETED', 'FAILED', 'ABORTED'],
        },
      },
    });

    backupEventRule.addTarget(new targets.LambdaFunction(drFunction));
  }

  /**
   * 设置备份监控
   */
  private setupBackupMonitoring(config: EnvironmentConfig): void {
    // 这里可以添加额外的备份监控逻辑
    // 例如：自定义CloudWatch指标、额外的告警等
  }
}
