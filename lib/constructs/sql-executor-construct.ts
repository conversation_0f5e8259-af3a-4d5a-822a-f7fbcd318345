import * as cdk from 'aws-cdk-lib';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as cr from 'aws-cdk-lib/custom-resources';
import * as logs from 'aws-cdk-lib/aws-logs';
import { Construct } from 'constructs';
import { EnvironmentConfig } from '../config';

export interface SqlExecutorConstructProps {
  config: EnvironmentConfig;
  vpc: ec2.Vpc;
  databaseCluster?: rds.DatabaseCluster;
  databaseInstance?: rds.DatabaseInstance;
  databaseSecret: secretsmanager.Secret;
  databaseSecurityGroup: ec2.ISecurityGroup;
  sqlCommands: string[];
  resourceName: string;
  description?: string;
  // 可选的额外密钥，用于密码替换等操作
  additionalSecrets?: { [key: string]: secretsmanager.Secret };
}

/**
 * 通用SQL执行构造
 * 使用AWS CDK Custom Resource结合Lambda函数来执行SQL操作
 * 支持数据库初始化、表创建、用户权限设置等SQL操作
 */
export class SqlExecutorConstruct extends Construct {
  public readonly customResource: cdk.CustomResource;
  public readonly lambdaFunction: lambda.Function;

  constructor(scope: Construct, id: string, props: SqlExecutorConstructProps) {
    super(scope, id);

    const {
      config,
      vpc,
      databaseCluster,
      databaseInstance,
      databaseSecret,
      databaseSecurityGroup,
      sqlCommands,
      resourceName,
      description = 'SQL execution via Lambda function',
      additionalSecrets = {}
    } = props;

    // 确定数据库端点和集群标识符
    const databaseEndpoint = databaseCluster 
      ? databaseCluster.clusterEndpoint.hostname
      : databaseInstance!.instanceEndpoint.hostname;
    
    const databasePort = databaseCluster 
      ? databaseCluster.clusterEndpoint.port
      : databaseInstance!.instanceEndpoint.port;

    const clusterIdentifier = databaseCluster 
      ? databaseCluster.clusterIdentifier
      : undefined;

    // 创建Lambda函数的安全组
    const lambdaSecurityGroup = new ec2.SecurityGroup(this, 'SqlExecutorLambdaSecurityGroup', {
      vpc,
      description: `Security group for SQL executor Lambda function - ${resourceName}`,
      allowAllOutbound: true,
    });

    // 允许Lambda访问数据库
    if (databaseSecurityGroup instanceof ec2.SecurityGroup) {
      databaseSecurityGroup.addIngressRule(
        lambdaSecurityGroup,
        ec2.Port.tcp(5432),
        `Allow SQL executor Lambda to access PostgreSQL - ${resourceName}`
      );
    } else {
      // 如果是导入的安全组，我们需要通过其他方式添加规则
      // 创建一个新的安全组规则
      new ec2.CfnSecurityGroupIngress(this, `SqlExecutorDatabaseAccess-${resourceName}`, {
        groupId: databaseSecurityGroup.securityGroupId,
        ipProtocol: 'tcp',
        fromPort: 5432,
        toPort: 5432,
        sourceSecurityGroupId: lambdaSecurityGroup.securityGroupId,
        description: `Allow SQL executor Lambda to access PostgreSQL - ${resourceName}`,
      });
    }

    // 创建Lambda执行角色
    const lambdaRole = new iam.Role(this, 'SqlExecutorLambdaRole', {
      assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
      description: `Lambda execution role for SQL executor - ${resourceName}`,
      managedPolicies: [
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole'),
        iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaVPCAccessExecutionRole'),
      ],
      inlinePolicies: {
        SqlExecutorPolicy: new iam.PolicyDocument({
          statements: [
            // Secrets Manager权限 - 最小权限原则
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'secretsmanager:GetSecretValue',
              ],
              resources: [
                databaseSecret.secretArn,
                ...Object.values(additionalSecrets).map(secret => secret.secretArn)
              ],
              conditions: {
                StringEquals: {
                  'secretsmanager:ResourceTag/Environment': config.environment,
                },
              },
            }),
            // RDS Data API权限（如果使用Aurora Serverless）
            ...(databaseCluster ? [
              new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                  'rds-data:ExecuteStatement',
                  'rds-data:BatchExecuteStatement',
                  'rds-data:BeginTransaction',
                  'rds-data:CommitTransaction',
                  'rds-data:RollbackTransaction',
                ],
                resources: [databaseCluster.clusterArn],
                conditions: {
                  StringEquals: {
                    'rds:cluster-tag/Environment': config.environment,
                  },
                },
              }),
              // RDS描述权限（用于验证集群状态）
              new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                  'rds:DescribeDBClusters',
                ],
                resources: [databaseCluster.clusterArn],
              })
            ] : []),
            // STS权限（用于获取账户信息）
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'sts:GetCallerIdentity',
              ],
              resources: ['*'],
            }),
            // CloudWatch Logs权限（增强日志记录）
            new iam.PolicyStatement({
              effect: iam.Effect.ALLOW,
              actions: [
                'logs:CreateLogGroup',
                'logs:CreateLogStream',
                'logs:PutLogEvents',
              ],
              resources: [
                `arn:aws:logs:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:log-group:/aws/lambda/*sql-executor*`,
              ],
            }),
          ],
        }),
      },
    });
    
    // Layers
    const psycopg2Layer = new lambda.LayerVersion(this, 'Psycopg2Layer', {
      code: lambda.Code.fromAsset('lambda-layers/psycopg2'),
      compatibleRuntimes: [lambda.Runtime.PYTHON_3_11],
      description: 'Psycopg2 library for PostgreSQL database operations',
    });

    // 创建Lambda函数
    this.lambdaFunction = new lambda.Function(this, 'SqlExecutorFunction', {
      runtime: lambda.Runtime.PYTHON_3_11,
      handler: 'index.handler',
      code: lambda.Code.fromInline(this.getLambdaCode()),
      layers: [psycopg2Layer],
      timeout: cdk.Duration.minutes(15),
      memorySize: 512,
      role: lambdaRole,
      vpc: vpc,
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
      securityGroups: [lambdaSecurityGroup],
      environment: {
        DATABASE_SECRET_ARN: databaseSecret.secretArn,
        DATABASE_ENDPOINT: databaseEndpoint,
        DATABASE_PORT: databasePort.toString(),
        CLUSTER_IDENTIFIER: clusterIdentifier || '',
        RESOURCE_NAME: resourceName,
        ENVIRONMENT: config.environment,
        // 添加额外密钥的ARN
        ADDITIONAL_SECRETS: JSON.stringify(
          Object.fromEntries(
            Object.entries(additionalSecrets).map(([key, secret]) => [key, secret.secretArn])
          )
        ),
      },
      logRetention: logs.RetentionDays.ONE_WEEK,
      description: `SQL executor Lambda function - ${resourceName}`,
    });

    // 创建Custom Resource Provider
    const provider = new cr.Provider(this, 'SqlExecutorProvider', {
      onEventHandler: this.lambdaFunction,
      logRetention: logs.RetentionDays.ONE_WEEK,
    });

    // 创建Custom Resource
    this.customResource = new cdk.CustomResource(this, 'SqlExecutorCustomResource', {
      serviceToken: provider.serviceToken,
      properties: {
        ResourceName: resourceName,
        SqlCommands: sqlCommands,
        DatabaseEndpoint: databaseEndpoint,
        Environment: config.environment,
        Description: description,
        // 添加时间戳以确保每次部署都会触发更新
        Timestamp: Date.now().toString(),
      },
    });

    // 确保Custom Resource在数据库创建后执行
    if (databaseCluster) {
      this.customResource.node.addDependency(databaseCluster);
    }
    if (databaseInstance) {
      this.customResource.node.addDependency(databaseInstance);
    }
    this.customResource.node.addDependency(databaseSecret);
  }

  /**
   * 获取Lambda函数代码
   * 使用Python 3.11运行时，不依赖Lambda Layers
   * 直接在代码中包含所需的依赖
   */
  private getLambdaCode(): string {
    return `
import json
import boto3
import logging
import os
import time
import urllib3
from typing import Dict, List, Any, Optional

# 配置日志
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# 禁用urllib3警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def handler(event: Dict[str, Any], context: Any) -> Dict[str, Any]:
    """
    SQL执行Lambda函数处理器
    支持通过RDS Data API或直接连接执行SQL命令
    """
    resource_name = 'unknown'

    try:
        logger.info(f"Received event: {json.dumps(event, default=str)}")

        request_type = event['RequestType']
        properties = event['ResourceProperties']
        resource_name = properties.get('ResourceName', 'unknown')

        # 记录Lambda上下文信息
        logger.info(f"Lambda context - Function: {context.function_name}, Version: {context.function_version}")
        logger.info(f"Remaining time: {context.get_remaining_time_in_millis()}ms")

        if request_type == 'Create' or request_type == 'Update':
            return execute_sql_commands(properties)
        elif request_type == 'Delete':
            return handle_delete(properties)
        else:
            raise ValueError(f"Unknown request type: {request_type}")

    except Exception as e:
        logger.error(f"Error in handler for resource {resource_name}: {str(e)}")
        import traceback
        traceback.print_exc()
        return format_error_response(e, resource_name)

def execute_sql_commands(properties: Dict[str, Any]) -> Dict[str, Any]:
    """
    执行SQL命令
    """
    resource_name = properties.get('ResourceName', 'unknown')

    try:
        sql_commands = properties['SqlCommands']
        environment = properties['Environment']
        description = properties.get('Description', 'SQL execution')

        logger.info(f"Executing SQL commands for resource: {resource_name}")
        logger.info(f"Environment: {environment}")
        logger.info(f"Description: {description}")
        logger.info(f"Number of SQL commands: {len(sql_commands)}")

        # 验证SQL命令
        validate_sql_commands(sql_commands)

        # 处理SQL命令中的密码占位符
        sql_commands = process_password_placeholders(sql_commands)

        # 获取数据库凭证
        database_credentials = get_database_credentials()

        # 检查是否可以使用RDS Data API
        cluster_identifier = os.environ.get('CLUSTER_IDENTIFIER')
        if cluster_identifier:
            logger.info("Using RDS Data API for SQL execution")
            results = execute_via_rds_data_api(sql_commands, cluster_identifier, database_credentials)
        else:
            logger.info("Using direct database connection for SQL execution")
            results = execute_via_direct_connection(sql_commands, database_credentials)

        # 记录执行摘要
        log_execution_summary(results, resource_name)

        # 检查是否有失败的命令
        failed_commands = [r for r in results if r.get('status') == 'error']
        if failed_commands:
            error_details = []
            for failed in failed_commands:
                error_details.append(f"Command {failed['command_index']}: {failed.get('error', 'Unknown error')}")

            error_message = f"SQL execution failed for resource {resource_name}. Failed commands: {'; '.join(error_details)}"
            logger.error(error_message)

            return {
                'Status': 'FAILED',
                'Reason': error_message,
                'PhysicalResourceId': f"sql-executor-{resource_name}",
                'Data': {
                    'ResourceName': resource_name,
                    'FailedCommands': len(failed_commands),
                    'TotalCommands': len(sql_commands),
                    'FailureDetails': error_details,
                    'Environment': environment,
                    'Timestamp': int(time.time())
                }
            }

        return {
            'Status': 'SUCCESS',
            'PhysicalResourceId': f"sql-executor-{resource_name}",
            'Data': {
                'ResourceName': resource_name,
                'ExecutedCommands': len(sql_commands),
                'Results': results[:10],  # 只返回前10个结果以避免响应过大
                'Environment': environment,
                'Timestamp': int(time.time())
            }
        }

    except Exception as e:
        logger.error(f"Error executing SQL commands for resource {resource_name}: {str(e)}")
        return format_error_response(e, resource_name)

def execute_via_rds_data_api(sql_commands: List[str], cluster_identifier: str, credentials: Dict[str, str]) -> List[Dict[str, Any]]:
    """
    通过RDS Data API执行SQL命令
    """
    rds_data = boto3.client('rds-data')
    results = []
    
    database_name = credentials.get('database', 'postgres')
    secret_arn = os.environ['DATABASE_SECRET_ARN']
    
    for i, sql_command in enumerate(sql_commands):
        try:
            logger.info(f"Executing SQL command {i+1}/{len(sql_commands)}: {sql_command[:100]}...")
            
            response = rds_data.execute_statement(
                resourceArn=f"arn:aws:rds:{boto3.Session().region_name}:{boto3.client('sts').get_caller_identity()['Account']}:cluster:{cluster_identifier}",
                secretArn=secret_arn,
                database=database_name,
                sql=sql_command
            )
            
            results.append({
                'command_index': i,
                'status': 'success',
                'records_updated': response.get('numberOfRecordsUpdated', 0),
                'generated_fields': response.get('generatedFields', [])
            })
            
            logger.info(f"SQL command {i+1} executed successfully")
            
        except Exception as e:
            logger.error(f"Error executing SQL command {i+1}: {str(e)}")
            results.append({
                'command_index': i,
                'status': 'error',
                'error': str(e)
            })
            # 继续执行其他命令，不中断整个过程
    
    return results

def execute_via_direct_connection(sql_commands: List[str], credentials: Dict[str, str]) -> List[Dict[str, Any]]:
    """
    通过直接数据库连接执行SQL命令（真实版本）
    注意：这需要psycopg2库，在生产环境中建议使用RDS Data API
    """
    try:
        import psycopg2
        from psycopg2.extras import RealDictCursor
        logger.info("psycopg2 imported successfully")
    except ImportError as e:
        logger.error(f"Failed to import psycopg2: {str(e)}")
        raise Exception("psycopg2 library is required for direct database connections")

    results = []
    connection = None

    try:
        # 建立数据库连接
        logger.info("Establishing database connection...")
        connection = psycopg2.connect(
            host=credentials['host'],
            port=int(credentials['port']),
            database=credentials.get('database', 'postgres'),  # 使用 database 字段，默认为 postgres
            user=credentials['username'],
            password=credentials['password'],
            connect_timeout=30
        )
        connection.autocommit = True  # 自动提交每个命令
        logger.info("Database connection established successfully")

        # 执行每个SQL命令
        with connection.cursor(cursor_factory=RealDictCursor) as cursor:
            for i, sql_command in enumerate(sql_commands):
                try:
                    logger.info(f"Executing SQL command {i+1}/{len(sql_commands)}: {sql_command[:100]}...")

                    cursor.execute(sql_command)

                    # 获取执行结果
                    if cursor.description:
                        # 有返回结果的查询（如SELECT）
                        rows = cursor.fetchall()
                        results.append({
                            'command_index': i,
                            'status': 'success',
                            'message': f'Query executed successfully, returned {len(rows)} rows',
                            'rows_returned': len(rows),
                            'sql_preview': sql_command[:100] + ('...' if len(sql_command) > 100 else '')
                        })
                        logger.info(f"SQL command {i+1} executed successfully, returned {len(rows)} rows")
                    else:
                        # 没有返回结果的命令（如CREATE, INSERT, UPDATE, DELETE）
                        rows_affected = cursor.rowcount if cursor.rowcount >= 0 else 0
                        results.append({
                            'command_index': i,
                            'status': 'success',
                            'message': f'Command executed successfully, {rows_affected} rows affected',
                            'rows_affected': rows_affected,
                            'sql_preview': sql_command[:100] + ('...' if len(sql_command) > 100 else '')
                        })
                        logger.info(f"SQL command {i+1} executed successfully, {rows_affected} rows affected")

                except psycopg2.Error as e:
                    logger.error(f"PostgreSQL error executing command {i+1}: {str(e)}")
                    results.append({
                        'command_index': i,
                        'status': 'error',
                        'error': f'PostgreSQL error: {str(e)}',
                        'sql_preview': sql_command[:100] + ('...' if len(sql_command) > 100 else '')
                    })
                except Exception as e:
                    logger.error(f"Unexpected error executing command {i+1}: {str(e)}")
                    results.append({
                        'command_index': i,
                        'status': 'error',
                        'error': f'Unexpected error: {str(e)}',
                        'sql_preview': sql_command[:100] + ('...' if len(sql_command) > 100 else '')
                    })

    except psycopg2.Error as e:
        logger.error(f"Database connection error: {str(e)}")
        raise Exception(f"Failed to connect to database: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in database connection: {str(e)}")
        raise Exception(f"Database connection failed: {str(e)}")
    finally:
        if connection:
            try:
                connection.close()
                logger.info("Database connection closed")
            except Exception as e:
                logger.warning(f"Error closing database connection: {str(e)}")

    return results



def get_database_credentials() -> Dict[str, str]:
    """
    从Secrets Manager获取数据库凭证
    """
    secrets_client = boto3.client('secretsmanager')
    secret_arn = os.environ['DATABASE_SECRET_ARN']
    
    try:
        response = secrets_client.get_secret_value(SecretId=secret_arn)
        secret_data = json.loads(response['SecretString'])
        
        logger.info("Successfully retrieved database credentials from Secrets Manager")
        
        return {
            'username': secret_data['username'],
            'password': secret_data['password'],
            'database': secret_data.get('database', 'postgres'),
            'host': secret_data.get('host', os.environ['DATABASE_ENDPOINT']),
            'port': secret_data.get('port', os.environ['DATABASE_PORT'])
        }
        
    except Exception as e:
        logger.error(f"Error retrieving database credentials: {str(e)}")
        raise

def handle_delete(properties: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理删除操作
    通常不需要执行任何清理操作，因为SQL命令的效果是持久的
    """
    resource_name = properties.get('ResourceName', 'unknown')
    logger.info(f"Handling delete for resource: {resource_name}")

    return {
        'Status': 'SUCCESS',
        'PhysicalResourceId': f"sql-executor-{resource_name}",
        'Data': {
            'Message': 'Delete operation completed (no cleanup required)'
        }
    }

def validate_sql_commands(sql_commands: List[str]) -> None:
    """
    验证SQL命令的安全性
    """
    dangerous_keywords = [
        'DROP DATABASE', 'DROP SCHEMA', 'TRUNCATE', 'DELETE FROM',
        'UPDATE SET', 'ALTER SYSTEM', 'COPY FROM', 'COPY TO'
    ]

    for i, command in enumerate(sql_commands):
        command_upper = command.upper().strip()

        # 检查危险关键字
        for keyword in dangerous_keywords:
            if keyword in command_upper:
                logger.warning(f"Potentially dangerous SQL command detected at index {i}: {keyword}")

        # 检查命令长度
        if len(command) > 10000:
            raise ValueError(f"SQL command at index {i} is too long (max 10000 characters)")

        # 检查空命令
        if not command.strip():
            raise ValueError(f"Empty SQL command at index {i}")

def format_error_response(error: Exception, resource_name: str) -> Dict[str, Any]:
    """
    格式化错误响应
    """
    error_message = str(error)

    # 清理敏感信息
    if 'password' in error_message.lower():
        error_message = "Database connection error (credentials issue)"

    return {
        'Status': 'FAILED',
        'Reason': error_message,
        'PhysicalResourceId': f"sql-executor-{resource_name}",
        'Data': {
            'Error': error_message,
            'Timestamp': int(time.time())
        }
    }

def log_execution_summary(results: List[Dict[str, Any]], resource_name: str) -> None:
    """
    记录执行摘要
    """
    total_commands = len(results)
    successful_commands = len([r for r in results if r.get('status') == 'success'])
    failed_commands = total_commands - successful_commands

    logger.info(f"Execution summary for {resource_name}:")
    logger.info(f"  Total commands: {total_commands}")
    logger.info(f"  Successful: {successful_commands}")
    logger.info(f"  Failed: {failed_commands}")

    if failed_commands > 0:
        logger.warning(f"Some SQL commands failed for resource {resource_name}")
        for result in results:
            if result.get('status') == 'error':
                logger.error(f"  Command {result['command_index']}: {result.get('error', 'Unknown error')}")

def process_password_placeholders(sql_commands: List[str]) -> List[str]:
    """
    处理SQL命令中的密码占位符
    """
    try:
        # 获取额外密钥配置
        additional_secrets_json = os.environ.get('ADDITIONAL_SECRETS', '{}')
        additional_secrets = json.loads(additional_secrets_json)

        if not additional_secrets:
            logger.info("No additional secrets configured, skipping password placeholder processing")
            return sql_commands

        logger.info(f"Processing password placeholders with {len(additional_secrets)} additional secrets")

        processed_commands = []
        secrets_client = boto3.client('secretsmanager')

        # 获取所有额外密钥的值
        secret_values = {}
        for secret_name, secret_arn in additional_secrets.items():
            try:
                response = secrets_client.get_secret_value(SecretId=secret_arn)
                secret_data = json.loads(response['SecretString'])
                secret_values[secret_name] = secret_data
                logger.info(f"Successfully retrieved secret: {secret_name}")
            except Exception as e:
                logger.error(f"Error retrieving secret {secret_name}: {str(e)}")
                raise

        # 处理每个SQL命令
        for command in sql_commands:
            processed_command = command

            # 替换Airflow密码占位符
            if '__AIRFLOW_PASSWORD__' in processed_command and 'airflow' in secret_values:
                airflow_password = secret_values['airflow']['password']
                processed_command = processed_command.replace('__AIRFLOW_PASSWORD__', airflow_password)
                logger.info("Replaced __AIRFLOW_PASSWORD__ placeholder")
            
            # 替换Turnkey密码占位符
            if '__TURNKEY_PASSWORD__' in processed_command and 'turnkey' in secret_values:
                turnkey_password = secret_values['turnkey']['password']
                processed_command = processed_command.replace('__TURNKEY_PASSWORD__', turnkey_password)
                logger.info("Replaced __TURNKEY_PASSWORD__ placeholder")

            processed_commands.append(processed_command)

        return processed_commands

    except Exception as e:
        logger.error(f"Error processing password placeholders: {str(e)}")
        # 如果处理失败，返回原始命令
        return sql_commands
`;
  }
}
