#!/bin/bash
# Lambda Layer构建脚本

set -e

# 清理旧的构建
rm -rf python/

# 创建python目录
mkdir -p python

# 使用Docker构建
docker build --platform=linux/amd64 -t psycopg2-layer .

# 从容器中复制构建的文件
docker run --platform=linux/amd64 --rm -v $(pwd):/host psycopg2-layer cp -r /opt/python /host/

# 清理不必要的文件
find python/ -name "*.pyc" -delete
find python/ -name "__pycache__" -type d -exec rm -rf {} +
find python/ -name "*.egg-info" -type d -exec rm -rf {} +

echo "Lambda Layer构建完成"