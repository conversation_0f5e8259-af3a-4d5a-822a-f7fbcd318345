# 元晖Odoo应用服务AWS基础设施即代码项目

本项目使用AWS CDK实现元晖Odoo应用服务的完整云原生架构，包括数据库读写分离、应用服务容器化部署、自动扩容、监控告警和备份恢复等功能。部署的服务器服务于元晖的企业应用项目，通过ECS服务实现数据库、Odoo应用服务器及其他基础服务的容器化部署。

## 🏗️ 架构概述

### 核心组件
- **网络层**: VPC、子网、安全组、NAT网关、OpenZiti零信任网络
- **数据库层**: AWS RDS Aurora Serverless v2 PostgreSQL集群（读写分离）
- **缓存层**: 基于ECS的单实例Redis缓存服务（持久化存储）
- **应用层**: ECS EC2集群容器服务（yherp、khmall、cron）
- **中间件层**: RabbitMQ消息队列 + Apache Airflow 3.x工作流引擎
- **服务发现**: ECS Service Connect统一服务通信
- **负载均衡**: Application Load Balancer（公网+内网）+ Aurora自动负载均衡
- **安全防护**: AWS WAF v2 + CloudFront CDN + SSL/TLS证书管理
- **监控**: CloudWatch + SNS告警 + 自定义仪表板
- **计算**: EC2实例 + 自动扩容组

### 技术栈
- **基础设施**: AWS CDK (TypeScript)
- **容器编排**: Amazon ECS（EC2模式，非Fargate）+ ECS Service Connect
- **数据库**: AWS RDS Aurora Serverless v2 PostgreSQL 15.4
- **缓存**: 基于ECS的Redis 7.2单实例缓存服务（持久化存储）
- **消息队列**: RabbitMQ 3.12 with Management UI
- **工作流引擎**: Apache Airflow 3.0 (Webserver + Scheduler + Worker)
- **存储**: Aurora自动存储扩展 + EFS共享文件系统（Airflow DAGs/Logs）
- **网络安全**: AWS WAF v2 + OpenZiti零信任网络 + VPC Flow Logs
- **CDN加速**: Amazon CloudFront（jmall.tw域名）
- **监控**: CloudWatch + SNS + 自定义告警策略 + Aurora Performance Insights
- **安全**: IAM + Secrets Manager + VPC + 多层安全组 + 自动密码轮换

## 🚀 快速开始

### 前置条件
```bash
# 安装依赖
node --version  # >= 18.x
aws --version   # >= 2.x
cdk --version   # >= 2.x

# 配置AWS凭证
aws configure
```

### 部署步骤
```bash
# 1. 安装依赖
npm install

# 2. 编译TypeScript
npm run build

# 3. 设置环境变量
export NODE_ENV=dev  # 或 prod
export CDK_DEFAULT_ACCOUNT=************
export CDK_DEFAULT_REGION=ap-east-2  # 香港区域

# 4. 初始化CDK环境（首次使用）
cdk bootstrap

# 5. 预览部署
cdk diff

# 6. 部署所有栈
cdk deploy --all
```

## 📁 项目结构

```
yuanhui-odoo-iac/
├── bin/                           # CDK应用入口
│   └── iac.ts                    # 主应用文件
├── lib/                          # 源代码
│   ├── config/                   # 环境配置
│   │   └── environment.ts        # 开发/生产环境配置
│   ├── constructs/               # 可复用构造
│   └── stacks/                   # CDK栈定义
│       ├── network-stack.ts      # 网络栈（VPC、安全组、WAF）
│       ├── ecs-stack.ts          # ECS集群栈
│       ├── service-connect-stack.ts # ECS Service Connect命名空间栈
│       ├── aurora-database-stack.ts # Aurora Serverless v2数据库栈
│       ├── redis-stack.ts        # Redis缓存服务栈（基于ECS单实例）
│       ├── rabbitmq-stack.ts     # RabbitMQ消息队列栈
│       ├── airflow-stack.ts      # Apache Airflow工作流引擎栈
│       ├── dns-stack.ts          # DNS和SSL证书栈
│       ├── application-stack.ts  # 应用服务栈（Odoo）
│       ├── cloudfront-stack.ts   # CloudFront CDN栈
│       ├── openziti-stack.ts     # OpenZiti零信任网络栈
│       ├── security-stack.ts     # 安全栈（GitHub Actions CI/CD权限）
│       └── monitoring-stack.ts   # 监控和告警栈
├── docs/                         # 项目文档
│   ├── architecture/             # 架构设计文档
│   │   └── README.md            # 详细架构说明
│   ├── deployment/               # 部署指南
│   │   └── README.md            # 完整部署流程
│   ├── network/                  # 网络配置文档
│   │   ├── README.md            # 网络架构概述
│   │   ├── deployment-guide.md  # 网络部署指南
│   │   ├── odoo-longpolling-guide.md # Longpolling配置详解
│   │   ├── security-best-practices.md # 安全最佳实践
│   │   └── troubleshooting-guide.md # 网络故障排除
│   └── operations/               # 运维指南
│       └── README.md            # 日常运维操作
├── scripts/                      # 自动化脚本
│   ├── deploy.sh                # 部署脚本
│   ├── test-network-routing.sh  # 网络路由测试
│   └── verify-ssl-certificates.sh # SSL证书验证
└── test/                         # 测试文件
    └── iac.test.ts              # CDK栈测试
```

## 🔧 配置说明

### 环境配置
主要配置文件：`lib/config/environment.ts`

```typescript
// 开发环境配置
export const devConfig: EnvironmentConfig = {
  region: 'ap-east-2',  // 香港区域
  environment: 'dev',
  vpc: {
    cidr: '10.0.0.0/16',
    maxAzs: 2,
    natGateways: 1
  },
  // 容器化PostgreSQL配置
  database: {
    allocatedStorage: 20,
    maxAllocatedStorage: 100,
    backupRetention: 7,
    multiAz: false
  },
  // ECS EC2集群配置
  ecs: {
    cpu: 512,
    memory: 1024,
    desiredCount: 1,
    minCapacity: 1,
    maxCapacity: 2
  },
  // Redis缓存配置（基于ECS单实例）
  redis: {
    cpu: 128,        // ECS任务CPU单位（最小值）
    memory: 256,     // ECS任务内存(MB)
    numCacheNodes: 1 // 单实例模式
  }
};

// 生产环境配置
export const prodConfig: EnvironmentConfig = {
  region: 'ap-east-2',  // 香港区域
  environment: 'prod',
  vpc: {
    cidr: '10.0.0.0/16',
    maxAzs: 3,
    natGateways: 3
  },
  // 生产级PostgreSQL配置
  database: {
    allocatedStorage: 100,
    maxAllocatedStorage: 1000,
    backupRetention: 30,
    multiAz: true
  },
  // 生产级ECS配置
  ecs: {
    cpu: 2048,
    memory: 4096,
    desiredCount: 3,
    minCapacity: 2,
    maxCapacity: 10
  },
  // 生产级Redis配置（基于ECS单实例）
  redis: {
    cpu: 256,        // ECS任务CPU单位
    memory: 512,     // ECS任务内存(MB)
    numCacheNodes: 1 // 单实例模式
  }
};
```

### 域名和路由配置

本项目支持多域名路由，实现基于主机头的路由策略：

#### 生产环境域名配置

| 域名 | 应用 | 访问方式 | 负载均衡器 | 特殊功能 |
|------|------|----------|------------|----------|
| **yh.kh2u.com** | Yherp | 内部访问 | 内部ALB | OpenZiti零信任网络 + Longpolling |
| **dp.kh2u.com** | Yherp | 公网访问 | 公网ALB | WAF防护 + Longpolling |
| **jmall.tw** | Khmall | 电商平台 | 公网ALB | CloudFront CDN加速 + Longpolling |

#### 开发环境域名配置

| 域名 | 应用 | 访问方式 | 负载均衡器 | 特殊功能 |
|------|------|----------|------------|----------|
| **yh-dev.kh2u.com** | Yherp | 内部访问 | 内部ALB | 基础WAF防护 + Longpolling |
| **dp-dev.kh2u.com** | Yherp | 公网访问 | 公网ALB | WAF防护 + Longpolling |
| **jmall-dev.tw** | Khmall | 电商平台 | 公网ALB | 基础负载均衡 + Longpolling |

**注意**: OpenZiti零信任网络和CloudFront CDN仅在生产环境启用。

### Odoo Longpolling配置

根据Odoo 18官方文档，本项目完整支持longpolling功能：

```typescript
// 容器端口配置
container.addPortMappings({
  containerPort: 8069,  // 主应用端口
  hostPort: 0,          // 动态端口映射
  protocol: ecs.Protocol.TCP,
});

container.addPortMappings({
  containerPort: 8072,  // Longpolling端口
  hostPort: 0,          // 动态端口映射
  protocol: ecs.Protocol.TCP,
});

// 环境变量配置
environment: {
  LONGPOLLING_PORT: '8072',
  WORKERS: '2',  // 启用多进程模式以支持longpolling
}
```

## 📊 监控和告警

### CloudWatch仪表板
部署完成后可访问：

```url
https://console.aws.amazon.com/cloudwatch/home?region=ap-east-2#dashboards:name=yuanhui-odoo-{env}
```

### 关键指标

- **应用性能**: CPU < 70%, 内存 < 80%, 响应时间 < 2s
- **数据库性能**: CPU < 80%, 连接数 < 80%, PgPool连接池使用率 < 85%
- **网络性能**: 健康目标 > 80%, 延迟 < 50ms
- **安全指标**: WAF阻止率监控, SSL证书有效期告警
- **CDN性能**: CloudFront缓存命中率 > 80%（jmall.tw）
- **零信任网络**: OpenZiti活跃连接数监控（yh.kh2u.com）

## 🔒 安全特性

### 多层安全防护

- **网络隔离**: VPC + 私有子网 + 多层安全组
- **零信任网络**: OpenZiti内网访问控制（yh.kh2u.com）
- **Web应用防护**: AWS WAF v2 + 自定义规则 + AWS托管规则
- **数据加密**: 传输加密（TLS 1.2+）+ 存储加密（EBS/Redis）
- **密钥管理**: AWS Secrets Manager + 自动轮换
- **访问控制**: IAM角色 + 最小权限原则 + 服务间认证
- **CI/CD安全**: 专用IAM用户 + 精确权限控制 + GitHub Actions集成
- **SSL/TLS管理**: AWS Certificate Manager + 自动续期
- **网络监控**: VPC Flow Logs + CloudTrail审计

## 💾 备份策略

### 自动备份
- **每日备份**: 保留30天
- **每周备份**: 保留1年（生产环境）
- **每月备份**: 保留7年（生产环境）

### 灾难恢复
- 跨可用区部署
- 自动故障转移
- 备份验证和恢复测试

## 📚 文档导航

### 🏗️ 架构设计
- [**架构概述**](docs/architecture/README.md) - 完整的技术架构说明，包括容器化部署、数据库读写分离、零信任网络等

### 🚀 部署指南
- [**部署流程**](docs/deployment/README.md) - 从环境准备到生产部署的完整指南
- [**网络部署**](docs/network/deployment-guide.md) - 多域名路由、SSL证书、WAF配置

### 🔧 配置指南
- [**网络配置**](docs/network/README.md) - 多域名路由架构和安全配置
- [**Longpolling配置**](docs/network/odoo-longpolling-guide.md) - Odoo实时通信功能配置详解
- [**安全最佳实践**](docs/network/security-best-practices.md) - WAF、OpenZiti、SSL配置
- [**GitHub Actions安全配置**](docs/security/github-actions-setup.md) - CI/CD权限管理和GitHub Actions集成

### 🛠️ 运维管理
- [**日常运维**](docs/operations/README.md) - 监控、日志、备份、故障处理
- [**故障排除**](docs/network/troubleshooting-guide.md) - 常见问题和解决方案

## 🛠️ 常用命令

```bash
# 开发命令
npm run build          # 编译TypeScript
npm run watch          # 监听文件变化
npm run test           # 运行测试

# CDK命令
cdk list               # 列出所有栈
cdk diff               # 查看变更
cdk deploy             # 部署栈
cdk destroy            # 删除栈
cdk synth              # 生成CloudFormation模板

# 运维命令
aws ecs list-services --cluster yuanhui-odoo-{env}
aws rds describe-db-clusters
aws logs tail /aws/ecs/yherp-{env} --follow

# 安全栈部署命令
./scripts/deploy-security-stack.sh                       # 部署开发环境安全栈
./scripts/deploy-security-stack.sh -e prod              # 部署生产环境安全栈
./scripts/deploy-security-stack.sh -d                   # 预览模式（仅生成模板）
```

## 🔍 故障排除

### 常见问题

1. **部署失败**: 检查AWS权限和配额
2. **服务无法启动**: 查看ECS任务日志
3. **数据库连接问题**: 验证安全组规则和PgPool配置
4. **性能问题**: 检查CloudWatch指标和容器资源使用
5. **Longpolling连接问题**: 验证8072端口路由配置
6. **OpenZiti连接失败**: 检查零信任网络配置和证书
7. **WAF误拦截**: 审查WAF规则和白名单配置
8. **SSL证书问题**: 验证ACM证书状态和域名验证

### 快速诊断命令

```bash
# 检查ECS服务状态
aws ecs describe-services --cluster yuanhui-odoo-{env} --services yherp-{env}

# 查看容器日志
aws logs tail /aws/ecs/yherp-{env} --follow

# 检查数据库连接
aws ecs execute-command --cluster yuanhui-odoo-{env} --task {task-id} --container OdooContainer --interactive --command "/bin/bash"

# 验证负载均衡器健康检查
aws elbv2 describe-target-health --target-group-arn {target-group-arn}
```

详细故障排除请参考[运维指南](docs/operations/README.md)。

## 💰 成本优化

- **开发环境**: 使用较小实例，单可用区部署
- **生产环境**: 使用预留实例，多可用区高可用
- **自动扩容**: 按需扩容，避免资源浪费
- **生命周期管理**: 自动清理旧备份和日志

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交变更
4. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证。

## 📞 支持

- **技术支持**: <EMAIL>
- **运维支持**: <EMAIL>
- **文档问题**: 请提交Issue

---

**注意**: 请确保在生产环境部署前仔细阅读所有文档，并进行充分的测试。
