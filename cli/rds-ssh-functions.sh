#!/bin/bash

# =============================================================================
# Yuan Hui RDS SSH 隧道全局函数
# =============================================================================
# 用途：通过跳板机建立到 Aurora PostgreSQL 数据库的 SSH 隧道
# 版本：2.0.0
# 安装方式：独立函数文件，避免污染用户配置文件
# =============================================================================

# 配置变量（将在安装时设置）
export YHRDS_SSH_KEY_PATH="${YHRDS_SSH_KEY_PATH:-}"
export YHRDS_LOCAL_PORT="${YHRDS_LOCAL_PORT:-6333}"
export YHRDS_BASTION_USER="${YHRDS_BASTION_USER:-ec2-user}"
export YHRDS_BASTION_HOST="dp.kh2u.com"
export YHRDS_DB_HOST="yuanhuiauroradatabase-pro-aurorapostgresqlcluster3-5g4olsblzz5x.cluster-c3c8ccka8h7t.ap-east-2.rds.amazonaws.com"
export YHRDS_DB_PORT="5432"
export YHRDS_PID_FILE="/tmp/yhrds_tunnel.pid"
export YHRDS_LOG_FILE="/tmp/yhrds_tunnel.log"
export YHRDS_CONFIG_DIR="$HOME/.yuanhui"
export YHRDS_CONFIG_FILE="$YHRDS_CONFIG_DIR/rds-ssh-config"

# 日志函数
_yhrds_log() {
    echo -e "$1" | tee -a "$YHRDS_LOG_FILE" 2>/dev/null
}

_yhrds_log_info() {
    _yhrds_log "\033[0;34m[INFO]\033[0m $1"
}

_yhrds_log_success() {
    _yhrds_log "\033[0;32m[SUCCESS]\033[0m $1"
}

_yhrds_log_warning() {
    _yhrds_log "\033[1;33m[WARNING]\033[0m $1"
}

_yhrds_log_error() {
    _yhrds_log "\033[0;31m[ERROR]\033[0m $1"
}

# 加载用户配置
_yhrds_load_config() {
    if [[ -f "$YHRDS_CONFIG_FILE" ]]; then
        source "$YHRDS_CONFIG_FILE"
    fi
}

# 检查隧道是否运行
_yhrds_is_tunnel_running() {
    if [[ -f "$YHRDS_PID_FILE" ]]; then
        local pid=$(cat "$YHRDS_PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 运行中
        else
            rm -f "$YHRDS_PID_FILE"
            return 1  # 未运行
        fi
    else
        return 1  # 未运行
    fi
}

# 检查端口是否被占用（跨平台）
_yhrds_check_port() {
    local port="$1"
    
    # macOS 使用 lsof
    if command -v lsof >/dev/null 2>&1; then
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            return 0  # 端口被占用
        fi
    fi
    
    # Linux 使用 netstat 或 ss
    if command -v netstat >/dev/null 2>&1; then
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            return 0  # 端口被占用
        fi
    elif command -v ss >/dev/null 2>&1; then
        if ss -tuln 2>/dev/null | grep -q ":$port "; then
            return 0  # 端口被占用
        fi
    fi
    
    return 1  # 端口空闲
}

# 启动 SSH 隧道
yhrds_start() {
    # 加载配置
    _yhrds_load_config
    
    _yhrds_log_info "启动 SSH 隧道到 Aurora 数据库..."
    
    if [[ -z "$YHRDS_SSH_KEY_PATH" ]] || [[ ! -f "$YHRDS_SSH_KEY_PATH" ]]; then
        _yhrds_log_error "SSH 密钥文件不存在: $YHRDS_SSH_KEY_PATH"
        _yhrds_log_info "请运行安装脚本重新配置或手动设置 YHRDS_SSH_KEY_PATH 环境变量"
        return 1
    fi
    
    if _yhrds_is_tunnel_running; then
        _yhrds_log_warning "SSH 隧道已在运行中"
        return 0
    fi
    
    if _yhrds_check_port "$YHRDS_LOCAL_PORT"; then
        _yhrds_log_error "本地端口 $YHRDS_LOCAL_PORT 已被占用"
        return 1
    fi
    
    _yhrds_log_info "建立连接: 127.0.0.1:${YHRDS_LOCAL_PORT} -> ${YHRDS_BASTION_HOST} -> ${YHRDS_DB_HOST}:${YHRDS_DB_PORT}"
    
    ssh -f -N \
        -L "${YHRDS_LOCAL_PORT}:${YHRDS_DB_HOST}:${YHRDS_DB_PORT}" \
        -o StrictHostKeyChecking=no \
        -o UserKnownHostsFile=/dev/null \
        -o ServerAliveInterval=60 \
        -o ServerAliveCountMax=3 \
        -i "${YHRDS_SSH_KEY_PATH}" \
        "${YHRDS_BASTION_USER}@${YHRDS_BASTION_HOST}" \
        2>>"${YHRDS_LOG_FILE}"
    
    local ssh_pid=$(ps aux | grep "ssh.*${YHRDS_LOCAL_PORT}:${YHRDS_DB_HOST}:${YHRDS_DB_PORT}" | grep -v grep | awk '{print $2}')
    
    if [[ -n "$ssh_pid" ]]; then
        echo "$ssh_pid" > "$YHRDS_PID_FILE"
        _yhrds_log_success "SSH 隧道启动成功，PID: $ssh_pid"
        sleep 3
        yhrds_test && _yhrds_log_success "隧道连接验证成功"
    else
        _yhrds_log_error "SSH 隧道启动失败"
        return 1
    fi
}

# 停止 SSH 隧道
yhrds_stop() {
    _yhrds_log_info "停止 SSH 隧道..."
    
    if ! _yhrds_is_tunnel_running; then
        _yhrds_log_warning "SSH 隧道未运行"
        return 0
    fi
    
    local pid=$(cat "$YHRDS_PID_FILE")
    
    if kill "$pid" 2>/dev/null; then
        rm -f "$YHRDS_PID_FILE"
        _yhrds_log_success "SSH 隧道已停止，PID: $pid"
    else
        _yhrds_log_error "无法停止 SSH 隧道，PID: $pid"
        rm -f "$YHRDS_PID_FILE"
        return 1
    fi
}

# 检查隧道状态
yhrds_status() {
    if _yhrds_is_tunnel_running; then
        local pid=$(cat "$YHRDS_PID_FILE")
        _yhrds_log_success "SSH 隧道运行中，PID: $pid"
        
        if _yhrds_check_port "$YHRDS_LOCAL_PORT"; then
            _yhrds_log_info "本地端口 $YHRDS_LOCAL_PORT 正在监听"
        else
            _yhrds_log_warning "本地端口 $YHRDS_LOCAL_PORT 未监听，隧道可能有问题"
        fi
        return 0
    else
        _yhrds_log_info "SSH 隧道未运行"
        return 1
    fi
}

# 测试端口连通性
yhrds_test() {
    if ! _yhrds_check_port "$YHRDS_LOCAL_PORT"; then
        _yhrds_log_error "本地端口 $YHRDS_LOCAL_PORT 未监听"
        return 1
    fi
    
    # 跨平台连通性测试
    if command -v nc >/dev/null 2>&1; then
        # macOS 和大部分 Linux 发行版
        if nc -z 127.0.0.1 "$YHRDS_LOCAL_PORT" 2>/dev/null; then
            _yhrds_log_success "端口连通性测试成功"
            return 0
        fi
    elif command -v ncat >/dev/null 2>&1; then
        # 一些 Linux 发行版使用 ncat
        if timeout 5 ncat -z 127.0.0.1 "$YHRDS_LOCAL_PORT" 2>/dev/null; then
            _yhrds_log_success "端口连通性测试成功"
            return 0
        fi
    elif [[ -n "$BASH_VERSION" ]]; then
        # bash 内置 /dev/tcp 测试
        if timeout 5 bash -c "echo >/dev/tcp/127.0.0.1/$YHRDS_LOCAL_PORT" 2>/dev/null; then
            _yhrds_log_success "端口连通性测试成功"
            return 0
        fi
    fi
    
    _yhrds_log_error "端口连通性测试失败"
    return 1
}

# 显示连接信息
yhrds_info() {
    # 加载配置
    _yhrds_load_config
    
    echo -e "\n\033[0;34m=== Yuan Hui RDS 连接信息 ===\033[0m"
    echo -e "\033[0;32m数据库连接配置:\033[0m"
    echo -e "  主机: 127.0.0.1"
    echo -e "  端口: ${YHRDS_LOCAL_PORT}"
    echo -e "  数据库: postgres"
    echo -e "  用户名: odoo_admin"
    echo -e "  密码: [从 AWS Secrets Manager 获取]"
    echo ""
    echo -e "\033[0;34m隧道信息:\033[0m"
    echo -e "  跳板机: ${YHRDS_BASTION_USER}@${YHRDS_BASTION_HOST}"
    echo -e "  目标数据库: ${YHRDS_DB_HOST}:${YHRDS_DB_PORT}"
    echo -e "  本地端口: ${YHRDS_LOCAL_PORT}"
    echo -e "  SSH 密钥: ${YHRDS_SSH_KEY_PATH}"
    echo ""
    echo -e "\033[1;33m客户端连接示例:\033[0m"
    echo -e "  psql: psql -h 127.0.0.1 -p ${YHRDS_LOCAL_PORT} -U odoo_admin -d postgres"
    echo -e "  连接字符串: postgresql://odoo_admin:PASSWORD@127.0.0.1:${YHRDS_LOCAL_PORT}/postgres"
    echo ""
    echo -e "\033[0;36m可用命令:\033[0m"
    echo -e "  yhrds_start  - 启动 SSH 隧道"
    echo -e "  yhrds_stop   - 停止 SSH 隧道"
    echo -e "  yhrds_status - 检查隧道状态"
    echo -e "  yhrds_test   - 测试端口连通性"
    echo -e "  yhrds_info   - 显示此信息"
    echo ""
}

# 初始化配置目录
if [[ ! -d "$YHRDS_CONFIG_DIR" ]]; then
    mkdir -p "$YHRDS_CONFIG_DIR"
fi

# 初始化时加载配置
_yhrds_load_config