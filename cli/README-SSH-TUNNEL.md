# Yuan Hui RDS SSH 隧道使用指南

本指南介绍如何使用 SSH 隧道安全连接到 Yuan Hui 的 Aurora PostgreSQL 数据库。

## 概述

这些脚本通过跳板机（`dp.kh2u.com`）建立安全的 SSH 隧道，允许本地客户端连接到位于私有子网中的 Aurora 数据库。

### 架构图

```
本地客户端 -> SSH隧道(端口6333) -> 跳板机(dp.kh2u.com) -> Aurora数据库
```

### v2.0.0 新架构

为了避免污染用户配置文件，v2.0.0 采用了全新的清洁架构：

- **Unix系统** (macOS/Linux): 使用独立函数文件 + 最小配置引用（仅1行）
- **Windows系统**: 使用独立命令脚本 + PATH集成
- **完全可逆**: 卸载时完全清理，不留残余
- **更新Aurora集群**: 使用最新的数据库端点

## 快速开始

### 1. 选择适合你操作系统的脚本

- **macOS**: `setup-rds-ssh-macos.sh`
- **Linux**: `setup-rds-ssh-linux.sh`  
- **Windows**: `setup-rds-ssh-windows.bat`

### 2. 运行脚本

```bash
# macOS/Linux - 永久安装到 shell 配置文件
chmod +x cli/setup-rds-ssh-macos.sh
./cli/setup-rds-ssh-macos.sh /path/to/your-key.pem

# Windows - 永久安装到系统 PATH
cli\setup-rds-ssh-windows.bat C:\path\to\your-key.pem
```

### 3. 重新加载配置 (Unix系统)

```bash
# macOS/Linux - 重新加载shell配置
source ~/.zshrc  # 或 ~/.bashrc, ~/.profile

# 或者重新打开终端
```

### 4. 启动隧道

```bash
# Unix系统
yhrds_start

# Windows系统
yhrds start
```


### 5. 连接数据库

使用任何 PostgreSQL 客户端连接到：
- **主机**: 127.0.0.1
- **端口**: 6333
- **数据库**: postgres
- **用户名**: odoo_admin

## 详细使用说明

### 脚本参数

所有脚本都支持以下参数：

```bash
./setup-rds-ssh-<platform>.sh <SSH_KEY_PATH> [OPTIONS]
./setup-rds-ssh-<platform>.sh --uninstall

参数:
    SSH_KEY_PATH     SSH 私钥文件路径 (必需，除非使用 --uninstall)

选项:
    -p, --port       本地端口 (默认: 6333)
    -u, --user       SSH 用户名 (默认: ec2-user)
    --uninstall      从 shell 配置文件或系统中移除全局函数
    -h, --help       显示帮助信息
```

### 环境变量

可以通过环境变量覆盖默认配置：

```bash
export LOCAL_PORT=5433      # 自定义本地端口
export BASTION_USER=admin   # 自定义 SSH 用户名
```

### 安装和卸载

#### 安装架构 (v2.0.0)

**Unix系统** (macOS/Linux):
- **独立函数文件**: 函数存储在 `~/.yuanhui/rds-ssh-functions.sh`
- **最小配置引用**: 仅在shell配置文件中添加1行引用
- **用户配置**: 个人设置存储在 `~/.yuanhui/rds-ssh-config`
- **清洁方式**: 避免污染用户配置文件

**Windows系统**:
- **独立命令脚本**: 命令存储在 `%USERPROFILE%\bin\yhrds.bat`
- **PATH集成**: 添加到用户PATH，无需重复安装
- **用户配置**: 个人设置存储在 `%USERPROFILE%\.yuanhui\rds-ssh-config.bat`
- **命令语法**: 使用 `yhrds <command>` 格式

#### 安装位置

**Unix系统**:
- **函数文件**: `~/.yuanhui/rds-ssh-functions.sh`
- **配置文件**: `~/.yuanhui/rds-ssh-config`
- **Shell引用**: `~/.zshrc`、`~/.bashrc` 或 `~/.profile` (仅1行)

**Windows系统**:
- **命令文件**: `%USERPROFILE%\bin\yhrds.bat`
- **配置文件**: `%USERPROFILE%\.yuanhui\rds-ssh-config.bat`
- **PATH集成**: 添加到用户环境变量

#### 卸载
```bash
# macOS/Linux
./setup-rds-ssh-macos.sh --uninstall

# Windows
setup-rds-ssh-windows.bat --uninstall
```

### 可用命令

脚本执行后，会在你的环境中创建以下命令：

#### Unix系统 (macOS/Linux)

| 函数 | 功能 |
|------|------|
| `yhrds_start` | 启动 SSH 隧道 |
| `yhrds_stop` | 停止 SSH 隧道 |
| `yhrds_status` | 检查隧道状态 |
| `yhrds_test` | 测试端口连通性 |
| `yhrds_info` | 显示连接信息 |

#### Windows系统

| 命令 | 功能 |
|------|------|
| `yhrds start` | 启动 SSH 隧道 |
| `yhrds stop` | 停止 SSH 隧道 |
| `yhrds status` | 检查隧道状态 |
| `yhrds test` | 测试端口连通性 |
| `yhrds info` | 显示连接信息 |
| `yhrds help` | 显示帮助信息 |

## 数据库客户端配置

### 1. psql 命令行

```bash
psql -h 127.0.0.1 -p 6333 -U odoo_admin -d postgres
```

### 2. pgAdmin

1. 创建新服务器连接
2. 在 "General" 标签页：
   - Name: Yuan Hui Aurora (via SSH)
3. 在 "Connection" 标签页：
   - Host: 127.0.0.1
   - Port: 6333
   - Database: postgres
   - Username: odoo_admin
   - Password: [从 AWS Secrets Manager 获取]

### 3. DBeaver

1. 创建新连接 -> PostgreSQL
2. 服务器配置：
   - Server Host: 127.0.0.1
   - Port: 6333
   - Database: postgres
   - Username: odoo_admin
   - Password: [从 AWS Secrets Manager 获取]

### 4. DataGrip

1. 创建新数据源 -> PostgreSQL
2. 配置连接：
   - Host: 127.0.0.1
   - Port: 6333
   - Database: postgres
   - User: odoo_admin
   - Password: [从 AWS Secrets Manager 获取]

### 5. 连接字符串

```
postgresql://odoo_admin:PASSWORD@127.0.0.1:6333/postgres
```

## 获取数据库密码

数据库密码存储在 AWS Secrets Manager 中。你可以通过以下方式获取：

### 使用 AWS CLI

```bash
aws secretsmanager get-secret-value \
    --secret-id YuanhuiAuroraDatabase-prod-AuroraDatabaseSecret \
    --region ap-east-2 \
    --query SecretString --output text | jq -r .password
```

### 使用 AWS 控制台

1. 登录 AWS 控制台
2. 导航到 Secrets Manager
3. 查找名为 `YuanhuiAuroraDatabase-prod-AuroraDatabaseSecret` 的密钥
4. 点击 "Retrieve secret value" 查看密码

## 常见问题

### Q: 端口 6333 被占用怎么办？

A: 使用不同的端口启动脚本：
```bash
./setup-rds-ssh-macos.sh /path/to/key.pem -p 5433
```

### Q: SSH 连接失败

A: 检查以下几点：
1. SSH 密钥文件路径是否正确
2. 密钥文件权限是否为 600 或 400
3. 网络连接是否正常
4. 跳板机是否可访问

### Q: 隧道启动成功但无法连接数据库

A: 尝试以下步骤：
1. 运行 `yhrds_test` 检查端口连通性
2. 运行 `yhrds_status` 检查隧道状态
3. 检查防火墙设置
4. 确认数据库服务正在运行

### Q: Windows 下如何使用？

A: Windows v2.0.0 使用独立命令脚本，确保：
1. 系统已安装 OpenSSH 客户端
2. 命令语法为 `yhrds <command>`（如 `yhrds start`）
3. PowerShell 可用（用于连接测试）
4. 命令脚本位于用户PATH中，全局可用

### Q: 如何在多个终端中使用命令？

A: v2.0.0 安装后命令会自动在所有新终端中可用：
- **Unix系统**: 通过shell配置文件中的1行引用自动加载
- **Windows系统**: 通过PATH环境变量全局可用
- 无需重复运行安装脚本

## 安全注意事项

1. **SSH 密钥安全**
   - 确保私钥文件权限设置为 600 或 400
   - 不要将私钥文件提交到版本控制
   - 定期轮换 SSH 密钥

2. **网络安全**
   - 隧道仅绑定到 127.0.0.1，只允许本地访问
   - 不要将本地端口暴露到公网
   - 使用完毕后及时关闭隧道

3. **密码安全**
   - 不要在命令行或脚本中硬编码密码
   - 使用环境变量或密钥管理工具存储密码
   - 定期更新数据库密码

## 故障排除

### 日志文件

v2.0.0 所有平台都使用统一的日志文件：
- **Unix系统**: `/tmp/yhrds_tunnel.log`
- **Windows**: `%TEMP%\yhrds_tunnel.log`

查看日志以诊断问题：
```bash
# Unix系统
tail -f /tmp/yhrds_tunnel.log

# Windows
type %TEMP%\yhrds_tunnel.log

# 或实时监控 (Windows PowerShell)
Get-Content $env:TEMP\yhrds_tunnel.log -Wait
```

### 清理僵尸进程

如果隧道进程意外终止，可能需要手动清理：

```bash
# Unix系统
ps aux | grep ssh | grep 6333
kill -9 <PID>

# Windows
tasklist | findstr ssh.exe
taskkill /PID <PID> /F
```

### 重置环境

如果遇到问题，可以重置环境：

```bash
# 停止隧道
# Unix系统
yhrds_stop
# Windows系统
yhrds stop

# 清理 PID 文件
rm -f /tmp/yhrds_tunnel.pid        # Unix系统
del %TEMP%\yhrds_tunnel.pid        # Windows

# 重新运行设置脚本
```

## 配置信息

### 当前配置 (v2.0.0)

- **跳板机**: dp.kh2u.com
- **SSH 用户**: ec2-user
- **数据库主机**: yuanhuiauroradatabase-pro-aurorapostgresqlcluster3-5g4olsblzz5x.cluster-c3c8ccka8h7t.ap-east-2.rds.amazonaws.com
- **数据库端口**: 5432
- **默认本地端口**: 6333
- **数据库用户**: odoo_admin
- **数据库名**: postgres
- **架构版本**: 2.0.0 (独立文件架构)

### 支持的操作系统

- macOS 10.15+
- Linux (Ubuntu, CentOS, RHEL, SUSE)
- Windows 10+ (需要 OpenSSH 客户端)

## 技术支持

如果遇到问题，请提供以下信息：

1. 操作系统和版本
2. 使用的脚本版本
3. 错误信息
4. 日志文件内容
5. 网络环境描述

## 更新日志

### v2.0.0 (2024-07-25) 🎉
- **全新清洁架构**: 避免污染用户配置文件
- **Unix系统**: 独立函数文件 + 最小配置引用（仅1行）
- **Windows系统**: 独立命令脚本 + PATH集成
- **更新数据库端点**: 使用最新Aurora集群地址
- **完全可逆安装**: 卸载时完全清理，不留残余
- **跨平台统一**: 统一的日志文件和错误处理
- **Windows命令语法**: 统一使用 `yhrds <command>` 格式

### v1.0.0 (2024-07-25)
- 初始版本
- 支持 macOS、Linux、Windows
- 提供 yhrds_* 全局函数
- 自动端口检测和连通性测试
- 完整的错误处理和日志记录