@echo off
setlocal enabledelayedexpansion

REM =============================================================================
REM Yuan Hui RDS SSH 隧道命令 - Windows 版本
REM =============================================================================
REM 版本：2.0.0
REM 架构：独立可执行脚本，避免污染环境变量
REM =============================================================================

REM 配置变量
set "YHRDS_CONFIG_DIR=%USERPROFILE%\.yuanhui"
set "YHRDS_CONFIG_FILE=%YHRDS_CONFIG_DIR%\rds-ssh-config.bat"
set "YHRDS_BASTION_HOST=dp.kh2u.com"
set "YHRDS_DB_HOST=yuanhuiauroradatabase-pro-aurorapostgresqlcluster3-5g4olsblzz5x.cluster-c3c8ccka8h7t.ap-east-2.rds.amazonaws.com"
set "YHRDS_DB_PORT=5432"
set "YHRDS_PID_FILE=%TEMP%\yhrds_tunnel.pid"
set "YHRDS_LOG_FILE=%TEMP%\yhrds_tunnel.log"

REM 默认值
set "YHRDS_SSH_KEY_PATH="
set "YHRDS_LOCAL_PORT=6333"
set "YHRDS_BASTION_USER=ec2-user"

REM 加载用户配置
if exist "%YHRDS_CONFIG_FILE%" call "%YHRDS_CONFIG_FILE%"

REM 颜色代码
set "RED=[31m"
set "GREEN=[32m"
set "YELLOW=[33m"
set "BLUE=[34m"
set "NC=[0m"

REM 日志函数
:log_info
echo %BLUE%[INFO]%NC% %~1
echo [%date% %time%] [INFO] %~1 >> "%YHRDS_LOG_FILE%" 2>nul
goto :eof

:log_success
echo %GREEN%[SUCCESS]%NC% %~1
echo [%date% %time%] [SUCCESS] %~1 >> "%YHRDS_LOG_FILE%" 2>nul
goto :eof

:log_warning
echo %YELLOW%[WARNING]%NC% %~1
echo [%date% %time%] [WARNING] %~1 >> "%YHRDS_LOG_FILE%" 2>nul
goto :eof

:log_error
echo %RED%[ERROR]%NC% %~1
echo [%date% %time%] [ERROR] %~1 >> "%YHRDS_LOG_FILE%" 2>nul
goto :eof

REM 检查隧道是否运行
:is_tunnel_running
if not exist "%YHRDS_PID_FILE%" (
    exit /b 1
)

set /p tunnel_pid=<"%YHRDS_PID_FILE%"
tasklist /FI "PID eq %tunnel_pid%" 2>nul | find "%tunnel_pid%" >nul
if %errorlevel% equ 0 (
    exit /b 0
) else (
    del "%YHRDS_PID_FILE%" 2>nul
    exit /b 1
)

REM 检查端口是否被占用
:check_port
netstat -an | find ":%~1 " | find "LISTENING" >nul
exit /b %errorlevel%

REM 显示帮助信息
:show_help
echo Yuan Hui RDS SSH 隧道命令 - Windows 版本 v2.0.0
echo.
echo 用法: yhrds ^<command^> [options]
echo.
echo 命令:
echo   start      启动 SSH 隧道
echo   stop       停止 SSH 隧道
echo   status     检查隧道状态
echo   test       测试端口连通性
echo   info       显示连接信息
echo   help       显示此帮助信息
echo.
echo 连接信息:
echo   跳板机: %YHRDS_BASTION_HOST%
echo   数据库: %YHRDS_DB_HOST%
echo   本地端口: %YHRDS_LOCAL_PORT%
echo.
goto :eof

REM 启动隧道
:start_tunnel
call :log_info "启动 SSH 隧道到 Aurora 数据库..."

if "%YHRDS_SSH_KEY_PATH%"=="" (
    call :log_error "SSH 密钥路径未配置，请运行安装脚本重新配置"
    exit /b 1
)

if not exist "%YHRDS_SSH_KEY_PATH%" (
    call :log_error "SSH 密钥文件不存在: %YHRDS_SSH_KEY_PATH%"
    exit /b 1
)

call :is_tunnel_running
if %errorlevel% equ 0 (
    call :log_warning "SSH 隧道已在运行中"
    exit /b 0
)

call :check_port %YHRDS_LOCAL_PORT%
if %errorlevel% equ 0 (
    call :log_error "本地端口 %YHRDS_LOCAL_PORT% 已被占用"
    exit /b 1
)

call :log_info "建立连接: 127.0.0.1:%YHRDS_LOCAL_PORT% -> %YHRDS_BASTION_HOST% -> %YHRDS_DB_HOST%:%YHRDS_DB_PORT%"

REM 启动 SSH 隧道
start /B ssh -N -L %YHRDS_LOCAL_PORT%:%YHRDS_DB_HOST%:%YHRDS_DB_PORT% -o StrictHostKeyChecking=no -o UserKnownHostsFile=nul -o ServerAliveInterval=60 -o ServerAliveCountMax=3 -i "%YHRDS_SSH_KEY_PATH%" %YHRDS_BASTION_USER%@%YHRDS_BASTION_HOST%

REM 等待连接建立
timeout /t 3 >nul

REM 查找 SSH 进程 PID
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq ssh.exe" /FO CSV ^| find "ssh.exe"') do (
    set "ssh_pid=%%i"
    set "ssh_pid=!ssh_pid:"=!"
)

if defined ssh_pid (
    echo !ssh_pid! > "%YHRDS_PID_FILE%"
    call :log_success "SSH 隧道启动成功，PID: !ssh_pid!"
    call :test_connection
    if !errorlevel! equ 0 (
        call :log_success "隧道连接验证成功"
    )
) else (
    call :log_error "SSH 隧道启动失败"
    exit /b 1
)
goto :eof

REM 停止隧道
:stop_tunnel
call :log_info "停止 SSH 隧道..."

call :is_tunnel_running
if %errorlevel% neq 0 (
    call :log_warning "SSH 隧道未运行"
    exit /b 0
)

set /p tunnel_pid=<"%YHRDS_PID_FILE%"
taskkill /PID %tunnel_pid% /F >nul 2>&1
if %errorlevel% equ 0 (
    del "%YHRDS_PID_FILE%" 2>nul
    call :log_success "SSH 隧道已停止，PID: %tunnel_pid%"
) else (
    call :log_error "无法停止 SSH 隧道，PID: %tunnel_pid%"
    del "%YHRDS_PID_FILE%" 2>nul
    exit /b 1
)
goto :eof

REM 检查状态
:check_status
call :is_tunnel_running
if %errorlevel% equ 0 (
    set /p tunnel_pid=<"%YHRDS_PID_FILE%"
    call :log_success "SSH 隧道运行中，PID: !tunnel_pid!"
    
    call :check_port %YHRDS_LOCAL_PORT%
    if !errorlevel! equ 0 (
        call :log_info "本地端口 %YHRDS_LOCAL_PORT% 正在监听"
    ) else (
        call :log_warning "本地端口 %YHRDS_LOCAL_PORT% 未监听，隧道可能有问题"
    )
    exit /b 0
) else (
    call :log_info "SSH 隧道未运行"
    exit /b 1
)

REM 测试连接
:test_connection
call :check_port %YHRDS_LOCAL_PORT%
if %errorlevel% neq 0 (
    call :log_error "本地端口 %YHRDS_LOCAL_PORT% 未监听"
    exit /b 1
)

REM 使用 PowerShell 测试连接
powershell -Command "try { $tcpClient = New-Object System.Net.Sockets.TcpClient; $tcpClient.Connect('127.0.0.1', %YHRDS_LOCAL_PORT%); $tcpClient.Close(); exit 0 } catch { exit 1 }" >nul 2>&1
if %errorlevel% equ 0 (
    call :log_success "端口连通性测试成功"
    exit /b 0
) else (
    call :log_error "端口连通性测试失败"
    exit /b 1
)

REM 显示连接信息
:show_info
echo.
echo %BLUE%=== Yuan Hui RDS 连接信息 ===%NC%
echo %GREEN%数据库连接配置:%NC%
echo   主机: 127.0.0.1
echo   端口: %YHRDS_LOCAL_PORT%
echo   数据库: postgres
echo   用户名: odoo_admin
echo   密码: [从 AWS Secrets Manager 获取]
echo.
echo %BLUE%隧道信息:%NC%
echo   跳板机: %YHRDS_BASTION_USER%@%YHRDS_BASTION_HOST%
echo   目标数据库: %YHRDS_DB_HOST%:%YHRDS_DB_PORT%
echo   本地端口: %YHRDS_LOCAL_PORT%
echo   SSH 密钥: %YHRDS_SSH_KEY_PATH%
echo.
echo %YELLOW%客户端连接示例:%NC%
echo   psql: psql -h 127.0.0.1 -p %YHRDS_LOCAL_PORT% -U odoo_admin -d postgres
echo   连接字符串: postgresql://odoo_admin:PASSWORD@127.0.0.1:%YHRDS_LOCAL_PORT%/postgres
echo.
echo %BLUE%可用命令:%NC%
echo   yhrds start  - 启动 SSH 隧道
echo   yhrds stop   - 停止 SSH 隧道
echo   yhrds status - 检查隧道状态
echo   yhrds test   - 测试端口连通性
echo   yhrds info   - 显示此信息
echo.
goto :eof

REM 主要逻辑
if "%1"=="" goto :show_help
if "%1"=="help" goto :show_help
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help
if "%1"=="/?" goto :show_help

if "%1"=="start" (
    call :start_tunnel
    exit /b %errorlevel%
)

if "%1"=="stop" (
    call :stop_tunnel
    exit /b %errorlevel%
)

if "%1"=="status" (
    call :check_status
    exit /b %errorlevel%
)

if "%1"=="test" (
    call :test_connection
    exit /b %errorlevel%
)

if "%1"=="info" (
    call :show_info
    exit /b 0
)

echo 未知命令: %1
echo 使用 'yhrds help' 查看可用命令
exit /b 1