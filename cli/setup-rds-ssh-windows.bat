@echo off
setlocal enabledelayedexpansion

REM =============================================================================
REM Yuan Hui RDS SSH 隧道脚本 - Windows 版本
REM =============================================================================
REM 用途：通过跳板机建立到 Aurora PostgreSQL 数据库的 SSH 隧道
REM 跳板机：dp.kh2u.com
REM 目标：生产环境 Aurora 集群
REM 本地端口：6333 (默认)
REM =============================================================================

REM 配置变量
set "BASTION_HOST=dp.kh2u.com"
if not defined BASTION_USER set "BASTION_USER=ec2-user"
set "DB_HOST=yuanhuiauroradatabase-pro-aurorapostgresqlcluster3-5g4olsblzz5x.cluster-c3c8ccka8h7t.ap-east-2.rds.amazonaws.com"
set "DB_PORT=5432"
if not defined LOCAL_PORT set "LOCAL_PORT=6333"
set "PID_FILE=%TEMP%\yhrds_tunnel.pid"
set "LOG_FILE=%TEMP%\yhrds_tunnel.log"
set "YHRDS_CONFIG_DIR=%USERPROFILE%\.yuanhui"
set "YHRDS_CONFIG_FILE=%YHRDS_CONFIG_DIR%\rds-ssh-config.bat"
set "YHRDS_COMMAND_FILE=%USERPROFILE%\bin\yhrds.bat"

REM 颜色代码
set "RED=[31m"
set "GREEN=[32m"
set "YELLOW=[33m"
set "BLUE=[34m"
set "NC=[0m"

REM 参数解析变量
set "UNINSTALL_MODE="
set "SSH_KEY_PATH="

REM 解析参数
:parse_args
if "%1"=="" goto :validate_args
if "%1"=="-h" goto :show_help
if "%1"=="--help" goto :show_help
if "%1"=="/?" goto :show_help
if "%1"=="--uninstall" (
    set "UNINSTALL_MODE=true"
    shift
    goto :parse_args
)
if "%1"=="-p" (
    set "LOCAL_PORT=%2"
    shift
    shift
    goto :parse_args
)
if "%1"=="--port" (
    set "LOCAL_PORT=%2"
    shift
    shift
    goto :parse_args
)
if "%1"=="-u" (
    set "BASTION_USER=%2"
    shift
    shift
    goto :parse_args
)
if "%1"=="--user" (
    set "BASTION_USER=%2"
    shift
    shift
    goto :parse_args
)
if "%SSH_KEY_PATH%"=="" (
    set "SSH_KEY_PATH=%1"
    set "SSH_KEY_PATH=%SSH_KEY_PATH:"=%"
    shift
    goto :parse_args
) else (
    echo %RED%[ERROR]%NC% 未知选项或多余参数: %1
    goto :show_help
)

:validate_args
REM 处理卸载模式
if "%UNINSTALL_MODE%"=="true" goto :uninstall_functions

REM 检查必需参数
if "%SSH_KEY_PATH%"=="" (
    echo %RED%[ERROR]%NC% 缺少 SSH 密钥文件路径
    goto :show_help
)

:show_help
echo Yuan Hui RDS SSH 隧道脚本 - Windows 版本
echo.
echo 用法: %0 ^<SSH_KEY_PATH^> [OPTIONS]
echo       %0 --uninstall
echo.
echo 参数:
echo     SSH_KEY_PATH     SSH 私钥文件路径 (必需，除非使用 --uninstall)
echo.
echo 选项:
echo     -p, --port       本地端口 (默认: 6333)
echo     -u, --user       SSH 用户名 (默认: ec2-user)
echo     --uninstall      从系统中移除全局函数
echo     -h, --help       显示此帮助信息
echo.
echo 环境变量:
echo     LOCAL_PORT       本地端口
echo     BASTION_USER     SSH 用户名
echo.
echo 示例:
echo     %0 C:\Users\<USER>\.ssh\my-key.pem              # 安装全局函数
echo     %0 C:\Users\<USER>\.ssh\my-key.pem -p 5433      # 自定义端口
echo     %0 C:\Users\<USER>\.ssh\my-key.pem -u admin     # 自定义用户
echo     %0 --uninstall                                # 卸载
echo.
echo 连接信息:
echo     跳板机: %BASTION_HOST%
echo     数据库: %DB_HOST%
echo     本地端口: %LOCAL_PORT%
echo.
echo 安装后可用的全局函数:
echo     yhrds_start      启动 SSH 隧道
echo     yhrds_stop       停止 SSH 隧道
echo     yhrds_status     检查隧道状态
echo     yhrds_test       测试端口连通性
echo     yhrds_info       显示连接信息
echo.
goto :eof

REM 脚本入口点
call :main %*
goto :eof

:main
REM 创建日志文件
type nul > "%LOG_FILE%"

call :log_info "Yuan Hui RDS SSH 隧道脚本启动 - Windows"

REM 解析参数
call :parse_args %*

REM 处理卸载模式
if "%UNINSTALL_MODE%"=="true" (
    call :log_info "卸载模式：移除永久安装的全局函数"
    call :uninstall_functions
    call :log_success "卸载完成"
    goto :eof
)

REM 检查必需参数
if "%SSH_KEY_PATH%"=="" (
    call :log_error "缺少 SSH 密钥文件路径"
    call :show_help
    exit /b 1
)

REM 检查 SSH 密钥文件
if not exist "%SSH_KEY_PATH%" (
    call :log_error "SSH 密钥文件不存在: %SSH_KEY_PATH%"
    exit /b 1
)

REM 永久安装模式（默认行为）
call :log_info "将全局函数永久安装到系统"
call :log_info "配置参数："
call :log_info "  SSH 密钥: %SSH_KEY_PATH%"
call :log_info "  跳板机用户: %BASTION_USER%"
call :log_info "  本地端口: %LOCAL_PORT%"
echo.

set /p "confirm=确认要将全局函数安装到您的系统吗？[y/N] "
if /i "%confirm%"=="y" (
    call :install_functions "%SSH_KEY_PATH%" "%LOCAL_PORT%" "%BASTION_USER%"
    call :log_success "安装完成"
) else (
    call :log_info "安装已取消"
)

goto :eof

REM =============================================================================
REM 永久安装和卸载函数
REM =============================================================================

:install_functions
set "install_ssh_key=%~1"
set "install_local_port=%~2"
set "install_bastion_user=%~3"

call :log_info "开始安装全局命令到系统..."

REM 创建配置目录
if not exist "%YHRDS_CONFIG_DIR%" mkdir "%YHRDS_CONFIG_DIR%"

REM 创建用户bin目录
set "USER_BIN_DIR=%USERPROFILE%\bin"
if not exist "%USER_BIN_DIR%" mkdir "%USER_BIN_DIR%"

REM 复制命令脚本
set "SCRIPT_DIR=%~dp0"
set "SOURCE_COMMAND=%SCRIPT_DIR%yhrds.bat"

if not exist "%SOURCE_COMMAND%" (
    call :log_error "命令脚本文件不存在: %SOURCE_COMMAND%"
    goto :eof
)

copy "%SOURCE_COMMAND%" "%YHRDS_COMMAND_FILE%" >nul
if %errorlevel% neq 0 (
    call :log_error "复制命令脚本失败"
    goto :eof
)

REM 生成配置文件
call :generate_config "%install_ssh_key%" "%install_local_port%" "%install_bastion_user%"

REM 添加到 PATH
call :add_to_path "%USER_BIN_DIR%"

call :log_success "全局命令已安装"
call :log_info "配置方式：独立命令脚本 + 用户配置文件"
call :log_info "命令文件位置: %YHRDS_COMMAND_FILE%"
call :log_info "配置文件位置: %YHRDS_CONFIG_FILE%"
call :log_info ""
call :log_info "您现在可以在任何命令提示符窗口中使用以下命令："
call :log_info "  yhrds start  - 启动 SSH 隧道"
call :log_info "  yhrds stop   - 停止 SSH 隧道"
call :log_info "  yhrds status - 检查隧道状态"
call :log_info "  yhrds test   - 测试端口连通性"
call :log_info "  yhrds info   - 显示连接信息"
call :log_info ""
call :log_info "注意：您可能需要重新打开命令提示符窗口以使更改生效"

goto :eof

:generate_config
set "config_ssh_key=%~1"
set "config_local_port=%~2"
set "config_bastion_user=%~3"

(
echo REM Yuan Hui RDS SSH 隧道配置文件
echo REM 生成时间: %date% %time%
echo REM 版本: 2.0.0
echo.
echo set "YHRDS_SSH_KEY_PATH=%config_ssh_key%"
echo set "YHRDS_LOCAL_PORT=%config_local_port%"
echo set "YHRDS_BASTION_USER=%config_bastion_user%"
) > "%YHRDS_CONFIG_FILE%"

call :log_success "配置文件已生成: %YHRDS_CONFIG_FILE%"
goto :eof

:uninstall_functions
call :log_info "卸载模式：移除全局命令"

set "USER_BIN_DIR=%USERPROFILE%\bin"
set "OLD_INSTALL_DIR=%APPDATA%\YuanhuiRDS"

REM 删除新版本的命令文件
if exist "%YHRDS_COMMAND_FILE%" (
    del "%YHRDS_COMMAND_FILE%" 2>nul
    call :log_success "已删除命令文件: %YHRDS_COMMAND_FILE%"
)

REM 删除配置文件
if exist "%YHRDS_CONFIG_FILE%" (
    del "%YHRDS_CONFIG_FILE%" 2>nul
    call :log_success "已删除配置文件: %YHRDS_CONFIG_FILE%"
)

REM 删除配置目录（如果为空）
if exist "%YHRDS_CONFIG_DIR%" (
    rmdir "%YHRDS_CONFIG_DIR%" 2>nul
    if %errorlevel% equ 0 (
        call :log_success "已删除配置目录: %YHRDS_CONFIG_DIR%"
    )
)

REM 清理旧版本安装（兼容性）
if exist "%OLD_INSTALL_DIR%" (
    call :log_info "检测到旧版本安装，正在清理..."
    call :remove_from_path "%OLD_INSTALL_DIR%"
    rmdir /s /q "%OLD_INSTALL_DIR%" 2>nul
    call :log_success "已清理旧版本安装"
)

call :log_success "全局命令已从系统中移除"
call :log_info "您可能需要重新打开命令提示符窗口以使更改生效"

goto :eof



:add_to_path
set "add_path=%~1"

REM 获取当前用户 PATH
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "current_path=%%b"

REM 检查是否已存在
echo %current_path% | findstr /C:"%add_path%" >nul
if not errorlevel 1 (
    call :log_info "路径已存在于 PATH 中"
    goto :eof
)

REM 添加到 PATH
if "%current_path%"=="" (
    set "new_path=%add_path%"
) else (
    set "new_path=%current_path%;%add_path%"
)

reg add "HKCU\Environment" /v PATH /t REG_EXPAND_SZ /d "%new_path%" /f >nul
if errorlevel 1 (
    call :log_error "无法更新 PATH 环境变量"
) else (
    call :log_info "已将 %add_path% 添加到 PATH"
)

goto :eof

:remove_from_path
set "remove_path=%~1"

REM 获取当前用户 PATH
for /f "tokens=2*" %%a in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "current_path=%%b"

REM 移除路径
set "new_path=%current_path%"
call set "new_path=%%new_path:%remove_path%;=%%"
call set "new_path=%%new_path:;%remove_path%=%%"
call set "new_path=%%new_path:%remove_path%=%%"

reg add "HKCU\Environment" /v PATH /t REG_EXPAND_SZ /d "%new_path%" /f >nul
if errorlevel 1 (
    call :log_error "无法更新 PATH 环境变量"
) else (
    call :log_info "已从 PATH 中移除 %remove_path%"
)

goto :eof

REM =============================================================================
REM 辅助函数
REM =============================================================================

:log
echo %~1
echo %~1 >> "%LOG_FILE%"
goto :eof

:log_info
call :log "%BLUE%[INFO]%NC% %~1"
goto :eof

:log_success
call :log "%GREEN%[SUCCESS]%NC% %~1"
goto :eof

:log_warning
call :log "%YELLOW%[WARNING]%NC% %~1"
goto :eof

:log_error
call :log "%RED%[ERROR]%NC% %~1"
goto :eof

:check_port
netstat -an | findstr ":%1 " >nul 2>&1
goto :eof

:is_tunnel_running
if not exist "%PID_FILE%" exit /b 1
set /p tunnel_pid=<"%PID_FILE%"
tasklist /FI "PID eq %tunnel_pid%" 2>nul | findstr "%tunnel_pid%" >nul
if errorlevel 1 (
    del /q "%PID_FILE%" 2>nul
    exit /b 1
)
exit /b 0

