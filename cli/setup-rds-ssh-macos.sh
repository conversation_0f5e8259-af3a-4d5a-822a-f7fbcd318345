#!/bin/bash

# =============================================================================
# Yuan Hui RDS SSH 隧道脚本 - macOS 版本
# =============================================================================
# 用途：通过跳板机建立到 Aurora PostgreSQL 数据库的 SSH 隧道
# 跳板机：dp.kh2u.com
# 目标：生产环境 Aurora 集群
# 本地端口：6333 (默认)
# =============================================================================

set -eo pipefail

# 防护VS Code终端环境变量冲突
export RPROMPT="${RPROMPT:-}"

# 配置变量
readonly BASTION_HOST="dp.kh2u.com"
readonly BASTION_USER="${BASTION_USER:-ec2-user}"
readonly DB_HOST="yuanhuiauroradatabase-pro-aurorapostgresqlcluster3-5g4olsblzz5x.cluster-c3c8ccka8h7t.ap-east-2.rds.amazonaws.com"
readonly DB_PORT="5432"
readonly LOCAL_PORT="${LOCAL_PORT:-6333}"
readonly PID_FILE="/tmp/yhrds_tunnel.pid"
readonly LOG_FILE="/tmp/yhrds_tunnel.log"
readonly YHRDS_CONFIG_DIR="$HOME/.yuanhui"
readonly YHRDS_FUNCTIONS_FILE="$YHRDS_CONFIG_DIR/rds-ssh-functions.sh"
readonly YHRDS_CONFIG_FILE="$YHRDS_CONFIG_DIR/rds-ssh-config"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# 帮助信息
show_help() {
    cat << EOF
Yuan Hui RDS SSH 隧道脚本 - macOS 版本

用法: $0 <SSH_KEY_PATH> [OPTIONS]
      $0 --uninstall

参数:
    SSH_KEY_PATH     SSH 私钥文件路径 (必需，除非使用 --uninstall)

选项:
    -p, --port       本地端口 (默认: 6333)
    -u, --user       SSH 用户名 (默认: ec2-user)
    --uninstall      从 shell 配置文件中移除全局函数
    -h, --help       显示此帮助信息

环境变量:
    LOCAL_PORT       本地端口
    BASTION_USER     SSH 用户名

示例:
    $0 ~/.ssh/my-key.pem              # 安装全局函数
    $0 ~/.ssh/my-key.pem -p 5433      # 自定义端口
    $0 ~/.ssh/my-key.pem -u admin     # 自定义用户
    $0 --uninstall                    # 卸载

连接信息:
    跳板机: ${BASTION_HOST}
    数据库: ${DB_HOST}
    本地端口: ${LOCAL_PORT}

安装后可用的全局函数:
    yhrds_start      启动 SSH 隧道
    yhrds_stop       停止 SSH 隧道
    yhrds_status     检查隧道状态
    yhrds_test       测试端口连通性
    yhrds_info       显示连接信息

EOF
}

# 日志函数
log() {
    echo -e "${1}" | tee -a "${LOG_FILE}"
}

log_info() {
    log "${BLUE}[INFO]${NC} $1"
}

log_success() {
    log "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    log "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    log "${RED}[ERROR]${NC} $1"
}

# 检查 SSH 密钥文件
check_ssh_key() {
    local key_file="$1"
    
    if [[ ! -f "$key_file" ]]; then
        log_error "SSH 密钥文件不存在: $key_file"
        return 1
    fi
    
    # 检查文件权限
    local perms=$(stat -f "%OLp" "$key_file")
    if [[ "$perms" != "600" ]] && [[ "$perms" != "400" ]]; then
        log_warning "SSH 密钥文件权限不安全: $perms"
        log_info "建议执行: chmod 600 $key_file"
    fi
    
    return 0
}

# 检查端口是否被占用
check_port() {
    local port="$1"
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口空闲
    fi
}

# 检查隧道是否运行
is_tunnel_running() {
    if [[ -f "$PID_FILE" ]]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # 运行中
        else
            # PID 文件存在但进程不存在，清理文件
            rm -f "$PID_FILE"
            return 1  # 未运行
        fi
    else
        return 1  # 未运行
    fi
}

# 检测 shell 配置文件
detect_shell_config() {
    local config_file=""
    
    # 根据当前 shell 检测配置文件
    case "${SHELL##*/}" in
        zsh)
            config_file="$HOME/.zshrc"
            ;;
        bash)
            if [[ -f "$HOME/.bashrc" ]]; then
                config_file="$HOME/.bashrc"
            elif [[ -f "$HOME/.bash_profile" ]]; then
                config_file="$HOME/.bash_profile"
            else
                config_file="$HOME/.profile"
            fi
            ;;
        fish)
            config_file="$HOME/.config/fish/config.fish"
            ;;
        *)
            # 默认使用 .profile
            config_file="$HOME/.profile"
            ;;
    esac
    
    echo "$config_file"
}

# 备份配置文件
backup_config() {
    local config_file="$1"
    local backup_file="${config_file}.yhrds_backup_$(date +%Y%m%d_%H%M%S)"
    
    if [[ -f "$config_file" ]]; then
        cp "$config_file" "$backup_file"
        log_info "配置文件已备份到: $backup_file"
        return 0
    fi
    
    return 1
}

# 复制独立函数文件
copy_functions_file() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local source_functions_file="$script_dir/rds-ssh-functions.sh"
    
    if [[ ! -f "$source_functions_file" ]]; then
        log_error "独立函数文件不存在: $source_functions_file"
        return 1
    fi
    
    # 创建配置目录
    mkdir -p "$YHRDS_CONFIG_DIR"
    
    # 复制函数文件
    cp "$source_functions_file" "$YHRDS_FUNCTIONS_FILE"
    chmod +x "$YHRDS_FUNCTIONS_FILE"
    
    log_success "函数文件已复制到: $YHRDS_FUNCTIONS_FILE"
    return 0
}

# 生成用户配置文件
generate_config() {
    local ssh_key_path="$1"
    local local_port="$2"
    local bastion_user="$3"
    
    cat > "$YHRDS_CONFIG_FILE" << EOF
# Yuan Hui RDS SSH 隧道配置文件
# 生成时间: $(date '+%Y-%m-%d %H:%M:%S')
# 版本: 2.0.0

export YHRDS_SSH_KEY_PATH="$ssh_key_path"
export YHRDS_LOCAL_PORT="$local_port"
export YHRDS_BASTION_USER="$bastion_user"
EOF
    
    log_success "配置文件已生成: $YHRDS_CONFIG_FILE"
}

# 安装全局函数到配置文件
install_functions() {
    local ssh_key_path="$1"
    local local_port="$2"
    local bastion_user="$3"
    
    local config_file=$(detect_shell_config)
    
    log_info "检测到的 shell 配置文件: $config_file"
    
    # 确保配置文件存在
    if [[ ! -f "$config_file" ]]; then
        log_info "配置文件不存在，创建新文件: $config_file"
        mkdir -p "$(dirname "$config_file")"
        touch "$config_file"
    fi
    
    # 备份配置文件
    backup_config "$config_file"
    
    # 检查是否已安装旧版本
    if grep -q "Yuan Hui RDS SSH 隧道全局函数" "$config_file" 2>/dev/null; then
        log_warning "检测到已安装的旧版本函数，将进行清理"
        uninstall_functions_silent
    fi
    
    # 检查是否已有新版本的引用
    if grep -q "rds-ssh-functions.sh" "$config_file" 2>/dev/null; then
        log_warning "检测到已安装的函数引用，将进行更新"
        # 删除现有的引用行
        sed -i.tmp '/rds-ssh-functions.sh/d' "$config_file"
        rm -f "${config_file}.tmp"
    fi
    
    # 复制独立函数文件
    if ! copy_functions_file; then
        return 1
    fi
    
    # 生成用户配置
    generate_config "$ssh_key_path" "$local_port" "$bastion_user"
    
    # 在 shell 配置文件中添加简洁的引用
    echo "" >> "$config_file"
    echo "# Yuan Hui RDS SSH 隧道函数 (v2.0.0)" >> "$config_file"
    echo "[ -f \"$YHRDS_FUNCTIONS_FILE\" ] && source \"$YHRDS_FUNCTIONS_FILE\"" >> "$config_file"
    
    log_success "全局函数已安装到: $config_file"
    log_info "配置方式：独立函数文件 + 简洁引用（仅1行）"
    log_info "函数文件位置: $YHRDS_FUNCTIONS_FILE"
    log_info "配置文件位置: $YHRDS_CONFIG_FILE"
    log_info ""
    log_info "请运行以下命令重新加载配置文件："
    log_info "  source $config_file"
    log_info ""
    log_info "或者重新打开终端，然后可以使用以下全局函数："
    log_info "  yhrds_start  - 启动 SSH 隧道"
    log_info "  yhrds_stop   - 停止 SSH 隧道"
    log_info "  yhrds_status - 检查隧道状态"
    log_info "  yhrds_test   - 测试端口连通性"
    log_info "  yhrds_info   - 显示连接信息"
}

# 静默卸载全局函数（用于更新时）
uninstall_functions_silent() {
    local config_file=$(detect_shell_config)
    
    if [[ ! -f "$config_file" ]]; then
        return 0
    fi
    
    # 删除旧版本的内容（大块函数代码）
    if grep -q "Yuan Hui RDS SSH 隧道全局函数" "$config_file" 2>/dev/null; then
        sed -i.tmp '/# Yuan Hui RDS SSH 隧道全局函数/,/# End Yuan Hui RDS SSH 隧道全局函数/d' "$config_file"
        rm -f "${config_file}.tmp"
    fi
    
    # 删除新版本的引用行
    if grep -q "rds-ssh-functions.sh" "$config_file" 2>/dev/null; then
        sed -i.tmp '/rds-ssh-functions.sh/d' "$config_file"
        sed -i.tmp '/# Yuan Hui RDS SSH 隧道函数/d' "$config_file"
        rm -f "${config_file}.tmp"
    fi
}

# 卸载全局函数
uninstall_functions() {
    local config_file=$(detect_shell_config)
    
    log_info "检测到的 shell 配置文件: $config_file"
    
    if [[ ! -f "$config_file" ]]; then
        log_warning "配置文件不存在: $config_file"
    else
        # 备份配置文件
        backup_config "$config_file"
        
        # 删除配置文件中的引用
        uninstall_functions_silent
        log_success "已从配置文件中移除引用: $config_file"
    fi
    
    # 删除独立函数文件和配置
    if [[ -f "$YHRDS_FUNCTIONS_FILE" ]]; then
        rm -f "$YHRDS_FUNCTIONS_FILE"
        log_success "已删除函数文件: $YHRDS_FUNCTIONS_FILE"
    fi
    
    if [[ -f "$YHRDS_CONFIG_FILE" ]]; then
        rm -f "$YHRDS_CONFIG_FILE"
        log_success "已删除配置文件: $YHRDS_CONFIG_FILE"
    fi
    
    # 删除配置目录（如果为空）
    if [[ -d "$YHRDS_CONFIG_DIR" ]] && [[ -z "$(ls -A "$YHRDS_CONFIG_DIR")" ]]; then
        rmdir "$YHRDS_CONFIG_DIR"
        log_success "已删除配置目录: $YHRDS_CONFIG_DIR"
    fi
    
    log_success "全局函数已完全卸载"
    log_info "请运行以下命令重新加载配置文件："
    log_info "  source $config_file"
    log_info ""
    log_info "或者重新打开终端生效"
}


# 参数解析
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --uninstall)
                UNINSTALL_MODE=true
                shift
                ;;
            -p|--port)
                LOCAL_PORT="$2"
                shift 2
                ;;
            -u|--user)
                BASTION_USER="$2"
                shift 2
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [[ -z "${SSH_KEY_PATH:-}" ]]; then
                    SSH_KEY_PATH="$1"
                else
                    log_error "多余的参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# 主函数
main() {
    # 创建日志文件
    touch "${LOG_FILE}"
    
    log_info "Yuan Hui RDS SSH 隧道脚本启动 - macOS"
    
    # 解析参数
    parse_args "$@"
    
    # 处理卸载模式
    if [[ "${UNINSTALL_MODE:-false}" == "true" ]]; then
        log_info "卸载模式：移除永久安装的全局函数"
        uninstall_functions
        log_success "卸载完成"
        exit 0
    fi
    
    # 检查必需参数
    if [[ -z "${SSH_KEY_PATH:-}" ]]; then
        log_error "缺少 SSH 密钥文件路径"
        show_help
        exit 1
    fi
    
    # 检查 SSH 密钥
    if ! check_ssh_key "$SSH_KEY_PATH"; then
        exit 1
    fi
    
    # 永久安装模式（默认行为）
    log_info "将全局函数永久安装到 shell 配置文件"
    log_info "配置参数："
    log_info "  SSH 密钥: $SSH_KEY_PATH"
    log_info "  跳板机用户: ${BASTION_USER}"
    log_info "  本地端口: ${LOCAL_PORT}"
    echo ""
    
    # 询问用户确认
    read -p "确认要将全局函数安装到您的 shell 配置文件吗？[Y/n] " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log_info "安装已取消"
        exit 0
    fi
    
    install_functions "$SSH_KEY_PATH" "$LOCAL_PORT" "$BASTION_USER"
    log_success "安装完成"
    
    # 提示用户重新加载配置
    local config_file=$(detect_shell_config)
    echo ""
    log_info "要立即使用全局函数，请运行："
    log_info "  source $config_file"
    echo ""
    log_info "或者重新打开终端，然后可以使用："
    log_info "  yhrds_start  - 启动 SSH 隧道"
    log_info "  yhrds_info   - 查看连接信息"
}

# 脚本入口点
main "$@"